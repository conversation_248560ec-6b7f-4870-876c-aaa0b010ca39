import { isDev } from '../is-dev';

export function devLog(component: string, message: any): void {
  if (isDev) {
    console.log(`[smartdeer-ui: ${component}] `, message);
  }
}

export function devWarning(component: string, message: string): void {
  if (isDev) {
    console.warn(`[smartdeer-ui: ${component}] ${message}`);
  }
}

export function devError(component: string, message: string) {
  if (isDev) {
    console.error(`[smartdeer-ui: ${component}] ${message}`);
  }
}

export function prodError(component: string, message: string) {
  console.error(`[smartdeer-ui: ${component}] ${message}`);
}

export function prodWarning(component: string, message: string) {
  console.warn(`[smartdeer-ui: ${component}] ${message}`);
}
