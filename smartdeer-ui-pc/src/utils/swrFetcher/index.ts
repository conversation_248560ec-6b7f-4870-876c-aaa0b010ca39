import { isObject } from 'lodash';
import queryString from 'query-string';
import { Middleware, SWRHook, SWRResponse } from 'swr';
import type { EncryptType } from '../../components/config-provider/context';
import {
  getSageEntityAuthTokens,
  getSecurityToken,
  setLoginModalOpen,
  setSageEntityAuth,
  setSecurityCheckModal,
  toLogin,
} from '../../components/config-provider/utils';
import { ACCEPT_LANGUAGE, STORE, type ACCEPT_LANGUAGE_KEY } from '../../consts';
import type { FetchMethodType } from '../../typing';
import rsa from '../../utils/rsa';
import base64EncoderDecoder from '../base64EncoderDecoder';
import { generateRandomString } from '../generateRandomString';
import S from '../storage';

type FetcherRecordType = Record<string, any>;

type FetcherExtraType = {
  forceEncryptType?: EncryptType;
  [key: string]: any;
};

type FetchConfigType = {
  method: FetchMethodType;
  headers: Record<string, any>;
  body?: string | FormData;
};

export const getDomainName = () => {
  const APP_API_BASE_URL = S.get(STORE.APP_API_BASE_URL, true) || '';

  return APP_API_BASE_URL;
};

export const swrMiddlewareBefore = () => {
  const swrMiddleware: Middleware =
    (useSWRNext: SWRHook) => (key, fetcher, config) => {
      // hook 运行之前...

      // 处理下一个中间件，如果这是最后一个，则处理 `useSWR` hook。
      const swr = useSWRNext(key, fetcher, config);

      // hook 运行之后...

      return swr;
    };

  return swrMiddleware;
};

export const swrMiddlewareAfter = (fn: (swr: SWRResponse) => SWRResponse) => {
  const swrMiddleware: Middleware =
    (useSWRNext: SWRHook) => (key, fetcher, config) => {
      // hook 运行之前...

      // 处理下一个中间件，如果这是最后一个，则处理 `useSWR` hook。
      const swr = useSWRNext(key, fetcher, config);

      // hook 运行之后...

      const { data: res, isLoading } = swr;

      if (isLoading) return swr;

      const newSwr = fn(swr);

      return newSwr;
    };

  return swrMiddleware;
};

const getSageEntityAuthToken = (url: string) => {
  const entityTokens = getSageEntityAuthTokens();
  const regex = /\/entity\/(\d+)/;
  if (!regex.test(url)) return;
  const match = url.match(regex);
  let entityId = null;
  if (match) {
    entityId = match[1];
  }
  if (!entityId) return;
  const accessToken = entityTokens[entityId]?.token;

  if (!accessToken) {
    return;
  }
  return accessToken;
};

/**
 * 下载文件函数
 *
 * @param url 文件下载地址
 * @param fileName 下载后的文件名
 * @returns 无返回值，执行下载操作
 */
export const swrDownloadFile = async (
  url: string,
  fileName: string,
  method?: FetchMethodType,
  params?: FetcherRecordType,
  headers?: FetcherRecordType,
  returnBlob?: boolean, // 是否直接返回blob
): Promise<void | Blob> => {
  // 定义 fetchUrl 变量，用于保存最终的请求 URL
  const fetchUrl: string = url.includes('http') ? url : getDomainName() + url;

  // 定义 FetchConfig 对象，用于配置 fetch 请求的参数
  const fetchConfig: FetchConfigType = {
    method: method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: S.getAuthToken() || S.get(STORE.TEMPORARY_TOKEN),
      'Accept-Language':
        ACCEPT_LANGUAGE[S.get(STORE.LOCALE, true) as ACCEPT_LANGUAGE_KEY] ||
        'zh',
      ...(headers || {}),
    },
  };

  if (getSageEntityAuthToken(fetchUrl)) {
    fetchConfig.headers['X-SAGE-ENTITY-AUTH'] =
      getSageEntityAuthToken(fetchUrl);
  }

  if (method === 'POST' || method === 'PUT') {
    fetchConfig.body = JSON.stringify(params);
  }

  // 使用 fetch API 从给定的 URL 获取文件, 并传入 url 和 config 作为参数
  const response = await fetch(fetchUrl, fetchConfig);

  // 检查响应状态是否成功（通常在 200-299 之间）
  if (!response.ok) {
    // 如果响应不成功，抛出一个错误
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  // 根据响应头的Content-Type判断返回类型
  const contentType = response.headers.get('Content-Type');
  let blob;

  // 如果指定返回json或Content-Type包含json，则返回json格式
  if (contentType && contentType.includes('application/json')) {
    const jsonData = await response.json();
    if (jsonData.data) {
      // 新增：如果 data 是直链，前端再拉取一次并转为 blob
      const fileResp = await fetch(jsonData.data);
      blob = await fileResp.blob();
    }
  } else {
    // 将响应体转换为 Blob 对象，Blob 对象表示不可变的原始数据
    blob = await response.blob();
  }

  if (!blob) {
    throw new Error('No blob data available for download');
  }

  if (returnBlob) {
    return blob;
  }

  // 创建一个 URL 对象，用于处理文件和 URL 的相关操作
  const urlCreator = window.URL || window.webkitURL;

  // 创建一个新的 a标签元素，用于触发下载操作
  const link = document.createElement('a');

  // 设置a标签的href属性为Blob对象的URL，使得点击这个链接会下载Blob对象代表的文件
  link.href = urlCreator.createObjectURL(blob);

  // 设置a标签的download属性为期望的文件名，这样浏览器就知道下载时要使用的文件名
  link.setAttribute('download', fileName);

  // 将a标签添加到文档的body中，这样它才能被正确识别和处理
  document.body.appendChild(link);

  // 模拟点击a标签，触发下载操作
  link.click();

  // 清理：从文档的body中移除a标签，释放相关资源
  document.body.removeChild(link);
};

/**
 * 上传文件函数
 *
 * @param url 请求的 URL
 * @param file 上传的文件
 * @returns 无返回值，执行上传操作
 */
export const swrUploadFile = async (
  url: string,
  file: File,
  data?: FetcherRecordType,
) => {
  // 定义 fetchUrl 变量，用于保存最终的请求 URL
  const fetchUrl = url;

  // 创建一个 FormData 实例
  const formData = new FormData();

  // 添加文件到 FormData 对象
  formData.append('file', file); // 'file' 是服务器期望的键名
  if (isObject(data)) {
    Object.keys(data).forEach((key) => {
      formData.append(key, data[key]);
    });
  }

  // 定义 FetchConfig 对象，用于配置 fetch 请求的参数
  const fetchConfig: FetchConfigType = {
    method: 'POST',
    headers: {
      Authorization: S.getAuthToken(),
      'Accept-Language': 'zh',
    },
    body: formData,
  };

  if (getSageEntityAuthToken(fetchUrl)) {
    fetchConfig.headers['X-SAGE-ENTITY-AUTH'] =
      getSageEntityAuthToken(fetchUrl);
  }

  // 使用 fetch API 从给定的 URL 获取文件, 并传入 url 和 config 作为参数
  const response = await fetch(fetchUrl, fetchConfig);

  // 检查响应状态是否成功（通常在 200-299 之间）
  if (!response.ok) {
    // 如果响应不成功，抛出一个错误
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  // 将响应数据解析为 JSON 格式的字符串
  const res = await response.json();

  if (res.code === 401) {
    toLogin();
    setLoginModalOpen(true);
    return Promise.reject(res);
  }

  // 返回解析后的响应数据
  if (res.code !== 0) {
    return Promise.reject(res);
  }

  return res;
};

/**
 * 用于存储 fetch 请求的 Promise 缓存的 Map 对象
 *
 * @type {Map<string, Promise<any>>}
 */
const fetchPromisesCache: Map<string, Promise<any>> = new Map();

/**
 * 生成缓存键的辅助函数
 *
 * @param {string} url - 请求的 URL
 * @param {string} method - 请求的方法 (GET, POST, etc.)
 * @param {any} data - 请求的数据
 * @returns {string} - 生成的缓存键
 */
export const generateFetchCacheKey = (
  url: string,
  method: string,
  data: any,
): string => {
  const cacheData = `${JSON.stringify(data)}`;

  return `${url}-${method}-${cacheData}`;
};

/**
 * 清除整个缓存的函数
 */
export const clearFetchPromisesCache = () => {
  fetchPromisesCache.clear();
};

/**
 * 发起带缓存的 HTTP 请求的函数
 *
 * @param url 请求的 URL
 * @param method 请求方法，可选值为 'GET' 或 'POST'
 * @param data 请求参数，将根据请求方法转化为查询字符串或 JSON 格式的请求体
 * @param extra extra
 * @returns 返回解析后的响应数据
 */
export const cachedSwrFetcher = async (
  url: string,
  method: FetchMethodType,
  data: FetcherRecordType = {},
  extra: FetcherExtraType = {},
) => {
  const cacheKey = generateFetchCacheKey(url, method, data);

  // 检查缓存中是否已存在该请求
  if (fetchPromisesCache.has(cacheKey)) {
    return fetchPromisesCache.get(cacheKey)!; // 使用非空断言操作符，因为我们知道它存在
  }

  // 发起新的 fetch 请求
  const promise = swrFetcher(url, method, data, extra);

  // 将 Promise 存储在缓存中
  fetchPromisesCache.set(cacheKey, promise);

  return promise;
};

/**
 * 发起 HTTP 请求的函数
 *
 * @param url 请求的 URL
 * @param method 请求方法，可选值为 'GET' 或 'POST'
 * @param data 请求参数，将根据请求方法转化为查询字符串或 JSON 格式的请求体
 * @param extra extra
 * @returns 返回解析后的响应数据
 */
const swrFetcher = async (
  url: string,
  method: FetchMethodType,
  data: FetcherRecordType = {},
  extra: FetcherExtraType = {},
) => {
  const { forceEncryptType } = extra;

  // 请求参数
  let params = data;

  // 定义 fetchUrl 变量，用于保存最终的请求 URL
  let fetchUrl: string = url.includes('http') ? url : getDomainName() + url;

  // 转大写
  const methodUppercase = method.toLocaleUpperCase() as FetchMethodType;

  // 定义 FetchConfig 对象，用于配置 fetch 请求的参数
  const fetchConfig: FetchConfigType = {
    method: methodUppercase,
    headers: {
      'Content-Type': 'application/json',
      'Accept-Language':
        ACCEPT_LANGUAGE[S.get(STORE.LOCALE, true) as ACCEPT_LANGUAGE_KEY],
      Authorization: S.getAuthToken(),
    },
    ...extra,
  };

  if (getSageEntityAuthToken(fetchUrl)) {
    fetchConfig.headers['X-SAGE-ENTITY-AUTH'] =
      getSageEntityAuthToken(fetchUrl);
  }

  const isRequestId = S.get(STORE.IS_REQUES_ID);

  const isFunctionOrProcess =
    isObject(params) && ('functionKey' in params || 'processKey' in params);

  const securityToken = getSecurityToken();

  if (securityToken && isFunctionOrProcess) {
    params = { ...params, cover: { token: securityToken } };
  }

  // 添加 requestId
  if (isRequestId && isFunctionOrProcess) {
    const requestId = generateRandomString();

    // 判断 URL 中是否包含问号，决定使用 ? 还是 / 来拼接 requestId
    const questionMarkIndex = fetchUrl.indexOf('?');
    if (questionMarkIndex !== -1) {
      // 在问号前插入 requestId
      fetchUrl = `${fetchUrl.slice(
        0,
        questionMarkIndex,
      )}/${requestId}${fetchUrl.slice(questionMarkIndex)}`;
    } else {
      // 在 URL 末尾添加 requestId
      fetchUrl = `${fetchUrl}/${requestId}`;
    }
  }

  const isEncrypt = S.get(STORE.IS_ENCRYPT) && isFunctionOrProcess;

  //   加密
  if (isEncrypt) {
    const encryptType = forceEncryptType || S.get(STORE.ENCRYPT_TYPE);

    const rsaPublicKey = S.get(STORE.RSA_PUBLIC_KEY);

    let payload: string = JSON.stringify(params);

    if (encryptType === 'RSA') {
      rsa.setPublicKey(rsaPublicKey);
      payload = rsa.encrypt(payload);
    } else if (encryptType === 'BASE64') {
      payload = base64EncoderDecoder.encrypt(payload);
    }

    params = { payload };
  }

  // 如果请求方法是 GET，则将 params 对象转化为查询字符串，并附加到 url 上
  if (methodUppercase === 'GET') {
    fetchUrl = queryString.stringifyUrl({ url: fetchUrl, query: params });
  }

  // 如果请求方法是 POST，则将 params 对象转化为 JSON 格式的字符串，并设置为请求体内容
  if (methodUppercase === 'POST' || methodUppercase === 'PUT') {
    fetchConfig.body = JSON.stringify(params);
  }

  // 使用 fetch 方法发送请求，并传入 url 和 config 作为参数
  const response = await fetch(fetchUrl, fetchConfig);

  // 将响应数据解析为 JSON 格式的字符串
  const res = await response.json();

  if (res.code === 401) {
    toLogin();
    setLoginModalOpen(true);
    return Promise.reject(res);
  }

  if (res.code === 6001) {
    // gs 打开安全校验弹窗
    setSecurityCheckModal(true, { ...res.data });
    // sage 打开登录弹窗
    setLoginModalOpen(true);
    return Promise.reject(res);
  }

  if (res.code === 6060) {
    setSageEntityAuth(false);
    return Promise.reject(res);
  }

  // 返回解析后的响应数据
  if (res.code !== 0) {
    return Promise.reject(res);
  }

  if (isEncrypt) {
    const encryptType = forceEncryptType || S.get(STORE.ENCRYPT_TYPE);
    const rsaPrivateKey = S.get(STORE.RSA_PRIVATE_KEY);

    if (encryptType === 'RSA') {
      rsa.setPrivateKey(rsaPrivateKey);

      res.data = rsa.decrypt(res.data);
    } else if (encryptType === 'BASE64') {
      res.data = base64EncoderDecoder.decrypt(res.data);
    }

    if (encryptType !== 'DISABLE') {
      res.data = JSON.parse(res.data);
    }
  }

  return res;
};

export default swrFetcher;
