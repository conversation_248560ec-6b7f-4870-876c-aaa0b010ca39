import { isArray, isObject } from 'lodash';
import { isSpecialValue } from '../isSpecialValue';
import pipe from '../pipe';

/**
 * 使用管道符号替换模板中的占位符
 *
 * @param template 模板字符串，其中包含形如 `*{key}` 的占位符
 * @param data 记录数据对象，用于替换模板中的占位符
 * @param regex 正则表达式, 使用正则表达式查找字符串中的参数占位符，例如：`*{key}`
 * @returns 生成的 HTML 字符串
 */
export const replaceTemplateWithPipes = (
  template: string,
  data: Record<string, any>,
  regex = /\*\{([^}]+)\}/g,
): string => {
  // 不存在，返回空。
  if (!template) return '';

  // 如果模板不是特殊值，则直接返回模板
  if (!isSpecialValue(template, regex)) return template;

  // 使用正则表达式替换模板中的占位符
  return template.replace(regex, (match, key: string) => {
    // 检查键中是否包含管道符号 '|'
    const hasPipe = key.includes('|');

    // 根据管道符号将键分割成两部分，键本身和管道函数数组
    const [rawKey, ...pipeFuncsStr] = hasPipe ? key.split('|') : [key, ''];

    // 将键按 '.' 分割成数组，用于处理形如 'a.b.c' 的键
    const keys = rawKey.split('.');

    // 当前值初始化为记录数据对象
    let value: any = data;

    // 遍历键数组，查找对应的值
    for (let i = 0; i < keys.length; i++) {
      // 如果当前值中存在键对应的属性
      if (value[keys[i].trim()] !== undefined) {
        // 更新当前值为键对应的属性值
        value = value[keys[i].trim()];
      } else {
        value = match;
      }
    }

    // 如果值是对象或数组，则将其转换为 JSON 字符串
    if (isObject(value) || isArray(value)) {
      value = JSON.stringify(value);
    } else {
      // 否则，将值转换为字符串
      value = String(value);
    }

    // 返回最终的值，用于替换模板中的占位符
    if (!hasPipe) {
      return value;
    }

    // 使用管道函数渲染处理后的值
    return pipe.render(
      value,
      // 去除管道函数字符串数组中的空白字符
      pipeFuncsStr.map((item) => item.trim()),
      data,
    );
  });
};
