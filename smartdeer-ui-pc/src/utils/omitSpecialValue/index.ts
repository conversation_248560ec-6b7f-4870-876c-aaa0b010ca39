import { isSpecialValue } from '../isSpecialValue';

type RecordType = Record<string, any>;

export const omitSpecialValue = (
  /**
   * 从对象中移除匹配特定正则表达式的特殊值
   *
   * @param obj 输入对象
   * @param regex 正则表达式，用于匹配需要移除的特殊值。默认为 /\*\{([^}]+)\}/
   * @returns 返回去除特殊值后的新对象。如果新对象为空，则返回 undefined
   */
  obj: RecordType,
  regex = /\*\{([^}]+)\}/,
): RecordType => {
  const newObj: RecordType = {};

  // 遍历输入对象的所有键
  Object.keys(obj || {}).forEach((key) => {
    // 如果当前值不是特殊值
    if (!isSpecialValue(obj[key], regex)) {
      // 将当前键值对添加到新对象中
      (newObj as any)[key] = obj[key];
    }
  });

  // 如果新对象为空
  if (Object.keys(newObj as Record<string, any>).length < 1) {
    // 返回 undefined
    return undefined as any;
  }

  // 返回新对象
  return newObj;
};

export default omitSpecialValue;
