import dayjs from 'dayjs';
import {isArray, isObject, isString} from 'lodash';
import {evaluate} from 'mathjs';
import base64EncoderDecoder, {
  base64Decode as globalBase64Decode,
} from '../base64EncoderDecoder';
import {isSpecialValue} from '../isSpecialValue';
import {devWarning} from '../log';
import {replaceVariablesInTemplate} from '../replaceVariablesInTemplate';
import {
  addTimezoneOffset,
  convertTimestampToTimezone,
  getUtcTimestamp,
} from '../time';
import {transformDataBasedOnRules} from '../transformDataBasedOnRules';
import {evaluateCondition} from "../evaluateCondition";

const truncateDecimal = (num: string | number, decimalPlaces = 3) => {
  const factor = Math.pow(10, decimalPlaces);
  return Math.floor(Number(num) * factor) / factor;
};

const specialValue = (
  target: any,
  propertyName: any,
  descriptor?: any,
): any => {
  if (!descriptor) return descriptor;

  const originalMethod = descriptor.value;

  if (typeof originalMethod !== 'function') {
    throw new Error(`@specialValue can only be used on methods.`);
  }

  descriptor.value = function (...args: any[]): any {
    const [firstValue] = args;

    if (isSpecialValue(firstValue)) return firstValue;

    try {
      const result = originalMethod.apply(this, args);
      return result;
    } catch (error) {
      console.error(`Error executing method ${propertyName}:`, error);
      throw error; // 重新抛出错误，让上层调用者处理
    }
  };

  return descriptor;
};

class Filters {
  /**
   * 在给定的时间值上添加指定的小时数，并返回新的时间戳
   *
   * @param value 时间值，字符串类型，格式为时间戳或可解析为时间戳的字符串
   * @param hour 要添加的小时数，字符串类型，表示小时数
   * @returns 返回新的时间戳，数值类型
   */
  @specialValue
  public addHour(value: string, hour = '0'): number {
    return dayjs(Number(value)).add(Number(hour), 'hour').valueOf();
  }

  /**
   * 在指定日期上增加指定的天数，并返回增加后的日期时间戳（毫秒级）
   *
   * @param value 初始日期，字符串类型，格式为数字（如：'1609459200'）
   * @param day 增加的天数，字符串类型，格式为数字（如：'3'）
   * @returns 返回增加后的日期时间戳（毫秒级），类型为数字
   */
  @specialValue
  public addDay(value: string, day = '0'): number {
    return dayjs(Number(value)).add(Number(day), 'day').valueOf();
  }

  /**
   * 向指定日期添加指定的周数
   *
   * @param value 日期的字符串表示形式，需为可以转换为数字的类型，用于表示日期的Unix时间戳
   * @param week 要添加的周数，需为可以转换为数字的类型
   * @returns 返回添加周数后的日期的Unix时间戳
   */
  @specialValue
  public addWeek(value: string, week = 0): number {
    return dayjs(Number(value)).add(Number(week), 'week').valueOf();
  }

  /**
   * 向指定日期添加月份，并返回新日期的时间戳
   *
   * @param value 原始日期，字符串类型，格式为可转换为数字的日期字符串
   * @param month 需要添加的月份数，字符串类型，可转换为数字的字符串
   * @returns 新日期的时间戳
   */
  @specialValue
  public addMonth(value: string, month = '0'): number {
    return dayjs(Number(value)).add(Number(month), 'month').valueOf();
  }

  /**
   * 从给定的时间值中减去指定的小时数
   *
   * @param value 时间值，字符串类型，需要转换为数字表示
   * @param hour 减去的小时数，字符串类型，需要转换为数字表示
   * @returns 返回减去指定小时数后的时间戳（毫秒为单位）
   */
  @specialValue
  public subtractHour(value: string, hour = '0'): number {
    return dayjs(Number(value)).subtract(Number(hour), 'hour').valueOf();
  }

  /**
   * 从给定日期减去指定天数，并返回结果的时间戳（毫秒数）
   *
   * @param value 日期的字符串表示，应能够被转换为数字类型（如：UNIX时间戳）
   * @param day 要减去的天数，应为字符串类型的数字
   * @returns 返回减去指定天数后的日期的时间戳（毫秒数）
   */
  @specialValue
  public subtractDay(value: string, day = '0'): number {
    return dayjs(Number(value)).subtract(Number(day), 'day').valueOf();
  }

  /**
   * 从给定的日期值中减去指定周数，并返回减去后的时间戳
   *
   * @param value 日期值，字符串类型，表示需要减去的日期
   * @param week 周数，字符串类型，表示需要减去的周数
   * @returns 返回减去指定周数后的时间戳，类型为数字
   */
  @specialValue
  public subtractWeek(value: string, week = '0'): number {
    return dayjs(Number(value)).subtract(Number(week), 'week').valueOf();
  }

  /**
   * 从当前时间戳中减去指定的周数
   *
   * @param week 周数，字符串类型，表示需要减去的周数
   * @returns 返回减去指定周数后的时间戳，类型为数字
   */
  @specialValue
  public subtractMonth(value: string, month = '0'): number {
    return dayjs(Number(value)).subtract(Number(month), 'month').valueOf();
  }

  /**
   * 格式化日期函数
   *
   * @param value 需要被格式化的时间戳
   * @param format 格式化的样式，默认为'YYYY-MM-DD'
   * @returns 返回格式化后的日期字符串
   */
  public formatDate(value: string, format = 'YYYY-MM-DD'): string {
    console.warn('formatDate函数已弃用，请改用formatTime函数。');
    // 如果传入的值是空或者无效（例如：无效的日期字符串），则返回短横线'-'
    if (isNaN(Number(value)) || Number(value) === 0) return '-';

    // 使用dayjs库来格式化日期，并返回格式化后的结果
    return dayjs(Number(value)).format(format || 'YYYY-MM-DD');
  }

  /**
   * 格式化时间函数
   *
   * @param value 需要被格式化的时间戳
   * @param format 格式化的样式，默认为'YYYY-MM-DD'
   * @returns 返回格式化后的日期字符串
   */
  @specialValue
  public formatTime(value: string, format = 'YYYY-MM-DD'): string {
    // 如果传入的值是空或者无效（例如：无效的日期字符串），则返回短横线'-'
    if (isNaN(Number(value)) || Number(value) === 0) return '-';

    // 使用dayjs库来格式化日期，并返回格式化后的结果
    return dayjs(Number(value)).format(format || 'YYYY-MM-DD');
  }

  /**
   * 出生日子转年龄
   *
   * @param value 出生日期
   * @returns 年龄
   */
  @specialValue
  public convertToAge(value: string): string {
    if (isNaN(Number(value)) || Number(value) === 0) return '-';
    const birth = dayjs(Number(value)).format('YYYY-MM-DD');
    const ageDifMs = Date.now() - new Date(birth).getTime();
    if (ageDifMs <= 0) {
      return '0';
    }
    const ageDate = new Date(ageDifMs);
    const age = Math.abs(ageDate.getUTCFullYear() - 1970);
    return age.toString();
  }

  /**
   * 将特定格式化的时间字符串转换为时间戳
   * @param time 特定格式化的时间字符串
   * @returns 返回时间戳
   */
  public convertToTimestamp(time: string | number, format = 'M/D/YY'): string {
    const inputTime = Number(time);
    if (!isNaN(inputTime) && inputTime > 0) {
      return time.toString();
    }
    const parsedDate = dayjs(time, format);

    if (parsedDate.isValid()) {
      return parsedDate.valueOf() + '';
    }

    return '';
  }

  /**
   * 传入时间和当前时间相差的天数
   * @param value 目标时间的时间戳
   * @returns 返回传入时间和当前时间相差的天数
   */
  @specialValue
  public diffDaysFromNow(value: string): number | string {
    if (isNaN(Number(value)) || Number(value) === 0) return '';
    const diffDays = dayjs(Number(value)).diff(dayjs().startOf('day'), 'day');
    return diffDays;
  }

  /**
   * 对格式化时间倒计时提示
   * @param value 需要被格式化的时间戳
   * @param format 格式化的样式，默认为'YYYY-MM-DD'
   * @returns 返回格式化后的日期及提示信息字符串
   */
  @specialValue
  public countdownToStr(value: string): string {
    if (isNaN(Number(value)) || Number(value) === 0) return '';

    let tip = '';
    const diffDays = dayjs(Number(value)).diff(dayjs().startOf('day'), 'day');

    if (diffDays < 0) {
      tip = `已过期 ${-diffDays} 天`;
    } else if (diffDays === 0) {
      tip = '今天';
    } else if (diffDays <= 3) {
      tip = `还剩 ${diffDays} 天`;
    } else {
      tip = `还有 ${diffDays} 天`;
    }
    return tip;
  }

  /**
   * 格式化时间并进行倒计时提示
   *
   * @param value 需要被格式化的时间戳
   * @param format 格式化的样式，默认为'YYYY-MM-DD'
   * @returns 返回格式化后的日期字符串
   */
  @specialValue
  public formatTimeTip(value: string, format = 'YYYY-MM-DD'): string {
    if (isNaN(Number(value)) || Number(value) === 0) return '-';

    const formattedDate = dayjs(Number(value)).format(format || 'YYYY-MM-DD');

    let tip = '';
    const diffDays = dayjs(Number(value)).diff(dayjs().startOf('day'), 'day');

    if (diffDays < 0) {
      tip = `已过期 ${-diffDays} 天`;
    } else if (diffDays === 0) {
      tip = '今天';
    } else if (diffDays <= 3) {
      tip = `还剩 ${diffDays} 天`;
    } else {
      tip = `还有 ${diffDays} 天`;
    }
    return `${formattedDate} (${tip})`;
  }

  /**
   * 根据传入时间和当前时间的对比，返回不同颜色的字符串
   * @param value 需要判断的时间戳
   * @param colorString 颜色字符串
   * @returns 传入时间戳小于当前时间，返回左边颜色，否则返回右边颜色字符串
   */
  @specialValue
  public diffTimeColor(value: string, colorString: string): string {
    const [leftColor, rightColor] = colorString.split(',');
    if (isNaN(Number(value)) || Number(value) === 0) return rightColor;

    const diffDays = dayjs(Number(value)).diff(dayjs().startOf('day'), 'day');

    if (diffDays < 0) {
      return leftColor;
    } else {
      return rightColor;
    }
  }

  /**
   * 计算2个时间的耗时
   * @param startTime
   * @param endTime
   */
  @specialValue
  public calculateDurationInMinutes(startTime: string, endTime: string) {
    const start = dayjs(Number(startTime));
    const end = dayjs(Number(endTime));
    const totalMinutes = end.diff(start, 'minute');

    if (totalMinutes < 60) {
      return `${totalMinutes}分钟`;
    } else if (totalMinutes < 1440) {
      // 1440 分钟 = 1 天
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      return `${hours}小时${minutes}分钟`;
    } else {
      const days = Math.floor(totalMinutes / 1440);
      const remainingMinutesAfterDays = totalMinutes % 1440;
      const hours = Math.floor(remainingMinutesAfterDays / 60);
      const minutes = remainingMinutesAfterDays % 60;
      return `${days}天${hours}小时${minutes}分钟`;
    }
  }

  /**
   * 如果给定的值为空或者包含特定格式的子串，则返回默认值；否则返回原值。
   *
   * @param value 要检查的值
   * @param defaultValue 默认值，默认为 '-'
   * @returns 如果值为空或者包含特定格式的子串，则返回默认值；否则返回原值
   */
  public displayIfEmpty(value: any, defaultValue = '-'): string {
    if (!value) return defaultValue || '-';
    if (isSpecialValue(value)) return defaultValue || '-';

    return value;
  }

  public displayIfNil(value: any, defaultValue = ''): string {
    if (!value) return defaultValue;
    if (isSpecialValue(value)) return defaultValue;

    return value;
  }

  public defaultIfCondition(leftValue: any, op: string, rightValue: any, defaultValue?: string): string | undefined {
    if (evaluateCondition(op.trim(), leftValue.trim(), rightValue.trim())) {
      return defaultValue?.trim() || ''
    }
    return leftValue;
  }

  /**
   * 将布尔值字符串转换为对应的中文文本。
   *
   * @param value 布尔值字符串，只接受 'true' 或 'false'。
   * @returns 返回对应的中文文本，'true' 对应 '是'，'false' 对应 '否'。
   */
  @specialValue
  public booleanToText(value: string): string {
    return value === 'true' ? '是' : '否';
  }

  /**
   * 计算两个日期之间的小时差
   *
   * @param leftValue 左侧日期字符串，应为可转为数值的日期表示形式，如时间戳
   * @param rightValue 右侧日期字符串，应为可转为数值的日期表示形式，如时间戳
   * @returns 返回两个日期之间的小时差，如果左侧日期晚于右侧日期，则返回负数
   */
  @specialValue
  public dateDiffHour(leftValue: string, rightValue: string) {
    const leftDate = dayjs(Number(leftValue));
    const rightDate = dayjs(Number(rightValue));
    return leftDate.diff(rightDate, 'hour', true);
  }

  /**
   * 计算两个日期之间的日差
   *
   * @param leftValue 左侧日期字符串，应为可转为数值的日期表示形式，如时间戳
   * @param rightValue 右侧日期字符串，应为可转为数值的日期表示形式，如时间戳，如果不传默认为当天
   * @returns 返回两个日期之间的日差，如果左侧日期晚于右侧日期，则返回负数
   */
  @specialValue
  public dateDiffDay(leftValue: string, rightValue: string) {
    const leftDate = dayjs(Number(leftValue));
    const rightDate = rightValue
      ? dayjs(Number(rightValue))
      : dayjs().startOf('day');
    return leftDate.diff(rightDate, 'day', true);
  }

  /**
   * 对给定的字符串进行 URI 编码
   *
   * @param value 要进行 URI 编码的字符串
   * @returns 编码后的字符串
   */
  @specialValue
  public encodeURI(value: string) {
    return encodeURIComponent(value);
  }

  /**
   * 判断字符串是否包含指定子串
   *
   * @param value 目标字符串
   * @param str 待搜索的子串
   * @returns 如果目标字符串中包含特殊占位符，则返回目标字符串；否则，返回目标字符串是否包含子串的布尔值
   */
  @specialValue
  public includes(value: string, str: string) {
    const reg = /\*\{([^}]+)\}/g;
    if (reg.test(value)) {
      return value;
    } else {
      return value.includes(str);
    }
  }

  /**
   * 判断字符串是否被指定字符串包含
   *
   * @param value 目标字符串
   * @param str 待搜索的子串
   * @returns 如果目标字符串中包含特殊占位符，则返回目标字符串；否则，返回目标字符串是否被指定字符串包含的布尔值
   */
  @specialValue
  public involved(value: string, str: string) {
    const reg = /\*\{([^}]+)\}/g;
    if (reg.test(value)) {
      return value;
    } else {
      return str.includes(value);
    }
  }

  /**
   * 为时间戳添加时区偏移量
   *
   * @param timestamp 时间戳，可以是字符串或数字
   * @param timezoneOffset 时区偏移量，默认为'+08:00'
   * @returns 返回新的时间戳
   */
  @specialValue
  public addTimezoneOffset(
    timestamp: string | number,
    timezoneOffset = '+08:00',
  ) {
    return addTimezoneOffset(timestamp, timezoneOffset);
  }

  /**
   * 将给定的时间戳按照指定的时区字符串转换为UTC时间戳
   *
   * @param timestamp 要转换的时间戳，可以是字符串或数字
   * @param timezoneString 时区字符串
   * @returns 转换后的UTC时间戳
   */
  @specialValue
  public getUtcTimestamp(
    timestamp: string | number,
    timezoneString = 'Asia/Shanghai',
  ) {
    return getUtcTimestamp(timestamp, timezoneString);
  }

  /**
   * 将给定的时间戳按照指定的时区字符串转换为对应的时间字符串
   *
   * @param timestamp 要转换的时间戳，可以是字符串或数字
   * @param timezoneString 时区字符串，默认为 'Asia/Shanghai'
   * @returns 转换后的时间字符串
   */
  @specialValue
  public convertTimestampToTimezone(
    timestamp: string | number,
    timezoneString = 'Asia/Shanghai',
    formatString = 'YYYY-MM-DD',
  ) {
    return convertTimestampToTimezone(timestamp, timezoneString, formatString);
  }

  /**
   * 将字符串中的逗号替换为空格
   *
   * @param value 需要格式化的字符串, 如: +86,12345678
   * @returns 返回格式化后的字符串, 如: +86 12345678
   */
  @specialValue
  public replaceCommaWithSpace(value: string) {
    return value.replace(/,/g, ' ');
  }

  /**
   * 处理字符串，返回的字符串中只保留规定字符
   * @param value 需要格式化的字符串, 如: +86, 1234 5678
   * @param allowedChars 允许保留的字符，用于构建正则，例如:'0-9,'(数字和逗号)
   * @returns 返回格式化后的字符串, 如: 86,12345678
   */
  @specialValue
  public retainAllowedCharacters(value: string, allowedChars: string) {
    // 将allowedChars中的特殊字符进行转义
    const escapedAllowedChars = allowedChars.replace(
      /[.*+?^${}()|[\]\\]/g,
      '\\$&',
    );

    // 使用转义后的allowedChars构建一个正则表达式，用于匹配不在allowedChars中的字符
    const regex = new RegExp(`[^${escapedAllowedChars}]`, 'g');

    // 使用正则表达式替换value中不在allowedChars中的字符为空字符串
    const filtered = value.replace(regex, '');

    // 返回过滤后的字符串
    return filtered;
  }

  /**
   * 格式化数字字符串
   *
   * @param numStr 数字字符串
   * @param decimalPlaces 小数位数，默认为2
   * @param locales 地域信息，默认为'en-US'
   * @returns 格式化后的数字字符串
   */
  @specialValue
  public formatNumber(numStr: string, decimalPlaces = '2', locales = 'en-US') {
    // 将数字字符串转换为数字（如果已经是数字则不需要）
    const num = parseFloat(numStr);

    // 使用Intl.NumberFormat来格式化数字
    // 第一个参数是locale，这里我们使用'en-US'表示美国英语
    // 第二个参数是一个选项对象，可以指定不同的格式化选项
    // currency: false 表示这不是货币，只是数字
    // minimumFractionDigits 和 maximumFractionDigits 用于控制小数位数
    const formatter = new Intl.NumberFormat(locales, {
      minimumFractionDigits: decimalPlaces ? Number(decimalPlaces) : 2,
      maximumFractionDigits: decimalPlaces ? Number(decimalPlaces) : 2,
      useGrouping: true,
    });

    // 格式化数字并返回
    return formatter.format(num);
  }

  /**
   * 将数组或字符串转换为字符串
   *
   * @param array 要转换的数组或字符串
   * @param separator 分隔符，默认为逗号（,）
   * @returns 转换后的字符串
   */
  @specialValue
  public arrayToString(array: string | string[], separator = ','): string {
    if (isArray(array) && array.length > 0) {
      return array.join(separator || ',');
    }

    return array as string;
  }

  /**
   * 将字符串转换为数组格式的 JSON 字符串
   *
   * @param value 待转换的字符串
   * @param separator 分隔符，默认为逗号（,）
   * @returns 转换后的 JSON 字符串，如果输入字符串为空，则返回 '[]'
   */
  @specialValue
  public stringToArray(value: string, separator = ','): string {
    if (value && value.length > 0) {
      return JSON.stringify(value.split(separator || ','));
    }
    return '[]';
  }

  @specialValue
  public arrayValueToString(values: any[]): string[] {
    if (values?.length > 0) {
      return values.map((value) => value.toString());
    }
    return [];
  }

  /**
   * 将字符串转换为数组格式
   *
   * @param value 待转换的字符串
   * @returns 转换后的数组，如果输入字符串为空，则返回 []
   */
  @specialValue
  public stringToObjectArray(value: string): Array<any> {
    let result = [];
    try {
      result = JSON.parse(value);
    } catch (error) {
      result = [];
    }
    return result;
  }

  /**
   * 将字符串或字符串数组转换为对象数组
   *
   * @param value 要转换的字符串或字符串数组
   * @param key 对象中的键名，默认为 'key'
   * @returns 转换后的对象数组，格式为 [{key: string}, {key: string}, ...]
   */
  @specialValue
  public stringArrayToObject(value: string | string[], key = 'key') {
    let arr: string[];
    if (Array.isArray(value)) {
      arr = value;
    } else {
      arr = JSON.parse(value);
    }
    const newKey = key || 'key';
    return JSON.stringify(arr.map((item) => ({[newKey]: item})));
  }

  /**
   * 将某个值, 包装成对象
   * @param value
   * @param key
   */
  @specialValue
  public toObject(value: any, key: string) {
    return JSON.stringify({[key]: JSON.parse(value)});
  }

  /**
   * 将数组对象格式化成你想要的返回的数组格式
   * @param value
   * @param keys
   */
  @specialValue
  public mapArray(value: any, keys: string) {
    let arr: string[];
    if (Array.isArray(value)) {
      arr = value;
    } else {
      arr = JSON.parse(value);
    }
    const splitKeys: any[] = keys.split(',');
    return JSON.stringify(
      arr.map((item) => {
        if (splitKeys.length === 1) {
          return item[splitKeys[0]];
        }
        const obj: Record<string, string> = {};
        splitKeys.forEach((key) => (obj[key] = item[key]));
        return obj;
      }),
    );
  }

  @specialValue
  public mapArrayWithRules(value: any, rules: string) {
    let arr: string[];
    if (Array.isArray(value)) {
      arr = value;
    } else if (isString(value)) {
      arr = JSON.parse(value);
    } else {
      arr = [];
    }
    try {
      const rulesStr = rules.replaceAll(';', ',');
      const rulesJson = JSON.parse(rulesStr)
      return arr.map((item: any) => {
        return transformDataBasedOnRules(item, rulesJson);
      });
    } catch (error: any) {
      console.error(error)
      return value;
    }
  }

  @specialValue
  public objectToArray(value: any, keyStr: string, valueStr: string) {
    if (!isObject(value)) {
      return value;
    }
    return Object.entries(value).map(([key, value]) => ({
      [keyStr || 'key']: key,
      [valueStr || 'value']: value,
    }));
  }


  @specialValue
  public indexOfArray(value: any, index: string) {
    if (!isArray(value)) {
      return value;
    }
    try {
      const i = parseInt(index);
      if (value.length > i) {
        return value[i];
      } else {
        return value
      }
    } catch (error: any) {
      console.error(error)
      return value
    }
  }

  /**
   * 解析 JSON 字符串
   *
   * @param value 要解析的 JSON 字符串
   * @returns 解析后的对象，如果解析失败则返回原值
   */
  public jsonParse(value: any) {
    try {
      return JSON.parse(value);
    } catch (error) {
      return value;
    }
  }

  /**
   * 将传入的值转换为 JSON 字符串
   *
   * @param value 需要转换的值
   * @returns 转换后的 JSON 字符串或原始值（如果转换失败）
   */
  public jsonStringify(value: any) {
    try {
      return JSON.stringify(value);
    } catch (error) {
      return value;
    }
  }

  /**
   * 删除当前对象或数据。
   *
   * @returns 总是返回 undefined。
   */
  public delete() {
    return undefined;
  }

  @specialValue
  public toString(value: string) {
    try {
      return value.toString();
    } catch (error) {
      return value;
    }
  }

  /**
   * 从HTML字符串中提取纯文本
   *
   * @param value 包含HTML标签的字符串
   * @returns 返回提取后的纯文本
   */
  @specialValue
  public getPlainTextFromHtml(value: string) {
    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = value;
      // 使用 textContent 或 innerText 取决于需要，textContent 更标准且不会触发重排
      return tempDiv.textContent || tempDiv.innerText;
    } catch (error) {
      return value;
    }
  }

  /**
   * 给字符串进行base64编码
   * @returns 返回base64编码后的字符串
   */
  @specialValue
  public base64Encode(value: string) {
    return base64EncoderDecoder.encrypt(value);
  }

  /**
   * 给字符串进行base64解码
   * @returns 返回base64编码后的字符串
   */
  @specialValue
  public base64Decode(value: string) {
    return globalBase64Decode(value);
  }

  /**
   * 获取一个区域的emoji
   * @returns 返回emoji
   */
  @specialValue
  public getRegionEmoji(str: string): string {
    let newStr = str;
    if (str === 'TW') newStr = 'CN';

    const firstLetter = newStr[0].toUpperCase().charCodeAt(0) - 0x41 + 0x1f1e6;
    const secondLetter = newStr[1].toUpperCase().charCodeAt(0) - 0x41 + 0x1f1e6;

    return (
      String.fromCodePoint(firstLetter) + String.fromCodePoint(secondLetter)
    );
  }

  @specialValue
  public mathjs(
    value: string,
    param: string | number = 0,
    operator = '+',
  ): string {
    if (isNaN(Number(value))) return '0';
    if (isNaN(Number(param))) return '0';

    return evaluate(`${value} ${operator} ${param}`);
  }

  /**
   * 将数字截断到指定的小数位数
   *
   * @param num 要截断的数字，可以是字符串或数字类型
   * @param decimalPlaces 指定的小数位数，默认为3
   * @returns 截断后的小数
   */
  @specialValue
  public truncateDecimal(num: string | number, decimalPlaces = 3) {
    const factor = Math.pow(10, decimalPlaces);
    return Math.floor(Number(num) * factor) / factor;
  }

  /**
   * 将字符串截断到指定长度，并在末尾添加省略号
   *
   * @param inputString - 要截断的字符串。
   * @param length - 截断后的字符串长度。
   * @returns 截断后的字符串，如果长度超过指定长度则添加省略号。
   */
  @specialValue
  public truncateString(inputString: string, length: string) {
    const lengthNum = Number(length);
    if (isNaN(lengthNum)) return inputString;
    if (inputString.length > lengthNum) {
      return inputString.slice(0, lengthNum) + '...';
    }
    return inputString;
  }

  /**
   * 获取数组的长度
   *
   * @param arr 要获取长度的字符串数组
   * @returns 返回数组的长度
   */
  @specialValue
  public arrayLength(arr: string[]) {
    return arr.length;
  }

  /**
   * 根据指定的键合并项并累加指定的值。
   *
   * @param items - 要合并的项数组。
   * @param mergeConfig - 配置对象，指定用于匹配的键（`sameKey`）和要累加的值键（`mergeValueKey`）。
   * @returns 一个对象，其中项基于指定的键进行了合并。
   */
  @specialValue
  public mergeItemsByKeyAndSumValue(
    items: string | any[],
    sameKey: string,
    mergeValueKey: string,
  ): string {
    const newItems = isString(items) ? JSON.parse(items) : items;

    const mergedItems: any[] = newItems.reduce(
      (accumulator: any, currentItem: any) => {
        const key = currentItem[sameKey];
        // 如果累加器中已有该键的项
        if (accumulator[key]) {
          // 累加值，假设truncateDecimal函数已在其他地方定义
          accumulator[key][mergeValueKey] = truncateDecimal(
            parseFloat(accumulator[key][mergeValueKey]) +
            parseFloat(currentItem[mergeValueKey]),
          );
        } else {
          // 在累加器中创建新条目
          accumulator[key] = {...currentItem};
        }
        return accumulator;
      },
      {},
    );

    // 返回合并后的项
    return JSON.stringify(Object.values(mergedItems));
  }
}

class Pipe {
  filters: Filters;

  constructor() {
    this.filters = new Filters();
  }

  /**
   * 从字符串中提取括号内的内容。
   *
   * @param str 要提取内容的字符串。
   * @returns 返回括号内的内容，如果未找到则返回 null。
   */
  public getFunctionParam(str: string): string | null {
    // 创建一个正则表达式 regex，用于匹配括号内的内容
    // 匹配函数名后面的括号内的内容
    const regex = /^[^(]+\((.*)\)$/;

    // 使用正则表达式在字符串 str 中进行匹配，并将结果存储在 match 变量中
    const match = str.match(regex);
    // 如果匹配成功（即 match 不为 null），则返回 match[1]（即括号内的内容）
    // 如果 match[1] 为空，则返回 null
    // 如果匹配失败（即 match 为 null），则直接返回 null
    return match ? match[1] || null : null;
  }

  /**
   * 获取函数名
   *
   * @param str 字符串，预期为函数定义或调用形式的字符串
   * @returns 返回解析出的函数名，如果无法解析则返回原字符串
   */
  public getFunctionName(str: string): string {
    // 使用正则表达式匹配字符串，查找以任意字符（非贪婪模式）开始，直到遇到左括号为止的部分
    const match = str.match(/^(.*?)\(/);

    // 如果匹配成功
    if (match) {
      // 返回匹配到的第一个分组，即函数名
      return match[1];
    } else {
      // 如果匹配失败，则返回原字符串
      return str;
    }
  }

  /**
   * 获取指定过滤器方法
   *
   * @param methodName 过滤器方法的名称
   * @returns 返回对应名称的过滤器方法，如果不存在或不是函数，则返回undefined
   */
  public getFilter<K extends keyof Filters>(
    methodName: K,
  ): Filters[K] | undefined {
    // 从当前类的filters属性中根据methodName获取对应的过滤器方法
    const method = this.filters[methodName];

    // 判断获取到的方法是否是函数类型
    // 如果是函数，则返回该方法
    // 如果不是函数，则返回undefined
    return typeof method === 'function' ? method : undefined;
  }

  /**
   * 渲染值，通过一系列函数进行过滤处理
   *
   * @param value 要处理的原始字符串值
   * @param funcs 过滤函数字符串数组
   * @param data 数据对象，用于替换模板中的占位符
   * @returns 经过过滤处理后的字符串
   */
  public render(
    value: string,
    funcs: string[],
    data: Record<string, any>,
  ): string {
    // 复制funcs数组，避免修改原数组
    const funcQueue = [...funcs];

    // 初始化当前处理的值为传入的value
    let currentValue = value;

    // 当函数队列不为空时继续处理
    while (funcQueue.length > 0) {
      // 从队列中移除并获取第一个函数字符串
      const funStr = funcQueue.shift()!;

      // 从函数字符串中提取函数名
      const funName = this.getFunctionName(funStr);

      // 从函数字符串中提取函数参数
      let funParam = this.getFunctionParam(funStr);

      // 字符串模版替换
      funParam = replaceVariablesInTemplate(
        data,
        funParam || '',
        /\*<([^}]+)>/g,
      );

      let funParamList: any[] = funParam ? funParam.split(',') : [funParam];

      funParamList = funParamList.map((item) => {
        return item.trim() === '' ? undefined : item;
      });

      // 获取对应的过滤器方法
      // 注意：这里的 as any 是一个类型断言，表示我们假设 funName 可以被 getFilter 方法接受
      // 实际上，这是一个潜在的类型风险，因为 funName 的类型可能并不符合 Filters 的键类型
      const filterMethod = this.getFilter(funName as any);

      if (filterMethod) {
        // 如果找到了对应的过滤器方法，则调用该方法并传入当前值和函数参数
        // 使用类型断言确保 filterMethod 的返回值为字符串，尽管在 TypeScript 中这可能不是必需的
        currentValue = filterMethod(currentValue, ...funParamList) as string;
      } else {
        // 如果找不到过滤器方法，则发出开发者警告
        devWarning('CurdTableColumn Pipe', `找不到筛选器方法 ${funName}`);
      }
    }

    // 返回最终处理过的字符串值
    // 这里使用类型断言确保返回的是字符串，尽管在 TypeScript 中这可能不是必需的，因为 currentValue 已经是字符串类型
    return currentValue as string;
  }
}

export default new Pipe();
