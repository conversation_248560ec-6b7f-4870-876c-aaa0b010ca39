import { isArray, isObject } from 'lodash';

type DeepObject = Record<string, any>;

/**
 * 深度替换不可变对象中的值
 *
 * @param obj 需要替换的对象或数组
 * @param targetKey 目标键名
 * @param newValue 新值
 * @param targetValue 目标值
 * @returns 返回替换后的对象或数组
 */
export const deepReplaceImmutable = (
  obj: DeepObject | Array<any>,
  targetKey: string,
  newValue: any,
  targetValue: any = '*',
): DeepObject | Array<any> => {
  if (isArray(obj)) {
    return obj.map((item) =>
      deepReplaceImmutable(item, targetKey, newValue, targetValue),
    );
  } else if (isObject(obj)) {
    const result: DeepObject = {};
    for (const key in obj) {
      if (Object.hasOwnProperty.call(obj, key)) {
        if (
          key === targetKey &&
          (obj[key] === targetValue || targetValue === '*')
        ) {
          result[key] = newValue;
        } else {
          result[key] = deepReplaceImmutable(
            obj[key],
            targetKey,
            newValue,
            targetValue,
          );
        }
      }
    }
    return result;
  } else {
    return obj;
  }
};
