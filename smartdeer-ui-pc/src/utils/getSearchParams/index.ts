/**
 * 从给定的字符串或当前窗口的URL中提取查询参数，并返回一个对象，其中键为参数名，值为参数值。
 *
 * @param str 可选的字符串，默认为当前窗口的URL。
 * @returns 一个包含查询参数的对象，其中键为参数名，值为参数值。
 */
export const getSearchParams = (str?: string): Record<string, string> => {
  const url = str || window.location.href;
  const urlObj = new URL(url);
  const paramsObj: Record<string, string> = {};
  const params = urlObj.searchParams;

  for (const [key, value] of params.entries()) {
    paramsObj[key] = value;
  }

  return paramsObj;
};
