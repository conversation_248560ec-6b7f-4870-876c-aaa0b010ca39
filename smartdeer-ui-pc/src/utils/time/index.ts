import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import weekday from 'dayjs/plugin/weekday';
import { isString } from 'lodash';

import type { TimezoneType } from '../../components/config-provider';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(weekday);
dayjs.extend(localeData);

const toggleSign = (num: number): number => {
  return num < 0 ? -num : -1 * num;
};

// 判断一个字符串是否是有效的时间格式
export const isTimeFormat = (str: any): boolean => {
  // 尝试使用dayjs库解析字符串
  const time = dayjs(str);
  // 检查解析后的对象是否有效，并且其Unix时间戳是否为数字
  return time.isValid() && !isNaN(time.unix());
};

/**
 * 将指定时区的时间转换为UTC时间
 *
 * @param time 时间字符串或dayjs对象
 * @param timezone 时区字符串
 * @param formatValue 输出格式，默认为'x'（Unix时间戳）
 * @returns 转换后的UTC时间，格式取决于formatValue
 */
export const convertTimezoneToUTC = (
  time: any,
  timezone: TimezoneType,
  formatValue?: string,
): Dayjs | string | number => {
  // 检查时间格式是否有效
  if (!isTimeFormat(time)) return time;

  // 根据时区差异调整时间
  const utcTime = dayjs(time);

  // const utcTime = dayjs(time).add(
  //   toggleSign(TIMEZONE_OFFSET_TO_UTC[timezone]),
  //   'hour',
  // );

  // 根据formatValue返回不同格式的时间
  if (!formatValue) {
    return utcTime; // 返回dayjs对象
  } else if (formatValue === 'x') {
    return utcTime.valueOf(); // 返回Unix时间戳
  } else {
    return utcTime.format(formatValue); // 返回格式化后的时间字符串
  }
};

/**
 * 将UTC时间转换为指定时区的时间
 *
 * @param time 时间字符串或dayjs对象
 * @param timezone 时区字符串
 * @param formatValue 输出格式，默认为空字符串（返回dayjs对象）
 * @returns 转换后的时区时间，格式取决于formatValue
 */
export const convertUTCToTimezone = (
  time: any,
  timezone: TimezoneType,
  formatValue?: string,
): Dayjs | string | number => {
  // 检查时间格式是否有效
  if (!isTimeFormat(time)) return time;

  // 根据时区差异调整时间
  const timezoneTime = dayjs(time);

  // const timezoneTime = dayjs(time).add(
  //   TIMEZONE_OFFSET_TO_UTC[timezone],
  //   'hour',
  // );
  // 根据formatValue返回不同格式的时间
  if (!formatValue) {
    return timezoneTime; // 返回dayjs对象
  } else if (formatValue === 'x') {
    return timezoneTime.valueOf(); // 返回Unix时间戳
  } else {
    return timezoneTime.format(formatValue); // 返回格式化后的时间字符串
  }
};

/**
 * 获取指定时区的时间
 *
 * @param timezone 时区字符串
 * @param formatValue 输出格式，默认为空字符串（返回dayjs对象）
 * @returns 转换后的时区时间，格式取决于formatValue
 */
export const getTimeInTimeZone = (
  timezone: TimezoneType,
  formatValue?: string,
): Dayjs | string | number => {
  const timezoneTime = dayjs().tz(timezone);

  // 根据formatValue返回不同格式的时间
  if (!formatValue) {
    return timezoneTime; // 返回dayjs对象
  } else if (formatValue === 'x') {
    return timezoneTime.valueOf(); // 返回Unix时间戳
  } else {
    return timezoneTime.format(formatValue); // 返回格式化后的时间字符串
  }
};

/**
 * 格式化日期为“上午/下午”加小时和分钟的字符串
 *
 * @param date 要格式化的日期对象
 * @returns 返回格式为 "上午/下午 HH:mm" 的字符串
 */
export const formatHour = (date: Date) => {
  let hour = date.getHours();
  let minute = date.getMinutes();
  const amPm = hour >= 12 ? '下午' : '上午';
  hour = hour % 12;
  hour = hour ? hour : 12; // 将0点转换为12点
  minute = minute < 10 ? 0 + minute : minute;
  return `${amPm} ${hour}:${minute}`;
};

/**
 * 计算给定时间戳与当前时间之间的时间差，并返回相应的描述字符串
 *
 * @param timeStamp 要计算时间差的时间戳
 * @returns 返回时间差描述字符串，如 "数秒前", "1分钟前", "HH:mm", "昨天 HH:mm", "X天前"
 */
export const timeAgo = (timestamp: number) => {
  const now = dayjs();
  const inputTime = dayjs(timestamp);
  const diffSeconds = now.diff(inputTime, 'second');
  const diffMinutes = now.diff(inputTime, 'minute');
  const diffHours = now.diff(inputTime, 'hour');
  const diffDays = now.diff(inputTime, 'day');
  const diffWeeks = now.diff(inputTime, 'week');
  const isSameDay = inputTime.isSame(now, 'day');

  if (isSameDay && diffSeconds < 60) {
    return `${diffSeconds} 秒前`;
  } else if (isSameDay && diffMinutes < 60) {
    return `${diffMinutes} 分钟前`;
  } else if (isSameDay && diffHours < 3) {
    return `${diffHours} 小时前`;
  } else if (isSameDay) {
    const formatString = inputTime.hour() >= 12 ? '下午 hh:mm' : '上午 hh:mm';
    return inputTime.format(formatString);
  } else if (diffDays < 1 && diffDays >= 0) {
    const formatString =
      inputTime.hour() >= 12 ? '昨天下午 hh:mm' : '昨天上午 hh:mm';
    return inputTime.format(formatString);
  } else if (diffDays < 7 && diffDays > 0) {
    return `${diffDays} 天前`;
  } else if (diffDays >= 7 && diffWeeks < 4) {
    return `${diffWeeks} 周前`;
  } else {
    return inputTime.format('YYYY-MM-DD');
  }
};

/**
 * 为时间戳添加时区偏移量
 *
 * @param timestamp 时间戳，可以是字符串或数字
 * @param timezoneOffset 时区偏移量，默认为'+08:00'
 * @returns 返回新的时间戳
 */
export const addTimezoneOffset = (
  timestamp: string | number,
  timezoneOffset = '+08:00',
) => {
  const newTimestamp = isString(timestamp) ? Number(timestamp) : timestamp;

  const formattedDate =
    dayjs(newTimestamp).format('YYYY-MM-DDTHH:mm:ss') + timezoneOffset;
  return dayjs(formattedDate).valueOf();
};

/**
 * 获取指定时区的UTC偏移量
 *
 * @param timezoneString 时区字符串，默认为 'Asia/Shanghai'
 * @returns 返回格式为 '+HH:mm' 或 '-HH:mm' 的时区偏移量字符串
 */
export const getTimezoneOffsetOffset = (timezoneString = 'Asia/Shanghai') => {
  return dayjs().tz(timezoneString).format('Z');
};

/**
 * 将给定的时间戳按照指定的时区字符串转换为UTC时间戳
 *
 * @param timestamp 要转换的时间戳，可以是字符串或数字
 * @param timezoneString 时区字符串
 * @returns 转换后的UTC时间戳
 */
export const getUtcTimestamp = (
  timestamp: string | number,
  timezoneString = 'Asia/Shanghai',
) => {
  const currentTimezoneOffset = getTimezoneOffsetOffset(timezoneString);
  let formattedDate = addTimezoneOffset(timestamp, currentTimezoneOffset);
  return dayjs(formattedDate).tz(timezoneString).valueOf();
};

/**
 * 将给定的时间戳按照指定的时区字符串转换为对应的时间字符串
 *
 * @param timestamp 要转换的时间戳，可以是字符串或数字
 * @param timezoneString 时区字符串，默认为 'Asia/Shanghai'
 * @returns 转换后的时间字符串
 */
export const convertTimestampToTimezone = (
  timestamp: string | number,
  timezoneString = 'Asia/Shanghai',
  formatString = 'YYYY-MM-DD',
) => {
  const newTimestamp = isString(timestamp) ? Number(timestamp) : timestamp;

  return dayjs(newTimestamp).tz(timezoneString).format(formatString);
};
