import { decode, encode } from 'js-base64';
import JSEncrypt from 'jsencrypt';
import { splitStringByLength } from '../splitStringByLength';

class Rsa {
  JSEncrypt: JSEncrypt;
  blockSize: number;

  constructor() {
    this.JSEncrypt = new JSEncrypt();
    this.blockSize = 50;
  }

  public setPublicKey(publicKey: string) {
    const key = publicKey
      .replace('-----BEGIN PUBLIC KEY-----', '')
      .replace('-----END PUBLIC KEY-----', '')
      .replace(/\s+/g, '');
    this.JSEncrypt.setPublicKey(key);
  }

  public setPrivateKey(privateKey: string) {
    const key = privateKey
      .replace('-----BEGIN PRIVATE KEY-----', '')
      .replace('-----END PRIVATE KEY-----', '')
      .replace(/\s+/g, '');

    this.JSEncrypt.setPrivateKey(key);
  }

  public setBlockSize(size: number) {
    this.blockSize = size;
  }

  public encrypt(str: string) {
    const list = splitStringByLength(str, this.blockSize);
    const newList = list.map((item) => {
      return this.JSEncrypt.encrypt(item);
    });

    const encryptStr = newList.join(',');

    return encode(encryptStr);
  }

  public decrypt(str: string) {
    const rsaStr = decode(str);

    const list = rsaStr.split(',');

    const newList = list.map((item) => {
      return this.JSEncrypt.decrypt(item);
    });

    return newList.join('');
  }
}

export default new Rsa();
