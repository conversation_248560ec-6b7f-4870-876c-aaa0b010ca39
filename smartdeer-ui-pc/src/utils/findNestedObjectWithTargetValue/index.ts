import { isArray, isObject } from 'lodash';

/**
 * 在给定的对象或数组中递归查找具有指定值的嵌套对象。
 *
 * @param input 要搜索的对象或数组
 * @param targetValue 要查找的目标值
 * @returns 找到的具有目标值的嵌套对象，如果未找到则返回 undefined
 */
export const findNestedObjectWithTargetValue = (
  input: any[] | object,
  targetValue: any,
): any | undefined => {
  /**
   * 递归遍历对象或数组以查找具有目标值的嵌套对象。
   *
   * @param obj 当前遍历的对象或数组项
   * @returns 找到的具有目标值的嵌套对象，如果未找到则返回 undefined
   */
  const traverse = (obj: any): any | undefined => {
    // 遍历对象的每个属性
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        // 如果当前属性的值与目标值相等，则返回当前对象
        if (obj[key] === targetValue) {
          return obj;
        }
        // 如果当前属性的值是数组，则递归搜索
        if (isArray(obj[key])) {
          for (const item of obj[key]) {
            const result = traverse(item);
            if (result) {
              return result;
            }
          }
        }
        // 如果当前属性的值是对象（并且不是数组），则递归搜索
        else if (isObject(obj[key])) {
          const result = traverse(obj[key]);
          if (result) {
            return result;
          }
        }
      }
    }
    // 如果没有找到匹配的值，返回 undefined
    return undefined;
  };

  // 开始从根对象或数组遍历
  return traverse(input);
};
