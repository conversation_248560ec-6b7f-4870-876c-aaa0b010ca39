/**
 * 生成一个随机的密码，包含小写字母、数字、特殊字符。
 *
 * @param length 生成的密码的长度，默认为 20。
 * @returns {string} 返回生成的密码。
 */
export const generateRandomPassword = (length = 20) => {
  // 定义字符集
  const lowerCase = 'abcdefghijklmnopqrstuvwxyz';
  const upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const digits = '0123456789';
  const specialChars = '!@#$%^&*()_-+=<>?';

  let password = [
    lowerCase[Math.floor(Math.random() * lowerCase.length)],
    upperCase[Math.floor(Math.random() * upperCase.length)],
    digits[Math.floor(Math.random() * digits.length)],
    specialChars[Math.floor(Math.random() * specialChars.length)],
  ];

  // 生成剩余部分
  const allChars = lowerCase + upperCase + digits + specialChars;
  for (let i = password.length; i < length; i++) {
    password.push(allChars[Math.floor(Math.random() * allChars.length)]);
  }

  // 打乱密码顺序
  password = password.sort(() => Math.random() - 0.5);

  // 返回生成的密码
  return password.join('');
};
