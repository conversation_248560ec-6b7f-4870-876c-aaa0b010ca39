/**
 * 从输入字符串中提取与正则表达式匹配的模式。
 *
 * @param input 要搜索的输入字符串
 * @param regex 用于匹配模式的正则表达式，默认为 /\*\{(.*?)\}/g
 * @returns 匹配到的模式数组，如果没有匹配到任何模式，则返回一个空数组
 */
export const extractPatternsFromInput = (
  input: string,
  regex: RegExp = /\*\{(.*?)\}/g,
): string[] => {
  let match;
  const matches: string[] = [];

  // 使用 exec 方法迭代匹配所有结果
  while ((match = regex.exec(input)) !== null) {
    // match[1] 包含括号中匹配的内容
    matches.push(match[1]);
  }

  return matches;
};

/**
 * 从字符串中提取比较操作符。
 *
 * @param condition 字符串形式的条件表达式。
 * @returns 返回找到的比较操作符，如果不存在则返回 null。
 */
export const extractComparisonOperator = (condition: string): string | null => {
  const match = condition.match(/(===|!==|>=|>|<=|<)/);
  return match ? match[0] : null;
};

/**
 * 从字符串中提取第一对圆括号内的内容。
 * 如果找到匹配项，则返回括号内的内容；否则，返回 null。
 *
 * @param str -  字符串，预期格式为 "functionName(param1, param2)"
 * @returns 圆括号内的内容或 null（如果未找到匹配项）。
 */
export const extractParenthesesContent = (str: string): string | null => {
  // 创建一个正则表达式 regex，用于匹配括号内的内容
  // \((.*?)\) 匹配一个左括号，接着是任意数量的任意字符（非贪婪匹配），最后是一个右括号
  const regex = /\((.*?)\)/;

  // 使用正则表达式在字符串 str 中进行匹配，并将结果存储在 match 变量中
  const match = str.match(regex);

  // 如果匹配成功（即 match 不为 null），则返回 match[1]（即括号内的内容）
  // 如果 match[1] 为空，则返回 null
  // 如果匹配失败（即 match 为 null），则直接返回 null
  return match ? match[1] || null : null;
};
