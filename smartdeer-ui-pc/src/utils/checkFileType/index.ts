import { isArray } from 'lodash';

const imageFileTypes = [
  'image/apng',
  // "image/bmp",
  'image/gif',
  'image/jpeg',
  // "image/pjpeg",
  'image/png',
  // "image/svg+xml",
  // "image/tiff",
  // "image/webp",
  // "image/x-icon",
];

const fileTypes = [
  'application/pdf',
  'application/doc',
  'application/msword',
  'application/docx',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
];

const compressedFileTypes = [
  'application/zip',
  'application/x-zip-compressed',
  'application/x-rar-compressed',
  'application/x-7z-compressed',
  'application/x-tar',
];

const compressedFileExtensions = ['zip', 'rar', '7z', 'tar'];

const videoFileTypes = ['video/mp4', 'video/quicktime', 'video/avi'];

const videoFileExtensions = ['mp4', 'avi', 'mov', 'mkv'];

/**
 * 判断文件类型是否有效
 *
 * @param fileType 文件类型，可以是 MIME 类型或文件扩展名
 * @param acceptedFileExtensions 可接受的文件扩展名数组，可以为 null，默认为 null
 * @param acceptedMimeTypes 可接受的 MIME 类型数组，可以为 null，默认为 null
 * @returns 如果文件类型有效，则返回 true；否则返回 false
 */
const isValidFileType = (
  fileType: string,
  acceptedFileExtensions: null | string[],
  acceptedMimeTypes: null | string[],
): boolean => {
  if (isArray(acceptedMimeTypes) && acceptedMimeTypes.includes(fileType)) {
    return true;
  }
  if (
    isArray(acceptedFileExtensions) &&
    acceptedFileExtensions.includes(fileType)
  ) {
    return true;
  }
  return false;
};

export const checkFileType = (
  file: File,
  uploadType: string | string[] = '',
) => {
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  const fileMimeType = file.type;

  const newUploadType = isArray(uploadType)
    ? uploadType
    : uploadType.split(',');

  const checkType = (type: string) => {
    switch (type) {
      case 'image':
        return isValidFileType(fileMimeType, null, imageFileTypes);
      case 'file':
        return isValidFileType(fileMimeType, null, fileTypes);
      case 'compressedFile':
        return (
          (fileMimeType &&
            isValidFileType(fileMimeType, null, compressedFileTypes)) ||
          (fileExtension &&
            isValidFileType(fileExtension, compressedFileExtensions, null))
        );
      case 'video':
        return (
          (fileMimeType &&
            isValidFileType(fileMimeType, null, videoFileTypes)) ||
          (fileExtension &&
            isValidFileType(fileExtension, videoFileExtensions, null))
        );
      default:
        return (
          isValidFileType(fileMimeType, null, imageFileTypes) ||
          isValidFileType(fileMimeType, null, fileTypes) ||
          (fileMimeType &&
            isValidFileType(fileMimeType, null, compressedFileTypes)) ||
          (fileExtension &&
            isValidFileType(fileExtension, compressedFileExtensions, null)) ||
          (fileMimeType &&
            isValidFileType(fileMimeType, null, videoFileTypes)) ||
          (fileExtension &&
            isValidFileType(fileExtension, videoFileExtensions, null))
        );
    }
  };

  return newUploadType.some(checkType);
};
