import { evaluateExpression } from '../index';

describe('evaluateExpression', () => {
  test('evaluates simple numeric expressions', () => {
    expect(evaluateExpression('1 == 1 ? true : false')).toBe('true');
    expect(evaluateExpression('1 != 1 ? true : false')).toBe('false');
    expect(evaluateExpression('1 === 1 ? true : false')).toBe('true');
    expect(evaluateExpression('1 !== 1 ? true : false')).toBe('false');
    expect(evaluateExpression('2 > 1 ? true : false')).toBe('true');
    expect(evaluateExpression('2 < 1 ? true : false')).toBe('false');
    expect(evaluateExpression('2 >= 1 ? true : false')).toBe('true');
    expect(evaluateExpression('2 <= 1 ? true : false')).toBe('false');
  });

  test('evaluates string expressions', () => {
    expect(evaluateExpression('foo == foo ? true : false')).toBe('true');
    expect(evaluateExpression('foo != bar ? true : false')).toBe('true');
    // 注意：'===' 和 '!==' 对于字符串而言，与 '==' 和 '!=' 在这里的行为是相同的
    expect(evaluateExpression('foo === foo ? true : false')).toBe('true');
    expect(evaluateExpression('foo !== bar ? true : false')).toBe('true');
  });

  test('returns original expression if not a ternary operator', () => {
    expect(evaluateExpression('just a string')).toBe('just a string');
    expect(evaluateExpression('1 + 1')).toBe('1 + 1');
  });

  test('handles expressions with spaces', () => {
    expect(evaluateExpression(' 1 == 1   ?   true   :   false  ')).toBe('true');
  });
});
