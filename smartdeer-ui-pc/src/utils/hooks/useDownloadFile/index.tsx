import React from 'react'
import { message } from 'antd'
import { ConfigContext } from '../../../components/config-provider'
import useParamsDict from '../useParamsDict'
import { replaceVariablesInTemplate } from '../../replaceVariablesInTemplate'
import swrFetcher, { swrDownloadFile } from '../../swrFetcher'


/**
 * 用于下载文件的自定义 Hook。
 *
 * @returns 返回一个对象，包含 isDownloadLoading 和 handleDownload 两个属性。
 *          - isDownloadLoading: 是否正在下载文件的标志。
 *          - handleDownload: 下载文件的处理函数，接受 key（文件标识）和 fileName（文件名）作为参数。
 */
const useDownloadFile = () => {
  const { appFunctionApi, appFileDownloadApi } = React.useContext(ConfigContext)
  const paramsDict = useParamsDict()

  const [isDownloadLoading, setIsDownloadLoading] = React.useState(false)

  const handleDownload = async (key: string, fileName: string) => {
    setIsDownloadLoading(true)
    try {
      let api = appFunctionApi as string

      const isEntityId = api?.includes('*{entityId}') || api?.includes('*{entityUUID}')

      if (isEntityId) {
        if (paramsDict.entityId || paramsDict.entityUUID) {
          api = replaceVariablesInTemplate(paramsDict, api)
        } else {
          api = api.replace('/entity/u/*{entityUUID}', '');
          api = api.replace('/entity/*{entityId}', '');
        }
      }

      const { data } = await swrFetcher(api, 'POST', {
        functionKey: 'get_private_file_tmp_code',
        params: {}
      })

      await swrDownloadFile(`${appFileDownloadApi}?key=${key}&code=${data.code}`, fileName)
    } catch (err) {
      message.error('Download failed！')
    }

    setIsDownloadLoading(false)
  }

  return {
    isDownloadLoading,
    handleDownload
  };
};

export default useDownloadFile;
