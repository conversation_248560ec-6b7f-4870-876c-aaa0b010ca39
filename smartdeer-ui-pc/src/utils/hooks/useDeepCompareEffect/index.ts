import type { DependencyList } from 'react';
import React from 'react';
import isDeepEqualReact from '../../isDeepEqualReact';

export const isDeepEqual = (a: any, b: any, ignoreKeys?: string[]) =>
  isDeepEqualReact(a, b, ignoreKeys);

/**
 * 使用深度比较的记忆化钩子函数
 *
 * @param value 要记忆化的值
 * @param ignoreKeys 可选参数，需要忽略的键名数组，用于在深度比较时忽略某些属性
 * @returns 返回记忆化后的值
 */
export const useDeepCompareMemoize = (value: any, ignoreKeys?: any) => {
  const ref = React.useRef();
  if (!isDeepEqual(value, ref.current, ignoreKeys)) {
    ref.current = value;
  }

  return ref.current;
};

/**
 * 使用深度比较的自定义 Hook，当依赖项发生深度变化时触发回调函数
 *
 * @param effect 回调函数，当依赖项发生深度变化时触发
 * @param dependencies 依赖项列表，用于触发 effect 的重新运行
 * @param ignoreKeys 可选参数，需要忽略的依赖项键名数组
 * @returns 无返回值
 */
const useDeepCompareEffect = (
  effect: React.EffectCallback,
  dependencies: DependencyList,
  ignoreKeys?: string[],
) => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  React.useEffect(
    effect,
    useDeepCompareMemoize(dependencies || [], ignoreKeys),
  );
};

export default useDeepCompareEffect;
