import React from 'react';
import { useDeepCompareMemoize } from '../useDeepCompareEffect';

/**
 * 使用深度比较进行记忆化的自定义 Hook。
 *
 * @param factory 用于创建并返回新值的工厂函数。
 * @param dependencies 用于记忆化的依赖项数组。当数组中的项发生变化时，将重新计算 factory 函数。
 * @returns 使用深度比较进行记忆化的值。
 * @template T 返回值的类型。
 */
const useDeepCompareMemo = <T>(
  factory: () => T,
  dependencies: React.DependencyList,
) => {
  return React.useMemo(
    factory,
    useDeepCompareMemoize(dependencies) as unknown as React.DependencyList,
  );
};

export default useDeepCompareMemo;
