import React from 'react';
import { create } from 'zustand';
import { ConfigContext } from '../../../components/config-provider';
import { getSearchParams } from '../../getSearchParams';
import { formatQueryString } from '../../index';
import swrFetcher from '../../swrFetcher';

interface FileAccessTokenStore {
  fileToken: string;
  isFetching: boolean;
  timeStamp: number;
  fetchFileToken: (
    appFileTokenApi: string,
    expireTime: string | number,
  ) => Promise<string>;

  reset: () => void;
}

const useFileAccessTokenStore = create<FileAccessTokenStore>((set, get) => ({
  fileToken: '',
  isFetching: false,
  timeStamp: 0,
  fetchFileToken: async (fileTokenApi: string, expireTime: string | number) => {
    const { fileToken, isFetching, timeStamp } = get();

    const isExpire = Date.now() - timeStamp < Number(expireTime);

    if (fileToken || isFetching || isExpire) return fileToken;

    set({ isFetching: true });

    const { data } = await swrFetcher(fileTokenApi, 'GET', {
      expireTime,
    });

    const token = formatQueryString(data);

    set({
      fileToken: token,
      timeStamp: Date.now(),
    });

    return token;
  },

  reset: () =>
    set({
      fileToken: '',
      isFetching: false,
      timeStamp: 0,
    }),
}));

const useFileAccessToken = () => {
  const fileToken = useFileAccessTokenStore((state) => state.fileToken);

  const fetchFileToken = useFileAccessTokenStore(
    (state) => state.fetchFileToken,
  );

  const reset = useFileAccessTokenStore((state) => state.reset);

  const { isFileToken, appFileTokenApi, appFileTokenExpireTime } =
    React.useContext(ConfigContext);

  const getFileToken = async () => {
    if (!isFileToken) return;

    return fetchFileToken(
      appFileTokenApi as string,
      appFileTokenExpireTime as string,
    );
  };

  const fileTokenTransform = async (url: string): Promise<string> => {
    const tokenStr = await getFileToken();

    return `${url}?${tokenStr}`;
  };

  const getTheCompleteFileUrl = async (
    fileKey: string,
    expireTime?: number,
  ): Promise<string> => {
    if (fileKey.includes('http')) return fileKey;

    if (!isFileToken) return '';

    const { data } = await swrFetcher(appFileTokenApi as string, 'GET', {
      expireTime: expireTime || Number(appFileTokenExpireTime),
      fileKey,
    });

    return `${data}&fileKey=${fileKey}`;
  };

  const simulateFetchRealImageUrl = async (
    originalSrc: string,
    expireTime?: number,
  ) => {
    const paramsObj = getSearchParams(originalSrc);

    const fileKey = paramsObj.fileKey;

    if (!fileKey) {
      return originalSrc;
    }

    const fileUrl = await getTheCompleteFileUrl(fileKey, expireTime);

    return fileUrl;
  };

  const replaceImageSrcInHtml = async (
    htmlContent: string,
    expireTime?: number,
  ) => {
    // 使用DOMParser解析HTML字符串
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    // 查找所有的<img>标签
    const images = doc.querySelectorAll('img');

    // 异步地替换每个<img>标签的src属性
    for (let img of images) {
      const originalSrc = img.getAttribute('src');

      // 这里假设我们有一个函数可以模拟从API获取真实URL
      // 在实际情况下，你应该将下面的代码替换为调用你的API的fetch请求
      const realSrc = await simulateFetchRealImageUrl(
        originalSrc as string,
        expireTime,
      );

      // 替换src属性
      img.setAttribute('src', realSrc);
    }

    // 将修改后的DOM对象转换回HTML字符串
    // 注意：这通常会包含额外的HTML结构，如<body>和<html>标签
    // 你可能需要根据需要提取或修改这部分内容
    return doc.body.innerHTML;
  };

  return {
    fileToken,
    fetchFileToken: getFileToken,
    fileTokenTransform,
    reset,
    getTheCompleteFileUrl,
    replaceImageSrcInHtml,
  };
};

export default useFileAccessToken;
