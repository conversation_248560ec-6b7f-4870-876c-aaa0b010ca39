import { message } from 'antd';
import { isEmpty } from 'lodash';
import React from 'react';
import { ConfigContext } from '../../../components/config-provider';
import type { FetchType } from '../../../typing';
import { checkKeysExistInObject } from '../../checkKeysExistInObject';
import { extractPatternsFromInput } from '../../extract';
import { getEffectFetchConfig } from '../../getEffectFetchConfig';
import swrFetcher from '../../swrFetcher';
import useParamsDict from '../useParamsDict';
import { useDeepCompareEffect } from 'ahooks';

const useGetEffectFetch = (config?: FetchType, dependenciesValues = {}) => {
  const configContext = React.useContext(ConfigContext);

  const paramsDict = useParamsDict(dependenciesValues);
  const dependenciesValuesMemo = React.useMemo(() => dependenciesValues, [dependenciesValues]);

  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [data, setData] = React.useState<any>(null);

  const fetch = async () => {
    setIsLoading(true);

    let fetchConfig = JSON.parse(JSON.stringify(config));

    const fetchDependentValue = extractPatternsFromInput(
      JSON.stringify(fetchConfig),
    );

    if (!isEmpty(fetchDependentValue) && fetchDependentValue.length > 0) {
      const has = checkKeysExistInObject(paramsDict, fetchDependentValue);

      if (!has) return;
    }

    try {
      const dataIndex = config?.data;

      const { api, method, params } = getEffectFetchConfig(
        config!,
        configContext,
        paramsDict,
      );

      const { data } = await swrFetcher(api, method, params);

      let newData = dataIndex ? data[dataIndex] : data;

      setData(newData);
    } catch (err: any) {
      message.error(err.message);
    }

    setIsLoading(false);
  };

  useDeepCompareEffect(() => {
    if (!config) return;
    fetch();
  }, [config, dependenciesValuesMemo]);

  return {
    data,
    isLoading,
  };
};

export default useGetEffectFetch;
