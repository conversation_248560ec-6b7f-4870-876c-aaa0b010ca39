import * as React from 'react';
import { ConfigContext } from '../../../components/config-provider';
import { STORE } from '../../../consts';
import { getSearchParams } from '../../getSearchParams';
import S from '../../storage';

const useParamsDict = (dataSource = {}): Record<string, any> => {
  const context = React.useContext(ConfigContext);

  const query = getSearchParams();

  const params = context.useUrlParams();
  const appDefaultEntityId = context.appDefaultEntityId;
  const appDefaultEntityUUID = context.appDefaultEntityUUID;

  const userAccountId = context?.userAccountId
    ? context?.userAccountId
    : S.get(STORE.USER_ACCOUNT_ID);

  const userAccountInfo = S.get(STORE.USER_ACCOUNT_INFO);

  const paramsDict: Record<string, any> = {
    ...params,
    ...query,
    userAccountId: userAccountId,
    userAccountName: userAccountInfo?.userName || '',
    userAccountEmail: userAccountInfo?.email || '',
    entityUUID: params.entityUUID || appDefaultEntityUUID,
    now: Date.now(),
    systemName: context.systemName,
    ...dataSource,
  };

  if (!paramsDict.entityId && appDefaultEntityId) {
    paramsDict['entityId'] = appDefaultEntityId;
  }

  return paramsDict;
};

export default useParamsDict;
