import React from 'react'

type SizeType = 'small' | 'middle' | 'large';

const useSize = (propSize?: SizeType) => {
  const newSize = window.innerWidth < 576 ? 'middle' : 'large';

  // 初始化state
  const [size, setSize] = React.useState<SizeType>(propSize || newSize);

  React.useEffect(() => {
    if (propSize) return
    // 处理resize事件
    const handleResize = () => {

      const newSize = window.innerWidth < 576 ? 'middle' : 'large';

      setSize(newSize)
    }

    // 绑定事件
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return size;
}

export default useSize
