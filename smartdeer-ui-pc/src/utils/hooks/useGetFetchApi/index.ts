import React from 'react';
import { ConfigContext } from '../../../components/config-provider';
import { replaceVariablesInTemplate } from '../../replaceVariablesInTemplate';
import useParamsDict from '../useParamsDict';

const useGetFetchApi = () => {
  const configContext = React.useContext(ConfigContext);

  const paramsDict = useParamsDict();

  const functionApi = replaceVariablesInTemplate(
    paramsDict,
    configContext.appFunctionApi!,
  );

  const processApi = replaceVariablesInTemplate(
    paramsDict,
    configContext.appProcessApi!,
  );
  return {
    functionApi,
    processApi,
  };
};

export default useGetFetchApi;
