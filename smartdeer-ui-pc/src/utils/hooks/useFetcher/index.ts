import useSWR, { Middleware } from 'swr';
import type { FetchMethodType } from '../../../typing';
import swrFetcher from '../../swrFetcher';

/**
 * 自定义 Hook，用于发起 HTTP 请求
 *
 * @param url API 的 URL 字符串
 * @param method HTTP 请求方法，类型为 FetchMethodType
 * @param params HTTP 请求参数，默认为空对象
 * @param middlewares 中间件
 * @returns 一个对象，包含 data、error 和 isLoading 三个属性
 */
const useFetcher = (
  url: string,
  method: FetchMethodType,
  params = {},
  middlewares: Middleware | Middleware[] = [],
) => {
  const use = Array.isArray(middlewares) ? middlewares : [middlewares];

  const { data, error, isLoading } = useSWR(
    url,
    (url) => swrFetcher(url, method, params),
    {
      use,
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    },
  );

  return { data, error, isLoading };
};

export default useFetcher;
