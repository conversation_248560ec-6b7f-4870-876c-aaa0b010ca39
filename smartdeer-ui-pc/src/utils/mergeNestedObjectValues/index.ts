import { isArray } from 'lodash';

/**
 * 合并嵌套对象的值
 *
 * @param obj 原始对象
 * @param valuesToMerge 要合并的值对象
 * @param path 要合并值的属性的路径，以数组形式提供
 * @returns 返回一个新对象，其中指定路径的值已合并，或者如果路径不存在，则在根级别合并值
 */
export const mergeNestedObjectValues = (
  obj: Record<string, any>,
  valuesToMerge: Record<string, any> | any[],
  path: string[] = [],
): any => {
  // 如果路径不存在或为空，则直接合并obj和valuesToMerge
  if (!path || path.length === 0) {
    return {
      ...obj,
      ...valuesToMerge,
    };
  }

  // 创建一个对象的深拷贝
  const newObj = JSON.parse(JSON.stringify(obj));

  // 获取路径中的最后一个键之前的部分
  const nestedPath = path.slice(0, -1);
  // 获取最后一个键
  const lastKey = path[path.length - 1];

  // 遍历路径，找到需要合并值的嵌套对象
  let nestedObj = nestedPath.reduce((currentObj, key) => {
    // 如果当前对象或当前对象的key不存在，则返回一个空对象
    const nested = currentObj[key] || {};

    // 返回嵌套对象，保证链式调用
    return nested;
  }, newObj);

  console.log(' nestedObj[lastKey]', nestedObj[lastKey]);

  if (isArray(nestedObj[lastKey]) && isArray(valuesToMerge)) {
    // 如果最后一个键对应的值是数组，则合并新值到该数组中
    nestedObj[lastKey] = [...(nestedObj[lastKey] || []), ...valuesToMerge];
  } else {
    // 合并新值到嵌套对象中
    nestedObj[lastKey] = {
      ...nestedObj[lastKey],
      ...valuesToMerge,
    };
  }

  return newObj;
};
