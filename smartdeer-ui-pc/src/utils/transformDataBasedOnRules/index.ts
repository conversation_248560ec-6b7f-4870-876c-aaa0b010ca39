import { get, isArray, isObject, set } from 'lodash';
import type { TransformDataRules } from '../../typing';
import pipe from '../pipe';
import {} from '../replaceVariablesInTemplate';

type RecordType = Record<string, any>;

/**
 * 设置嵌套对象的值
 *
 * @param {RecordType} data - 目标对象，将在其上设置嵌套值
 * @param {Array<string>} keys - 键的数组，表示嵌套路径
 * @param {any} value - 要设置的值
 */
const setNestedValue = (data: RecordType, keys: Array<string>, value: any) => {
  let current = data;
  keys.forEach((key, index) => {
    if (index === keys.length - 1) {
      current[key] = value;
    } else {
      current[key] = current[key] || {};
      current = current[key];
    }
  });
};

/**
 * 获取嵌套对象的值
 *
 * @param {Object} data - 目标对象，从中获取嵌套值
 * @param {Array<string>} keys - 键的数组，表示嵌套路径
 * @returns {any} - 返回嵌套值，如果不存在则返回 undefined
 */
const getNestedValue = (data: RecordType, keys: Array<string>) => {
  return keys.reduce(
    (acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined),
    data,
  );
};

/**
 * 将转换规则应用于对象
 *
 * @param item 待转换的对象
 * @param rules 转换规则数组
 * @returns 返回应用转换规则后的新对象
 */
const applyTransformationRulesToObject = (
  item: RecordType,
  rules: TransformDataRules,
) => {
  let result = { ...item };

  rules.forEach((rule) => {
    const { from, to, format } = rule;

    const tokey = to || from;

    const fromStr = isArray(from) ? from.join('.') : from;

    const fromValue = get(result, fromStr);

    if (isArray(tokey)) {
      const toStr = tokey.join('.');

      if (format) {
        const newFromValue = pipe.render(fromValue, [format], result);
        set(result, toStr, newFromValue);
      } else {
        set(result, toStr, fromValue);
      }
    } else {
      if (format) {
        const newFromValue =
          format === 'delete'
            ? undefined
            : pipe.render(fromValue, [format], result);

        set(result, tokey, newFromValue);
      } else {
        set(result, tokey, fromValue);
      }
    }
  });

  return result;
};

type DataSourceType = RecordType | Array<RecordType>;

/**
 * 根据规则转换数据
 *
 * @param dataSource 要转换的数据，可以是对象、对象数组、字符串、数字或布尔值
 * @param rules 转换规则数组
 * @returns 转换后的数据
 */
export const transformDataBasedOnRules = (
  dataSource: DataSourceType,
  rules: TransformDataRules,
) => {
  if (!rules?.length) return dataSource;

  const applyRules = (item: Record<string, any>) =>
    applyTransformationRulesToObject(item, rules);

  let result = dataSource;

  const rootRules = rules.filter((rule) => rule.from === 'root');

  if (rootRules?.length) {
    const rootData = applyTransformationRulesToObject(
      { root: dataSource },
      rootRules,
    );
    if (Object.keys(rootData).length === 1 && rootData.hasOwnProperty('root')) {
      result = rootData.root;
    } else {
      result = rootData;
    }
  }

  if (isArray(result)) {
    result = result.map(applyRules);
  } else if (isObject(result)) {
    result = applyRules(result);
  }

  return result;
};
