const HASH_ALGORITHM_SHA512 = 'SHA-512';
const HASH_ALGORITHM_SHA256 = 'SHA-256';

/**
 * 将 ArrayBuffer 对象转换为十六进制字符串。
 *
 * @param hashBuffer ArrayBuffer 对象，包含需要转换的二进制数据。
 * @returns 转换后的十六进制字符串。
 */
const toHashHex = (hashBuffer: ArrayBuffer) => {
  // 将ArrayBuffer转换为Uint8Array
  const hashArray = Array.from(new Uint8Array(hashBuffer));

  // 将每个字节转换为16进制字符串，并补全为两位
  return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
};

/**
 * 使用指定的哈希算法对消息进行哈希处理
 *
 * @param message 要进行哈希处理的消息字符串
 * @param algorithm 要使用的哈希算法，例如 'SHA-256'
 * @returns 返回消息的哈希值，以十六进制字符串形式表示
 */
const hashMessage = async (message: string, algorithm: string) => {
  // 创建 TextEncoder 实例
  const encoder = new TextEncoder();

  // 使用 TextEncoder 对消息进行编码
  const data = encoder.encode(message);

  // 使用 crypto.subtle.digest 方法对编码后的数据进行哈希运算
  const hashBuffer = await crypto.subtle.digest(algorithm, data);

  // 将哈希运算结果转换为十六进制字符串
  return toHashHex(hashBuffer);
};

/**
 * 对给定的字符串消息进行 SHA-512 哈希处理。
 *
 * @param message 需要进行哈希处理的字符串消息。
 * @returns 返回一个 Promise，该 Promise 解析为字符串消息的 SHA-512 哈希值。
 */
export const sha512Hash = async (message: string) => {
  // 使用hashMessage函数对消息进行哈希处理，算法为SHA512
  return hashMessage(message, HASH_ALGORITHM_SHA512);
};

/**
 * 使用 SHA-256 算法对消息进行哈希处理。
 *
 * @param message 要进行哈希处理的消息字符串。
 * @returns 返回哈希处理后的字符串。
 */
export const sha256Hash = async (message: string) => {
  // 使用 hashMessage 函数对消息进行哈希处理
  // HASH_ALGORITHM_SHA256 是哈希算法的参数，表示使用 SHA-256 算法
  return hashMessage(message, HASH_ALGORITHM_SHA256);
};
