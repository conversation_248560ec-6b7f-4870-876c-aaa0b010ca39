/**
 * 将数据合并到参数对象中，替换参数对象中所有的占位符（如 *{key}）。
 *
 * @param data 数据对象，用于替换参数对象中的占位符。
 * @param params 参数对象，可能包含占位符需要被替换。
 * @param regex 用于匹配占位符的正则表达式，默认为 /\*\{(\w+)\}/g。
 * @returns 合并数据后的参数对象。
 */
export const mergeDataIntoParams = (
  data: Record<string, any>,
  params: Record<string, any>,
  regex: RegExp = /\*\{(\w+)\}/g,
): Record<string, any> => {
  // 浅拷贝 params 对象以避免直接修改原对象
  let mergedParams: Record<string, any> = JSON.parse(JSON.stringify(params));

  /**
   * 替换占位符的辅助函数。
   *
   * @param value 需要进行替换的值。
   * @returns 替换后的值。
   */
  const replacePlaceholders = (value: any): any => {
    if (typeof value === 'string') {
      // 使用正则表达式匹配所有的 ${} 占位符
      return value.replace(regex, (match, key) => {
        // 在 data 对象中查找对应的 key
        return (key in data ? data[key] : match) as string;
      });
    } else if (Array.isArray(value)) {
      // 如果是数组，递归处理数组的每个元素
      return value.map(replacePlaceholders);
    } else if (typeof value === 'object' && value !== null) {
      // 如果是对象，递归处理对象的每个属性
      return Object.keys(value).reduce((obj, key) => {
        obj[key] = replacePlaceholders(value[key]);
        return obj;
      }, {} as Record<string, any>);
    }
    // 如果不是字符串、数组或对象，则直接返回原值
    return value;
  };

  // 递归替换占位符
  return replacePlaceholders(mergedParams);
};
