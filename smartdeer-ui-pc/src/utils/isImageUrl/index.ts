/**
 * 获取文件扩展名
 *
 * @param url 文件路径或URL，默认为空字符串
 * @returns 返回文件扩展名，如果找不到则返回空字符串
 */
const extname = (url: string = '') => {
  const temp = url.split('/');
  const filename = temp[temp.length - 1];
  const filenameWithoutSuffix = filename.split(/#|\?/)[0];
  const extensionMatch =  (/\.[^./\\]*$/.exec(filenameWithoutSuffix) || [''])[0];

  //  补充path带参数的过滤, eg: .jpeg_400x400c
  return extensionMatch.split("_")[0]
};

/**
 * 判断给定的字符串是否为图片URL
 *
 * @param url 待判断的字符串
 * @returns 返回布尔值，true表示是图片URL，false表示不是
 */
export const isImageUrl = (url: any): boolean => {
  if (!url) return false;
  const extension = extname(url);
  if (
    /^data:image\//.test(url) ||
    /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(extension)
  ) {
    return true;
  }
  if (/^data:/.test(url)) {
    return false;
  }
  if (extension) {
    return false;
  }
  return true;
};
