import swrFetcher from '../swrFetcher';

// 定义接口
interface FunctionData {
  functionKey: string;
  params: any;
}

interface MCPCall {
  method: string;
  params: any;
  id: string;
}

/**
 * 工作流事件接口定义
 * 用于定义工作流执行过程中的各个生命周期钩子函数
 */
interface WorkflowEvents {
  /**
   * 工作流执行前的钩子函数
   * @param data - 工作流数据对象
   */
  beforeRun?: (data: Record<string, any>) => void;

  /**
   * 工作流执行后的钩子函数
   * @param resp - 工作流执行响应结果
   */
  afterRun?: (resp: any) => void;

  /**
   * 工作流执行出错时的钩子函数
   * @param resp - 错误响应结果
   */
  onError?: (resp: any) => void;

  /**
   * 工作流执行完成时的钩子函数
   * @param resp - 完成时的响应结果
   */
  onComplete?: (resp: any, params: any) => void;

  onStep: (resp: any) => void;
}

interface WorkflowParams {
  message: string;
  events?: WorkflowEvents;
  params: {
    data?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

// MCPServer 类定义
export class MCPServer {
  private registry: Record<string, (params: any) => any>;

  constructor(initialRegistry: Record<string, (params: any) => any> = {}) {
    this.registry = initialRegistry;
  }

  register(functionKey: string, functionImpl: (params: any) => any): void {
    this.registry[functionKey] = functionImpl;
  }

  runFunction(data: FunctionData): any {
    const fn = this.registry[data.functionKey];
    if (!fn) {
      throw new Error(`Function not found: ${data.functionKey}`);
    }
    return fn(data.params);
  }
}

export class MCPClient {
  private servers: Record<string, MCPServer | any>;
  private api: string; // 后端 API 地址

  constructor(
    api: string,
    initialServers: Record<string, MCPServer | any> = {},
  ) {
    this.api = api;
    this.servers = initialServers;
    this.addServer('sage', {
      runFunction: (params: any) => this.runFunction(params),
    });
  }

  addServer(name: string, server: MCPServer | any): void {
    this.servers[name] = server;
  }

  // 通用请求封装
  async runFunction(data: any) {
    const { params, ...rest } = data;
    let newApi = this.api;
    let { entity, ...other } = params;
    let newParams = other;
    if (params && entity) {
      newApi = this.api.replace('/0/', `/${entity}/`);
    }

    if(data.functionKey === 'x_sage_digiworker_workflow') {
      const res = await swrFetcher('/v1/gs/digiworker/workflow', 'POST', {
        ...rest,
        params: newParams,
      });
      console.log("res", "res");
      return res
    }

    const res = await swrFetcher(newApi, 'POST', {
      ...rest,
      params: newParams,
    });
    return res;
  }

  async runWorkflowAsync(params: WorkflowParams, events: WorkflowEvents, workflowRequest: any): Promise<any> {

    const data: Record<string, any> = {};
    const mcpHistory: Record<string, boolean> = {};
    const workflowParams = { ...params };

    const dispatchMCPCall = async (mcp: MCPCall): Promise<any> => {
      const key = mcp.method + JSON.stringify(mcp.params);
      if (mcpHistory[key]) {
        throw new Error(`Duplicate MCP call detected: ${mcp.method}`);
      }
      mcpHistory[key] = true;
      const parts = mcp.method.split('.');

      const ns = parts.length > 1 ? parts[0] : 'sage';
      const method = parts.length > 1 ? parts[1] : parts[0];

      if (!this.servers.hasOwnProperty(ns)) {
        throw new Error(`Unknown MCP server: ${ns}`);
      }

      events?.onStep({ type: 'api', function_key: method, params: mcp.params });

      const res = await this.servers[ns].runFunction({
        functionKey: method,
        params: mcp.params,
      });

      events?.onStep(res);
      return res;
    };

    while (true) {
      try {
        events?.beforeRun?.(data);

        let newData = Object.keys(data).map((key) => {
          const item = { ...data[key] };
          if (Array.isArray(item?.data?.data?.rs)) {
            item.data.data.rs = item.data.data.rs.map(
              (r: any) => r.id || r.uuid,
            );
          }
          return { ...item };
        });

        workflowParams.params.data = JSON.stringify(newData);

        const resp = await workflowRequest(workflowParams)

        console.log("workflowRequest resp", resp);

        events?.afterRun?.(resp);
        events?.onStep({ ...resp, type: 'workflow' });

        // if (!resp || resp.code !== 0) {
        //   events?.onError?.(resp);
        //   return resp;
        // }

        if (
          !resp.mcp ||
          (Array.isArray(resp.mcp) && resp.mcp.length === 0)
        ) {
          console.log("resp", resp);
          events?.onComplete?.(resp, params);
          return resp;

        } else {
          let mcps = resp.mcp;
          if (!Array.isArray(mcps)) {
            mcps = [mcps];
          }

          for (const mcp of mcps) {
            const mcpResp = await dispatchMCPCall(mcp);
            data[mcp.id] = {
              method: mcp.method,
              params: mcp.params,
              data: mcpResp,
            };
          }
        }
      } catch (e) {
        events?.onError?.((e as Error).message || '请求服务异常, 请稍后重试');
        return e;
      }
    }
  }

}
