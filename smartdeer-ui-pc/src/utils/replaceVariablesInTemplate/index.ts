import { isEmpty } from 'lodash';
import { isSpecialValue } from '../isSpecialValue';

// 辅助函数：将样式对象转换为 CSS 字符串
const styleObjectToCssString = (style: Record<string, string>) => {
  return Object.entries(style)
    .map(([key, value]) => `${key}: ${value};`)
    .join(' ');
};

/**
 * 根据给定的数据对象和模板字符串，进行插值并返回结果字符串。
 *
 * @param data 数据对象，其键为字符串，值为字符串或数字。
 * @param template 模板字符串，其中可以包含形如 `*{key}` 的占位符。
 * @param regex 正则表达式, 使用正则表达式查找字符串中的参数占位符，例如：`*{key}`
 * @param options 替换选项对象，包含以下可选属性：
 *     - original: 是否保留原始占位符，默认为 false
 *     - color: 用于包裹替换值的 span 标签的文本颜色，默认为空字符串
 * @returns 替换后的模板字符串
 */
export const replaceVariablesInTemplate = (
  data: Record<string, string | number | boolean>,
  template: string,
  regex = /\*\{([^}]+)\}/g,
  options = {
    original: false,
    style: {},
  },
): string => {
  // 如果模板字符串不是特殊值，则直接返回模板字符串
  if (!isSpecialValue(template, regex)) return template;

  // 移除模板字符串中的空格
  const trimmedTemplate = template.trim();

  const { original, style } = options;

  // 使用正则表达式替换模板中的占位符
  return trimmedTemplate.replace(regex, (match, key) => {
    // 去除键名两侧的空格，并将键名按点分割成数组
    const keys = key.trim().split('.');

    // 使用 reduce 遍历键数组，并尝试从数据中获取对应的值
    const value = keys.reduce((currentValue: any, currentKey: any) => {
      // 如果当前值是一个对象，并且包含当前键对应的属性，则返回该属性的值
      return currentValue?.[currentKey] ?? null;
    }, data);

    // 如果最终获取到的值为 undefined || value，则返回空字符串
    if (value === undefined || value === null) {
      return original ? match : '';
    }

    // 如果设置了颜色，则将值用特定颜色的 span 标签包裹
    if (!isEmpty(style)) {
      const cssString = styleObjectToCssString(style);
      return `<span style='${cssString}'>${value}</span>`;
    }

    // 返回值的字符串形式
    return value.toString();
  });
};
