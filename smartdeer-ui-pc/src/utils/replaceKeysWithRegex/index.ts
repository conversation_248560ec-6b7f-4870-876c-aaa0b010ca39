import { isArray, isEmpty, isObject } from 'lodash';
import { isSpecialValue } from '../isSpecialValue';
import { replaceTemplateWithPipes } from '../replaceTemplateWithPipes';

type RecordType = Record<string, any>;

/**
 * 使用正则表达式替换对象或对象数组中的键。
 *
 * @param input 要处理的输入对象或对象数组。
 * @param record 包含替换模板中变量的记录对象。
 * @returns 替换后的对象或对象数组。
 */
export const replaceKeysWithRegex = (
  input: RecordType[] | RecordType,
  record: RecordType,
): RecordType[] | RecordType => {
  // 如果输入不是数组也不是对象，直接返回输入（因为它们是基本类型，不需要进一步处理）
  if (!isObject(input) && !isArray(input)) {
    return input;
  }

  if (isEmpty(record)) return input;

  const fn = (
    input: RecordType[] | RecordType,
  ): RecordType[] | RecordType | string => {
    // 判断输入是否为数组
    if (isArray(input)) {
      // 遍历数组的每个元素并递归处理
      return input.map((item) => fn(item));
    }

    // 判断输入是否为对象
    if (isObject(input)) {
      // 创建一个新的对象来存储替换后的键值对
      const newObject: Record<string, any> = {};
      // 遍历对象的每个键
      Object.keys(input).forEach((key: string) => {
        // 获取对象的键值
        const value = input[key];

        if (input.scope && key === 'children') {
          newObject[key] = value;
        } else {
          // 判断键的值是否需要替换
          if (isSpecialValue(value)) {
            // 如果键的值是字符串且匹配特定的正则表达式，则使用record中的值进行替换
            newObject[key] = replaceTemplateWithPipes(value, record);
          } else {
            // 如果键的值不是需要替换的字符串，则递归处理该值
            newObject[key] = fn(input[key]);
          }
        }
      });

      // 返回替换后的新对象
      return newObject;
    }

    if (isSpecialValue(input)) {
      return replaceTemplateWithPipes(input, record);
    }

    return input;
  };

  return fn(input) as RecordType[] | RecordType;
};
