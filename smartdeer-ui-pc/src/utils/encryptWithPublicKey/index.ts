/**
 * 使用公钥对数据进行加密
 *
 * @param publicKey 公钥对象，类型为 CryptoKey
 * @param data 待加密的数据，类型不限
 * @returns 返回加密后的数据，类型为 ArrayBuffer
 */
export const encryptWithPublicKey = async (publicKey: CryptoKey, data: any) => {
  // 创建一个 TextEncoder 对象，用于将字符串转换为 Uint8Array
  const encoder = new TextEncoder();
  // 将待加密的数据转换为 Uint8Array 格式
  const encodedData = encoder.encode(data);

  // 使用 window.crypto.subtle.encrypt 方法进行加密
  // 加密算法为 RSA-OAEP
  // publicKey 为公钥对象
  // encodedData 为待加密的 Uint8Array 数据
  const encryptedData = await window.crypto.subtle.encrypt(
    {
      name: 'RSA-OAEP',
    },
    publicKey,
    encodedData,
  );

  // 返回加密后的数据，类型为 ArrayBuffer
  return encryptedData;
};
