import { isEmpty, isObject, isString } from 'lodash';

/**
 * 替换输入对象中的特定字符串引用为另一个对象中的值。
 *
 * 此函数遍历输入对象的每个键值对。如果值是字符串且匹配指定的正则表达式（默认为查找以`*{...}`形式出现的引用），
 * 则尝试从第二个对象（`record`）中获取对应的值来替换它。如果值是一个对象，则递归地对该对象执行相同的操作。
 *
 * @param {Record<string, any>} input - 需要处理的输入对象。
 * @param {Record<string, any>} record - 引用目标对象，用于查找并替换输入对象中的引用。
 * @param {RegExp} [regex=/^\*\{(.+?)\}$/] - 用于匹配输入对象中引用的正则表达式。默认为查找以`*{...}`形式出现的引用。
 * @returns {Record<string, any>} - 处理后的对象，其中所有匹配的引用都已被替换为`record`中的相应值（如果找到的话），否则为`null`。
 */
export const replaceReferences = (
  input: Record<string, any>,
  record: Record<string, any>,
  regex = /^\*\{(.+?)\}$/,
): Record<string, any> => {
  // 如果记录为空，则直接返回输入
  if (isEmpty(record)) return input;

  // 初始化结果对象，根据输入是否为数组决定是数组还是对象
  const result: Record<string, any> = Array.isArray(input) ? [] : {};

  // 遍历输入的每个键值对
  Object.keys(input).forEach((key: string) => {
    const value = input[key];

    // 如果值是字符串并且匹配正则表达式
    if (isString(value) && regex.test(value)) {
      // 提取引用键
      const refKey = value.match(regex)?.[1];
      // 如果引用键存在，则替换为记录中的值，否则替换为null
      result[key] = refKey ? record[refKey] ?? null : null;
    }
    // 如果值是对象且不为null
    else if (isObject(value) && value !== null) {
      // 递归替换对象中的引用
      result[key] = replaceReferences(value, record);
    }
    // 其他情况直接赋值
    else {
      result[key] = value;
    }
  });

  return result;
};
