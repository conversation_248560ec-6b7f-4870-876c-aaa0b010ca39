import type { FetchWayType } from '../../typing';
import { mergeDataIntoParams } from '../mergeDataIntoParams';

export const getFetchParams = (
  type: FetchWayType,
  data: Record<string, string | number | boolean>,
  defaultParams: Record<string, string | number | boolean>,
  functionKey?: string,
) => {
  const params: Record<string, string | number | boolean> = {};

  const newDefaultParams: Record<string, string | number> = mergeDataIntoParams(
    data,
    defaultParams || {},
  );

  if (type === 'function') {
    Object.assign(params, {
      functionKey: functionKey,
      params: newDefaultParams,
    });
  } else if (type === 'api') {
    Object.assign(params, newDefaultParams);
  }

  return params;
};
