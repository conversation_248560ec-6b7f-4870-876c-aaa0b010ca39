import { isValidNumberString } from '../index';

/**
 * 根据给定的操作符评估左值和右值之间的条件，并返回结果。
 *
 * @param op 操作符，用于比较左值和右值。支持的操作符有：'==', '===', '!=', '!==', '>=', '>', '<=', '<'.
 * @param leftValue 左值，可以是任何类型的值。
 * @param rightValue 右值，可以是任何类型的值。
 * @returns 如果操作符对应的条件为真，则返回 true；否则返回 false。
 * @throws 如果给定的操作符不被支持，则抛出错误。
 */
export const evaluateCondition = (
  op: string,
  leftValue: any,
  rightValue: any,
): boolean => {
  let result = false;

  if (isValidNumberString(leftValue)) {
    leftValue = Number(leftValue);
  }

  if (isValidNumberString(rightValue)) {
    rightValue = Number(rightValue);
  }

  switch (op) {
    case '==':
      // eslint-disable-next-line eqeqeq
      result = leftValue == rightValue;
      break;
    case '===':
      result = leftValue === rightValue;
      break;
    case '!=':
      // eslint-disable-next-line eqeqeq
      result = leftValue != rightValue;
      break;
    case '!==':
      result = leftValue !== rightValue;
      break;
    case '>=':
      result = leftValue >= rightValue;
      break;
    case '>':
      result = leftValue > rightValue;
      break;
    case '<=':
      result = leftValue <= rightValue;
      break;
    case '<':
      result = leftValue < rightValue;
      break;
    default:
      throw new Error(`Unsupported operator: ${op}`);
  }

  return result;
};
