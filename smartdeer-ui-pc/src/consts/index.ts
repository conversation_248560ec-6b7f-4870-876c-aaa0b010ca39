const companyDefaultImage =
  'https://global-image.smartdeer.work/p/images/0x8e7af95449a14505b790631b1386f2d4.png';
const companyAuthImage =
  'https://global-image.smartdeer.work/p/images/0xb95fba43d6114adda9b671ecf311f5c6.png';

export const IMG = {
  companyDefaultImage,
  companyAuthImage,
} as const;

export const STORE = {
  TOKEN_IDENTIFIER: 'deer_auth_token',
  APP_API_BASE_URL: 'deer_app_api_base_url',
  APP_FUNCTION_API: 'deer_app_function_api',
  LOCALE: 'deer_locale',
  TIMEZONE: 'deer_timezone',
  IS_REQUES_ID: 'deer_is_reques_id',
  IS_ENCRYPT: 'deer_is_encrypt',
  ENCRYPT_TYPE: 'deer_encrypt_type',
  RSA_PUBLIC_KEY: 'deer_rsa_public_key',
  RSA_PRIVATE_KEY: 'deer_rsa_private_key',
  USER_ACCOUNT_ID: 'deer_user_account_id',
  TOKEN_SECURITY: 'deer_security_token',
  USER_ACCOUNT_INFO: 'deer_user_account_info',
  TEMPORARY_TOKEN: 'deer_temporary_token',
  MODULE_2FA: 'deer_module_2fa',
  SAGE_ENTITY_AUTH: 'deer_sage_entity_auth',
} as const;

export type STORE_KEY = keyof typeof STORE;
export type STORE_VALUE = (typeof ACCEPT_LANGUAGE)[ACCEPT_LANGUAGE_KEY];

// 定义从各时区到UTC的时差（负值表示该时区比UTC慢）
export const TIMEZONE_OFFSET_TO_UTC = {
  'Asia/Shanghai': +16,
  'America/New_York': -8, // 纽约比UTC慢4小时
} as const;

export type TIMEZONE_OFFSET_TO_UTC_KEY = keyof typeof TIMEZONE_OFFSET_TO_UTC;
export type TIMEZONE_OFFSET_TO_UTC_VALUE =
  (typeof ACCEPT_LANGUAGE)[ACCEPT_LANGUAGE_KEY];

export const ACCEPT_LANGUAGE = {
  'zh-CN': 'zh',
  'en-US': 'en',
  'zh-TW': 'zht',
  'ja-JP': 'ja',
} as const;

export type ACCEPT_LANGUAGE_KEY = keyof typeof ACCEPT_LANGUAGE;
export type ACCEPT_LANGUAGE_VALUE =
  (typeof ACCEPT_LANGUAGE)[ACCEPT_LANGUAGE_KEY];

export const PAGE_EVENT_NAME = {
  'form-submit': 'form-submit',
  'form-reset': 'form-reset',
  'form-steps-to': 'form-steps-to',
  'form-steps-back': 'form-steps-back',
  'form-steps-next': 'form-steps-next',
  'form-steps-fetch-next': 'form-steps-fetch-next',
  'form-steps-submit': 'form-steps-submit',
};

export type PAGE_EVENT_NAME_KEY = keyof typeof PAGE_EVENT_NAME;
export type PAGE_EVENT_NAME_VALUE =
  (typeof PAGE_EVENT_NAME)[PAGE_EVENT_NAME_KEY];
