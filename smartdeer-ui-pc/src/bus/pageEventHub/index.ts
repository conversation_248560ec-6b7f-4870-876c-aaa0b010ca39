import EventEmitter from 'eventemitter3';
import { prodWarning } from '../../utils/log';

class PageEventBus {
  ee: EventEmitter;

  /**
   * 构造函数
   *
   * 初始化一个EventEmitter实例，并将其赋值给类的属性ee
   */
  constructor() {
    this.ee = new EventEmitter();
  }

  /**
   * 订阅指定事件
   *
   * @param eventName 事件名称
   * @param callback 事件触发时调用的回调函数
   */
  subscribe(eventName: string, callback: (...args: any[]) => void) {
    this.ee.on(eventName, callback);
  }

  /**
   * 取消订阅某个事件
   *
   * @param eventName 事件名称
   * @param callback 事件回调函数
   */
  unsubscribe(eventName: string, callback: (...args: any[]) => void) {
    this.ee.removeListener(eventName, callback);
  }

  /**
   * 取消订阅所有事件监听器
   *
   * 调用此方法将移除当前实例中所有事件监听器。
   */
  unsubscribeAll() {
    // devLog('PageEventBus', '------------------------------');
    // devLog('PageEventBus', `当前 Page 事件监听器被移除`);
    // devLog('PageEventBus', '------------------------------');

    this.ee.removeAllListeners();
  }

  /**
   * 发布事件
   *
   * @param eventName 事件名称
   * @param args 事件参数列表
   */
  async publish(eventName: string, ...args: any[]) {
    // devLog('PageEventBus', '------------------------------');
    // devLog('PageEventBus', `发布事件：${eventName}`);
    // devLog('PageEventBus', `${eventName}事件参数列表：${JSON.stringify(args)}`);
    // devLog('PageEventBus', '------------------------------');

    const eventNames = this.ee.eventNames();

    if (!eventNames.includes(eventName)) {
      prodWarning('PageEventBus', `不存在该事件：${eventName}`);
      return;
    }

    // 返回为给定事件注册的侦听器。
    const listeners = this.ee.listeners(eventName);

    const results = await Promise.all(
      listeners.map((listener) => listener(...args)),
    );

    return results;
  }
}

const pageEventBus = new PageEventBus();

export default pageEventBus;
