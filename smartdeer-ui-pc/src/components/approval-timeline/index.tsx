import React, { FC } from 'react'
import classNames from 'classnames'
import { Avatar, Empty, Space } from 'antd'
import { mergeProps } from '../../utils/withDefaultProps'

import './index.less'
import dayjs from 'dayjs';

const classPrefix = `deer-approval-timeline`

export interface ApprovalTimelineProps {
  className?: string;
  style?: React.CSSProperties;

  dataInfo?: any[];
  rowKey?: string
}

const defaultProps = {
  dataInfo: [],
  rowKey: 'updateTime'
}

const ApprovalTimeline: FC<ApprovalTimelineProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,

    dataInfo,
    rowKey,
  } = props

  const dataInfoLength = dataInfo.length

  if (dataInfoLength === 0) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {dataInfo.map((item, index: number) => {
        const ymdStr = dayjs(item.updateTime).format('YYYY-MM-DD')
        const hsStr = dayjs(item.updateTime).format('HH:mm')

        const uniqueKey = item[rowKey] || index

        return (
          <div key={uniqueKey} className={`${classPrefix}-item`}>
            {(dataInfoLength - 1) !== index && <div className={`${classPrefix}-item-tail`} />}

            <div className={`${classPrefix}-item-content`}>
              <Space>
                <div className={`${classPrefix}-item-avatar`}>
                  <Avatar size={26} style={{ backgroundColor: item.avatarColor }}>
                    {item.avatarTitle}
                  </Avatar>
                </div>

                <div className={`${classPrefix}-item-box`}>
                  <div className={`${classPrefix}-item-title`} style={{ color: item.titleColor }}>
                    {item.title}
                  </div>
                  <div className={`${classPrefix}-item-description`} style={{ color: item.descriptionColor }}>
                    {item.description}
                  </div>
                </div>
              </Space>

              <div className={`${classPrefix}-item-time`}>
                <div>{ymdStr} {hsStr}</div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default ApprovalTimeline
