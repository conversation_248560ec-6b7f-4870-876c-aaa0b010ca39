import React from 'react'
import { <PERSON>lex, Typo<PERSON>, Button } from 'antd'
import { FileImageOutlined, FileOutlined, DownloadOutlined } from '@ant-design/icons';
import { useDownloadFile } from '../../utils/hooks'

import './index.less'

const classPrefix = `deer-download-files`

type FileType = {
  url: string;
  name: string;
  size?: number;
  uid?: string;
  type?: string;
}

export interface DownloadFilesProps {
  list: FileType[]
}

const DownloadFile: React.FC<{ item: FileType }> = ({ item }) => {
  const [downloadLoading, setDownloadLoading] = React.useState(false)

  const { handleDownload } = useDownloadFile()

  const handleClickDownload = async (item: FileType) => {
    setDownloadLoading(true)

    await handleDownload(item.url, item.name)

    setDownloadLoading(false)
  }

  return (
    <Flex justify='space-between' align='center' gap={8}>
      <Flex align='center' gap={8}>
        {item?.type?.includes('image') ? (
          <FileImageOutlined style={{ fontSize: '45px' }} />
        ) : (
          <FileOutlined style={{ fontSize: '45px' }} />
        )}
        <Typography.Text>
          {item.name}
        </Typography.Text>
      </Flex>

      <div className='pointer' onClick={() => handleClickDownload(item)}>
        <Button loading={downloadLoading} type="text" icon={<DownloadOutlined />} />
      </div>
    </Flex>
  )
}

const DownloadFiles: React.FC<DownloadFilesProps> = (props) => {

  const { list } = props

  return (
    <div className={classPrefix}>
      {list.map(item => {
        return (
          <div key={item.name} className={`${classPrefix}-item`}>
            <DownloadFile item={item} />
          </div>
        )
      })}
    </div>
  )
}

export default DownloadFiles
