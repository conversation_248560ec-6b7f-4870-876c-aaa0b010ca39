import React from 'react';
import { FileView } from '@smartdeer-ui/pc'

export default () => {
  return (
    <>
      <FileView
        src='https://global-image.smartdeer.work/files/test/gs/file-bf42e17d-bcab-43b0-810f-6900cd0edc80.jpg?q-sign-algorithm=sha1&q-ak=40563fcdb63f47bc90efaef8d8282c7f'
      />

      <FileView
        className={`mt-4`}
        src='https://global-image.smartdeer.work/test/images/0x6ea6d756c26f487db9baf40f2f8bf38d.png'
      />

      <FileView.Group
        className={`mt-4`}
        list={[
          'https://global-image.smartdeer.work/test/images/0x6ea6d756c26f487db9baf40f2f8bf38d.png',
          'https://global-image.smartdeer.work/test/images/0x6ea6d756c26f487db9baf40f2f8bf38d.png'
        ]}
      />

      <FileView.Group
        className={`mt-4`}
        isPrivate
        layout='text'
        list={[
          {
            "url": "files/k/bd79223013pd833d4fb3461a366c51bf7d5730def1763044651fcc9ea0332b22b8a74c4a550b915cf2e77c331ea4c488191a5f5d0e75e0b9580d1e0740da9979aa980b96e40e4db04",
            "uid": "rc-upload-1720135176715-11",
            "type": "image/png",
            "name": "image004.png",
            "size": 1334.0
          }
        ]}
      />
    </>
  )
}
