import React from 'react'
import classNames from 'classnames'
import { Flex, Spin, Button } from 'antd'
import { FileOutlined, FileImageOutlined, PaperClipOutlined } from '@ant-design/icons';
import { ConfigContext } from '../config-provider'
import { mergeProps } from '../../utils/withDefaultProps'
import { useFileAccessToken, useDownloadFile } from '../../utils/hooks'
import Image from '../image'
import { classPrefix } from '.'
import { isImageUrl } from '../../utils/isImageUrl'
import Icon from '../icon';

export interface FileViewProps {
  className?: string;
  style?: React.CSSProperties;
  src: string;
  name?: string;
  isPrivate?: boolean;
  layout?: 'default' | 'text';
}

const defaultProps = {
  isPrivate: false,
  layout: 'default',
}

const FileView: React.FC<FileViewProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,
    src,
    name,
    isPrivate,
    layout,
  } = props

  const { isFileToken, language } = React.useContext(ConfigContext)

  const { handleDownload } = useDownloadFile()

  const [isLoading, setIsLoading] = React.useState(false)

  const { getTheCompleteFileUrl } = useFileAccessToken()

  const fileName = React.useMemo(() => {
    if (name) return name

    if (!src) return ''

    const regex = /\/([^/?]*)(?:\?|$)/;
    const match = src.match(regex);
    if (match && match.length > 1) {
      return match[1];
    } else {
      return '';
    }
  }, [src, name])

  const handleOpen = async () => {
    let url = src

    if (isFileToken && !url.includes('http')) {
      url = await getTheCompleteFileUrl(url)
    }

    window.open(url)
  }

  const hnadleClick = async () => {
    if (!src) return

    setIsLoading(true)

    if (isPrivate) {
      await handleDownload(src, fileName)
    } else {
      await handleOpen()
    }

    setIsLoading(false)
  }

  return (
    <Spin spinning={isLoading}>
      {layout === 'text' ? (
        <div
          className={classNames(classPrefix, `${classPrefix}-text`, className)} style={{ ...style }}
        >

          <PaperClipOutlined style={{ fontSize: '14px', color: 'rgba(0,0,0,0.45)' }} />

          <div className={`${classPrefix}-text-name`}>
            {fileName}
          </div>

          <Button
            type='text'
            className={`${classPrefix}-text-download`}
            onClick={hnadleClick}
            size='small'
            icon={<Icon.DownloadOutlined style={{ color: 'rgba(0,0,0,0.45)' }} />}
          >
            <span style={{ marginLeft: '3px', color: 'rgba(0,0,0,0.45)' }}>{language.fileView.download}</span>
          </Button>
        </div>
      ) : (
        <Flex
          gap={8}
          align='center'
          className={classNames(classPrefix, `${classPrefix}-default`, className)} style={{ ...style }}
          onClick={hnadleClick}
        >

          {isPrivate ? (
            <FileOutlined style={{ fontSize: '48px', color: '#08c' }} />
          ) : (
            <Image
              src={src}
              width={48}
              height={48}
              style={{ minWidth: 48 }}
            />
          )}

          <div className={`${classPrefix}-default-name`}>
            {fileName}
          </div>
        </Flex>
      )}
    </Spin>
  )
}

export default FileView
