import type { FileViewProps } from './file-view';
import InternalFileView from './file-view';
import Group from './group';

import './index.less';

export const classPrefix = `deer-file-view`;

export type FileType = {
  name?: string;
  src?: string;
  url?: string;
  [key: string]: any;
};

export { type FileViewProps };

type CompoundedComponent = React.FC<FileViewProps> & {
  Group: typeof Group;
};

const FileView = InternalFileView as CompoundedComponent;

FileView.Group = Group;

export default FileView;
