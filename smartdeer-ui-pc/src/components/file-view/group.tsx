import React from 'react'
import classNames from 'classnames'
import { Flex } from 'antd';
import { isObject } from 'lodash';
import { mergeProps } from '../../utils/withDefaultProps'
import FileView from './file-view'
import type { FileViewProps } from './file-view'
import { classPrefix } from '.'
import type { FileType } from '.'


export interface FileViewGroupProps {
  className?: string;
  style?: React.CSSProperties;
  list: Array<FileType | string>,
  wrap?: boolean;
  isPrivate?: boolean;
  layout?: FileViewProps['layout']
}

const defaultProps = {
  wrap: true,
  isPrivate: false,
  layout: 'default' as FileViewProps['layout'],
}

const FileViewGroup: React.FC<FileViewGroupProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,
    list,
    wrap,
    isPrivate,
    layout
  } = props

  const newList = React.useMemo(() => {
    return list.map((item: string | FileType) => {
      return {
        name: isObject(item) ? item?.name : '',
        src: isObject(item) ? item?.src || item?.url : item
      }
    })
  }, [list])

  const childNode = React.useMemo(() => {
    return newList.map((item, index) => {
      return (
        <FileView
          key={index}
          src={item.src as string}
          name={item.name as string}
          isPrivate={isPrivate}
          layout={layout}
        />
      )
    })
  }, [newList])

  return (
    <React.Fragment>
      {layout === 'text' ? (
        <Flex gap={8} vertical className={classNames(`${classPrefix}-group`, className)} style={{ ...style }}>
          {childNode}
        </Flex>
      ) : (
        <Flex
          wrap={wrap}
          gap={8}
          className={classNames(`${classPrefix}-group`, className)} style={{ ...style }}
        >
          {childNode}
        </Flex>
      )}
    </React.Fragment>
  )
}

export default FileViewGroup
