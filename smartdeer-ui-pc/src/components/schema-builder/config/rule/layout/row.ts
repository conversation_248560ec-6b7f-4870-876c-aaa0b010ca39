import { FormBuilderItemRule } from '../../../typing';

const title = '网格布局';
const type = 'row';

const row: FormBuilderItemRule = {
  menu: 'main',
  icon: 'icon-row',
  title,
  type,
  columns: [
    {
      type: 'row',
      field: 'row_1',
      children: [
        {
          type: 'col',
          field: 'col_1',
          props: {
            span: '12',
          },
          children: [],
        },
        {
          type: 'col',
          field: 'col_2',
          props: {
            span: '12',
          },
          children: [],
        },
      ],
    },
  ],
};

export default row;
