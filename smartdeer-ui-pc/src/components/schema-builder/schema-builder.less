@text-color: #333;
@border-color: #d9d9d9;
@bg-color: #eeeeee;

.deer-schema-builder {
  height: 100%;

  &-main {
    height: 100%;
    width: 100%;
    display: flex;
    background-color: @bg-color;
  }

  &-composite {
    width: 295px;
    user-select: none;
    background-color: white;
    border-right: 1px solid @border-color;
    height: 100%;
    flex-grow: 0;
    flex-shrink: 0;

    &-icon {
      display: inline-flex;
      align-items: center;
      color: inherit;
      font-style: normal;
      line-height: 0;
      text-align: center;
      text-transform: none;
      vertical-align: -0.125em;
      text-rendering: optimizeLegibility;
      svg {
        display: inline-block;
      }
    }

    .ant-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;

      .ant-tabs-content-holder {
        flex-grow: 2;
        flex-shrink: 2;
        overflow: overlay;
        overflow-x: hidden;
        height: 100%;
      }
    }

    .ant-tabs-top >.ant-tabs-nav {
      margin:  0;
    }

  }

  &-resource {

    .ant-collapse {
      border-radius: 0;
      border-right: 0;
      border-left: 0;

      .ant-collapse-item {
        border-radius: 0 !important;

        .ant-collapse-header {
          background-color: @bg-color;
          border-radius: 0 !important;
        }

        .ant-collapse-content{
          .ant-collapse-content-box {
            padding: 0;
          }
        }
      }
    }

    &-content {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(3, 33.3333%);
      grid-gap: 1px;
      background-color: @border-color;
      border-top: 1px solid @border-color;
      border-bottom: 1px solid @border-color;

      &-wrap{
        display: flex;
        justify-content: center;
        background: white;
      }
    }

    &-item {
      position: relative;
      user-select: none;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      min-height: 44px;
      padding: 12px 0;
      cursor: pointer;
      transition: color 0.1s ease-out;

      &-name {
        margin-top: 12px;
      }

      &:hover {
        color: var(--icb-color-g1);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        z-index: 1;
      }
    }
  }

  &-workspace {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    padding: 4px;

    &-simulator {
      flex-grow: 2;
      flex-shrink: 2;
      background-color: white;

      &-hover-item {
        height: 35px;
        line-height: 35px;
        text-align: center;
        border-radius: 6px;
        border: 1px dashed #474747;
        font-size: 12px;
        background-color: @bg-color;
      }
    }
  }

  &-settings {
    width: 300px;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    background-color: white;
    border-left: 1px solid @border-color;
  }
}
