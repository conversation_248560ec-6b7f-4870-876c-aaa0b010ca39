import React from "react";
import { Tabs as AntdTabs } from 'antd';
import { ComponentIcon, OutlineIcon } from '../icons'
import type { TabsProps } from 'antd';
import Resource from './resource'

import { classPrefix } from '../.'

const Composite = () => {
  const [activeKey, setActiveKey] = React.useState('component')

  const Tabitems: TabsProps['items'] = React.useMemo(() => {
    return [
      {
        key: 'component',
        label: '组件',
        icon: <ComponentIcon className={`${classPrefix}-composite-icon`} />,
        children: <Resource />
      },
      {
        key: 'outline',
        label: '大纲树',
        icon: <OutlineIcon className={`${classPrefix}-composite-icon`} />,
      }
    ]
  }, [])

  return (
    <div className={`${classPrefix}-composite`}>
      <AntdTabs
        centered
        items={Tabitems}
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key)}
      />
    </div>
  )
}

export default React.memo(Composite)
