import React from 'react';
import { Collapse } from 'antd';
import type { CollapseProps } from 'antd';
import { resourceList } from '../../config'
import Content from './comps/content'
import { classPrefix } from '../..'


const Resource = () => {

  const defaultActiveKey = React.useMemo(() => { return resourceList.map(item => item.type) }, []);

  const items: CollapseProps['items'] = React.useMemo(() => {

    return resourceList.map((item) => {
      return {
        label: item.title,
        key: item.type,
        children: <Content list={item.children} />,
      }
    })
  }, []);

  return (
    <div className={`${classPrefix}-resource`}>
      <Collapse
        size='small'
        items={items}
        defaultActiveKey={defaultActiveKey}
      />
    </div>
  )
}

export default React.memo(Resource)
