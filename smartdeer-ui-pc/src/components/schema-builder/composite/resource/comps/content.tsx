import React from "react";
import Item from './item';
import type { FormBuilderItemRule } from '../../../typing'
import { classPrefix } from '../../..'

interface ContentProps {
  list: FormBuilderItemRule[]
}

const Content: React.FC<ContentProps> = (
  props
) => {

  const { list } = props

  return (
    <div className={`${classPrefix}-resource-content-wrap`}>
      <div className={`${classPrefix}-resource-content`}>
        {list.map(item => {
          return (
            <Item key={item.type} info={item} />
          )
        })}
      </div>
    </div>
  )
}

export default React.memo(Content)
