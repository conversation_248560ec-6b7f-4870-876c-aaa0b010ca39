import React from 'react';
import type { FormBuilderItemRule } from '../../../typing'
import Icon from '../../../icons/icon'
import { classPrefix } from '../../..'
import { Drag } from '../../../../drag-and-drop'

interface ItemProps {
  info: FormBuilderItemRule;
}

const Item: React.FC<ItemProps> = (props) => {

  const { type, icon, title } = props.info

  return (
    <Drag
      type={type}
      className={`${classPrefix}-resource-item`}
      item={props.info}
    >
      <div className={`${classPrefix}-item-icon`}>
        <Icon name={icon} />
      </div>
      <div className={`${classPrefix}-item-name`}>
        {title}
      </div>
    </Drag>
  )
}

export default React.memo(Item)
