import React from 'react';
import type { FormBuilderItemRule } from '../../typing'
import { classPrefix } from '../..'
import Icon from '../../icons/icon'
import type { FormColumnsType } from '../../../schema-form/typing'
import { generateRandomString } from '../../../../utils/generateRandomString'
import { Drop } from '../../../drag-and-drop'
import { simulatorDropAcceptList } from '../../config'


const Simulator: React.FC = () => {

  const [schemaFormColumns, setSchemaFormColumns] = React.useState<FormColumnsType[]>([
    {
      type: 'gird',
      field: 'ref_1',
      title: 'gird',
      __isDrag: true,
      children: [
        {
          title: 'col',
          type: 'col',
          field: 'col_1',
          props: {
            span: 12,
          },
        },
        {
          title: 'col',
          type: 'col',
          field: 'col_2',
          props: {
            span: 12,
          },
        },
      ],
    }
  ])

  return (
    <Drop
      accept={simulatorDropAcceptList}
      className={`${classPrefix}-workspace-simulator`}
      onMove={(args) => {
        console.log(args)
      }}
    >
      {(collectedProps) => {
        const { isOver, item } = collectedProps
        console.log('collectedProps', collectedProps)
        return (
          <React.Fragment>
            {isOver && (
              <div className={`${classPrefix}-workspace-simulator-hover-item`}>
                <div>{item.title}</div>
              </div>
            )}
          </React.Fragment>
        )
      }}
    </Drop>
  )
}

export default React.memo(Simulator)
