import React from 'react';
import {
  InputIcon,
  TextareaIcon,
  PasswordIcon,
  NumberIcon,
  RateIcon,
  SliderIcon,
  RowIcon,
} from './index'

interface IconProps {
  name: string;
}

const Icon: React.FC<IconProps> = (props) => {

  const { name } = props

  const childNode = React.useMemo(() => {
    switch (name) {
      case 'icon-input':
        return <InputIcon />
      case 'icon-textarea':
        return <TextareaIcon />
      case 'icon-password':
        return <PasswordIcon />
      case 'icon-number':
        return <NumberIcon />
      case 'icon-rate':
        return <RateIcon />
      case 'icon-slider':
        return <SliderIcon />
      case 'icon-row':
        return <RowIcon />
    }

  }, [name])

  return (
    <>{childNode}</>
  )
}

export default React.memo(Icon)
