
import React from 'react';

const PasswordIcon = (): JSX.Element => {

  return (
    <svg viewBox="0 0 1424 1024" height="40" width="150" fill="currentColor" focusable="false" aria-hidden="true">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <path d="M1344,218 C1388.18278,218 1424,253.81722 1424,298 L1424,726 C1424,770.18278 1388.18278,806 1344,806 L80,806 C35.81722,806 0,770.18278 0,726 L0,298 C0,253.81722 35.81722,218 80,218 L1344,218 Z M1344,238 L80,238 C47.1942859,238 20.5378857,264.328343 20,297.00779 L20,298 L20,726 C20,758.805714 46.328343,785.462114 79.0077903,785.991962 L80,786 L1344,786 C1376.80571,786 1403.46211,759.671657 1404,726.99221 L1404,726 L1404,298 C1404,265.194286 1377.67166,238.537886 1344.99221,238.008038 L1344,238 Z" fill="#999999" fillRule="nonzero"></path>
        <circle fill="#D8D8D8" cx="170" cy="512" r="50"></circle>
        <circle fill="#D8D8D8" cx="388" cy="512" r="50"></circle>
        <circle fill="#D8D8D8" cx="606" cy="512" r="50"></circle>
        <circle fill="#D8D8D8" cx="824" cy="512" r="50"></circle>
        <path d="M1154,452 C1120.86292,452 1094,478.862915 1094,512 C1094,545.137085 1120.86292,572 1154,572 C1187.13708,572 1214,545.137085 1214,512 C1214,478.862915 1187.13708,452 1154,452 Z M1154,472 C1176.09139,472 1194,489.90861 1194,512 C1194,534.09139 1176.09139,552 1154,552 C1131.90861,552 1114,534.09139 1114,512 C1114,489.90861 1131.90861,472 1154,472 Z" fill="var(--adm-color-primary)" fillRule="nonzero"></path>
        <path d="M1154,395.333333 C1091.33002,395.333333 1035.15313,432.784592 985.698343,506.424832 L981.954212,512 L985.698343,517.575168 C1035.15313,591.215408 1091.33002,628.666667 1154,628.666667 C1216.66998,628.666667 1272.84687,591.215408 1322.30166,517.575168 L1326.04579,512 L1322.30166,506.424832 C1272.84687,432.784592 1216.66998,395.333333 1154,395.333333 Z M1155.65312,415.343433 C1208.50839,415.989823 1257.0702,447.668766 1301.54117,511.497045 L1301.889,511.999 L1301.54117,512.502955 C1256.60696,576.996112 1207.49624,608.666667 1154,608.666667 L1152.34688,608.656567 C1099.49161,608.010177 1050.9298,576.331234 1006.45883,512.502955 L1006.11,511.999 L1006.45883,511.497045 C1051.39304,447.003888 1100.50376,415.333333 1154,415.333333 L1155.65312,415.343433 Z" fill="#979797" fillRule="nonzero"></path>
      </g>
    </svg>
  )
}

export default PasswordIcon;
