
import React from 'react';

const RateIcon = (): JSX.Element => {

  return (
    <svg viewBox="0 0 1024 1024" height="40" width="150" fill="currentColor" focusable="false" aria-hidden="true">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <path d="M487.286155,34.2736908 L320.815283,276.187129 C319.518769,278.071208 317.615604,279.453938 315.423095,280.104784 L33.9074136,363.672628 C30.3952048,364.715226 27.103676,366.392344 24.1957719,368.620957 C11.0451673,378.699558 8.55481647,397.53055 18.6334176,410.681154 L197.264441,643.759726 C198.655661,645.574996 199.382605,647.8123 199.324074,650.098623 L191.808687,943.65983 C191.714925,947.32232 192.29282,950.971008 193.513765,954.425268 C199.035324,970.046695 216.175101,978.234256 231.796528,972.712697 L508.667444,874.849738 C510.82378,874.087558 513.17622,874.087558 515.332556,874.849738 L792.203472,972.712697 C795.657733,973.933642 799.30642,974.511537 802.96891,974.417775 C819.532026,973.993747 832.615341,960.222946 832.191313,943.65983 L824.675926,650.098623 C824.617395,647.8123 825.344339,645.574996 826.735559,643.759726 L1005.36658,410.681154 C1007.5952,407.77325 1009.27231,404.481721 1010.31491,400.969512 C1015.02992,385.086021 1005.97608,368.387637 990.092586,363.672628 L708.576905,280.104784 C706.384396,279.453938 704.481231,278.071208 703.184717,276.187129 L536.713845,34.2736908 C534.636943,31.2555623 532.024771,28.6433902 529.006642,26.5664883 C515.357563,17.1739791 496.678664,20.6246113 487.286155,34.2736908 Z M517.668881,43.0423848 C518.674924,43.7346854 519.545648,44.6054094 520.237948,45.6114523 L686.708821,287.524891 C690.598363,293.177126 696.307856,297.325316 702.885386,299.277853 L984.401066,382.845698 C989.695563,384.417367 992.713511,389.983495 991.141841,395.277992 C990.794309,396.448729 990.235269,397.545905 989.492398,398.515206 L810.861375,631.593778 C806.687715,637.039589 804.506882,643.751501 804.682477,650.610471 L812.197864,944.171677 C812.339206,949.692716 807.978101,954.282983 802.457063,954.430326 C801.236233,954.45558 800.020004,954.262948 798.868583,953.855966 L521.997667,855.993007 C515.528661,853.706468 508.471339,853.706468 502.002333,855.993007 L225.131417,953.855966 C219.924274,955.696486 214.211015,952.967299 212.370496,947.760157 C211.963514,946.608737 211.770882,945.392507 211.802136,944.171677 L219.317523,650.610471 C219.493118,643.751501 217.312285,637.039589 213.138625,631.593778 L34.5076015,398.515206 C31.1480678,394.131671 31.9781848,387.854674 36.3617196,384.49514 C37.331021,383.75227 38.4281973,383.19323 39.5989335,382.845698 L321.114614,299.277853 C327.692144,297.325316 333.401637,293.177126 337.291179,287.524891 L503.762052,45.6114523 C506.83698,41.1430036 512.897807,39.9537114 517.42357,42.8787518 L517.668881,43.0423848 Z" fill="#999999" fillRule="nonzero"></path>
        <rect fill="var(--adm-color-primary)" transform="translate(512.000000, 659.000000) scale(-1, 1) rotate(90.000000) translate(-512.000000, -659.000000) " x="502" y="479" width="20" height="360" rx="10"></rect>
      </g>
    </svg>
  )
}

export default RateIcon;
