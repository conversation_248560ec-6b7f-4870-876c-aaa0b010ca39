
import React from 'react';

const NumberIcon = (): JSX.Element => {

  return (
    <svg viewBox="0 0 1424 1024" height="40" width="150" fill="currentColor" focusable="false" aria-hidden="true">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <path d="M1344,218 C1388.18278,218 1424,253.81722 1424,298 L1424,726 C1424,770.18278 1388.18278,806 1344,806 L80,806 C35.81722,806 0,770.18278 0,726 L0,298 C0,253.81722 35.81722,218 80,218 L1344,218 Z M1344,238 L80,238 C47.1942859,238 20.5378857,264.328343 20,297.00779 L20,298 L20,726 C20,758.805714 46.328343,785.462114 79.0077903,785.991962 L80,786 L1344,786 C1376.80571,786 1403.46211,759.671657 1404,726.99221 L1404,726 L1404,298 C1404,265.194286 1377.67166,238.537886 1344.99221,238.008038 L1344,238 Z" fill="#999999" fillRule="nonzero"></path>
        <rect fill="#999999" transform="translate(1096.000000, 512.000000) scale(-1, 1) translate(-1096.000000, -512.000000) " x="1086" y="218" width="20" height="588" rx="10"></rect>
        <rect fill="#999999" transform="translate(1260.000000, 512.000000) scale(-1, 1) rotate(90.000000) translate(-1260.000000, -512.000000) " x="1250" y="348" width="20" height="328" rx="10"></rect>
        <path d="M1319.65311,602.492777 C1322.69467,597.882919 1328.89737,596.611559 1333.50722,599.653115 C1338.03621,602.64131 1339.3428,608.6807 1336.50299,613.263154 L1336.34689,613.507223 L1279.01464,700.401401 C1276.82002,703.727631 1273.9847,706.583294 1270.67424,708.801642 C1257.04789,717.932702 1238.64758,714.416301 1229.33159,700.989873 L1229.05208,700.579981 L1170.74424,613.56675 C1167.66981,608.978753 1168.8968,602.767127 1173.4848,599.692696 C1177.99087,596.673166 1184.06309,597.802804 1187.19117,602.190685 L1187.35885,602.43325 L1245.66669,689.446481 C1248.74112,694.034478 1254.95274,695.261466 1259.54074,692.187035 C1260.53388,691.52153 1261.39873,690.683951 1262.09506,689.714816 L1262.32087,689.386954 L1319.65311,602.492777 Z" fill="var(--adm-color-primary)" fillRule="nonzero"></path>
        <path d="M1319.65311,314.492777 C1322.69467,309.882919 1328.89737,308.611559 1333.50722,311.653115 C1338.03621,314.64131 1339.3428,320.6807 1336.50299,325.263154 L1336.34689,325.507223 L1279.01464,412.401401 C1276.82002,415.727631 1273.9847,418.583294 1270.67424,420.801642 C1257.04789,429.932702 1238.64758,426.416301 1229.33159,412.989873 L1229.05208,412.579981 L1170.74424,325.56675 C1167.66981,320.978753 1168.8968,314.767127 1173.4848,311.692696 C1177.99087,308.673166 1184.06309,309.802804 1187.19117,314.190685 L1187.35885,314.43325 L1245.66669,401.446481 C1248.74112,406.034478 1254.95274,407.261466 1259.54074,404.187035 C1260.53388,403.52153 1261.39873,402.683951 1262.09506,401.714816 L1262.32087,401.386954 L1319.65311,314.492777 Z" fill="var(--adm-color-primary)" fillRule="nonzero" transform="translate(1253.525806, 367.940873) scale(1, -1) translate(-1253.525806, -367.940873) "></path>
        <path d="M178.761719,654.887695 C188.066406,654.887695 192.71875,649.718424 192.71875,639.379883 L192.71875,639.379883 L192.71875,408.313477 C192.71875,398.750326 188.195638,393.96875 179.149414,393.96875 C176.564779,393.96875 172.817057,397.070312 167.90625,403.273438 C158.601562,415.16276 148.90918,422.787435 138.829102,426.147461 C135.469076,426.922852 132.755208,427.439779 130.6875,427.698242 C122.416667,429.765951 118.28125,433.513672 118.28125,438.941406 C118.28125,447.470703 122.804362,451.735352 131.850586,451.735352 C140.379883,451.735352 151.88151,447.21224 166.355469,438.166016 L166.355469,438.166016 L166.355469,639.379883 C166.355469,649.718424 170.490885,654.887695 178.761719,654.887695 Z M421.84668,649.072266 C432.443685,649.072266 437.742188,644.549154 437.742188,635.50293 C437.742188,626.973633 432.443685,622.708984 421.84668,622.708984 L421.84668,622.708984 L307.088867,622.708984 C317.944336,607.459635 331.772135,591.564128 348.572266,575.022461 C358.652344,565.200846 374.41862,550.985352 395.871094,532.375977 C423.009766,509.114258 436.579102,485.723307 436.579102,462.203125 C436.579102,440.233724 428.179036,423.045898 411.378906,410.639648 C396.646484,399.267253 378.166341,393.581055 355.938477,393.581055 C332.159831,393.581055 312.258138,401.334961 296.233398,416.842773 C280.725586,432.609049 272.97168,449.926107 272.97168,468.793945 C272.97168,479.132487 277.753255,484.301758 287.316406,484.301758 C292.485677,484.301758 295.974935,478.61556 297.78418,467.243164 C299.593424,456.129232 304.116536,446.824544 311.353516,439.329102 C323.501302,426.147461 338.362956,419.556641 355.938477,419.556641 C370.412435,419.556641 382.43099,423.045898 391.994141,430.024414 C402.591146,437.77832 407.889648,448.504557 407.889648,462.203125 C407.889648,480.295573 397.421875,498.646484 376.486328,517.255859 C358.39388,533.797526 344.953776,546.203776 336.166016,554.474609 C317.039714,571.016276 301.661133,586.394857 290.030273,600.610352 C276.848633,616.893555 270.257812,628.265951 270.257812,634.727539 C270.257812,644.29069 276.590169,649.072266 289.254883,649.072266 L289.254883,649.072266 L421.84668,649.072266 Z M547.847656,654.887695 C571.109375,654.887695 591.527995,648.813802 609.103516,636.666016 C629.263672,622.708984 639.34375,604.358073 639.34375,581.613281 C639.34375,548.271484 622.026693,526.04362 587.392578,514.929688 C619.442057,503.557292 635.466797,485.20638 635.466797,459.876953 C635.466797,437.649089 625.903646,420.590495 606.777344,408.701172 C591.269531,398.879557 571.884766,393.96875 548.623047,393.96875 C531.047526,393.96875 514.764323,399.008789 499.773438,409.088867 C482.714844,420.461263 474.185547,435.064453 474.185547,452.898438 C474.185547,462.978516 479.096354,468.018555 488.917969,468.018555 C494.08724,468.018555 497.705729,463.883138 499.773438,455.612305 C502.358073,445.273763 507.527344,437.132161 515.28125,431.1875 C525.102865,423.950521 536.216797,420.332031 548.623047,420.332031 C563.613932,420.332031 576.537109,423.17513 587.392578,428.861328 C600.832682,435.839844 607.552734,445.79069 607.552734,458.713867 C607.552734,475.513997 598.764974,487.40332 581.189453,494.381836 C570.333984,498.775716 554.050781,501.360352 532.339844,502.135742 C519.933594,502.652669 513.730469,507.046549 513.730469,515.317383 C513.730469,524.105143 519.933594,528.499023 532.339844,528.499023 C561.28776,528.499023 581.189453,532.763672 592.044922,541.292969 C604.451172,551.373047 610.654297,564.813151 610.654297,581.613281 C610.654297,596.08724 603.675781,607.718099 589.71875,616.505859 C577.829427,624.259766 563.872396,628.136719 547.847656,628.136719 C516.315104,628.136719 497.447266,614.825846 491.244141,588.204102 C488.659505,578.124023 485.041016,573.083984 480.388672,573.083984 C470.567057,573.083984 465.65625,578.770182 465.65625,590.142578 C465.65625,610.819661 474.702474,627.102865 492.794922,638.992188 C508.302734,649.589193 526.653646,654.887695 547.847656,654.887695 Z" stroke="#FFFFFF" fill="#999999" fillRule="nonzero"></path>
      </g>
    </svg>
  )
}

export default NumberIcon;
