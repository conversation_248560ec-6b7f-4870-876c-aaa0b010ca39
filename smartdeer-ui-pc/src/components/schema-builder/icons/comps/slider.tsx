
import React from 'react';

const SliderIcon = (): JSX.Element => {

  return (
    <svg viewBox="0 0 1024 1024" height="40" width="150" fill="currentColor" focusable="false" aria-hidden="true">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <rect fill="#999999" x="289" y="304" width="735" height="20" rx="10"></rect>
        <rect fill="#999999" x="0" y="702" width="531" height="20" rx="10"></rect>
        <rect fill="#999999" x="799" y="702" width="225" height="20" rx="10"></rect>
        <path d="M150,164 C67.1572875,164 0,231.157288 0,314 C0,396.842712 67.1572875,464 150,464 C232.842712,464 300,396.842712 300,314 C300,231.157288 232.842712,164 150,164 Z M150,184 C221.797017,184 280,242.202983 280,314 C280,385.797017 221.797017,444 150,444 C78.2029825,444 20,385.797017 20,314 C20,242.202983 78.2029825,184 150,184 Z" fill="#999999" fillRule="nonzero"></path>
        <path d="M664,562 C581.157288,562 514,629.157288 514,712 C514,794.842712 581.157288,862 664,862 C746.842712,862 814,794.842712 814,712 C814,629.157288 746.842712,562 664,562 Z M664,582 C735.797017,582 794,640.202983 794,712 C794,783.797017 735.797017,842 664,842 C592.202983,842 534,783.797017 534,712 C534,640.202983 592.202983,582 664,582 Z" fill="var(--adm-color-primary)" fillRule="nonzero"></path>
      </g>
    </svg>
  )
}

export default SliderIcon;
