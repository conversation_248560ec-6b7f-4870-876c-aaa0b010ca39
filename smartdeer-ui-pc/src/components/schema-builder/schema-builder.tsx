import React from 'react'
import DndProvider from '../drag-and-drop'
import { HTML5Backend } from 'react-dnd-html5-backend'
import Main from './main'

import type { SchemaBuilderProps } from '.'
import { classPrefix } from '.'

import './schema-builder.less'

const SchemaBuilder: React.FC<SchemaBuilderProps> = (props) => {

  const {
  } = props

  return (
    <div className={classPrefix}>
      <DndProvider backend={HTML5Backend}>
        <Main />
      </DndProvider>
    </div>
  )
}

export default SchemaBuilder
