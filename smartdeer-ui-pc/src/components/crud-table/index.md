---
toc: content
group: 
  title: 数据展示
  order: 9
---

# CrudTable 表格 Pro

高级表格

## 示例

<code src="./demos/index.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| title | 标题 | `ReactNode` | `--` | |
| description | 描述 | `ReactNode` | `--` | |
| size | 表格大小	| `large` \| `middle` \| `small` | `large` | |
| scroll | 表格是否可滚动，也可以指定滚动区域的宽、高，[配置项](#scroll) | `object` | `--` |  |
| columns | 表格列的配置描述，具体项见下表 | `CurdTableColumnType[]` | `--` | |
| dataSource | 数据数组 | `object[]` | `--` | |
| pagination | 分页器 | `object` \| `false` | `false` | |
| onRefresh | 刷新数据 | `() => void` | `--` | |
| onChange | 分页、排序、筛选变化时触发 | `function(pagination, filters, sorter, extra: { currentDataSource: [], action: paginate \| sort \| filter })` | `--` |  |
| onRowClick | 点击行 | `(record: object, index: number \| undefined) => void` | `--` |  |
| onRowDoubleClick | 双击行 | `(record: object, index: number \| undefined) => void` | `--` |  |
| onRowContextMenu | 上下文菜单 | `(record: object, index: number \| undefined) => void` | `--` |  |
| onRowMouseEnter | 鼠标移入击行 | `(record: object, index: number \| undefined) => void` | `--` |  |
| onRowMouseLeave | 鼠标移出行 | `(record: object, index: number \| undefined) => void` | `--` |  |

### scroll

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| scrollToFirstRowOnChange | 当分页、排序、筛选变化后是否滚动到表格顶部 | `boolean` | `--` |
| x | 设置横向滚动，也可用于指定滚动区域的宽，可以设置为像素值，百分比，`true` 和 ['max-content'](https://developer.mozilla.org/zh-CN/docs/Web/CSS/width#max-content) | `string` \| `number` \| `true` | `--` |
| y | 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值 | `string` \| `number` | `--` |


### columns

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| title | 列头显示文字 | `string` | `--` | 
| key | React 需要的 key，如果已经设置了唯一的 `dataIndex`，可以忽略这个属性 | `string` | `--` | 
| dataIndex | 列数据在数据项中对应的路径，支持通过数组查询嵌套路径 | `string` \| `string[]` | `--` | 
| type | 数据类型 | `text` \| `modal` \| `template` \| `template-json` \| `status` \| `action` | `text` | 
| fixed | IE 下无效）列是否固定，可选 `true` (等效于 `left`) `left` `right` | `boolean` \| `left` \| `right` | `false` | 
| width | 列宽度 | `string` \| `number` | `--` | 
| template | 模版 | `string` \| `TemplateJsonType` \| `TemplateStatusType[]` \| `TemplateActionType[]` | `--` | 

### template

```ts
type FetchType = {
  type: FetchWayType;
  api?: string;
  functionKey?: string;
  method?: FetchMethodType;
  paramsInjectionLocation?: string[];
  defaultParams?: Record<string, string | boolean | number>;
};

type OnSuccessType = {
  type: 'link';
  url?: string;
  message?: string;
};

type TemplateJsonChildrenType = {
  type: string;
  props?: {
    to?: string;
    style?: Record<string, string>;
  };
  children?: string | TemplateJsonChildrenType;
};

type TemplateJsonType = {
  type: string;
  children: TemplateJsonChildrenType[];
};

type TemplateStatusType = {
  vif?: string;
  name: string;
  color?: string;
};

type TemplateActionType = {
  vif?: string;
  name: string;
  color?: string;
  type?: string;
  popProps?: {
    title: string;
    description: string;
    okText?: string;
    cancelText?: string;
  };
  onSubmit?: FetchType;
  onSuccess?: OnSuccessType;
  to?: string;
};
```
