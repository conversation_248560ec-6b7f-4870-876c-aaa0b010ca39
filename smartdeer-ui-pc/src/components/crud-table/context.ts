import React from 'react';
type RecordType = Record<string, any>;
import { RenderComponentsType } from '../components-provider';

export interface CrudTableContextProps {
  onRefresh: () => void;
  onRowClick?: (ecord: RecordType, index: number | undefined) => void;
  onShowDrawer?: (column: any, record: any) => void;
  onCloseDrawer?: (refresh?: boolean) => void;
  onCloseModal?: (refresh?: boolean) => void;
  mock?: Record<string, any>;

  methods?: Record<string, (record: RecordType) => Promise<void>>;
  renderComponents?: Array<RenderComponentsType>;
}

export const CrudTableContext = React.createContext<CrudTableContextProps>({
  onRefresh: () => undefined,

});
