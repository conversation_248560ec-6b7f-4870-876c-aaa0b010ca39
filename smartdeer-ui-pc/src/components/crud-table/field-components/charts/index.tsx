import React, {useMemo} from 'react';
import {Line} from '@ant-design/charts';

type ChartsType = {
  data: any
  type: 'tinyLine'  //  后续有需要支持的图表, 在这里添加
  xField: string
  yField: string
  empty?: string
}

// 用于迷你图, 默认参数
const defaultTinyProps = {
  colorField: '#fe9111',
  width: 120,
  height: 40,
  axis: {
    y: false,
    x: false,
  },
  style: {
    lineWidth: 1,
  },
  tooltip: false,
}


function Charts(props: ChartsType) {

  const {type, empty = "-", data, ...rest} = props;

  const newData = useMemo(() => {
    let tempData = data;
    if (typeof tempData === 'string') {
      tempData = tempData.split(",").map((item, index) => {
        return {value: Number(item), index}
      })
    }
    return tempData
  }, [data])


  if (newData.length > 0) {
    switch (type) {
      case "tinyLine":
        return <Line data={newData} {...defaultTinyProps} {...rest}  />
      default:
        return <span>{`type=${type}, 请传入支持的图表`}</span>
    }
  }
  return empty

}

export default Charts
