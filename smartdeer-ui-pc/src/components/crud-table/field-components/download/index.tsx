import React from 'react';
import { Spin, message } from 'antd';
import type { TemplateActionType } from '../../typing'
import { useParamsDict, useDownloadFile } from '../../../../utils/hooks'
import { ConfigContext } from '../../../config-provider'
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes'
import { swrDownloadFile } from '../../../../utils/swrFetcher';
import { replaceKeysWithRegex } from '../../../../utils/replaceKeysWithRegex';

interface FieldLinkProps {
  item: TemplateActionType;
  record: Record<string, any>
}

const FieldDownload: React.FC<FieldLinkProps> = ({ item, record = {} }) => {
  const { name, props } = item

  const { type, downloadProps } = props

  const { router } = React.useContext(ConfigContext)
  const paramsDict = useParamsDict(record)

  const [isLoading, setIsLoading] = React.useState(false)

  const { handleDownload } = useDownloadFile()

  const handleClick = async(e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (type === 'swrDownloadFile') {

      const props = replaceKeysWithRegex(downloadProps, { ...paramsDict, ...record }) as any

      const { url, method, defaultParams, fileName } = props;

      try {
        await swrDownloadFile(url, fileName, method, defaultParams);
      }catch(e) {
        message.error('download error')
      }
      return
    }

    if (!props?.fileKey) {
      console.warn('fileKey is not defined')
      return
    }

    setIsLoading(true)

    try {

      const fileKey = replaceTemplateWithPipes(props?.fileKey, paramsDict)

      let fileName = ''

      if (props?.fileName) {
        fileName = replaceTemplateWithPipes(props?.fileName, paramsDict)
      } else {
        fileName = fileKey.split('/').pop() || ''
      }

      handleDownload(fileKey, fileName)

    } catch (err: any) {
      message.error(err?.message || 'download failed')
    }

    setIsLoading(false)
  }

  return (
    <Spin spinning={isLoading} size='small' >
      <a onClick={handleClick}>
        {name}
      </a>
    </Spin>
  );
};

export default React.memo(FieldDownload)
