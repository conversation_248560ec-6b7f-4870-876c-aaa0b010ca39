import React from 'react'
import { Popconfirm as AntdPopconfirm, message } from 'antd';
import type { TemplateActionType } from '../../typing'
import { CrudTableContext } from './../../context'
import { ConfigContext } from './../../../config-provider/context'
import swrFetcher from '../../../../utils/swrFetcher'
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes'
import { useGetEffectFetchConfig } from '../../../../utils/hooks'
import InnerHtml from '../../../inner-html'
import { delay } from '../../../../utils'

const classPrefix = `deer-crud-table`

interface FieldPopconfirmProps {
  item: TemplateActionType;
  record: Record<string, any>
}

const FieldPopconfirm: React.FC<FieldPopconfirmProps> = ({ item, record = {} }) => {
  const { type, props, onSubmit: submitConfig } = item

  const titleText = props?.title || `确认需要 [${item.name}] 吗？`
  const descriptionText = props?.description ? <InnerHtml content={replaceTemplateWithPipes(props?.description, record)} /> : null

  const crudTableContext = React.useContext(CrudTableContext)

  const { language } = React.useContext(ConfigContext)

  const { api, method, params } = useGetEffectFetchConfig(submitConfig, record)

  const handleConfirm = async () => {

    // console.log('submitConfig', submitConfig)

    // return

    if (!submitConfig) {
      throw new Error(`[smartdeer-ui: ${item.name}功能]: 未定义 onSubmit`);
    }

    if (!api) {
      throw new Error(`[smartdeer-ui: ${item.name}功能]: onSubmit 未配置 api`);
    }

    try {
      await swrFetcher(api, method, params)

      message.success(language.message.success)

      await delay(200)

      crudTableContext?.onRefresh()
    } catch (err: any) {
      message.error(err.message)
    }
  }

  return (
    <AntdPopconfirm
      {...props}
      title={titleText}
      description={() => descriptionText}
      onConfirm={handleConfirm}
    >
      {type === 'pop' ? (
        <a>{item.name}</a>
      ) : (
        <span className={`${classPrefix}-button-del`}>{item.name}</span>
      )}
    </AntdPopconfirm>
  )
}


export default React.memo(FieldPopconfirm)
