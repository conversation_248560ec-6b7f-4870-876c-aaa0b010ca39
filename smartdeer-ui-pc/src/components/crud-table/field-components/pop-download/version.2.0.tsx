import {
  Popconfirm as AntdPopconfirm,
  Form,
  Input,
  Radio,
  Select,
  message,
} from 'antd';
import Paragraph from 'antd/es/typography/Paragraph';
import React from 'react';
import { FieldPopDownloadProps } from '.';
import base64EncoderDecoder from '../../../../utils/base64EncoderDecoder';
import { generateRandomPassword } from '../../../../utils/generateRandomPassword';
import {
  useGetEffectFetchConfig,
  useParamsDict,
} from '../../../../utils/hooks';
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes';
import { swrDownloadFile } from '../../../../utils/swrFetcher';
import { ConfigContext } from '../../../config-provider';
import './index.less';

const classPrefix = `deer-pop-download`;

const Version2: React.FC<FieldPopDownloadProps> = ({ item, record = {} }) => {
  const { props, onSubmit: submitConfig } = item;

  const paramsDict = useParamsDict(record);

  const { language } = React.useContext(ConfigContext);

  const [form] = Form.useForm();

  const password = Form.useWatch('password', form);

  const defaultPassword = Form.useWatch('defaultPassword', form);

  const passwordType = Form.useWatch('passwordType', form);

  const newPassword = React.useMemo(() => {
    return passwordType === '1' ? defaultPassword : password;
  }, [password, defaultPassword, passwordType]);

  const {
    title: titleText,
    verify,
    passwordStyle = {},
    selectLanguageText,
    setPasswordTypeText,
  } = props;

  const defaultPwd = (props.defaultPassword && paramsDict.defaultPassword)
    ? replaceTemplateWithPipes(props.defaultPassword, paramsDict)
    : undefined;

  const { api, method, params } = useGetEffectFetchConfig(submitConfig, record);

  const handleConfirm = async () => {
    if (!submitConfig) {
      throw new Error(`[smartdeer-ui: ${item.name}功能]: 未定义 onSubmit`);
    }

    if (!api) {
      throw new Error(`[smartdeer-ui: ${item.name}功能]: onSubmit 未配置 api`);
    }

    let values: Record<string, any> = {};
    await form.validateFields();
    values = form.getFieldsValue();
    values.password = newPassword;

    const fileName = replaceTemplateWithPipes(
      props?.fileName || '',
      paramsDict,
    );

    const fetchParams = {
      ...params,
      password: base64EncoderDecoder.encrypt(values.password),
    };

    const headers = {
      'Accept-Language': values.language,
    };

    try {
      await swrDownloadFile(api, fileName, method, fetchParams, headers);

      message.success(language.popDownload.successfully);
    } catch (err: any) {
      message.error(err.message);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (open) {
      form.setFieldValue(
        'defaultPassword',
        defaultPwd ? defaultPwd : generateRandomPassword(25),
      );
      form.setFieldValue('password', undefined);
      form.setFieldValue('passwordType', '1');
      form.setFieldValue('language', 'en');
    }
  };

  const passwordRules = React.useMemo(() => {
    const rules: any = [
      {
        required: true,
        message: language.popDownload.pleaseInput,
      },
    ];

    if (verify !== false) {
      rules.push({
        required: true,
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*]).+$/,
        message: language.popDownload.passwordRules,
      });
    } else {
      rules.push({
        min: 6,
        message: language.popDownload.atLeast,
      });
    }
    return rules;
  }, [verify]);

  const descriptionNode = () => {
    return (
      <div style={{ width: '300px', marginTop: '20px' }}>
        <Form
          form={form}
          initialValues={{
            language: 'en',
            passwordType: '1',
          }}
          layout="vertical"
        >
          <Form.Item
            name="language"
            label={selectLanguageText || language.popDownload.selectLanguage}
            rules={[{ required: true }]}
          >
            <Select
              options={[
                { label: 'English', value: 'en' },
                { label: '简体中文', value: 'zh' },
                { label: '繁體中文', value: 'zht' },
              ]}
            ></Select>
          </Form.Item>
          <Form.Item
            name="passwordType"
            label={setPasswordTypeText || language.popDownload.setPasswordType}
            rules={[{ required: true }]}
          >
            <Radio.Group
              options={[
                { label: language.popDownload.systemGenerated, value: '1' },
                { label: language.popDownload.custom, value: '2' },
              ]}
            ></Radio.Group>
          </Form.Item>
          {passwordType === '2' && (
            <Form.Item
              name="password"
              label={language.popDownload.setPassword}
              rules={passwordRules}
            >
              <Input.Password />
            </Form.Item>
          )}
          {passwordType === '1' && (
            <Form.Item
              name="defaultPassword"
              label={language.popDownload.systemGeneratedPassword}
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input readOnly />
            </Form.Item>
          )}
        </Form>

        {!!newPassword && (
          <>
            <p className="font-semibold">{language.popDownload.saveTip}</p>
            <div className="mt-[10px] font-semibold">
              <Paragraph
                copyable
                style={{
                  marginTop: '10px',
                  color: '#fe9111',
                  ...passwordStyle,
                }}
              >
                {newPassword}
              </Paragraph>
            </div>
          </>
        )}
      </div>
    );
  };

  return (
    <div className={classPrefix}>
      <AntdPopconfirm
        {...props}
        destroyTooltipOnHide
        title={titleText}
        onOpenChange={handleOpenChange}
        description={descriptionNode}
        onConfirm={handleConfirm}
      >
        <a>{item.name}</a>
      </AntdPopconfirm>
    </div>
  );
};

export default React.memo(Version2);
