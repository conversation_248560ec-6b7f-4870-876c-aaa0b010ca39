import { Popconfirm as AntdPopconfirm, message, Select } from 'antd';
import React, { useState } from 'react';
import { FieldPopDownloadProps } from '.';
import {
  useGetEffectFetchConfig,
  useParamsDict,
} from '../../../../utils/hooks';
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes';
import { swrDownloadFile } from '../../../../utils/swrFetcher';
import { ConfigContext } from '../../../config-provider';
import './index.less';

const classPrefix = `deer-pop-download`;

const options = [
  { label: 'English', value: 'en' },
  { label: '简体中文', value: 'zh' },
  { label: '繁體中文', value: 'zht' },
];

const DescriptionNode = ({
  value,
  description,
  setValue,
}: {
  value: string;
  description: string;
  setValue: React.Dispatch<React.SetStateAction<string>>;
}) => {
  return (
    <div className={`${classPrefix}-description`}>
      <span className={`${classPrefix}-description-title`}>{description}</span>
      <Select
        options={options}
        value={value}
        onChange={(e) => setValue(e)}
        style={{ width: 219, marginBottom: 10 }}
      />
    </div>
  );
};

const Version1: React.FC<FieldPopDownloadProps> = ({ item, record = {} }) => {
  const { type, props, onSubmit: submitConfig } = item;

  const { language } = React.useContext(ConfigContext);

  const [value, setValue] = useState('en');

  const titleText = props?.title || `确认需要 [${item.name}] 吗？`;

  const description = props?.description || '';

  const descriptionText = (
    <DescriptionNode
      value={value}
      setValue={setValue}
      description={description}
    />
  );

  const paramsDict = useParamsDict(record);

  const { api, method, params } = useGetEffectFetchConfig(submitConfig, record);

  const handleConfirm = async () => {
    if (!submitConfig) {
      throw new Error(`[smartdeer-ui: ${item.name}功能]: 未定义 onSubmit`);
    }

    if (!api) {
      throw new Error(`[smartdeer-ui: ${item.name}功能]: onSubmit 未配置 api`);
    }

    const fileName = replaceTemplateWithPipes(
      props?.fileName || '',
      paramsDict,
    );

    const headers = {
      'Accept-Language': value,
    };

    try {
      await swrDownloadFile(api, fileName, method, params, headers);

      message.success(language.popDownload.successfully);
    } catch (err: any) {
      message.error(err.message);
    }
  };

  return (
    <div className={classPrefix}>
      <AntdPopconfirm
        {...props}
        title={titleText}
        description={() => descriptionText}
        onConfirm={handleConfirm}
      >
        <a>{item.name}</a>
      </AntdPopconfirm>
    </div>
  );
};

export default React.memo(Version1);
