import React from 'react';
import Version1 from './version.1.0';
import Version2 from './version.2.0';

import './index.less';

export type FieldPopDownloadProps = {
  item: Record<string, any>;
  record: Record<string, any>;
};

const FieldPopDownload: React.FC<FieldPopDownloadProps> = (props) => {
  const { version = '1.0' } = props.item;

  return (
    <>{version === '1.0' ? <Version1 {...props} /> : <Version2 {...props} />}</>
  );
};

export default FieldPopDownload;
