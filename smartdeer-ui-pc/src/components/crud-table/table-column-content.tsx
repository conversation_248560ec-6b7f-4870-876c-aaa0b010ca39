import React from 'react'
import { Typo<PERSON>, Space, Avatar, Tag, Flex } from 'antd';
import { isString } from 'lodash'
import { ConfigContext, ConfigContextProps } from '../config-provider'
import { replaceTemplateWithPipes, renderTemplateJson, renderTemplateStatus, renderTemplateAction, renderTemplateDrawer, renderTemplateArray, renderTemplateSwitch } from './render-column-template'
import type { CurdTableColumnProps } from '.'
import { timeAgo } from '../../utils/time'
import InnerHtml from '../inner-html'
import Image from '../image'
import isDeepEqualReact from '../../utils/isDeepEqualReact'
import { useParamsDict } from '../../utils/hooks'
import Charts from './field-components/charts';
import { CrudTableContext } from './context';
import { RenderComponentsType } from '../components-provider';

const classPrefix = `deer-crud-table-column`

const renderChildNode = (props: CurdTableColumnProps, configContext: ConfigContextProps, paramsDict: Record<string, any>) => {
  const { column, text, record, index, onShowModal, onShowDrawer } = props
  const { language, router } = configContext

  let childNode: React.ReactNode = null;

  switch (column.type) {
    case 'avatar':
      childNode = (
        text ? <Avatar shape='square' size={38} src={<img src={text} alt='avatar' />} /> : <Avatar shape='square' size={38} />
      )
      break;

    case 'image':
      childNode = (
        text ? <Image src={text} width={38} height={38} {...column.props} /> : <Avatar shape='square' size={38} />
      )
      break;

    case 'timeAgo':
      childNode = (
        <>{timeAgo(text)}</>
      )
      break;

    case 'modal':
      childNode = column?.template ? (
        <Typography.Text
          className={`pointer`}
          onClick={() => onShowModal(column, { ...paramsDict, ...record })}
        >
          <InnerHtml
            content={replaceTemplateWithPipes(column.template, { ...paramsDict, ...record })}
          />
        </Typography.Text>
      ) : (
        <Typography.Text
          underline
          className={`pointer`}
          onClick={() => onShowModal(column, { ...paramsDict, ...record })}
        >
          {language.table.details}
        </Typography.Text>
      );
      break;

    case 'drawer': {
      childNode = (
        <Space>{renderTemplateDrawer(column.template, { ...paramsDict, ...record }, configContext.timezone!, onShowDrawer)}</Space>
      )
      break;
    }

    case 'template':
      childNode = (
        <InnerHtml content={replaceTemplateWithPipes(column.template, { ...paramsDict, ...record })} />
      )
      break;

    case 'template-json':
      childNode = (
        <>{renderTemplateJson(column.template, { ...paramsDict, ...record }, configContext.router)}</>
      )
      break;

    case 'templateJson':
      childNode = (
        <>{renderTemplateJson(column.template, { ...paramsDict, ...record }, configContext.router)}</>
      )
      break;

    case 'status':
      childNode = (
        <Space>
          {renderTemplateStatus(column.template, { ...paramsDict, ...record }, configContext.timezone!)}
        </Space>
      )
      break;

    case 'action':
      childNode = (
        <Space>
          {renderTemplateAction(column.template, { ...paramsDict, ...record }, configContext.timezone!, onShowDrawer, onShowModal)}
        </Space>
      )
      break;

    case 'selectStr':
      childNode = '-'
      try {
        childNode = column.props.options.find((find: { label: string, value: string }) => find.value === text)?.label
      } catch (e) {
        console.error((e as Error).message)
      }
      break;

    case 'tag':
      childNode = <Flex gap="4px 0" wrap>{text?.map((item: string) => <Tag {...column.props} key={item}>{item}</Tag>)}</Flex>
      break;

    case 'templateArray':
      childNode = (
        <InnerHtml
          content={renderTemplateArray(record[column.key], column.template)}
        />
      );
      break;

    case 'templateSwitch':
      childNode = (
        <>{renderTemplateSwitch(props, configContext, paramsDict, renderChildNode)}</>
      );
      break;

    case 'charts':
      childNode = <Charts data={text} {...column.props} />
      break;

    case 'link':
      childNode = (
        <a onClick={() => {
          const url = replaceTemplateWithPipes(column.props.url, { ...paramsDict, ...record })
          router?.push(url)
        }}>{text}</a>
      )
      break

    default:
      if (isString(text)) {
        childNode = (
          <>{text}</>
        )
      } else {

        childNode = (
          <>{JSON.stringify(text)}</>
        )
      }

      break;
  }
  return childNode;
}

const CurdTableColumn: React.FC<CurdTableColumnProps> = (props) => {
  const configContext = React.useContext(ConfigContext)
  const paramsDict = useParamsDict()

  const { renderComponents } = React.useContext(CrudTableContext)


  const renderComponent = renderComponents?.find((item: RenderComponentsType) => item.type === props.column.type)

  let childNode: React.ReactNode = null;

  if (renderComponent) {
    childNode = typeof renderComponent.render === 'function' ? renderComponent.render(props.record) : renderComponent.render
  } else {
    childNode = renderChildNode(props, configContext, paramsDict);
  }

  return (<div className={classPrefix}>{childNode}</div>)
}

export default React.memo(CurdTableColumn, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, []);
});
