import React from 'react'
import { <PERSON><PERSON>, Modal as AntdModal, Space, Typography } from 'antd';
import { FormatJson } from '../common'
import CrudTable from './table';
import type { CurdTableColumnType } from '.'
import { useDeepCompareMemo } from '../../utils/hooks'
import DynamicForm from '../dynamic-form';
import { CrudTableContext } from './context';
import DynamicPage from '../dynamic-page';
import SchemaPage from '../schema-page';
import { replaceKeysWithRegex } from '../../utils/replaceKeysWithRegex';

const classPrefix = `deer-crud-table-modal`

export interface CudTableModalProps {
  open: boolean;
  title?: string;
  componentContent?: string | any[] | Record<string, any>;
  componentId?: string | number;
  componentName?: string;
  tablecColumns?: CurdTableColumnType[];
  onCancel: () => void;
  record: Record<string, any>;
  [key: string]: any;
}

type DefaultRenderComponentType = {
  componentName: string;
  componentContent?: string | any[] | Record<string, any>;
  tablecColumns: CurdTableColumnType[];

  [key: string]: any;
}

const defaultRenderComponent = (props: DefaultRenderComponentType) => {

  const {
    componentName,
    componentContent,
    tablecColumns,
    onClose,

    type,
    conf,
    confObject,
    record
  } = props

  let childNode: React.ReactNode = null;

  switch (componentName) {
    case 'format-json':
      childNode = <FormatJson content={componentContent as any} />
      break;

    case 'formatJson':
      childNode = <FormatJson content={componentContent as any} />
      break;

    case 'crud-table':
      childNode = <CrudTable dataSource={componentContent as any} columns={tablecColumns} />
      break;

    case 'crudTable':
      childNode = <CrudTable dataSource={componentContent as any} columns={tablecColumns} />
      break;

    case 'dynamicForm':
      childNode = (
        <DynamicForm
          type={type}
          conf={conf}
          back={props.back}
          onBack={props.onBack}
          footer={(btn) => <Space className={"flex justify-end w-full"}>
            <Button onClick={onClose} className={"mr-[16px]"}>取消</Button>
            {btn}
          </Space>
          }
          confObject={confObject}
          onCrudTableRetry={() => onClose(true)}
          formVariables={record}
        />
      );
      break;

    case 'dynamicPage':
      childNode = (
        <DynamicPage
          conf={conf}
          confObject={confObject}
        />
      );
      break;

    case 'schemaPage': {
      const columns = replaceKeysWithRegex(confObject, {
        ...record,
      }) as any[];
      childNode = <SchemaPage variables={{ ...record, onModalClose: onClose }} columns={columns} />;
      break;
    }

    default:
      childNode = (
        <Typography.Text type="danger">CudTableModal 没有实现的组件: {componentName}</Typography.Text>
      )
      break;
  }

  return <>{childNode}</>
}

const CudTableModal: React.FC<CudTableModalProps> = (props) => {
  const {
    open,
    title,
    width,
    componentName,
    componentContent,
    onCancel,
    tablecColumns,

    ...restProps
  } = props

  const crudTableContext = React.useContext(CrudTableContext);


  const handleClose = (update?: boolean) => {
    onCancel?.();

    if (update) {
      crudTableContext?.onRefresh();
    }
  };


  const renderedDom = useDeepCompareMemo(() => {
    return defaultRenderComponent({
      componentName: componentName || '',
      componentContent,
      onClose: handleClose,
      tablecColumns: tablecColumns || [],
      ...restProps
    })
  }, [componentName, componentContent, tablecColumns])

  return (
    <AntdModal
      open={open}
      title={title}
      width={width || 520}
      destroyOnClose={true}
      footer={null}
      className={classPrefix}
      onCancel={onCancel}
      maskClosable={restProps?.maskClosable}
    >
      {renderedDom}
    </AntdModal>
  )
}

export default React.memo(CudTableModal)
