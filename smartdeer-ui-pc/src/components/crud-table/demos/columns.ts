import type { CurdTableColumnType } from '@smartdeer-ui/pc';

const columns: CurdTableColumnType[] = [
  {
    title: '合同',
    key: 'title',
    type: 'templateJson',
    template: {
      type: 'div',
      children: [
        {
          type: 'link',
          props: {
            // "to": "/components/table"
          },
          children: '*{variables.title}',
        },
        {
          type: 'div',
          props: {
            style: {
              fontSize: '12px',
              color: '#9ca3af',
              marginTop: '5px',
            },
          },
          children: {
            type: 'span',
            children: '*{taskVariables.email}',
          },
        },
      ],
    },
  },
  {
    title: '合同类型',
    key: 'variables,contractTypeStr',
    dataIndex: ['variables', 'contractTypeStr'],
  },
  {
    title: '职位名称',
    key: 'contractTypeStr',
    dataIndex: ['taskVariables', 'jobTilte'],
  },
  {
    title: '客户名称',
    key: 'customerEntityStr',
    dataIndex: ['taskVariables', 'customerEntityStr'],
  },
  {
    title: '工作地',
    key: 'employingStateStr',
    dataIndex: ['variables', 'employingStateStr'],
  },
  {
    title: '合同开始时间',
    key: 'expectedStartDate',
    type: 'template',
    template:
      '<div>*{variables.expectedStartDate | formatTime(YYYY-MM-DD)}</div>',
  },
  {
    title: 'finishTime',
    key: 'finishTime',
    type: 'template',
    template: '<div>*{createTime | mathjs(*<aa>,+)}</div>',
  },
  {
    title: '合同状态',
    key: 'system.status',
    type: 'status',
    template: [
      {
        vif: '*{variables.expectedStartDate} < now',
        name: '已失效',
        color: '#9ca3af',
      },
      {
        vif: '*{system_status}===0',
        name: '草稿',
        color: '#9ca3af',
      },
      {
        vif: '*{system_status}===1',
        name: '待邀请',
        color: '#fbbf24',
      },
      {
        vif: '*{system_status}===2',
        name: '信息审核中',
        color: '#3b82f6',
      },
      {
        vif: '*{system_status}===3',
        name: '信息已审核',
        color: '#10b981',
      },
      {
        vif: '*{system_status}===4',
        name: '待开始',
        color: '#ea580c',
      },
      {
        vif: '*{system_status}===5',
        name: '生效中',
        color: '#16a34a',
      },
      {
        vif: '*{system_status}===6',
        name: '已取消',
        color: '#f43f5e',
      },
      {
        vif: '*{system_status}===7',
        name: '已邀请',
        color: '#7c3aed',
      },
    ],
  },
  {
    title: '业务操作',
    key: 'action',
    type: 'action',
    fixed: 'right',
    width: '200px',
    template: [
      {
        vif: '*{system_status}===0',
        name: '发送邀请',
        type: 'pop',
        props: {
          title: '发送邀请入职邮件',
          description:
            '<div><div>确定要给预入职员工发送邀请邮件吗？</div><div>发出后将无法撤回！</div><div>邮件将发送给 <a>*{variables.email}</a></div></div>',
          okText: '是',
          cancelText: '否',
        },
        // "onSubmit": {
        //   "type": "function",
        //   "functionKey": "flow_start",
        //   "defaultParams": {
        //     "flowKey": "global_gs_c_flow",
        //     "version": "41808f76f6384287",
        //     "system.parentFlowId": "*{id}",
        //     "uniqueKey": "*{id}"
        //   }
        // }
      },
      {
        vif: '*{system_status}===0',
        name: '编辑',
        type: 'link',
        props: {
          url: '/agencyoperations/entry/edit/*{id}',
        },
      },
      {
        vif: '*{system_status} === 2 || *{system_status} === 3 || *{system_status} === 4 || *{system_status} === 5',
        name: '上传 SOW',
        type: 'upload',
      },
      {
        vif: '*{system_status} === 2 || *{system_status} === 3 || *{system_status} === 4 || *{system_status} === 5',
        name: '上传押金凭证',
        type: 'upload',
      },
      {
        vif: '*{system_status} === 3 || *{system_status} === 4 || *{system_status} === 5',
        name: '上传员工合同',
        type: 'upload',
      },
      {
        vif: '*{system_status}===4',
        name: '确认入职',
        type: 'pop',
      },
      {
        vif: '*{system_status}===0',
        name: '继续填写',
        type: 'link',
      },
      {
        vif: '*{system_status}===7',
        name: '再次发送邀请',
        type: 'pop',
      },
    ],
  },
  {
    title: '系统操作',
    key: 'action',
    type: 'action',
    fixed: 'right',
    width: '100px',
    template: [
      {
        vif: '*{system_status}===0 || *{system_status}===0',
        name: '修改',
        type: 'link',
        props: {
          url: '/agencyoperations/entry/edit/*{id}',
        },
      },
      {
        vif: '*{system_status} === 0 || *{system_status} === 1 || *{system_status} === 2 || *{system_status} === 3 || *{system_status} === 4',
        name: '取消',
        type: 'del',
        props: {
          title: '取消合同',
          description: '<div>您确定要取消此合同吗？</div>',
          okText: '是',
          cancelText: '否',
        },
      },
    ],
  },
  {
    title: '操作',
    key: 'action',
    type: 'action',
    width: '270px',
    fixed: 'right',
    template: [
      {
        name: '详情',
        type: 'link',
        props: {
          url: 'detail/*{id}',
        },
      },
      {
        name: '发布',
        type: 'pop',
        onSubmit: {
          type: 'function',
          functionKey: 'x_sage_corehr_release_payroll',
          method: 'post',
          defaultParams: {
            id: '*{id}',
            __is_admin__: 'true',
          },
        },
        onSuccess: {
          type: 'retry',
        },
        vif: '*{isRelease}===0',
      },
      {
        name: '撤销发布',
        type: 'pop',
        onSubmit: {
          type: 'function',
          functionKey: 'x_sage_corehr_rollback_payroll',
          method: 'post',
          defaultParams: {
            id: '*{id}',
            __is_admin__: 'true',
          },
        },
        onSuccess: {
          type: 'retry',
        },
        vif: '*{isRelease}!==0',
      },
      {
        name: 'drawer 修改',
        type: 'drawer',
        props: {
          title: '修改',
          componentName: 'dynamicForm',
          type: 'add',
          confObject: {
            onCreate: {
              type: 'function',
              functionKey: 'x_sage_add_account',
              paramsInjectionLocation: ['params'],
              defaultParams: {},
            },
            form: {
              size: 'large',
              supplementingTheString: false,
              layout: 'vertical',
              gridProps: {
                col: '1',
              },
              defaultValues: {
                account: {
                  permissions: [],
                  roles: [],
                },
              },
              columns: [
                {
                  type: 'input',
                  field: ['alias'],
                  title: '邮箱',
                  props: {},
                  labelProps: {
                    rules: [
                      {
                        required: true,
                        message: '请输入邮箱',
                      },
                    ],
                  },
                },
                {
                  type: 'input',
                  field: ['realName'],
                  title: '用户名',
                  props: {},
                  labelProps: {
                    rules: [
                      {
                        required: true,
                        message: '请输入用户名',
                      },
                    ],
                  },
                },
              ],
            },
          },
        },
      },
      {
        name: 'modal 修改',
        type: 'modal',
        props: {
          title: '修改',
          componentName: 'dynamicForm',
          type: 'add',
          confObject: {
            onCreate: {
              type: 'function',
              functionKey: 'x_sage_add_account',
              paramsInjectionLocation: ['params'],
              defaultParams: {},
            },
            form: {
              size: 'large',
              supplementingTheString: false,
              layout: 'vertical',
              gridProps: {
                col: '1',
              },
              defaultValues: {
                account: {
                  permissions: [],
                  roles: [],
                },
              },
              columns: [
                {
                  type: 'input',
                  field: ['alias'],
                  title: '邮箱',
                  props: {},
                  labelProps: {
                    rules: [
                      {
                        required: true,
                        message: '请输入邮箱',
                      },
                    ],
                  },
                },
                {
                  type: 'input',
                  field: ['realName'],
                  title: '用户名',
                  props: {},
                  labelProps: {
                    rules: [
                      {
                        required: true,
                        message: '请输入用户名',
                      },
                    ],
                  },
                },
              ],
            },
          },
        },
      },
      {
        name: '删除',
        type: 'del',
        props: {
          title: '删除',
          description:
            '<div>您确定要删除此薪资项吗？</div><div style="margin-top: 5px; font-weight: bolder; color: #e52c2c">删除之后无法反悔！可能会影响其他数据！</div>',
          okText: '是',
          cancelText: '否',
        },
        onSubmit: {
          type: 'function',
          functionKey: 'x_sage_corehr_remove_payroll',
          defaultParams: {
            id: '*{id}',
          },
        },
      },
    ],
  },
];

export default columns;
