const dataSource = [
  {
    subFlowIds: [],
    finishTime: 0,
    variables: {
      needAWorkPermitStr: '没有签证，需要代办',
      expectedStartDate: '1710547200000',
      employeeFullName: '测试',
      contractType: '1',
      contractDeadline: '1710460800000',
      customerEntityStr: 'ID HOLDINGS (HK) LIMITED',
      title: '测试 sunxu',
      nationalityStr: '津巴布韦',
      needAWorkPermit: '2',
      organizationStr: 'ICB',
      customerEntity: '2',
      'system.status': '0',
      employingStateStr: '中国香港',
      nationality: '181',
      employeeLastName: 'sunxu',
      emailLang: '2',
      entityToSignEOR: 'smartdeer',
      organization: '17',
      jobTilte: '职位名称',
      email: '<EMAIL>',
      contractTypeStr: 'EOR',
      employingState: '1',
      emailLangStr: 'English',
    },
    updateTime: 1710314222031,
    createTime: 1710212281084,
    taskVariables: {
      template: '1',
      scopeOfWork: '工作范围',
      employeeFullName: '测试',
      probationaryPeriod: '1',
      contractDeadline: '1710460800000',
      customerEntityStr: 'smartdeer',
      title: '测试 sunxu',
      customerEntity: '2',
      probationaryPeriodDays: '30',
      salaryAmount: '75600.0',
      entityToSignEOR: 'IDEAL-CAREERBRIDGE HOLDINGS(HK) LIMITED',
      pesignationNoticePeriod: '1',
      jobTilte: '职位名称',
      probationaryPeriodNotificationDays: '7',
      workingHours: '40.0',
      email: '<EMAIL>',
      expectedStartDate: '1710547200000',
      JobLevel: '8',
      salaryType: '1',
      employmentType: '1',
      paidAnnualLeaveDays: '7',
      'signingBonus ': '2',
      entityToSignEORStr: 'smartdeer',
      templateStr: '开发',
      organizationStr: 'ICB',
      employeeLastName: 'sunxu',
      annualLeave: '1',
      organization: '17',
      contractTerm: '2',
      JobLevelStr: '部门领导',
      numberOfFormalEmployeeNotificationDays: '30.0',
    },
    system_status: '0',
    id: 1,
    complete: 0,
    status: 1,
    aa: 'aa',
  },
];

export default dataSource;
