import { Drawer as AntdDrawer, Empty, message, Typography } from 'antd';
import React, { useState } from 'react';
import { useDeepCompareEffect } from 'ahooks'

import SchemaPage from '../schema-page';
import { PageColumnsType } from '../schema-page/typing';

import DynamicForm from '../dynamic-form';
// import { PageColumnsType } from '../dynamic-form';

import { ConfigContext } from '../../components/config-provider';
import { getEffectFetchConfig } from '../../utils/getEffectFetchConfig';
import { useParamsDict } from '../../utils/hooks';
import swrFetcher from '../../utils/swrFetcher';
import { transformDataBasedOnRules } from '../../utils/transformDataBasedOnRules';
import Loading from '../loading';
import { CrudTableContext } from './context';
import { useDeepCompareMemo } from '../../utils/hooks'
import { isEmpty } from 'lodash';

const classPrefix = `deer-crud-table-drawer`;

export interface CudTableDrawerProps {
  open: boolean;
  title?: string;
  componentContent?: string | any[] | Record<string, any>;
  componentId?: string | number;
  componentName?: string;
  componentColumns?: PageColumnsType[];
  onCancel: () => void;
  record: Record<string, any>;
  [key: string]: any;
}

type DefaultRenderComponentType = {
  componentName: string;
  componentContent?: string | any[] | Record<string, any>;
  componentColumns: PageColumnsType[];
  onRetry: () => void;
  onClose: (update?: boolean) => void;
  [key: string]: any;
}

const defaultRenderComponent = (props: DefaultRenderComponentType) => {
  const {
    componentName,
    componentContent,
    componentColumns,
    onRetry,
    onClose,

    type,
    conf,
    confObject,
    record
  } = props;

  let childNode: React.ReactNode = null;

  switch (componentName) {
    case 'schemaPage':
      childNode = (
        componentContent ?
          <SchemaPage
            dataSource={componentContent as Record<string, any>}
            columns={componentColumns}
            variables={record}
            onRetry={onRetry}
          /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      );
      break;
    case 'dynamicForm':
      childNode = (
        <DynamicForm
          type={type}
          conf={conf}
          confObject={confObject}
          back={props.back}
          onBack={props.onBack}
          onCrudTableRetry={() => onClose(true)}
          formVariables={record}
        />
      );
      break;

    default:
      childNode = (
        <Typography.Text type="danger">
          CrudTableDrawer 没有实现的组件: {componentName}
        </Typography.Text>
      );
      break;
  }

  return <>{childNode}</>;
};

const CrudTableDrawer: React.FC<CudTableDrawerProps> = (props) => {
  const {
    open,
    title,
    width,
    style,
    componentName,
    componentContent,
    componentColumns,
    record,
    effect,
    onClose,
    onCancel,

    ...restProps
  } = props;

  const configContext = React.useContext(ConfigContext);

  const paramsDict = useParamsDict();

  const crudTableContext = React.useContext(CrudTableContext);

  const [data, setData] = React.useState<any>(null);

  const [isLoading, setIsLoading] = useState(false);

  const isRefreshRef = React.useRef(false);

  const effectFetch = effect?.fetch || {};

  const { transform } = effectFetch;

  const fetch = async () => {
    const mockKey = effectFetch?.functionKey || effectFetch?.processKey

    if (mockKey && crudTableContext?.mock && crudTableContext?.mock[mockKey]) {
      const data = crudTableContext?.mock[mockKey];
      setData(data);
      return;
    }

    setIsLoading(true);

    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(
        effectFetch!,
        configContext,
        { ...record, ...paramsDict },
      );

      const { data } = await swrFetcher(api, method, params);

      let newData = dataIndex ? data[dataIndex] : data;

      if (transform) {
        newData = transformDataBasedOnRules(newData, transform);
      }
      setData(newData);
    } catch (err: any) {
      // message.error(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useDeepCompareEffect(() => {
    if (!isEmpty(effectFetch) && open) {
      fetch();
    }
  }, [effectFetch, open]);

  const handleRetry = () => {
    isRefreshRef.current = true;
    setIsLoading(true);

    setTimeout(() => {
      fetch();
    }, 2000);
  };


  const handleClose = (update?: boolean) => {
    onCancel?.();

    if (update || (isRefreshRef.current && onClose?.type === 'retry')) {
      crudTableContext?.onRefresh();
    }
  };

  const renderedDom = useDeepCompareMemo(() => {
    return defaultRenderComponent({
      componentName: componentName || '',
      componentContent: componentContent || data,
      onRetry: handleRetry,
      onClose: handleClose,
      componentColumns: componentColumns || [],
      record,
      ...restProps
    });
  }, [componentName, componentContent, data, restProps, componentColumns, restProps, handleRetry])

  return (
    <AntdDrawer
      open={open}
      title={title}
      width={width || 525}
      style={style}
      className={classPrefix}
      styles={restProps?.styles}
      onClose={() => handleClose()}
      destroyOnClose
    >
      {isLoading ? <Loading /> : renderedDom}
    </AntdDrawer>
  );
};

export default React.memo(CrudTableDrawer);
