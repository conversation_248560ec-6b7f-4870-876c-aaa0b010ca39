import React, { FC, useEffect, useState } from 'react';
import { Table, TableProps } from 'antd';
import classNames from 'classnames'
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate';
import CudTableModal from './table-modal'
import CrudTableDrawer from './table-drawer'
import CurdTableColumnContent from './table-column-content'
import type { CrudTableProps, CurdTableColumnType } from '.'
import { CrudTableContext } from './context'
import { mergeProps } from '../../utils/withDefaultProps'
import { getDataSourceKey } from './utils'

import './table.less'
import { isString } from 'lodash';
import InnerHtml from '../inner-html';

const classPrefix = `deer-crud-table`

type ModalStateType = {
  open: boolean;
  title?: string;
  componentContent?: string | any[] | Record<string, any>;
  componentId?: string | number;
  componentName?: string;
  record: Record<string, any>;

  [key: string]: any;
}

const defaultProps = {
  columns: [],
  dataSource: [],
  size: 'large',
  pagination: false,
  rowKey: 'id',
  scroll: { x: 768 },
}

const CrudTable: FC<CrudTableProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,
    size,
    scroll,
    rowKey,

    columns,
    dataSource,
    pagination,
    rowSelection,
    sortDirections,
    onRefresh,

    onChange,
    onRowClick,
    onRowDoubleClick,
    onRowContextMenu,
    onRowMouseEnter,
    onRowMouseLeave,

    methods,
    renderComponents,

    mock
  } = props

  const [modalState, setModalState] = React.useState<ModalStateType>({
    open: false,
    record: {}
  })

  const [drawerState, setDrawerState] = React.useState<ModalStateType>({
    open: false,
    record: {}
  })

  const handleShowModal = (column: any, record: any) => {
    const title = replaceVariablesInTemplate(record, column.props.title)

    let componentContent = ''
    if (Array.isArray(column.props.componentContent)) {
      const paths = column.props.componentContent
      let map = JSON.parse(JSON.stringify(record))

      paths.forEach((item: string) => {
        try {
          map = record[item]
        } catch {
          map = {}
        }
      })
      componentContent = map
    } else {
      componentContent = record[column.props.componentContent]
    }

    setModalState({
      ...column.props,
      open: true,
      title: title,
      componentContent: componentContent,
      componentName: column.props.componentName,
      componentId: record[column.props.componentId],
      record: record
    })
  }

  const handleShowDrawer = (column: any, record: any) => {
    const title = replaceVariablesInTemplate(record, column.props.title)

    let componentContent = ''
    if (Array.isArray(column.props.componentContent)) {
      const paths = column.props.componentContent
      let map = JSON.parse(JSON.stringify(record))

      paths.forEach((item: string) => {
        try {
          map = record[item]
        } catch {
          map = {}
        }
      })
      componentContent = map
    } else {
      componentContent = record[column.props.componentContent]
    }

    setDrawerState({
      ...column.props,
      record,
      open: true,
      title: title,
      componentContent: componentContent,
      componentName: column.props.componentName,
      componentId: record[column.props.componentId],
    })
  }

  const handleRefresh = () => {
    onRefresh?.()
  }

  const handleChange = (...args: any) => {
    onChange?.(...args)
  }

  const newColumns: CurdTableColumnType[] = React.useMemo(() => {
    return columns.length !== 0 ? columns : getDataSourceKey(dataSource)
  }, [columns, dataSource])

  const handleCloseDrawer = (refreshTable?: boolean) => {
    setDrawerState({ ...drawerState, open: false })
    if(refreshTable) {
      handleRefresh?.();
    }
  }

  const handleCloseModal = (refreshTable?: boolean) => {
    setModalState({ ...modalState, open: false })
    if(refreshTable) {
      handleRefresh?.();
    }
  }

  return (
    <CrudTableContext.Provider value={{
      onRefresh: handleRefresh,
      onRowClick: onRowClick,
      onShowDrawer: handleShowDrawer,
      onCloseDrawer: handleCloseDrawer,
      onCloseModal: handleCloseModal,
      methods,
      renderComponents,
      mock: mock,
    }}>
      <div
        className={classNames(classPrefix, className)}
        style={{ ...style }}
      >
        <Table
          rowKey={rowKey}
          dataSource={dataSource}
          size={size}
          scroll={scroll}
          pagination={pagination}
          rowSelection={rowSelection}
          sortDirections={sortDirections}
          onChange={handleChange}
          onRow={(record: Record<string, any>, index: number | undefined) => {
            return {
              onClick: (e: React.MouseEvent) => {
                e.stopPropagation()
                onRowClick?.(record, index)
              },
              onDoubleClick: (e: React.MouseEvent) => {
                e.stopPropagation()
                onRowDoubleClick?.(record, index)
              },
              onContextMenu: (e: React.MouseEvent) => {
                e.stopPropagation()
                console.log(12)
                onRowContextMenu?.(record, index)
              },
              onMouseEnter: (e: React.MouseEvent) => {
                e.stopPropagation()
                onRowMouseEnter?.(record, index)
              },
              onMouseLeave: (e: React.MouseEvent) => {
                e.stopPropagation()
                onRowMouseLeave?.(record, index)
              },
            };
          }}
        >
          {newColumns.map((column) => {
            let dataIndex = column.dataIndex || column.key
            dataIndex = isString(dataIndex) ?
              dataIndex.includes(',') ? dataIndex.split(',') :
                dataIndex : dataIndex.join(',');

            let key = column.key
            key = isString(key) ? key : key.join(',');

            const title = <InnerHtml content={column.title} />

            return (
              <Table.Column
                title={title}
                key={key}
                dataIndex={dataIndex}
                fixed={column?.fixed ? column?.fixed : false}
                width={column.width}
                sorter={column.sorter}
                defaultSortOrder={column.defaultSortOrder}
                render={(text: any, record: any, index: number) => {
                  return <CurdTableColumnContent
                    column={column}
                    text={text}
                    index={index}
                    record={record}
                    onShowModal={handleShowModal}
                    onShowDrawer={handleShowDrawer}
                  />
                }}
              />
            )
          })}
        </Table>

        {modalState.open && (
          <CudTableModal
            {...modalState}
            onCancel={() => {
              setModalState({ ...modalState, open: false })
            }}
          />
        )}

        {drawerState.open && (
          <CrudTableDrawer
            {...drawerState}
            onCancel={() => {
              setDrawerState({ ...drawerState, open: false })
            }}
          />
        )}
      </div>
    </CrudTableContext.Provider>
  )
}

export default CrudTable
