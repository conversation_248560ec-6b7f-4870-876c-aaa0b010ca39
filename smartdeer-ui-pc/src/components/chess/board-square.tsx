import React from 'react'
import Square from './square'
import { canMoveKnight, moveKnight } from './game'
import { ItemTypes } from './consts'
import { useDrop } from 'react-dnd'
import { Drop } from '../drag-and-drop'

interface BoardSquareProps {
  x: number;
  y: number;
  children: React.ReactNode;
}

const Overlay = ({ color }: { color: string }) => {
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        height: '100%',
        width: '100%',
        zIndex: 1,
        opacity: 0.5,
        backgroundColor: color,
      }}
    />
  )
}

const BoardSquare: React.FC<BoardSquareProps> = (props) => {
  const { x, y, children } = props

  const black = (x + y) % 2 === 1

  return (
    <Drop
      accept={ItemTypes.KNIGHT}
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
      }}
      onMove={() => moveKnight(x, y)}
      canMove={() => canMoveKnight(x, y)}
    >
      {({ isOver, canDrop }) => {
        return (
          <React.Fragment>
            <Square black={black}>
              {children}
            </Square>

            {(isOver && !canDrop) && <Overlay color='red' />}
            {!isOver && canDrop && <Overlay color='yellow' />}
            {isOver && canDrop && <Overlay color='green' />}
          </React.Fragment>
        )
      }}
    </Drop>
  )
}

export default BoardSquare
