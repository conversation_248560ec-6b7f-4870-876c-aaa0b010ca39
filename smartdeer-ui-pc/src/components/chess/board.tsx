import React from 'react'
import Knight from './knight'
import { moveKnight, canMoveKnight } from './game'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import BoardSquare from './board-square'


const handleSquareClick = (toX: number, toY: number) => {
  if (canMoveKnight(toX, toY)) {
    moveKnight(toX, toY)
  }
}

const renderPiece = (x: number, y: number, [knightX, knightY]: number[]) => {
  if (x === knightX && y === knightY) {
    return <Knight />
  }
}

const renderSquare = (i: number, knightPosition: number[]) => {
  const x = i % 8
  const y = Math.floor(i / 8)

  return (
    <div
      key={i}
      style={{ width: '12.5%', height: '12.5%' }}
    >
      <BoardSquare x={x} y={y}>
        {renderPiece(x, y, knightPosition)}
      </BoardSquare>
    </div>
  )
}

interface BoardProps {
  knightPosition: number[]
}

const Board: React.FC<BoardProps> = (props) => {
  const { knightPosition } = props

  const squares = []

  for (let i = 0; i < 64; i++) {
    squares.push(renderSquare(i, knightPosition))
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          flexWrap: 'wrap'
        }}
      >
        {squares}
      </div>
    </DndProvider>

  )
}

export default Board
