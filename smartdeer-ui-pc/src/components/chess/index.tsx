import React from 'react';
import Board from './board'
import { observe } from './game'


const Chess = () => {

  const [knightPosition, setKnightPosition] = React.useState([7, 4])

  React.useEffect(() => {
    observe((knightPosition: number[]) =>
      setKnightPosition(knightPosition)
    )
  }, [])

  return (
    <div style={{ width: '400px', height: '400px', border: '1px solid #e4e9ec' }}>
      <Board knightPosition={knightPosition} />
    </div>
  );
}

export default Chess;

