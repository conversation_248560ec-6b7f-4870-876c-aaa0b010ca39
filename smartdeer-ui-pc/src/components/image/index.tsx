import React from 'react'
import { Image as AntdImage, Skeleton } from 'antd'
import type { ImageProps } from 'antd'
import classNames from 'classnames'
import { ConfigContext } from '../config-provider'
import { mergeProps } from '../../utils/withDefaultProps'
import { useFileAccessToken } from '../../utils/hooks'
// import { updateImageLink } from '../../utils/updateImageLinks'
import { isImageUrl } from '../../utils/isImageUrl'
import { FileOutlined, EyeOutlined } from '@ant-design/icons';

import './index.less'

export const classPrefix = `deer-image`;

export {
  ImageProps
}

const defaultProps = {
  preview: false,
  type: 'default'
}

const Image: React.FC<ImageProps> = (p) => {
  const props = mergeProps(defaultProps, p)


  const [isLoading, setIsLoading] = React.useState(true)

  const {
    className,
    preview,
    src,
    ...restProps
  } = props

  const [fileUrl, setFileUrl] = React.useState<undefined | string>()

  const { isFileToken } = React.useContext(ConfigContext)

  const { getTheCompleteFileUrl } = useFileAccessToken()

  const initFileUrl = async () => {
    if (!src) return

    if (!isFileToken) {
      setFileUrl(src)
      setIsLoading(false)
      return
    }

    const newSrc = await getTheCompleteFileUrl(src)

    setFileUrl(newSrc)
    setIsLoading(false)
  }

  React.useEffect(() => {
    initFileUrl()
  }, [src])

  if (isLoading) return <div className={classPrefix}><Skeleton.Image active /></div>

  const isImage = isImageUrl(fileUrl)

  return (
    <div className={classPrefix}>
      {isImage ? (
        <AntdImage
          className={classNames(classPrefix, className)}
          {...restProps}
          preview={preview && {
            mask: <EyeOutlined />,
          }}
          src={fileUrl}
        />
      ) : (
        <FileOutlined style={{ fontSize: '48px', color: '#08c' }} />
      )}
    </div>
  )
}

export default Image
