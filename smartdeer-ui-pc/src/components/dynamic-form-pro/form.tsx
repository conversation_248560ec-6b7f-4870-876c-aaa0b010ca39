import React, { useState } from 'react'
import classNames from 'classnames'
import { message } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons'
import Loading from '../loading'
import { ConfigContext } from '../config-provider'
import swrFetcher from '../../utils/swrFetcher'
import type { SchemaFormProProps, SchemaFormProRef } from '../schema-form-pro'
import SchemaFormPro from '../schema-form-pro'
import { getEffectFetchConfig } from '../../utils/getEffectFetchConfig'
import { useParamsDict, useSuccess } from '../../utils/hooks'
import type { FetchType, OnSuccessType } from '../../typing'
import type { DynamicFormConfigType } from './typing'
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate'
import { replaceReferences } from '../../utils/replaceReferences'
import { isArray, isBoolean } from 'lodash'
import { transformDataBasedOnRules } from '../../utils/transformDataBasedOnRules'
import { omitUndefined } from '../../utils/omitUndefined'
import { evaluateLogicalExpression } from '../../utils/evaluateLogicalExpression'

import './form.less'

export interface DynamicFormProProps {
  className?: string
  style?: React.CSSProperties

  conf?: {
    language?: string;
    version?: string;
    formKey: string;
  }
  confObject?: DynamicFormConfigType
  type?: 'add' | 'edit'

  transformFinishValues?: any;
  transformInitialValues?: any;

  variables?: Record<string, any>

  onBeforeCreate?: (values: Record<string, any>) => Promise<Record<string, any>>
}

const classPrefix = `deer-dynamic-form-pro`

const DynamicFormPro: React.FC<DynamicFormProProps> = (props) => {
  const {
    className,
    style,
    conf,
    confObject,
    onBeforeCreate,
    variables,
    ...restProps
  } = props

  const configContext = React.useContext(ConfigContext)
  const language = configContext.language.dynamicForm

  const formRef = React.useRef<SchemaFormProRef>(null)

  const paramsDict = useParamsDict(variables)

  const handleReset = () => {
    formRef.current?.resetFields()
  }

  const handleRetry = () => {
    configContext.router?.go(0)
  }

  const onSuccess = useSuccess({
    onReset: handleReset,
    onRetry: handleRetry,
  })

  const [isLoading, setIsLoading] = React.useState(true)

  const [schemaFormColumns, setSchemaFormColumns] = React.useState<SchemaFormProProps['columns']>([])
  const [initialValues, setInitialValues] = React.useState({})

  const [submitLoading, setSubmitLoading] = React.useState(false)

  const formConfRef = React.useRef<DynamicFormConfigType>()

  const formConfGetRef = React.useRef<DynamicFormConfigType['onGet']>()

  const formConfBeforeCreateRef = React.useRef<DynamicFormConfigType['onBeforeCreate']>()
  const formConfCreateRef = React.useRef<DynamicFormConfigType['onCreate']>()
  const formConfAfterCreateRef = React.useRef<DynamicFormConfigType['onAfterCreate']>()

  const formConfBeforeUpdateRef = React.useRef<DynamicFormConfigType['onBeforeUpdate']>()
  const formConfUpdateRef = React.useRef<DynamicFormConfigType['onUpdate']>()
  const formConfAfterUpdateRef = React.useRef<DynamicFormConfigType['onAfterUpdate']>()

  const formConfSuccessRef = React.useRef<DynamicFormConfigType['onSuccess']>()

  const [formProps, setFormProps] = useState<Record<string, any>>()

  const isHandleAdd = restProps.type === 'add'

  const getFormConf = async () => {
    if (confObject) {
      return confObject
    }

    if (!conf) {
      message.error(`[smartdeer-ui: DynamicForm Form Conf 配置错误！`)
    }

    try {
      const api = replaceVariablesInTemplate(paramsDict, configContext.appFunctionApi!)

      const { data } = await swrFetcher(api!, 'POST', {
        functionKey: configContext.formatterFormByKey,
        params: {
          formKey: conf?.formKey,
          language: conf?.language || 'gl',
          version: conf?.version || '1',
        }
      })

      const { page, ...rest } = JSON.parse(data.rs)

      const newPage = replaceReferences(page, rest)

      return newPage

    } catch (err) {
      message.error(`[smartdeer-ui: DynamicForm Form Conf 配置错误！`)
    }
  }

  const getFormInitialValues = async (fetchConfig?: FetchType) => {
    if (isHandleAdd) {
      return {}
    }

    if (!fetchConfig) {
      message.error(`[smartdeer-ui: DynamicFormPro] onGet 解析错误！`)
      return
    }

    const { api, method, params, dataIndex } = getEffectFetchConfig(fetchConfig, configContext, paramsDict)

    const { data } = await swrFetcher(api, method, params)

    return dataIndex ? data[dataIndex] : data
  }

  const fetch = async () => {
    setIsLoading(true)

    if (!formConfRef.current) {
      formConfRef.current = await getFormConf()
    }

    if (!formConfRef.current) {
      return
    }

    const columns = formConfRef.current?.form?.columns

    setSchemaFormColumns(columns || [])
    setFormProps(formConfRef.current?.form)

    // formConfGetRef.current = formConfRef.current?.onGet
    // formConfBeforeCreateRef.current = formConfRef.current?.onBeforeCreate
    // formConfCreateRef.current = formConfRef.current?.onCreate
    // formConfAfterCreateRef.current = formConfRef.current?.onAfterCreate

    // formConfUpdateRef.current = formConfRef.current?.onUpdate
    // formConfSuccessRef.current = formConfRef.current?.onSuccess

    // const formInitialValues = await getFormInitialValues(formConfGetRef.current)

    // setInitialValues({
    //   ...formConfRef.current?.form?.initialValues,
    //   ...formInitialValues,
    // })

    setIsLoading(false)
  }

  React.useEffect(() => {
    fetch()
  }, [])

  const handleBeforeCreate = async (conf: FetchType, values: Record<string, any>) => {
    if (!conf) {
      message.error(`[smartdeer-ui: DynamicFormPro] formConfBeforeCreateRef 解析错误！`)
      return
    }

    if (conf.if) {
      const is = evaluateLogicalExpression({ ...paramsDict, ...values }, conf.if)

      if (!is) return
    }

    const { api, method, params, dataIndex } = getEffectFetchConfig(conf, configContext, { ...paramsDict, ...values }, {})

    const { data } = await swrFetcher(api, method, params)

    let result

    if (dataIndex) {
      result = data[dataIndex]
    } else {
      result = data
    }

    try {
      result = JSON.parse(result);
    } catch (e) {
    }

    if (conf.transform) {
      result = transformDataBasedOnRules(result, conf.transform);
    }

    return result;
  }

  const handleCreate = async (values: Record<string, any>) => {
    if (!formConfCreateRef.current) {
      message.error(`[smartdeer-ui: DynamicFormPro] onCreate 解析错误！`)
      return
    }

    const { api, method, params } = getEffectFetchConfig(formConfCreateRef.current, configContext, paramsDict, values)

    const res = await swrFetcher(api, method, params)

    if (formConfAfterCreateRef.current) {
      return res.data
    }

    if (formConfSuccessRef.current) {
      await onSuccess(formConfSuccessRef.current, res.data)
    } else {
      message.success(language.messageCreateSuccess)
    }
  }

  // const handleAfterCreate = async (values: Record<string, any>) => {
  //   if (!formConfAfterCreateRef.current) {
  //     return
  //   }

  //   let newValues = values

  //   if (formConfAfterCreateRef.current?.transformParams) {
  //     newValues = transformDataBasedOnRules(newValues, formConfAfterCreateRef.current.transformParams)
  //   }

  //   const { api, method, params } = getEffectFetchConfig(formConfAfterCreateRef.current, configContext, paramsDict, newValues)

  //   const res = await swrFetcher(api, method, params)
  //   if (formConfSuccessRef.current) {
  //     await onSuccess(formConfSuccessRef.current, res.data)
  //   } else {
  //     message.success(language.messageCreateSuccess)
  //   }
  // }

  const handleUpdate = async (values: Record<string, any>) => {
    if (!formConfUpdateRef.current) {
      message.error(`[smartdeer-ui: DynamicFormPro] onUpdate 解析错误！`)
      return
    }

    const { api, method, params } = getEffectFetchConfig(formConfUpdateRef.current, configContext, paramsDict, values)

    await swrFetcher(api, method, params)

    if (formConfSuccessRef.current) {
      onSuccess(formConfSuccessRef.current)
    } else {
      message.success(language.messageUpdateSuccess)
    }
  }

  const onFinish = async (values: Record<string, any>) => {

    setSubmitLoading(true)

    try {

      let newValues = omitUndefined(values)

      if (formConfBeforeCreateRef.current) {
        const formBeforeCreateList = isArray(formConfBeforeCreateRef.current) ? formConfBeforeCreateRef.current : [formConfBeforeCreateRef.current]

        const promiseList = formBeforeCreateList.map(item => handleBeforeCreate(item, newValues))

        const extraValues = await Promise.all(promiseList)

        try {
          extraValues.filter(item => { return !!item }).forEach((item) => {
            newValues = { ...item, ...newValues }
          })
        } catch (err) {
          console.error(err)
        }
      }

      if (onBeforeCreate) {
        newValues = await onBeforeCreate(newValues)
      }


      newValues = omitUndefined(newValues)

      // console.log('newValues', newValues)

      // return

      if (isHandleAdd) {
        const res = await handleCreate(newValues as Record<string, any>)

        // handleAfterCreate({ ...newValues, ...res })
      } else {
        await handleUpdate(newValues as Record<string, any>)
      }

    } catch (err: any) {
      message.error((err as Error).message)
    }

    setSubmitLoading(false)
  }

  if (isLoading) {
    return <Loading />
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <div className={`${classPrefix}-content`}>
        <SchemaFormPro
          ref={formRef}
          {...formProps}
          columns={schemaFormColumns}
          initialValues={initialValues}
          submitLoading={submitLoading}
          onFinish={onFinish}
          variables={variables}
        />
      </div>
    </div>
  )
}

export default DynamicFormPro
