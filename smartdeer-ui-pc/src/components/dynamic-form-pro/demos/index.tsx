import React from 'react';
import { DynamicForm } from '@smartdeer-ui/pc'

const confObject = {
  "onGet": {
    "type": "function",
    "functionKey": "x_sage_get",
    "defaultParams": {
      "id": "*{id}"
    }
  },
  "onUpdate": {
    "type": "function",
    "functionKey": "x_sage_update",
    "paramsInjectionLocation": [
      "params"
    ],
    "defaultParams": {
      "id": "*{id}"
    }
  },
  "onCreate": {
    "type": "function",
    "functionKey": "x_sage_add_role",
    "paramsInjectionLocation": [
      "params"
    ],
    "defaultParams": {}
  },
  "form": {
    "size": "large",
    "supplementingTheString": false,
    "layout": "vertical",
    "gridProps": {
      "col": "2"
    },
    "transformFinishValues": [
      {
        "from": "questions",
        "to": "questions",
        "format": "arrayToString"
      },
    ],
    "columns": [
      {
        "type": "transfer",
        "field": "questions",
        "title": "权限列表",
        "col": "2",
        "props": {
          "columns": [
            {
              dataIndex: 'label',
              title: '题目',
            },
            {
              dataIndex: 'description',
              title: '答案',
            },
            {
              dataIndex: 'extra',
              title: '额外的',
            },
          ],

          "options": [
            {
              "value": "1",
              "label": "这是问题1",
              "description": "这是答案1",
              "extra": "额外的1"
            },
            {
              "value": "2",
              "label": "这是问题2",
              "description": "这是答案2",
              "extra": "额外的2"
            }
          ],

          "fieldNames": {
            "value": "name",
            "label": "name",
            "description": "description",
            "extra": "extra"
          }
        },
        // "effect": {
        //   "fetch": {
        //     "type": "function",
        //     "functionKey": "x_sage_list_permissions",
        //     "defaultParams": {
        //       "entityId": "17"
        //     },
        //     "data": "dataInfo"
        //   }
        // }
      }
    ]
  }
}

export default () => {

  return (
    <DynamicForm
      type={'add'}
      confObject={confObject as any}
    />
  )
}
