import React from 'react';
import classNames from 'classnames';
import { JSONEditor, Mode } from 'vanilla-jsoneditor';

const classPrefix = `deer-vanilla-jsoneditor`

interface VanillaJsoneditorProps {
  className?: string;
  style?: React.CSSProperties;

  value: string;
  onChange?: (value: string) => void
  readOnly?: boolean
}

const VanillaJsoneditor: React.FC<VanillaJsoneditorProps> = (props) => {
  const {
    className,
    style,

    value,
    onChange: setValue,
    readOnly,
  } = props

  const containerRef = React.useRef<any>(null)
  const editorRef = React.useRef<any>(null)

  React.useEffect(() => {
    if (!containerRef.current || editorRef.current) return

    editorRef.current = new JSONEditor({
      target: containerRef.current,
      props: {
        // content: {
        //   text: value
        // },
        readOnly,
        mode: Mode.text,
        mainMenuBar: false,
        navigationBar: false,
        statusBar: false,
        indentation: 4,
        onChange: (updatedContent: any, previousContent, { contentErrors, patchResult }) => {
          setValue?.(updatedContent.text)
        }
      },
    });

    return () => {
      if (editorRef.current) {
        // editorRef.current.destroy();
        // editorRef.current = null;
      }
    };
  }, [containerRef.current]);

  React.useEffect(() => {
    if (!editorRef.current) return

    editorRef.current.update({
      text: value,
    });
  }, [value]);

  return (
    <div
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      ref={containerRef}
    />
  )
};

export default VanillaJsoneditor
