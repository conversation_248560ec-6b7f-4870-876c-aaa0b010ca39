import React from 'react';
import { Flex, Modal } from 'antd';
import { DeleteOutlined, ImportOutlined, FullscreenOutlined, FullscreenExitOutlined, ExportOutlined } from '@ant-design/icons';
import VanillaJsoneditor from './vanilla-jsoneditor'
import { ConfigContext } from '../config-provider'
import Button from '../button'

import './index.less'
import classNames from 'classnames';

const classPrefix = `deer-json-editor`

export interface JsonEditorProps {
  className?: string;
  style?: React.CSSProperties;

  value?: string;
  showTool?: boolean;
  onChange?: (value: string) => void
}

const defaultValue = JSON.stringify({ 'key': 'value' }, null, 4)

const JsonEditor: React.FC<JsonEditorProps> = (props) => {
  const {
    className,
    style,
    showTool = true,
    onChange,

    ...restProps
  } = props

  const { language } = React.useContext(ConfigContext)

  const [value, setValue] = React.useState(restProps.value || '')

  const [isModalOpen, setIsModalOpen] = React.useState(false)

  React.useEffect(() => {
    if ('value' in restProps) {
      setValue(restProps.value || '');
    }
  }, [restProps.value])

  const handleChange = (value: string) => {
    setValue(value)
    onChange?.(value)
  }

  const handleClickClear = () => {
    setValue('')
    onChange?.('')
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {showTool && <div className={`${classPrefix}-tool`}>
        <Flex gap='middle' justify='flex-end'>
          <Button
            icon={<FullscreenOutlined />}
            size='small'
            onClick={() => setIsModalOpen(true)}
          >{language.jsonEditor.fullScreen}</Button>

          <Button.ImportJson icon={<ImportOutlined />} size='small' type='primary' onCallback={handleChange}>{language.jsonEditor.import}</Button.ImportJson>
          <Button.ExportJson json={value} icon={<ExportOutlined />} type='primary' size='small'>{language.jsonEditor.export}</Button.ExportJson>
          <Button onClick={handleClickClear} icon={<DeleteOutlined />} danger type='primary' size='small'>{language.jsonEditor.clear}</Button>
        </Flex>
      </div>}

      <VanillaJsoneditor className={`${classPrefix}-content`} value={value} onChange={handleChange} readOnly={restProps.readOnly} />

      <Modal
        wrapClassName={`${classPrefix}-modal`}
        open={isModalOpen}
        width='100vw'
        style={{ top: 0, padding: 0 }}
        closeIcon={< FullscreenExitOutlined />}
        closable={false}
        keyboard={true}
        footer={() => null}
        onCancel={() => setIsModalOpen(false)}
      >
        <div className={`${classPrefix}-full`} style={{ height: '100%' }}>

          <Flex className={`${classPrefix}-tool`} gap='middle' justify='flex-end'>
            <Button
              icon={<FullscreenExitOutlined />}
              size='small'
              onClick={() => setIsModalOpen(false)}
            >{language.jsonEditor.retract}</Button>
            <Button.ImportJson icon={<ImportOutlined />} size='small' type='primary' onCallback={handleChange}>{language.jsonEditor.import}</Button.ImportJson>
            <Button.ExportJson json={value} icon={<ExportOutlined />} type='primary' size='small'>{language.jsonEditor.export}</Button.ExportJson>
            <Button onClick={() => handleClickClear()} icon={<DeleteOutlined />} danger type='primary' size='small'>{language.jsonEditor.clear}</Button>
          </Flex>

          <div className={`${classPrefix}-content`}>
            <VanillaJsoneditor className={`${classPrefix}-content-internal`} value={value} onChange={handleChange} />
          </div>
        </div>
      </Modal>
    </div>
  )
};

export default JsonEditor
