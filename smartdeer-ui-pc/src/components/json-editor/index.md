---
toc: content
group: 
  title: 数据录入
  order: 10
---

# JsonEditor 编辑

通过鼠标或键盘输入内容，是最基础的表单域的包装。

## 示例

<code src="./demos/index.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| value | 输入值 | `string` | `--` | |
| onChange | 输入框内容变化时的回调 | `(value: string) => void` | `--` |  |
