.deer-json-editor {
  display: flex;
  flex-direction: column;

  &-content{
    flex-grow: 1;
    margin-top: 8px;
    overflow-y: scroll;
    border-radius: 8px;
    border: 1px solid #BFBFBF;

    .jse-text-mode, .jse-contents {
      border: 0 none !important;
    }

    // div {
    //   border: 0 none !important;
    // }

    &-internal{
      min-height: 100%;
      height: 0;
      overflow: auto;
    }
  }

  &-modal {

    .ant-modal {
      max-width: 100vw;

      .ant-modal-content {
        border-radius: 0;
        height: 100vh;

        .ant-modal-body {
          height: 100%;
        }
      }
    }
  }

  &-full {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
