import React from 'react'
import classNames from 'classnames';
import { Flex, Input, Button } from 'antd'
import { FileImageOutlined, CloseOutlined } from '@ant-design/icons';
import { ConfigContext } from '../config-provider'
import UploadImg from '../common/upload-img'
import type { UploadImgRef } from '../common/upload-img'

import { mergeProps } from '../../utils/withDefaultProps'

import './index.less'

const classPrefix = `deer-comment-editor`;

export type MessageType = {
  text: string;
  imgs: string[]
}

export interface CommentEditorProps {
  className?: string;
  style?: React.CSSProperties;
  maxLength?: number;
  uploadImgApi?: string;
  maxImgCount?: number;
  onSend?: (message: MessageType) => void;
}

const defaultProps = {
  maxLength: 500,
  maxImgCount: 1
}

const CommentEditor: React.FC<CommentEditorProps> = (p) => {
  const props = mergeProps(defaultProps, p)
  const {
    className,
    style,
    maxLength,
    uploadImgApi,
    maxImgCount,
    onSend
  } = props

  const { language } = React.useContext(ConfigContext)

  const [text, setText] = React.useState('')
  const [imgs, setImgs] = React.useState<string[]>([])

  const uploadRef = React.useRef<UploadImgRef>(null);

  const handleClickImageBtn = () => {
    if (!uploadRef.current) {
      return
    }

    if (imgs.length === maxImgCount && maxImgCount !== 1) return

    uploadRef.current.click()
  }

  const handleUploadImgCallback = (url: string) => {
    let list = []
    if (maxImgCount === 1) {
      list = [url]
    } else {
      list = [...imgs, url]
    }

    setImgs(list)
  }

  const handleDeleteImg = (index: number) => {
    const list = [...imgs]
    list.splice(index, 1)
    setImgs(list)
  }

  const handleClickSubmit = () => {
    onSend?.({
      text,
      imgs,
    })
  }

  const isDubmitDisabled = text.length === 0

  const textCountReal = text.length

  const submitText = language.commentEditor.submit

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <div className={`${classPrefix}-input`}>
        <Input.TextArea
          value={text}
          maxLength={maxLength}
          autoSize={{ minRows: 4 }}
          onChange={(e) => setText(e.target.value)}
        />
      </div>

      <div className={`${classPrefix}-image-preview-list`}>
        {imgs.map((item, index) => {
          return (
            <div
              key={item}
              className={`${classPrefix}-image-preview`}
              style={{
                backgroundImage: `url(${item})`
              }}
            >
              <div
                className={`${classPrefix}-image-preview-clean-btn`}
                onClick={() => handleDeleteImg(index)}
              >
                <CloseOutlined style={{ fontSize: '10px' }} />
              </div>
            </div>
          )
        })}

      </div>

      <div className={`${classPrefix}-action-box`}>
        <div
          className={`${classPrefix}-image-btn`}
          onClick={handleClickImageBtn}
        >
          <FileImageOutlined style={{ fontSize: '18px' }} />
        </div>

        <Flex align='center' gap={12}>
          <div>
            <span className='text-count-real'>{textCountReal}</span>
            <span className='text-count-limit'> / {maxLength}</span>
          </div>
          <Button
            disabled={isDubmitDisabled}
            type='primary'
            onClick={handleClickSubmit}
          >
            {submitText}
          </Button>
        </Flex>
      </div>

      <div style={{ display: 'none' }}>
        <UploadImg
          ref={uploadRef}
          api={uploadImgApi}
          onSuccess={handleUploadImgCallback}
        />
      </div>
    </div>
  )
}

export default CommentEditor
