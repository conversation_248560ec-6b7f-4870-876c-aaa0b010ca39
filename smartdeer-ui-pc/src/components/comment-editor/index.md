---
toc: content
group: 
  title: 数据录入
  order: 10
---

# CommentEditor 评论编辑器

评论编辑器

## 示例

<code src="./demos/index.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| maxLength | 最大长度		 | `number` | `500` |  |
| uploadImgApi | 上传图片地址	 | `string` | `--` |  |
| maxImgCount | 限制上传数量。当为 1 时，始终用最新上传的文件代替当前文件	 | `number` | `1` |  |
| onSend | 发送事件	 | `({ text: string, imgs: string[]}) => void` | `--` |  |

