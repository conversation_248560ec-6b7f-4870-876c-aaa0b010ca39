.deer-comment-editor {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  transition: all 0.3s,height 0s;

  &:hover {
    border-color: var(--icb-color-t1);
    box-shadow: 0 0 0 2px rgba(255, 175, 5, 0.1);
    outline: 0;
    background-color: #ffffff;
  }

  .ant-input {
    border: none !important;
    box-shadow: none !important;
  }

  &-input{
    padding: 4px 1px;
  }

  &-image-preview-list {
    padding: 0 12px;
    display: flex;
    flex-wrap: wrap;
    margin-left: -8px;
  }

  &-image-preview{
    min-width: 64px;
    width: 64px;
    height: 64px;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 4px;
    border: 1px solid rgb(228 230 235 / 50%);
    position: relative;
    margin-left: 8px;
    margin-bottom: 8px;

    &-clean-btn {
      background: #E4E6EB;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #515767;
      position: absolute;
      right: 5px;
      top: 5px;
    }
  }

  &-action-box{
    height: 48px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;

    .text-count-real {
      color: #515767;
      font-size: 14px;
    }

    .text-count-limit {
      font-size: 14px;
      color: #8a919f;
    }
  }

  &-image-btn{
    cursor: pointer;
    color: #8a919f;
    transition: all 0.3s;

    &:hover {
      color: var(--icb-color-t1);
      box-shadow: 0 0 0 2px rgba(255, 175, 5, 0.1);
    }
  }
}
