import React from 'react'
import classNames from 'classnames';
import { Spin } from 'antd';
import type { SpinProps } from 'antd';

import './index.less'

export interface LoadingProps extends SpinProps {
  className?: string;
}

const classPrefix = `deer-loading`

const Loading: React.FC<LoadingProps> = (props) => {
  const { className, ...restProps } = props

  return (
    <Spin size='large' {...restProps}>
      <div className={classNames(classPrefix, className)} />
    </Spin>
  )
}

export default Loading
