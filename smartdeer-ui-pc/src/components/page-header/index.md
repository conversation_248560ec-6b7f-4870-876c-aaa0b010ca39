---
toc: content
group: 
  title: 导航
  order: 2
---

# PageHeader 页头

如果页面的路径比较简单，推荐使用页头组件而非面包屑组件。

## 示例

### 标准的

<code src="./demos/index.tsx"></code>

## 面包屑

<code src="./demos/breadcrumb.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| title | 标题 | `string` | `--` | |
| content | 内容 | `ReactNode` | `--` | |
| description | 描述 | `ReactNode` | `--` | |
| suffix | 后缀 | `ReactNode` | `--` | |
| onBack | 点击左侧区域触发	| `() => void` | `--` | |
