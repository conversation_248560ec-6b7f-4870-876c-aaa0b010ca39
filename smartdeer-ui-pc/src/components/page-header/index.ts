import type { PageHeaderProps } from './page-header';
import InternalPageHeader from './page-header';

// export type { CheckListProps, CheckListValueType } from './check-list';

export type { PageHeaderProps };

export const classPrefix = `deer-page-header`;

type CompoundedComponent = React.FC<PageHeaderProps> & {
  // ImportJson: typeof ImportJson;
  // ExportJson: typeof ExportJson;
};

const PageHeader = InternalPageHeader as CompoundedComponent;

// Button.ImportJson = ImportJson;
// Button.ExportJson = ExportJson;

export default PageHeader;
