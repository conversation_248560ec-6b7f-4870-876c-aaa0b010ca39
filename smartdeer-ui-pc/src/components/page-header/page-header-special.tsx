import React, { FC } from 'react'
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Space, Flex } from 'antd';
import classNames from 'classnames'
import { classPrefix } from '.'

import './page-header.less'

export type PageHeaderProps = {
  className?: string;
  style?: React.CSSProperties;

  onBack?: () => void;
  title?: React.ReactNode;
  description?: React.ReactNode;
  suffix?: React.ReactNode;
  children?: React.ReactNode;
}

const PageHeader: FC<PageHeaderProps> = (props) => {

  const { className, style, onBack, title, suffix, description, children } = props

  return (
    <div
      className={classNames(classPrefix, className)}
      style={{ ...style }}
    >
      <Flex justify='space-between' align='center'>
        <a
          className={`${classPrefix}-back`}
          onClick={() => onBack?.()}
        >
          <Space>
            <ArrowLeftOutlined />

            <span>返回</span>
          </Space>
        </a>

        <div className={`${classPrefix}-title`}>
          {title}
        </div>

        <div className={`${classPrefix}-suffix`}>
          {suffix}
        </div>
      </Flex>

      {!!description && (
        <div className={`${classPrefix}-description`}>
          {description}
        </div>
      )}

      {children}
    </div>
  )
}

export default PageHeader
