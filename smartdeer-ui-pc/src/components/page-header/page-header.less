.deer-page-header {

  &-back{
    cursor: pointer;
    transition: all 0.3s;
    padding: 0 5px;
    margin-left: -5px;
    font-size: 20px;

    &:hover {
      color: var(--icb-color-t1);
    }
  }

  &-title {
    font-size: 20px;
    font-weight: bold;
  }

  &-suffix {
    font-size: 12px;
  }

  &-description {
    margin-top: 16px;
    font-size: 12px;
  }

  &-pointer {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: var(--icb-color-t1);
    }
  }

  &-separator {
    color: #a8abb2;
    margin: 0 9px;
    font-weight: 700;
    font-size: 14px;
  }
}
