import React, { FC } from 'react'
import Icon from '../icon';
import { Space, Flex } from 'antd';
import classNames from 'classnames'
import { classPrefix } from '.'
import { isArray, isObject } from 'lodash'
import { ConfigContext } from '../config-provider'

import './page-header.less'

export type PageHeaderProps = {
  className?: string;
  style?: React.CSSProperties;

  back?: boolean;
  onBack?: () => void;
  title?: React.ReactNode | string[] | { path?: string, name: string }[];
  content?: React.ReactNode;
  description?: React.ReactNode;
  suffix?: React.ReactNode;
  children?: React.ReactNode;
}

const PageHeader: FC<PageHeaderProps> = (props) => {

  const {
    className,
    style,
    back,
    onBack,
    title,
    content,
    suffix,
    description,
    children
  } = props

  const { router } = React.useContext(ConfigContext)

  const handleClickBack = () => {
    if (onBack) {
      onBack()
      return
    }
    router?.go(-1)
  }

  const handleClickItem = (delta: number, path?: string,) => {
    if (!path) {
      router?.go(delta)
      return
    }

    router?.push(path)
  }

  const showBack = back || !!onBack

  return (
    <div
      className={classNames(classPrefix, className)}
      style={{ ...style }}
    >
      <Flex justify='space-between' align='center'>
        <Space className={`${classPrefix}-left`} align='center'>
          {showBack && (
            <div className={`${classPrefix}-back`} onClick={handleClickBack}>
              <Icon.ArrowLeftOutline />
            </div>
          )}

          {!!title && (
            <div className={`${classPrefix}-title`}>
              {isArray(title) ? (
                <>
                  {title.map((item, index) => {
                    const itemStyle = index === title.length - 1 ? {
                      fontSize: '14px',
                      color: '#606266'
                    } : {
                      fontSize: '20px'
                    }

                    const name = isObject(item) ? item.name : item
                    const path = isObject(item) ? item.path : ''

                    return (
                      <React.Fragment key={index}>
                        {
                          title.length - 1 !== index ? (<>
                            <span
                              style={itemStyle}
                              className={`${classPrefix}-pointer`}
                              onClick={() => handleClickItem(index - (title.length - 1), path)}
                            >
                              {name}
                            </span>
                            <span className={`${classPrefix}-separator`} >/</span>
                          </>) : (<>
                            <span style={itemStyle}>
                              {name}
                            </span>
                          </>)
                        }
                      </React.Fragment>
                    )
                  })}
                </>
              ) : title}
            </div>
          )}

          {!!content && (
            <div className={`${classPrefix}-content`}>
              {content}
            </div>
          )}
        </Space>

        <div className={`${classPrefix}-suffix`}>
          {suffix}
        </div>
      </Flex>

      {!!description && (
        <div className={`${classPrefix}-description`}>
          {description}
        </div>
      )}

      {children}
    </div>
  )
}

export default PageHeader
