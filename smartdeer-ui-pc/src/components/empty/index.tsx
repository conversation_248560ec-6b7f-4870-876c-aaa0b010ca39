import React from 'react'
import { Empty as AntdEmpty } from 'antd'
import type { EmptyProps } from 'antd'
import { mergeProps } from '../../utils/withDefaultProps'
import classNames from 'classnames'

import './index.less'

export const classPrefix = `deer-empty`;

export {
  EmptyProps
}

const defaultProps = {
  image: AntdEmpty.PRESENTED_IMAGE_SIMPLE,
}

const Empty: React.FC<EmptyProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,
    ...restProps
  } = props

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <AntdEmpty {...restProps} />
    </div>
  )
}

export default Empty
