import {
  CheckboxOptionType,
  Form,
  Input,
  message,
  Modal,
  Radio,
  RadioChangeEvent,
} from 'antd';
import React, { useEffect, useState } from 'react';

import { useGetFetchApi } from '../../utils/hooks';
import swrFetcher from '../../utils/swrFetcher';
import { ConfigContext } from '../config-provider';
import {
  setModule2FA,
  setSecurityCheckModal,
  setSecurityToken,
} from '../config-provider/utils';
import api from './api';
import Button from '../button';

import './index.less';

const classPrefix = `deer-2fa-verify`;

type RecordType = Record<string, any>;

export type X2faVerifyProps = {
  open: boolean;

  checkType?: string;
  coverTime?: string;
  coverUnit?: 'MINUTES' | 'SECONDS';

  onCancel: () => void;
  onSuccess?: () => void;
};

const X2faVerify: React.FC<X2faVerifyProps> = (props) => {

  const {
    open,
    coverTime,
    checkType = '2',
    coverUnit = 'MINUTES',
    onCancel,
    onSuccess,
  } = props;

  const [step, setStep] = useState(1);

  const [isLoading, setIsLoading] = useState(true);

  const [checkList, setCheckList] = useState<CheckboxOptionType<any>[]>([]);
  const [checkedItem, setCheckedItem] = useState<RecordType>();

  const [checkedType, setCheckedType] = useState('');

  const [submitLoading, setSubmitLoading] = useState(false);
  const [hasSendCode, setHasSendCode] = useState(false);

  const { functionApi } = useGetFetchApi();
  const { language, systemName = 'GS', useUrlParams } = React.useContext(ConfigContext);

  const urlParams = useUrlParams();

  const intl = language.f2a;

  const checkTypeStrMap: RecordType = {
    '1': intl.mobile,
    '2': intl.email,
  };

  const coverUnitStrMap = {
    MINUTES: intl.minutes,
    SECONDS: intl.seconds,
  };

  const checkTypeList = checkType.split(',');

  React.useEffect(() => {
  }, [urlParams]);

  const fetchInfo = async () => {
    setIsLoading(true);

    try {
      const { data } = await swrFetcher(functionApi!, 'POST', {
        functionKey: api[systemName].x_get_security_account_info,
        params: {
          productLine: systemName,
        },
      });

      if (!data?.rs?.length) {
        setIsLoading(false);
        return;
      }

      const list = data.rs.filter((item: any) =>
        checkTypeList.includes(String(item.type)),
      );

      setCheckList(list);

      if (list?.length) {
        setCheckedType(list[0].type);
        setCheckedItem(list[0]);
      }
    } catch (e: any) {
    }

    setIsLoading(false);

  };

  useEffect(() => {
    fetchInfo();
  }, []);

  const handleSuccess = () => {
    setModule2FA('2fa');
    if (!onSuccess) {
      window.location.reload();
      return;
    }
    onSuccess();
  };

  const handleSubmit = async (values: RecordType) => {
    setSubmitLoading(true);

    try {
      const { data } = await swrFetcher(functionApi!, 'POST', {
        functionKey: api[systemName].x_verify_2fa_permission_code,
        params: {
          code: values.code,
          aliasType: checkedItem?.type,
          productLine: systemName,
        },
      });
      const token = data.token;
      setSecurityToken(token);
      setSecurityCheckModal(false);

      Modal.success({
        title: intl.verificationSuccessful,
        content: coverTime
          ? intl.timeLimitTip.replace(
            '{timeStr}',
            `${coverTime} ${coverUnitStrMap[coverUnit]}`,
          )
          : '',
        okText: intl.gotIt,
        cancelButtonProps: { style: { display: 'none' } },
        onOk: handleSuccess,
        onCancel: handleSuccess,
      });
    } catch (e: any) {

    }

    setSubmitLoading(false);
  };

  const modalConfig = React.useMemo(() => {
    let config = {
      title: intl.securityVerification,
      okText: intl.sendVerificationCode,
      cancelButtonProps: { style: { display: 'none' } },
      onCancel,
      onOk: () => {
        setStep(2);
      },
    };

    switch (step) {
      case 1:
        return config;
      case 2:
        return {
          title: intl.enterVerificationCode,
          footer: [
            <Button key='backs' onClick={() => setStep(1)}>
              {intl.previousStep}
            </Button>,
            <Button
              key='submit'
              type='primary'
              htmlType='submit'
              loading={submitLoading}
            >
              {intl.confirm}
            </Button>,
          ],
          onCancel,
        };

      default:
        return config;
    }
  }, [step]);

  const handleClickSendCode = async (): Promise<boolean> => {
    if (!checkedItem?.alias) {
      return false;
    }

    try {
      await swrFetcher(functionApi!, 'POST', {
        functionKey: api[systemName].x_get_2fa_permission_code,
        params: {
          alias: checkedItem?.alias,
          aliasType: checkedItem?.type,
          productLine: systemName,
        },
      });

      setHasSendCode(true);

      return true;
    } catch (err: any) {
      message.error(err.message);
      return false;
    }
  };

  const handleChangeRadio = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setCheckedItem(checkList?.find((item: any) => item.type === value));
    setCheckedType(value);
  };

  const stepOneNode = React.useMemo(() => {
    return (
      <div style={{ display: step === 1 ? 'block' : 'none' }}>

        <div>{intl.verifyTip}</div>

        <div className={`${classPrefix}-select-type`}>
          <Radio.Group onChange={handleChangeRadio} value={checkedType}>
            {(checkList || []).map((item: any) => {
              return (
                <div key={item.type} className={`${classPrefix}-select-radio`}>
                  <Radio value={item.type}>
                    <span className={`${classPrefix}-select-label`}>
                      {item.type === 1
                        ? intl.mobileVerification
                        : intl.emailVerification}
                    </span>
                  </Radio>
                  <div className={`${classPrefix}-select-text`}>
                    {item.alias}
                  </div>
                </div>
              );
            })}
          </Radio.Group>
        </div>
      </div>
    );
  }, []);

  const stepTwoNode = React.useMemo(() => {
    return (
      <div style={{ display: step === 2 ? 'block' : 'none' }}>
        {hasSendCode && (
          <div>
            {intl.codeTip.replace(
              '{userEmail}',
              `${checkTypeStrMap[checkedItem?.type]} ${checkedItem?.alias}`,
            )}
          </div>
        )}
        <div className={`${classPrefix}-code-form`}>
          <Form onFinish={handleSubmit}>
            <Form.Item
              name="code"
              rules={[
                {
                  required: true,
                  message: intl.enterCode,
                  whitespace: true,
                },
              ]}
            >
              <div className="w-full flex justify-between gap-[8px]">
                <Input.Password
                  placeholder={intl.enterCode}
                  maxLength={4}
                  style={{ width: '100%' }}
                />
                <Button.SendCode onClick={handleClickSendCode} />
              </div>
            </Form.Item>
          </Form>
        </div>
      </div>
    );
  }, []);

  return (
    <Modal
      width={450}
      open={open}
      {...modalConfig}
      onClose={onCancel}
      destroyOnClose
      maskClosable={false}
    >
      {stepOneNode}
      {stepTwoNode}
    </Modal>
  );
};

export default X2faVerify;
