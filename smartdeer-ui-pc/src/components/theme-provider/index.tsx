import React from 'react';
import { mergeProps } from '../../utils/withDefaultProps'
import { ThemeContext } from './context';
import type { ThemeContextProps } from './context';

export {
  ThemeContext,
  type ThemeContextProps,
};

export type ConfigProviderProps = {
  children?: React.ReactNode;
} & ThemeContextProps

const defaultProps = {
}

const ConfigProvider: React.FC<ConfigProviderProps> = (p) => {
  const props = mergeProps(defaultProps, p)
  const {
    theme,

    children
  } = props

  return (
    <ThemeContext.Provider
      value={{
        theme,
      }}>
      {children}
    </ThemeContext.Provider>
  )
};

export default ConfigProvider;
