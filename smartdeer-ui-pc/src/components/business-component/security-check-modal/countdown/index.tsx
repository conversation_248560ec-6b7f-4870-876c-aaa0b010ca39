import React, { memo, useEffect, useState } from 'react';

interface Props {
  time?: number;
  onSuccess: () => void;
}

// 定时器
let _timer: any = null;

const Countdown: React.FC<Props> = memo((props) => {
  const { onSuccess } = props;
  const time = props.time || 60;

  const [value, setValue] = useState(time);

  useEffect(() => {
    // 监听 value 变化，value <= 0 关闭定时器，重制 value
    if (value <= 0) {
      clearInterval(_timer);
      setValue(time);
      onSuccess()
    }
  }, [value]);

  useEffect(() => {
    // 开始执行定时器
    _timer = setInterval(() => {
      setValue((value) => value - 1);
    }, 1000);

    // 不依赖任何变量，在组件卸载前执行清理操作
    return () => {
      clearInterval(_timer);
    };
  }, []);

  return (
    <>
      { value }
    </>
  );
});

export default Countdown;
