import {
  Button,
  CheckboxOptionType,
  Form,
  Input,
  message,
  Modal,
  Radio,
  RadioChangeEvent,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

import { useGetFetchApi } from '../../../utils/hooks';
import swrFetcher from '../../../utils/swrFetcher';
import { ConfigContext } from '../../config-provider';
import {
  setModule2FA,
  setSecurityCheckModal,
  setSecurityToken,
} from '../../config-provider/utils';
import api from '../api';
import './index.less';
import SendCode from './send-code';

const classPrefix = `deer-security-check-modal`;

export type SecurityCheckModalProps = {
  open: boolean;
  checkType?: string;
  coverTime?: string;
  coverUnit?: 'MINUTES' | 'SECONDS';
  onCancel: () => void;
  onSuccess?: () => void;
};

type RecordType = Record<string, any>;

const SecurityCheckModal: React.FC<SecurityCheckModalProps> = ({
  open,
  coverTime,
  checkType = '2',
  coverUnit = 'MINUTES',
  onCancel,
  onSuccess,
}) => {
  const [step, setStep] = useState(1);
  const [checkList, setCheckList] = useState<CheckboxOptionType<any>[]>([]);
  const [checkedItem, setCheckedItem] = useState<RecordType>();
  const [checkedType, setCheckedType] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [hasSendCode, setHasSendCode] = useState(false);
  const [form] = Form.useForm();
  const { functionApi } = useGetFetchApi();
  const { language, systemName = 'GS', useUrlParams } = React.useContext(ConfigContext);

  const urlParams = useUrlParams();

  const intl = language.businessComponent.securityCheckModal;

  const checkTypeStrMap: RecordType = {
    '1': intl.mobile,
    '2': intl.email,
  };

  const coverUnitStrMap = {
    MINUTES: intl.minutes,
    SECONDS: intl.seconds,
  };

  const checkTypeList = checkType.split(',');

  React.useEffect(() => {
  }, [urlParams]);

  const fetchInfo = async () => {
    try {
      setIsLoading(true);
      const { data } = await swrFetcher(functionApi!, 'POST', {
        functionKey: api[systemName].x_get_security_account_info,
        params: {
          productLine: systemName,
        },
      });
      if (!data?.rs?.length) {
        setIsLoading(false);
        return;
      }
      const list = data.rs.filter((item: any) =>
        checkTypeList.includes(String(item.type)),
      );
      setCheckList(list);
      if (list?.length) {
        setCheckedType(list[0].type);
        setCheckedItem(list[0]);
      }
    } catch (e: any) {
      message.error(e?.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInfo();
  }, []);

  const handleSuccess = () => {
    setModule2FA('2fa');
    if (!onSuccess) {
      window.location.reload();
      return;
    }
    onSuccess();
  };

  const handleSubmit = () => {
    form
      .validateFields()
      .then(async (values) => {
        try {
          setSubmitLoading(true);
          const { data } = await swrFetcher(functionApi!, 'POST', {
            functionKey: api[systemName].x_verify_2fa_permission_code,
            params: {
              code: values.code,
              aliasType: checkedItem?.type,
              productLine: systemName,
            },
          });
          const token = data.token;
          setSecurityToken(token);
          setSecurityCheckModal(false);

          Modal.success({
            title: intl.verificationSuccessful,
            content: coverTime
              ? intl.timeLimitTip.replace(
                '{timeStr}',
                `${coverTime} ${coverUnitStrMap[coverUnit]}`,
              )
              : '',
            okText: intl.gotIt,
            cancelButtonProps: { style: { display: 'none' } },
            onOk: handleSuccess,
            onCancel: handleSuccess,
          });
        } catch (e: any) {
          message.error(e?.message);
        } finally {
          setSubmitLoading(false);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const modalConfig = useMemo(() => {
    let config = {
      title: intl.securityVerification,
      okText: intl.sendVerificationCode,
      cancelButtonProps: { style: { display: 'none' } },
      onCancel,
      onOk: () => {
        setStep(2);
      },
    };
    switch (step) {
      case 1:
        return config;
      case 2:
        return {
          title: intl.enterVerificationCode,
          footer: [
            <Button key="back" onClick={() => setStep(1)}>
              {intl.previousStep}
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={submitLoading}
              onClick={handleSubmit}
            >
              {intl.confirm}
            </Button>,
          ],
          onCancel,
        };

      default:
        return config;
    }
  }, [step]);

  const handleClickSendCode = async (): Promise<boolean> => {
    if (!checkedItem?.alias) {
      return false;
    }

    try {
      await swrFetcher(functionApi!, 'POST', {
        functionKey: api[systemName].x_get_2fa_permission_code,
        params: {
          alias: checkedItem?.alias,
          aliasType: checkedItem?.type,
          productLine: systemName,
        },
      });

      setHasSendCode(true);

      return true;
    } catch (err: any) {
      message.error(err.message);
      return false;
    }
  };

  const handleChangeRadio = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setCheckedItem(checkList?.find((item: any) => item.type === value));
    setCheckedType(value);
  };

  const renderStepOne = () => {
    return (
      <div style={{ display: step === 1 ? 'block' : 'none' }}>
        <div>{intl.verifyTip}</div>
        <div className={`${classPrefix}-select-type`}>
          <Radio.Group onChange={handleChangeRadio} value={checkedType}>
            {(checkList || []).map((item: any) => {
              return (
                <div key={item.type} className={`${classPrefix}-select-radio`}>
                  <Radio value={item.type}>
                    <span className={`${classPrefix}-select-label`}>
                      {item.type === 1
                        ? intl.mobileVerification
                        : intl.emailVerification}
                    </span>
                  </Radio>
                  <div className={`${classPrefix}-select-text`}>
                    {item.alias}
                  </div>
                </div>
              );
            })}
          </Radio.Group>
        </div>
      </div>
    );
  };

  const renderStepTwo = () => {
    return (
      <div style={{ display: step === 2 ? 'block' : 'none' }}>
        {hasSendCode && (
          <div>
            {intl.codeTip.replace(
              '{userEmail}',
              `${checkTypeStrMap[checkedItem?.type]} ${checkedItem?.alias}`,
            )}
          </div>
        )}
        <div className={`${classPrefix}-code-form`}>
          <Form form={form}>
            <Form.Item
              name="code"
              rules={[
                {
                  required: true,
                  message: intl.enterCode,
                  whitespace: true,
                },
              ]}
            >
              <div className="w-full flex justify-between gap-[8px]">
                <Input.Password
                  placeholder={intl.enterCode}
                  maxLength={4}
                  style={{ width: '100%' }}
                />
                <SendCode onClick={handleClickSendCode} />
              </div>
            </Form.Item>
          </Form>
        </div>
      </div>
    );
  };

  return (
    !isLoading && (
      <Modal
        width={450}
        open={open}
        {...modalConfig}
        onClose={onCancel}
        destroyOnClose
        maskClosable={false}
      >
        {renderStepOne()}
        {renderStepTwo()}
      </Modal>
    )
  );
};

export default SecurityCheckModal;
