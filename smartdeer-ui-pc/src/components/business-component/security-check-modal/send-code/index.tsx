import { Button } from 'antd';
import React, { memo, useCallback, useState } from 'react';
import Countdown from '../countdown';
import { ConfigContext } from '../../../config-provider';

interface Props {
  time?: number;
  onClick: () => Promise<boolean>;
}

const SendCode: React.FC<Props> = memo((props) => {
  const { onClick } = props;
  const time = props.time || 60;
  const [status, setStatus] = useState(0);

  const { language } = React.useContext(ConfigContext);

  const intl = language.businessComponent.securityCheckModal;

  const handleClick = async () => {
    if (status !== 0) return;

    // 发送中
    setStatus(1);

    // 调用父级组件
    const res = await onClick();

    // 发送失败
    if (!res) {
      setStatus(0);
      return;
    }

    // 发送成功, 开始倒计时
    setStatus(2);
  };

  const handleClear = useCallback(() => {
    setStatus(0);
  }, []);

  return (
    <>
      {status === 0 ? (
        <Button type="default" onClick={handleClick}>
          <div className={`text-#p1  text-[14px]`}>
            {intl.sendCode}
          </div>
        </Button>
      ) : status === 1 ? (
        <Button type="default">
          <div className={`text-[#c7c7c7] text-[14px]`}>
            {intl.sending}
          </div>
        </Button>
      ) : (
        <Button type="default">
          <div className={`text-[#c7c7c7] text-[14px]`}>
            <Countdown time={time} onSuccess={handleClear} />
            &nbsp;
            {intl.seconds}
          </div>
        </Button>
      )}
    </>
  );
});

export default SendCode;
