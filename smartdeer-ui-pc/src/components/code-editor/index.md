---
toc: content
group: 
  title: 数据录入
  order: 10
---

# CodeEditor 编辑

通过鼠标或键盘输入内容，是最基础的表单域的包装。

## 示例

<code src="./demos/index.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| defaultLang | 默认语言 | `java` \| `javascript` \| `sql` | `java` | |
| height | 编辑器内容高度 | `string` | `200px` | |
| value | 输入值 | `string` | `--` | |
| onChange | 内容变化时的回调 | `(value: string) => void` | `--` |  |
