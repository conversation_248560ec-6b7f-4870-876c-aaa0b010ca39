import { java } from '@codemirror/lang-java';
import { javascript } from '@codemirror/lang-javascript';
import { sql } from '@codemirror/lang-sql';
import { html } from '@codemirror/lang-html';
import { vscodeDark } from '@uiw/codemirror-theme-vscode';
import CodeMirror from '@uiw/react-codemirror';
import { Button, Modal, Select, Space } from 'antd';
import classNames from 'classnames';
import * as prettierPluginBabel from 'prettier/plugins/babel';
import * as prettierPluginHtml from 'prettier/plugins/html';
import { format as codeFormat } from 'prettier/standalone';
import React from 'react';
import { mergeProps } from '../../utils/withDefaultProps';
import { ConfigContext } from '../config-provider';

import './index.less';

const classPrefix = `deer-code-editor`;

export type LangType = 'java' | 'javascript' | 'sql' | 'html';

export interface CodeEditorProps {
  className?: string;
  style?: React.CSSProperties;

  defaultLang?: LangType;

  height?: '200px';

  value?: string;

  format?: boolean;
  onChange?: (value: string) => void;
}

const langOptions = [
  {
    value: 'java',
    label: 'java',
  },
  {
    value: 'javascript',
    label: 'javascript',
  },
  {
    value: 'sql',
    label: 'sql',
  },
  {
    value: 'html',
    label: 'html',
  },
];

const langMap = {
  java,
  javascript,
  sql,
  html,
};

const defaultProps = {
  defaultLang: 'java',
  height: '200px',
  format: false,
};

const CodeEditor: React.FC<CodeEditorProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const {
    className,
    style,

    defaultLang,

    height,
    onChange,

    format,

    ...restProps
  } = props;

  const { language } = React.useContext(ConfigContext);

  const [langValue, setLangValue] = React.useState<LangType>(defaultLang);
  const [value, setValue] = React.useState("console.log('hello world!');");

  const [openPreviewModal, setOpenPreviewModal] = React.useState(false);

  React.useEffect(() => {
    if ('value' in restProps) {
      setValue(restProps.value || '');
    }
  }, [restProps.value]);

  const handleChangeLang = (value: LangType) => {
    setLangValue(value);
  };

  const handleFormatCode = async () => {
    if (langValue === 'javascript') {
      const formatted = await codeFormat(value, {
        parser: 'babel',
        plugins: [prettierPluginBabel],
      });
      setValue(formatted);

    }

    if (langValue === 'html') {
      const formatted = await codeFormat(value, {
        parser: 'html',
        plugins: [prettierPluginHtml],
      });
      setValue(formatted);
    }
  };

  const handleChangeCodeMirror = React.useCallback(
    (value: string, viewUpdate: any) => {
      if (!('value' in restProps)) {
        setValue(value);
      }

      onChange?.(value);
    },
    [],
  );

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <div className={`${classPrefix}-toolbar`}>
        <Space>
          <div>{language.codeEditor.programmingLanguage}</div>
          <Select
            style={{ width: '150px', textAlign: 'left' }}
            value={langValue}
            options={langOptions}
            onChange={handleChangeLang}
            size="small"
          />
          {format && (
            <div className={`${classPrefix}-toolbar-right`}>
              <Button type="default" size="small" onClick={handleFormatCode}>
                格式化
              </Button>
            </div>
          )}
          {langValue === 'html' && (
            <div className={`${classPrefix}-toolbar-right`}>
              <Button type="default" size="small" onClick={() => setOpenPreviewModal(true)}>
                预览
              </Button>
            </div>
          )}
        </Space>
      </div>

      <CodeMirror
        value={value}
        height={height}
        theme={vscodeDark}
        extensions={[langMap[langValue]()]}
        onChange={handleChangeCodeMirror}
      />

      <Modal
        open={openPreviewModal}
        onCancel={() => setOpenPreviewModal(false)}
        title='HTML 内容'
        width={'80%'}
      >
        <div style={{ height: '80vh', overflow: 'auto' }} dangerouslySetInnerHTML={{ __html: value }} />
      </Modal>
    </div>
  );
};

export default CodeEditor;
