import React, { FC } from 'react'
import classNames from 'classnames'
import { Avatar, Empty, Space, Timeline } from 'antd'
import { mergeProps } from '../../utils/withDefaultProps'
import dayjs from 'dayjs';
import { isArray } from 'lodash'
import { UserOutlined } from '@ant-design/icons';
import { ConfigContext } from '../config-provider'

import './index.less'

const classPrefix = `deer-approval-process`

export interface ApprovalProcessProps {
  className?: string;
  style?: React.CSSProperties;

  showAvatar?: boolean;
  dataInfo?: any[];
  rowKey?: string
}

const defaultProps = {
  dataInfo: [],
  showAvatar: false,
  rowKey: 'updateTime'
}

interface CurrentAvatarProps {
  src?: string
}

const CurrentAvatar: React.FC<CurrentAvatarProps> = ({ src }) => {
  return (
    <Avatar size={22} src={src || <UserOutlined />} style={{ backgroundColor: '#ffaa3b' }} />
  )
}

const ApprovalProcess: FC<ApprovalProcessProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const { language } = React.useContext(ConfigContext)

  const {
    className,
    style,
    showAvatar,
    dataInfo,
  } = props

  const dataInfoLength = dataInfo.length

  const ccText = language.approvalProcess.cc
  const peopleText = language.approvalProcess.people

  if (dataInfoLength === 0) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
  }

  const renderTimelineChildren = (item: any) => {
    const children = (
      <div className={`${classPrefix}-item`}>
        <div>{item.title}</div>

        {isArray(item.approver) && (
          <Space wrap className={`${classPrefix}-workers`}>
            {item.approver.map((row: any, rowIndex: number) => {
              return (
                <div key={rowIndex} className={`${classPrefix}-worker`}>
                  {showAvatar && (
                    <CurrentAvatar src={row.avatar} />
                  )}
                  <div className={`${classPrefix}-worker-name`}>{row.name}</div>
                </div>
              )
            })}

          </Space>
        )}

        {isArray(item.CCList) && (
          <div>
            <div className={`${classPrefix}-cc-num`}>
              {ccText} {item.CCList.length} {peopleText}
            </div>
            <Space wrap className={`${classPrefix}-workers`}>
              {item.CCList.map((row: any, rowIndex: number) => {
                return (
                  <div key={rowIndex} className={`${classPrefix}-worker`}>
                    {showAvatar && (
                      <CurrentAvatar src={row.avatar} />
                    )}
                    <div className={`${classPrefix}-worker-name`}>{row.name}</div>
                  </div>
                )
              })}
            </Space>
          </div>
        )}
      </div>
    )

    return {
      children
    }
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <Timeline
        items={dataInfo.map(item => renderTimelineChildren(item))}
      />
    </div>
  )
}

export default ApprovalProcess
