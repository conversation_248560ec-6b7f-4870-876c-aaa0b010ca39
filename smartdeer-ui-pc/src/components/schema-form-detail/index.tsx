import React from 'react';
import classNames from 'classnames';
import DownloadFiles from '../download-files';
import { findLabelByValue, renderTemplate } from './utils'
import { convertUTCToTimezone } from '../../utils/time'
import { ConfigContext } from '../config-provider'

const classPrefix = `deer-schema-form-detail`

interface SchemaFormDetailProps {
  className?: string;
  style?: React.CSSProperties;

  title: React.ReactNode;
  data: Record<string, any>
  list: any[];
}

const SchemaFormDetail: React.FC<SchemaFormDetailProps> = (props) => {

  const {
    className,
    style,

    title,
    list = [],
    data = {}
  } = props

  const configContext = React.useContext(ConfigContext)

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <div className={`py-4 mb-[-20px] font-bold text-[16px]`}>
        {title}
      </div>

      {list.filter(item => {
        return !!data[item.field]
      }).map((item: any, index: number) => {
        const value = data[item.field];
        const isMulti = value && value.groupKey

        if (isMulti) {
          return (
            <div key={`multiadd_${index}`}>
              <div className={`mt-[20px]`}>
                <span className={`font-bold`}>{value.groupKeyStr || '未命名分组'}</span>
              </div>

              {value.groupKey === 'encryptfiles' ? (
                <div className={`pt-2`}>
                  <DownloadFiles list={value.list} />
                </div>
              ) : (<>
                {value.list.map((item: any, index: number) => (
                  <div key={index} className={`mt-[20px]`}>
                    <div
                      className={`h-[45px] flex items-center justify-between bg-[#eee] px-[20px] mt-[20px] rounded-[8px] ml-[20px]`}
                    >
                      {renderTemplate(item, value.template)}
                    </div>
                  </div>
                ))}
              </>)}
            </div>
          );
        }

        let label = findLabelByValue(item.options, value);

        if (item.type === 'datePicker') {
          label = convertUTCToTimezone(Number(label), configContext.timezone!, 'YYYY-MM-DD')
        }

        return (
          <div
            key={item.field}
            className={`h-[45px] flex items-center justify-between bg-[#eee] px-[20px] mt-[20px] rounded-[8px]`}
          >
            <span className={`font-bold`}>{item.title}</span>

            <span>{!!label ? label : ''}</span>
          </div>
        )
      })}
    </div >
  );
};

export default React.memo(SchemaFormDetail);
