---
toc: content
group: 
  title: 数据展示
  order: 9
---

# SchemaFormDetail 表单渲染

FormDetail 表单渲染，所以我们封装了一个组件来进行数据处理。

## 示例

<code src="./demos/index.tsx"></code>

<!-- <code src="./demos/test.tsx"></code> -->

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| title | 标题 | `ReactNode` | `--` | |
| data | 数据源 | `object[]` | `--` | |
| list | 列表 | `object[]` | `--` | |
