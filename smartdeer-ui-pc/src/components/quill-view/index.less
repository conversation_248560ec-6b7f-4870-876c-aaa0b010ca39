.deer-quill-view {
  border: 0 none !important;
  font-size: 14px;

  .ql-editor {
    padding: 0;
    line-height: inherit;

    h1,h2,h3,h4,h5,h6 {
      margin-top: 10px;
    }

    img {
      display: inline-block;
      width: 100%;
      max-width: 800px !important;
      min-width: 250px !important;
    }

    a {
      color: var(--icb-color-t1) !important;
      transition: all 0.3s;
      cursor: pointer;
      text-decoration: none !important;

      &:hover {
        opacity: 0.7;
      }
    }

    td {
      border: 1px solid #d0d7de;
      padding: 16px;
    }

    p {
      span {
        padding: 1px 5px 3px 5px;
        border-radius: 10px;

        &:first-child {
          padding-left: 0;
        }
      }
    }


    .ql-ui {
      padding: 0;
    }

    table {
      margin-top: 20px;
      margin-bottom: 20px;
      line-height: 20px;
      font-size: 1em;
    }
  }
}
