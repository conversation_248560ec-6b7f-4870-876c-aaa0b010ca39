import React from 'react'
import classNames from 'classnames'
import { Skeleton } from 'antd'
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill-table-ui/dist/index.css'
// import 'highlight.js/styles/atom-one-dark.css'
// import hljs from 'highlight.js';
import InnerHtml from '../inner-html'
import { ConfigContext } from '../config-provider'
import { useFileAccessToken } from '../../utils/hooks'
import { hasImageTag } from '../../utils'
import { getSearchParams } from '../../utils/getSearchParams'

import './index.less'

const classPrefix = 'deer-quill-view'

export interface QuillViewProps {
  className?: string;
  style?: React.CSSProperties;
  content?: string;
}

const QuillView: React.FC<QuillViewProps> = (props) => {
  const {
    className,
    style,
    content,
  } = props

  const { isFileToken } = React.useContext(ConfigContext)

  const { replaceImageSrcInHtml } = useFileAccessToken()

  const [isSkeleton, setIsSkeleton] = React.useState(isFileToken)

  const [htmlContent, setHtmlContent] = React.useState('')

  // const containerRef = React.useRef<any>(null)

  // React.useEffect(() => {
  //   if (!containerRef.current) return

  //   // if (!content) return
  //   // const res = hljs.highlightAuto(content)

  //   // console.log('res', res)

  //   const dom = document.querySelector('.ql-code-block-container')

  //   console.log('dom', dom)

  //   // hljs.highlightElement(dom)

  //   const aa = hljs.configure({
  //     noHighlightRe: /^do-not-highlightme$/i,
  //     languageDetectRe: /\data-language/i, // for `grammar-swift` style CSS naming
  //     cssSelector: 'div'
  //   });

  //   console.log(aa)

  // }, [containerRef.current])

  const fileTransform = async () => {
    if (!isFileToken || !hasImageTag(content || '')) {
      setHtmlContent(content || '')
      setIsSkeleton(false)
      return
    }

    // 调用函数并处理结果
    const newContent = await replaceImageSrcInHtml(content || '')

    setHtmlContent(newContent || '')

    setIsSkeleton(false)
  }

  React.useEffect(() => {
    fileTransform()
  }, [content])

  return (
    <div
      className={classNames('ql-container ql-snow', classPrefix, className)}
      style={{ ...style }}
    >
      <Skeleton active loading={isSkeleton} >
        <div className='ql-editor'>
          <InnerHtml content={htmlContent} />
        </div>
      </Skeleton>
    </div >
  )
}

export default QuillView
