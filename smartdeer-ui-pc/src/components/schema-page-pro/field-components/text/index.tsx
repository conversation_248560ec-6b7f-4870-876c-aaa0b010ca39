import React from 'react';
import { Typography } from 'antd';
import type { TypographyProps } from 'antd';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const { Text } = Typography;

const classPrefix = `deer-page-field-text`

const defaultFieldProps = {
}

interface FieldRowProps {
  // fieldProps?: TypographyProps['Text']
}

const FieldText: ProFieldFC<FieldRowProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    level,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)


  return (
    <Text
      {...restProps}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {children}
    </Text>
  );
};

export default FieldText
