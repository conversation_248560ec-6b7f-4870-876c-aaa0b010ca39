import React from 'react';
import classNames from 'classnames';
import InnerHtml from '../../../inner-html'
import { ProFieldFC } from '../../typing'
import { } from '../../context'

const classPrefix = `deer-page-field-html`

interface FieldHtmlProps {
  fieldProps: {
    content?: string;
  }
}

const FieldHtml: ProFieldFC<FieldHtmlProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restFieldProps
  } = fieldProps

  return (
    <InnerHtml
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restFieldProps}
    >
    </InnerHtml>
  );
};

export default FieldHtml
