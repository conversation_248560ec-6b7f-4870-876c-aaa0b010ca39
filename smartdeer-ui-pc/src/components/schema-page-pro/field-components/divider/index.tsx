import React from 'react';
import { Divider } from 'antd';
import type { DividerProps } from 'antd';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const classPrefix = `deer-page-field-divider`

const defaultFieldProps = {
}

interface FieldRowProps {
  fieldProps?: DividerProps
}

const FieldDivider: ProFieldFC<FieldRowProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <Divider
      {...restProps}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {children}
    </Divider>
  );
};

export default FieldDivider
