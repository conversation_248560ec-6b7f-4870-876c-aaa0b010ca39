import React from 'react';
import classNames from 'classnames';
import InnerHtml from '../../../inner-html'
import { ProFieldFC } from '../../typing'
import { } from '../../context'
import { But<PERSON>, Modal } from 'antd';
import { SchemaPageContext } from '../../context'
import { transformDataBasedOnRules } from '../../../../utils/transformDataBasedOnRules'
import { replaceVariablesInTemplate } from '../../../../utils/replaceVariablesInTemplate'
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes'

const classPrefix = `deer-page-field-contract-view`

interface FieldContractViewProps {
  fieldProps: {
    content?: string;
  }
}

const FieldContractView: ProFieldFC<FieldContractViewProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    content = '',
    ...restFieldProps
  } = fieldProps

  const [template, setTemplate] = React.useState('')
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  // const { dataSource } = React.useContext(SchemaPageContext)

  const showModal = () => {
    try {
      setTemplate(content)

      setIsModalOpen(true);
    } catch (error) {
      console.error(error)
    }
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Button onClick={() => showModal()}>合同预览</Button>

      <Modal
        title='合同预览'
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={800}
      >
        <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          <InnerHtml content={template} />
        </div>
      </Modal>
    </>
  );
};

export default FieldContractView
