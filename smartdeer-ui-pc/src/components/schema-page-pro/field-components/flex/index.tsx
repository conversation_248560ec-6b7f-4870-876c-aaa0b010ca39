import React from 'react';
import classNames from 'classnames';
import { Flex } from 'antd'
import type { FlexProps } from 'antd'
import { ProFieldFC } from '../../typing'

const classPrefix = `deer-page-field-flex`

interface FieldFlexProps {
  fieldProps: Omit<FlexProps, 'children'>
}

const FieldFlex: ProFieldFC<FieldFlexProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restFieldProps
  } = fieldProps

  return (
    <Flex
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restFieldProps}
    >
      {children}
    </Flex>
  );
};

export default FieldFlex
