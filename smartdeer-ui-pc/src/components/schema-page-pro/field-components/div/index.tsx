import React from 'react';
import classNames from 'classnames';
import { Typography } from 'antd';
import type { ParagraphProps } from 'antd/es/typography/Paragraph';
import { ProFieldFC } from '../../typing'
const { Paragraph } = Typography;

const classPrefix = `deer-page-field-div`

interface FieldParagraphProps {
  fieldProps?: ParagraphProps
}

const FieldDiv: ProFieldFC<FieldParagraphProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restProps
  } = fieldProps

  return (
    <Paragraph
      {...restProps}
      className={classNames(classPrefix, className)}
      style={{ ...style }}
    >
      {children}
    </Paragraph>
  );
};

export default FieldDiv
