import React from 'react';
import classNames from 'classnames';
import { ProFieldFC } from '../../typing'
import PageHeader from '../../../page-header'
import type { PageHeaderProps } from '../../../page-header'

const classPrefix = `deer-page-field-page-header`

interface FieldPageHeaderProps {
  fieldProps: PageHeaderProps
}

const FieldPageHeader: ProFieldFC<FieldPageHeaderProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restFieldProps
  } = fieldProps

  return (
    <PageHeader
      {...restFieldProps}
      className={classNames(classPrefix, className)} style={{ ...style }}
    >
      {children}
    </PageHeader>
  );
};

export default FieldPageHeader
