import React from 'react';
import classNames from 'classnames';
import { ProFieldFC } from '../../typing'

const classPrefix = `deer-page-field-strong`

const FieldStrong: ProFieldFC = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
  } = fieldProps

  return (
    <strong className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </strong>
  );
};

export default FieldStrong
