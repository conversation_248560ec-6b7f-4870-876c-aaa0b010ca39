import React from 'react';
import { Typography } from 'antd';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'
import type { TitleProps } from 'antd/es/typography/Title';

const { Title } = Typography;

const classPrefix = `deer-page-field-title`

const defaultFieldProps = {
  level: '1'
}

interface FieldTitleProps {
  // fieldProps?: TitleProps
}

const FieldTitle: ProFieldFC<FieldTitleProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    level,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <Title
      {...restProps}
      level={Number(level) as any}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {children}
    </Title>
  );
};

export default FieldTitle
