.deer-page-field-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ddd;
  background: white;

  &-internal {
    width: 100%;
    white-space: normal;
    word-break: break-all;
    font-size: 13px;

    td {
      padding: 12px;
      width: 200px;
      border-right: 1px solid rgba(0, 0, 0, 0.06);

      &:last-child {
        border-right: 0 none;
      }
    }

    th {
      padding: 12px;
      position: relative;
    }

    tr {
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);

      &:last-child {
        border-bottom: 0 none;
      }
    }

    .important {
      font-weight: bolder;
      text-align: left;
      background: #f0f0f0;
    }

    .highlight {
      font-weight: 700;
      font-size: 14px;
      text-align: left;
      background: #bdbbbb;
    }

    .bolder {
      font-weight: bolder;
    }
  }
}
