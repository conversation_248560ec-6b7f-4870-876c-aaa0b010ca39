import { isObject } from 'lodash';
import React, { memo } from 'react';
import { ProFieldFC } from '../../typing';
import './index.less';

const classPrefix = `deer-page-field-table`;

const parseStyle = (str: string | React.CSSProperties) => {
  if (isObject(str)) {
    return str;
  }
  if (!str) {
    return {};
  }
  const jsonString = str.replace(/(\w+):/g, '"$1":').replace(/'/g, '"');
  let styleObj: React.CSSProperties = {};
  try {
    styleObj = JSON.parse(jsonString);
  } catch (e) {
    styleObj = {};
  }
  return styleObj;
};

const sanitizeInput = (str: string) => {
  let sanitizedStr = str.replace(/<script>/g, '');
  sanitizedStr = sanitizedStr.replace(/<\/script>/g, '');
  return sanitizedStr;
};

const TableHeader = memo(({ headers }: { headers: any[] }) => {
  if (headers.length === 0) return '';

  return (
    <thead>
      <tr>
        {headers.map((header, index) => (
          <th
            key={index}
            className={header.className}
            style={parseStyle(header.style)}
          >
            {header.text}
          </th>
        ))}
      </tr>
    </thead>
  );
});

const TableBody = memo(({ data }: { data: any[] }) => {
  if (data.length === 0) return '';

  return (
    <tbody>
      {data.map((row, rowIndex) => (
        <tr key={rowIndex}>
          {row.map((cell: any, cellIndex: any) => {
            if (cell.colSpan || cell.rowSpan) {
              return (
                <td
                  key={cellIndex}
                  colSpan={cell.colSpan || 1}
                  rowSpan={cell.rowSpan || 1}
                  className={cell.className}
                  style={parseStyle(cell.style)}
                >
                  <div
                    dangerouslySetInnerHTML={{
                      __html: sanitizeInput(cell.text),
                    }}
                  ></div>
                </td>
              );
            } else {
              return (
                <td
                  key={cellIndex}
                  className={cell.className}
                  style={parseStyle(cell.style)}
                >
                  <div
                    dangerouslySetInnerHTML={{
                      __html: sanitizeInput(cell.text),
                    }}
                  ></div>
                </td>
              );
            }
          })}
        </tr>
      ))}
    </tbody>
  );
});

interface FieldTableProps {
  fieldProps: {
    dataSource?: string;
  };
}

const FieldTable: ProFieldFC<FieldTableProps> = (props) => {
  const { fieldProps } = props;

  const { dataSource, style, className } = fieldProps;

  const list: any[] = React.useMemo(() => {
    let result = [];

    try {
      result = JSON.parse(dataSource as string);
    } catch (err) {
      console.error(
        `[smartdeer-ui: FieldTable] dataSource is not a json string`,
      );
    }

    return result;
  }, [dataSource]);

  return (
    <>
      {list.map((item, index) => {
        return (
          <div
            key={index}
            className={`${classPrefix} ${className}`}
            style={style}
          >
            <table className={`${classPrefix}-internal`}>
              <TableHeader headers={item?.headers || []} />
              <TableBody data={item.data} />
            </table>
          </div>
        );
      })}
    </>
  );
};

export default FieldTable;
