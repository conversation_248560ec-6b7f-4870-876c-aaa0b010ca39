import React from 'react';
import { isDeepEqualReact } from '../../../utils'
import type { ItemType, EffectType } from '../typing'
import { omitUndefined } from '../../../utils/omitUndefined'

// 布局组件
import FieldContainer from './container'
import FieldRow from './row'
import FieldCol from './col'
import FieldDivider from './divider'
import FieldFlex from './flex'
import FieldSpace from './space'

// 展示组件
import FieldTable from './table'
import FieldHtml from './html'
import FieldBadge from './badge'

// 导航组件
import FieldPageHeader from './page-header'

// html 元素
import FieldDiv from './div'
import FieldText from './text'
import FieldStrong from './strong'
import FieldTitle from './title'

// 临时组件
import FieldContractView from './contract-view'

interface FieldComponentProps {
  type: string;
  fieldProps: ItemType['fieldProps'];
  children?: React.ReactNode;
  effect?: EffectType;
}


const defaultRenderComponent = (
  valueType: string,
  props: Omit<FieldComponentProps, 'type'>
) => {

  const { children, effect, ...restProps } = props

  let childNode: React.ReactNode = null;

  switch (valueType) {
    // ---------------------------- 布局组件 ----------------------------
    case 'container':
      childNode = <FieldContainer {...restProps}>{children}</FieldContainer>;
      break;

    case 'row':
      childNode = <FieldRow {...restProps}>{children}</FieldRow>;
      break;

    case 'col':
      childNode = <FieldCol {...restProps}>{children}</FieldCol>;
      break;

    case 'divider':
      childNode = <FieldDivider {...restProps}>{children}</FieldDivider>;
      break;

    case 'flex':
      childNode = <FieldFlex {...restProps}>{children}</FieldFlex>;
      break;

    case 'space':
      childNode = <FieldSpace {...restProps}>{children}</FieldSpace>;
      break;

    // ---------------------------- 展示组件 ----------------------------

    case 'table':
      childNode = <FieldTable {...restProps}>{children}</FieldTable>;
      break;

    case 'html':
      childNode = <FieldHtml {...restProps}>{children}</FieldHtml>;
      break;

    case 'badge':
      childNode = <FieldBadge {...restProps}>{children}</FieldBadge>;
      break;

    // ---------------------------- 导航组件 ----------------------------

    case 'pageHeader':
      childNode = <FieldPageHeader {...restProps}>{children}</FieldPageHeader>;
      break;

    // ---------------------------- 元素组件 ----------------------------

    case 'div':
      childNode = <FieldDiv {...restProps}>{children}</FieldDiv>;
      break;

    case 'text':
      childNode = <FieldText {...restProps}>{children}</FieldText>;
      break;

    case 'strong':
      childNode = <FieldStrong {...restProps}>{children}</FieldStrong>;
      break;

    case 'title':
      childNode = <FieldTitle {...restProps}>{children}</FieldTitle>;
      break;

    // ---------------------------- 临时组件 ----------------------------

    case 'contractView':
      childNode = <FieldContractView {...restProps}>{children}</FieldContractView>;
      break;

    default:
      childNode = <div className={`bg-red-500 text-white rounded px-2 py-1 mb-[20px]`}>没有实现的组件: {valueType}</div>
  }

  return <>{childNode}</>
}

const FieldComponent: React.FC<FieldComponentProps> = (props) => {
  const {
    type,
    effect,
    ...restProps
  } = props;

  const renderedDom = defaultRenderComponent(type, omitUndefined({
    ...restProps,
    effect: omitUndefined({
      fetch: effect?.fetch,
      publish: effect?.publish,
      click: effect?.click,
    })
  }))

  return (
    <React.Fragment>
      {renderedDom}
    </React.Fragment>
  )
}

export default React.memo(FieldComponent, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps);
});
