import React from 'react';
import classNames from 'classnames';
import { Space } from 'antd'
import type { SpaceProps } from 'antd'
import { ProFieldFC } from '../../typing'

const classPrefix = `deer-page-field-space`

interface FieldSpaceProps {
  fieldProps: SpaceProps
}

const FieldSpace: ProFieldFC<FieldSpaceProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restFieldProps
  } = fieldProps

  return (
    <Space
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restFieldProps}
    >
      {children}
    </Space >
  );
};

export default FieldSpace
