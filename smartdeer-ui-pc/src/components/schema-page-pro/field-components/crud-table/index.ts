import type { TablePaginationConfig } from 'antd';
import InternalTable from './table';

import { TableRowSelection } from 'antd/es/table/interface';
import type { CurdTableColumnType } from './typing';

export type { CurdTableColumnType };

type RecordType = Record<string, any>;

export interface CrudTableProps {
  className?: string;
  style?: React.CSSProperties;

  size?: 'large' | 'middle' | 'small';
  scroll?: {
    scrollToFirstRowOnChange?: boolean;
    x?: string | number | true;
    y?: string | number;
  };

  rowKey?: string;
  columns?: CurdTableColumnType[];
  dataSource?: RecordType[];
  pagination?: TablePaginationConfig;
  rowSelection?: TableRowSelection<Record<string, any>>;
  onChange?: (...args: any) => void;
  onRefresh?: () => void;
  onRowClick?: (record: RecordType, index: number | undefined) => void;
  onRowDoubleClick?: (record: RecordType, index: number | undefined) => void;
  onRowContextMenu?: (record: RecordType, index: number | undefined) => void;
  onRowMouseEnter?: (record: RecordType, index: number | undefined) => void;
  onRowMouseLeave?: (record: RecordType, index: number | undefined) => void;
}

export interface CurdTableColumnProps {
  column: any;
  text: any;
  record: any;
  index: number;
}

type CompoundedComponent = React.FC<CrudTableProps>;

const CrudTable = InternalTable as CompoundedComponent;

export default CrudTable;
