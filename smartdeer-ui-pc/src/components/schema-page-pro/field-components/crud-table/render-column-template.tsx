import React from 'react'
import type { TemplateJsonType, TemplateStatusType, TemplateActionType, TemplateDrawerType } from './typing'
import type { ConfigContextProps, TimezoneType } from '../../../config-provider'
import { isString, isArray, isUndefined } from 'lodash'
import { evaluateLogicalExpression } from '../../../../utils/evaluateLogicalExpression'
import { replaceVariablesInTemplate } from '../../../../utils/replaceVariablesInTemplate'
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes'
import { FieldPopconfirm, FieldLink, FieldButton } from './field-components'
import { CurdTableColumnProps } from '.';


const classPrefix = `deer-crud-table`

export {
  replaceTemplateWithPipes
}

/**
 * 根据模板和记录数据生成 HTML 字符串
 *
 * @param template 模板字符串，其中包含形如 `*{key}` 的占位符
 * @param record 记录数据对象，用于替换模板中的占位符
 * @returns 生成的 HTML 字符串
 */

/**
 * 渲染一个 JSON 模板并返回一个 React 元素。
 *
 * @param template 包含模板结构信息的对象。
 *                 它应该包含 type 属性来指定元素的类型，
 *                 props 属性来包含元素的属性，
 *                 children 属性（可选）来包含子元素的数组。
 *
 * @param record 与模板关联的数据记录对象。
 *               它用于在模板渲染过程中提供动态数据。
 *
 * @param router 路由函数
 *
 * @returns 渲染后的 React 元素。
 */
export const renderTemplateJson = (
  template: TemplateJsonType,
  record: Record<string, any>,
  router: any
) => {

  // renderNode 是一个递归函数，用于渲染模板中的每个节点
  const renderNode = (node: TemplateJsonType | string): React.ReactNode => {
    // 如果节点是字符串或未定义，则使用 replaceTemplateWithPipes 函数处理
    if (isString(node) || isUndefined(node)) {
      return replaceTemplateWithPipes(node as string || '', record);
    }

    // 处理子节点
    let childrenText: React.ReactNode;
    if (isArray(node.children)) {
      // 如果子节点是数组，则遍历数组并递归渲染每个子节点
      childrenText = node.children.map((child: any, childIndex: number) => (
        <React.Fragment key={childIndex}>{renderNode(child)}</React.Fragment>
      ));
    } else {
      // 否则，直接递归渲染子节点
      childrenText = renderNode(node.children);
    }

    // 根据节点类型返回不同的 React 元素
    switch (node?.type) {
      case 'span':
        // 如果节点类型是 span，返回带有 props 的 span 元素
        return <span {...node.props}>{childrenText}</span>;
      case 'link':
        // 如果节点类型是 link，返回带有 props 的 a 元素
        return <a onClick={() => {
          if (node?.props && node.props.url) {
            const path = replaceVariablesInTemplate(record, node.props.url)
            router?.push(path)
          }
        }} {...node.props}>{childrenText}</a>;
      case 'a':
        // 如果节点类型是 link，返回带有 props 的 a 元素
        return <a onClick={() => {
          if (node?.props && node.props.url) {
            const path = replaceVariablesInTemplate(record, node.props.url)
            router?.push(path)
          }
        }} {...node.props}>{childrenText}</a>;
      default:
        // 其他类型返回带有 props 的 div 元素
        return <div {...node.props}>{childrenText}</div>;
    }
  };

  // 调用 renderNode 函数，传入模板作为起始节点进行渲染
  return renderNode(template);
};

export const renderTemplateStatus = (template: TemplateStatusType[], record: Record<string, any>, timezone: TimezoneType) => {

  const elementList = template.filter(item => {
    if (!item.vif) return true

    return evaluateLogicalExpression(record, item.vif, timezone)
  }).map(item => {
    return (
      <div
        key={item.vif + item.name}
        className={`${classPrefix}-status-tag`}
        style={{
          border: '1px solid' + item.color,
          color: item.color,
        }}
      >
        <div
          className={`${classPrefix}-status-tag-bg`}
          style={{
            background: item.color,
          }}
        />
        {item.name}
      </div>
    )
  })

  return elementList
}

export const renderTemplateAction = (
  template: TemplateActionType[],
  record: Record<string, any>,
  timezone: TimezoneType,
) => {
  const elementList = template.filter(item => {
    if (!item.vif) return true

    return evaluateLogicalExpression(record, item.vif, timezone)
  }).map(item => {
    let childNode: React.ReactNode = null;

    switch (item.type) {
      case 'pop':
        childNode = (
          <FieldPopconfirm item={item} record={record} />
        )
        break;
      case 'del':
        childNode = (
          <FieldPopconfirm item={item} record={record} />
        )
        break;
      case 'link':
        childNode = (
          <FieldLink item={item} record={record} />
        )
        break;
      case 'drawer':
        childNode = (
          // <a onClick={() => onShowDrawer(item, record)}>
          //   {item?.name}
          // </a>
          <div></div>
        )
        break;
      case 'modal':
        childNode = (
          // <a onClick={() => onShowModal(item, record)}>
          //   {item?.name}
          // </a>
          <div></div>
        )
        break;
      default:
        childNode = (<FieldButton item={item} record={record} />)
        break;
    }

    return <div key={item.vif + item.name} className={`${classPrefix}-button`}>{childNode}</div>
  })

  return elementList
}

export const renderTemplateDrawer = (
  template: TemplateDrawerType[],
  record: Record<string, any>,
  timezone: TimezoneType,
  onShowDrawer: (column: Record<string, any>, record: Record<string, any>) => void,
) => {
  const elementList = template.filter((item) => {
    if (!item.vif) return true;

    return evaluateLogicalExpression(record, item.vif, timezone);
  })
    .map((item, index) => {
      return (
        <a key={index} onClick={() => onShowDrawer(item, record)}>
          {item?.name}
        </a>
      );
    });
  return elementList
}

export const renderTemplateArray = (
  text: any[],
  template: string,
) => {
  if (!isArray(text)) return '-'

  const newText = text.map((item: any) => replaceTemplateWithPipes(template, item))

  return newText.join(', ');
};

export const renderTemplateSwitch = (
  props: CurdTableColumnProps,
  context: ConfigContextProps,
  renderChildNode: (
    props: CurdTableColumnProps,
    configContext: ConfigContextProps,
  ) => React.ReactNode,
) => {
  const { column, record } = props;
  const { template } = column;
  const elementList = template
    .filter((item: any) => {
      if (!item.case) return true;

      return evaluateLogicalExpression(record, item.case);
    })
    .map((item: any) => {

      return (
        <React.Fragment key={item.case}>
          {renderChildNode({ ...props, column: item }, context)}
        </React.Fragment>
      );
    });
  return elementList;
};
