import { isObject, isString } from 'lodash';
import { omitUndefined } from '../../utils/omitUndefined';

type RecordType = Record<string, any>;

/**
 * 从给定的 HTML 字符串中提取所有 `${...}` 格式的变量名。
 *
 * @param htmlString 要提取变量的 HTML 字符串。
 * @returns 返回一个包含所有提取到的变量名的数组。
 */
export const extractVariables = (htmlString: string): string[] => {
  // 定义正则表达式，用于匹配 `${...}` 格式的字符串
  const regex = /\$\{([^}]+)\}/g;

  // 使用 matchAll 方法查找所有匹配项
  const matches = htmlString.matchAll(regex);

  // 初始化一个空数组，用于存储提取到的变量名
  const variables: string[] = [];

  // 遍历匹配项
  for (const match of matches) {
    // 将捕获组中的内容（即变量名）添加到 variables 数组中
    variables.push(match[1]);
  }

  // 返回包含所有提取到的变量名的数组
  return variables;
};

export const getDataSourceKey = (dataSource: RecordType[]) => {
  const data = dataSource[0];

  if (!isObject(data)) return [];

  return Object.keys(data).map((key) => {
    return {
      title: key,
      key: key,
      dataIndex: key,
    };
  });
};

type columnsType = string | RecordType;

export const crudTableColumnsTransform = (columns: columnsType[]) => {
  return columns.map((item) => {
    let returnValues: RecordType = {};

    if (isString(item)) {
      returnValues = {
        title: item,
        key: item,
      };
    } else if (isObject(item)) {
      returnValues = omitUndefined({
        title: item.title || item.key,
        key: item.key || 'key_' + Date.now() + '',
        dataIndex: item.dataIndex,
        type: item.type,
        fixed: item.fixed,
        width: item.width,
        template: item.template,
        props: item.props,
      });
    }

    return returnValues;
  });
};
