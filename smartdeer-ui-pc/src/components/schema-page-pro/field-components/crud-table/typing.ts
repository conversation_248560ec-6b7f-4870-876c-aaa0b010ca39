import { CompareFn, SortOrder } from 'antd/es/table/interface';
import React from 'react';
import type {
  EffectType,
  FetchType,
  OnCloseType,
  OnSuccessType,
} from '../../../../typing';

export type TemplateJsonChildrenType = {
  type: string;
  props?: {
    url?: string;
    style?: React.CSSProperties;
  };
  children?: string | TemplateJsonChildrenType;
};

export type TemplateJsonType = {
  type: string;
  props?: {
    url?: string;
    style?: React.CSSProperties;
  };
  children: TemplateJsonChildrenType[];
};

export type TemplateStatusType = {
  vif?: string;
  name: string;
  color?: string;
};

export type TemplateActionType = {
  vif?: string;
  name: string;
  type?: string;
  version?: string;
  props?: {
    title?: string;
    description?: string;
    okText?: string;
    cancelText?: string;
    url?: string;
    color?: string;
    fileKey?: string;
    fileName?: string;
  };
  onSubmit?: FetchType;
  onSuccess?: OnSuccessType;
  onClose?: OnCloseType;
};

export type TemplateDrawerType = {
  vif?: string;
  name: string;
  type?: string;
  props?: {
    width?: string;
    okText?: string;
    cancelText?: string;
    title?: string;
    color?: string;
    componentContent?: string | any[] | Record<string, any>;
    componentName?: string;
    componentColumns?: any[];
    effect?: EffectType;
    onClose?: OnCloseType;
    [key: string]: any;
  };
};

export type CurdTableColumnType = {
  title: string;
  key: string | string[];
  dataIndex?: string | string[];
  type?:
    | 'avatar'
    | 'image'
    | 'template'
    | 'templateJson'
    | 'selectStr'
    | 'tag'
    | 'templateSwitch'
    | 'status'
    | 'action'
    | 'text';
  fixed?: boolean | 'left' | 'right';
  width?: string | number;
  template?:
    | string
    | TemplateJsonType
    | TemplateStatusType[]
    | Array<TemplateActionType | TemplateDrawerType>;
  props?: Record<string, any>;
  sorter?:
    | boolean
    | CompareFn<any>
    | {
        compare?: CompareFn<any>;
        multiple?: number;
      };
  defaultSortOrder?: SortOrder;
};
