import React from 'react'
import { Space, Avatar, Tag, Flex } from 'antd';
import { isString } from 'lodash'
import { ConfigContext, ConfigContextProps } from '../../../config-provider'
import { replaceTemplateWithPipes, renderTemplateJson, renderTemplateStatus, renderTemplateAction, renderTemplateDrawer, renderTemplateArray, renderTemplateSwitch } from './render-column-template'
import type { CurdTableColumnProps } from '.'
import InnerHtml from '../../../inner-html'
import Image from '../../../image'
import isDeepEqualReact from '../../../../utils/isDeepEqualReact'

const classPrefix = `deer-crud-table-column`

const renderChildNode = (props: CurdTableColumnProps, configContext: ConfigContextProps) => {
  const { column, text, record, index } = props

  let childNode: React.ReactNode = null;

  switch (column.type) {
    case 'avatar':
      childNode = (
        text ? <Avatar shape='square' size={38} src={<img src={text} alt='avatar' />} /> : <Avatar shape='square' size={38} />
      )
      break;

    case 'image':
      childNode = (
        text ? <Image src={text} width={38} height={38} {...column.props} /> : <Avatar shape='square' size={38} />
      )
      break;

    case 'template':
      childNode = (
        <InnerHtml content={replaceTemplateWithPipes(column.template, record)} />
      )
      break;

    case 'templateJson':
      childNode = (
        <>{renderTemplateJson(column.template, record, configContext.router)}</>
      )
      break;

    case 'selectStr':
      childNode = '-'
      try {
        childNode = column.props.options.find((find: { label: string, value: string }) => find.value === text)?.label
      } catch (e) {
        console.error((e as Error).message)
      }
      break;

    case 'tag':
      childNode = <Flex gap="4px 0" wrap>{text?.map((item: string) => <Tag {...column.props} key={item}>{item}</Tag>)}</Flex>
      break;

    case 'templateSwitch':
      childNode = (
        <>{renderTemplateSwitch(props, configContext, renderChildNode)}</>
      );
      break;

    case 'status':
      childNode = (
        <Space>
          {renderTemplateStatus(column.template, record, configContext.timezone!)}
        </Space>
      )
      break;

    case 'action':
      childNode = (
        <Space>
          {renderTemplateAction(column.template, record, configContext.timezone!)}
        </Space>
      )
      break;

    default:
      if (isString(text)) {
        childNode = (
          <>{text}</>
        )
      } else {

        childNode = (
          <>{JSON.stringify(text)}</>
        )
      }

      break;
  }
  return childNode;
}

const CurdTableColumn: React.FC<CurdTableColumnProps> = (props) => {
  const configContext = React.useContext(ConfigContext)

  const childNode = renderChildNode(props, configContext);

  return (<div className={classPrefix}>{childNode}</div>)
}

export default React.memo(CurdTableColumn, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, []);
});
