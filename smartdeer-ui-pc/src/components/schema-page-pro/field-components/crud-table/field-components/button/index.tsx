import React from 'react'
import type { TemplateActionType } from '../../typing'
import { CrudTableContext } from '../../context'

interface FieldPopconfirmProps {
  item: TemplateActionType;
  record: Record<string, any>
}

const Button: React.FC<FieldPopconfirmProps> = ({ item, record = {} }) => {
  const { type, props, onSubmit: submitConfig } = item

  const { onRowClick } = React.useContext(CrudTableContext)

  const handleClick = async () => {
    onRowClick?.(record, undefined)
  }

  return (
    <a onClick={handleClick}>{item.name}</a>
  )
}


export default React.memo(Button)
