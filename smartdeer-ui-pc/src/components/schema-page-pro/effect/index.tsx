import { message, Skeleton } from 'antd';
import { get, isArray, isObject, isString } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { ConfigContext } from '../../../components/config-provider';
import { getEffectFetchConfig } from '../../../utils/getEffectFetchConfig';
import { useParamsDict } from '../../../utils/hooks';
import { replaceKeysWithRegex } from '../../../utils/replaceKeysWithRegex';
import swrFetcher, { cachedSwrFetcher } from '../../../utils/swrFetcher';
import { transformDataBasedOnRules } from '../../../utils/transformDataBasedOnRules';
import { replaceTemplateWithPipes } from '../../../utils/replaceTemplateWithPipes';
import { SchemaPageContext } from '../context';
import { PageColumnsType, RenderValueTypeHelpers } from '../typing';
import { useDebounceEffect } from 'ahooks'
import { delay } from '../../../utils'
import { prodError } from '../../../utils/log'

const classPrefix = `deer-page-field-effect`;

export interface EffectProps extends PageColumnsType {
  genItems: RenderValueTypeHelpers['genItems']
}

type RecordType = Record<string, any>;

const Effect: React.FC<EffectProps> = (params) => {
  const { effect, props, children, genItems, field } = params;

  if (!field) {
    prodError('SchemaPagePro', 'effect 组件必须配置 field 属性')
  }

  const [data, setData] = useState<any>(null);

  const [isLoading, setIsLoading] = useState(false);

  const paramsDict = useParamsDict();
  const configContext = React.useContext(ConfigContext);
  const { mock } = React.useContext(SchemaPageContext);

  const effectFetch = effect?.fetch;

  const { transformResponseData } = effectFetch || {};

  const fetch = async (fetchParams = {}, needLoading = true) => {
    setIsLoading(needLoading);

    try {
      let data: Record<string, any> = {}

      const mockKey = effectFetch?.functionKey || effectFetch?.processKey

      if (mock && mockKey && mock[mockKey]) {
        data = mock[mockKey];

        await delay(1000)

      } else {
        const { api, method, params, dataIndex } = getEffectFetchConfig(
          effectFetch!,
          configContext,
          { ...paramsDict },
        );

        const res = await cachedSwrFetcher(api, method, {
          ...params,
          params: {
            ...(params?.params || {}),
            ...fetchParams,
          },
        });
        data = dataIndex ? get(res.data, dataIndex) : res.data;
      }

      if (transformResponseData) {
        data = transformDataBasedOnRules(data, transformResponseData);
      }

      setData({
        [field as string]: data
      });
    } catch (err: any) {

    }

    setIsLoading(false);
  };

  React.useEffect(() => {
    if (!effectFetch) return;
    setIsLoading(true)
    fetch();
  }, []);

  const handleRetry = (params = {}, loading = true) => {
    fetch(params, loading);
  };

  const columns = useMemo(() => {
    if (!data || (!isArray(children) && !isObject(children))) return null;

    const result = replaceKeysWithRegex(
      children as RecordType | RecordType[],
      { ...paramsDict, ...data },
    ) as any;

    return isArray(result) ? result : [result];
  }, [data, paramsDict]);

  if (isLoading)
    return (
      <div style={{ marginTop: '20px' }}>
        <Skeleton active paragraph={{ rows: 3 }} />
      </div>
    );

  return (
    <div className={classPrefix}>
      {columns && <>{genItems(columns)}</>}
    </div>
  );
};

export default Effect;
