import React from "react"
import classNames from "classnames"
import { Empty } from 'antd'
import { mergeProps } from '../../utils/withDefaultProps'
import renderValueType from './value-type'
import { omitUndefined } from '../../utils/omitUndefined'
import type { PageColumnsType, RenderValueTypeHelpers } from './typing'
import { replaceKeysWithRegex } from '../../utils/replaceKeysWithRegex'
import { SchemaPageContext } from './context'
import type { SchemaPageContextType } from './context'
import { isEmpty } from "lodash"
import { useDeepCompareMemo, useParamsDict } from '../../utils/hooks'

import './index.less'

const classPrefix = `deer-schema-page-pro`

export {
  SchemaPageContext,
  type SchemaPageContextType,
};

export type SchemaPageProProps = {
  className?: string;
  style?: React.CSSProperties;

  columns?: PageColumnsType[];
  dataSource?: Record<string, any>;
  forceUpdate?: boolean;
  onRetry?: (params?: Record<string, any>) => void
  mock?: Record<string, any>
}

const defaultProps = {
  dataSource: {},
  columns: [],
  forceUpdate: false,
}

const SchemaPagePro: React.FC<SchemaPageProProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,

    columns,
    dataSource,
    onRetry,
    mock,
    ...restProps
  } = props

  const paramsDict = useParamsDict()

  const [update, setUpdate] = React.useState(false);

  /**
   * 处理重试操作的函数
   */
  const handleRetry = () => {
    if (restProps.forceUpdate) {
      setUpdate(true)

      setTimeout(() => {
        setUpdate(false)
      }, 10)
    }

    onRetry?.()
  }

  /**
   * 使用正则表达式替换键名
   *
   * @param columns 要处理的列数组
   * @returns 替换后的列数组
   */
  const getReplaceKeysWithRegex = (columns: any[]) => {

    if (isEmpty(dataSource)) {
      return columns
    }

    return replaceKeysWithRegex(columns, { ...paramsDict, ...dataSource }) as any[]
  }

  /**
   * 生成渲染项的函数。
   *
   * @param items 页面列数组
   * @returns 过滤和映射后的渲染项数组
   */
  const genItems: RenderValueTypeHelpers['genItems'] = (items: PageColumnsType[]) => {

    return items.filter(item => {
      return !item.hidden
    }).map((originItem, index) => {
      const item = omitUndefined({
        scope: originItem.scope,
        type: originItem.type,
        field: originItem.field,
        hidden: originItem.hidden,
        fieldProps: omitUndefined({
          ...originItem.props,
        }) || {},
        effect: originItem.effect,
        children: originItem.children
      })

      return (
        renderValueType(item, {
          originItem,
          index,
          genItems,
          paramsDict
        })
      );
    }).filter((field) => {
      return Boolean(field);
    });
  };

  const childNode = useDeepCompareMemo(() => {
    if (columns.length === 0) return null

    return genItems(getReplaceKeysWithRegex(columns));
  }, [columns, dataSource]);

  if (columns.length === 0 || isEmpty(childNode)) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <SchemaPageContext.Provider value={{
        onRetry: handleRetry,
        dataSource,
        mock
      }}>
        {!update && (
          <>{childNode}</>
        )}
      </SchemaPageContext.Provider>
    </div>
  )
}

export default SchemaPagePro
