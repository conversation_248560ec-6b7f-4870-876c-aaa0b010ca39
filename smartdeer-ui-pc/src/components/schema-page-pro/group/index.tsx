import classNames from 'classnames';
import React from 'react';

const classPrefix = `deer-page-field-group`;

export interface GroupProps {
  className?: string;
  style?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;

  title?: string;
  index: number;
  children?: React.ReactNode;
}

const Group: React.FC<GroupProps> = (props) => {
  const {
    className,
    style,
    bodyStyle,
    title,
    index,
    children,
  } = props;

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {!!title && <div className={`${classPrefix}-title`}>{title} {index + 1}</div>}

      <div className={`${classPrefix}-body`} style={{ ...bodyStyle }}>
        {children}
      </div>
    </div>
  );
};

export default Group;
