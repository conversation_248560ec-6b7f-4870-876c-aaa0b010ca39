import React from 'react';
import type { FetchType, OnFail, OnSuccessType } from '../../typing';

export type EffectType = {
  show?: string;
  fetch?: FetchType;
  onSuccess?: OnSuccessType;
  onFail?: OnFail;
  publish?: string | string[];
  click?: string | string[];
};

export type ComponentType =
  | 'effect'
  | 'group'
  | 'container'
  | 'row'
  | 'col'
  | 'divider'
  | 'flex'
  | 'space'
  | 'table'
  | 'html'
  | 'badge'
  | 'pageHeader'
  | 'div'
  | 'title'
  | 'text'
  | 'strong';

export type PageColumnsType = {
  // 作用域
  scope?: true;

  // 组件类型
  type: ComponentType;

  // 唯一标识
  field?: string;

  // 显示隐藏
  hidden?: boolean | 'true';

  // 组件的 props
  props?: {
    style?: React.CSSProperties;
    bodyStyle?: React.CSSProperties;
    className?: string;
    [keyof: string]: any;
  } & Record<string, any>;

  // 副作用
  effect?: EffectType;

  // 孩子
  children?: string | PageColumnsType | PageColumnsType[];
};

export type ItemType = Omit<PageColumnsType, 'hidden' | 'props'> & {
  fieldProps: {
    style?: React.CSSProperties;
    bodyStyle?: React.CSSProperties;
    className?: string;
    [keyof: string]: any;
  };
};

export type RenderValueTypeHelpers = {
  originItem: PageColumnsType;
  index: number;
  genItems: (items: PageColumnsType[]) => React.ReactNode[];
  paramsDict: Record<string, any>;
};

export type SchemaRenderValueTypeFunction<
  InjectItemType = {},
  HelpersType = {},
> = (
  item: ItemType & InjectItemType,
  helpers: RenderValueTypeHelpers & HelpersType,
) => React.ReactNode;

export type ProFieldFC<T = {}> = React.ForwardRefRenderFunction<
  any,
  {
    children?: React.ReactNode | React.ReactNode[];
    fieldProps: ItemType['fieldProps'];
  } & T
>;
