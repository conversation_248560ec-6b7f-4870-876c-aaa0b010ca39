import type { ItemType, SchemaRenderValueTypeFunction, RenderValueTypeHelpers } from '../typing';
import field from './field'
import group from './group'
import effect from './effect'

const tasks: SchemaRenderValueTypeFunction[] = [
  group,
  effect
];

const renderValueType = (
  item: ItemType,
  helpers: RenderValueTypeHelpers,
) => {

  for (let cur = 0; cur < tasks.length; cur++) {
    const task = tasks[cur];
    const dom = task(item, helpers);

    // False 不再遍历
    // if (dom === false) {
    //   return false;
    if (dom === true) {
      // True 继续下一次
      continue;
    } else {
      // Other Is Dom
      return dom;
    }
  }

  return field(item, helpers);
};

export default renderValueType
