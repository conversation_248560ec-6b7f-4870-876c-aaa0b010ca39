import React from 'react';
import { Alert } from 'antd';

import ProEffect from '../effect';
import type { SchemaRenderValueTypeFunction } from '../typing';
import { isObject } from 'lodash';
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression';

const Effect: SchemaRenderValueTypeFunction = (item, helpers) => {

  if (item.type === 'effect') {

    const { index, genItems, paramsDict } = helpers

    if (item.effect && item.effect.show) {

      const show = evaluateLogicalExpression({ ...paramsDict }, item.effect.show)

      if (!show) return <React.Fragment key={[item.field, index || 0].join('_')} />
    }

    if (!isObject(item.children) && !Array.isArray(item.children)) return null;

    if (!item.field) {
      return <Alert message='effect 组件必须配置 field 属性' type='error' />
    }

    return (
      <React.Fragment key={index}>
        <ProEffect {...item} genItems={genItems} />
      </React.Fragment>
    );
  }

  return true;
};

export default Effect;
