
import React from 'react';
import type { ItemType } from '../typing';
import FieldComponent from '../field-components'
import { isString, isArray, isObject } from 'lodash';
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'

const field = (
  item: ItemType,
  helpers: Record<string, any>,
) => {
  const { index, genItems, paramsDict } = helpers

  if (item.effect && item.effect.show) {

    const show = evaluateLogicalExpression({ ...paramsDict }, item.effect.show)

    if (!show) return <React.Fragment key={[item.field, index || 0].join('_')} />
  }

  let children: React.ReactNode

  if (isArray(item.children)) {
    children = genItems(item.children)
  } else if (isObject(item.children)) {
    children = genItems([item.children])
  } else if (isString(item.children)) {
    children = item.children
  }

  const getField = () => {
    return (
      <FieldComponent
        key={[item.field, index || 0].join('_')}
        type={item.type}
        fieldProps={item.fieldProps || {}}
        effect={item.effect}
        children={children}
      />
    );
  }

  return getField();
};

export default field
