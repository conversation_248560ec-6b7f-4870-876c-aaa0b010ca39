import { PageHeader } from '@smartdeer-ui/pc';
import { history, useParams } from '@umijs/max';
import { Tabs, message } from 'antd';
import { isArray } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import {
  transformDataByKeys,
  transformKeys,
  transformValueToString,
} from '../utils';
import BaseInfoForm from '../comps/base-info-form';


const NewWorkingEmployee: React.FC = (props: any) => {

  const { hideHeader = false, callback, config } = props;

  const params = useParams();
  const entityId = props.entityId || (params.entityId as string);
  const [activeTab, setActiveTab] = useState('');
  const [submitLoading, setSubmitLoading] = useState(false);
  

  const formRefMap = useMemo(() => {
    if (!config?.length) return {};
    const refMap: Record<string, React.RefObject<any>> = {};
    config.forEach((item: any) => {
      refMap[item.key] = React.createRef();
    });
    return refMap;
  }, [config]);

  const submit = async (values: any) => {
    try {
      setSubmitLoading(true);
      let data = { ...values };

      data['system_login_mobile'] =
        data.areaCode + ',' + data.system_login_mobile;
      data['nationalityStr'] = data['nationality'];
      // TODO: 头像最多上传一张
      data.employeePhoto = data.employeePhoto
        ? data.employeePhoto.join(',')
        : undefined;
      delete data.areaCode;
      data.system_admin_permission = isArray(data.system_admin_permission)
        ? data.system_admin_permission.join(',')
        : undefined;
      data.system_admin_deny_permission = isArray(
        data.system_admin_deny_permission,
      )
        ? data.system_admin_deny_permission.join(',')
        : undefined;
      data.system_hrbp = isArray(data.system_hrbp)
        ? data.system_hrbp?.join(',')
        : data.system_hrbp;
      const transformedData = transformDataByKeys(data, transformKeys, config);
      if (!transformedData) {
        setSubmitLoading(false);
        return;
      }
      data = transformedData;
      data = transformValueToString(data);
      await api.run.functionRuntime(
        entityId,
        api.run.FUNCTION_DICT.xAddCorePeople,
        data,
      );
      if (callback) {
        callback();
      } else {
        history.push(`/entity/${entityId}/corehr/employee/working/list`);
      }
    } catch (error: any) {
      message.error(error.message);
    }
    setSubmitLoading(false);
  };

  const onSubmitForm = () => {
    const keys = Object.keys(formRefMap);
    let data = {};
    const errorKeys: string[] = [];
    let passed = 0;
    keys.forEach((key: any) => {
      if (formRefMap[key].current) {
        formRefMap[key].current
          .validateFields()
          .then((values: any) => {
            data = { ...data, ...values };
            passed += 1;
            if (passed === keys.length) {
              submit(data);
            }
          })
          .catch(() => {
            message.error('请完善表单信息');
            errorKeys.push(key);
            setActiveTab(errorKeys[0]);
          });
      }
    });
  };

  const tabItems = useMemo(() => {
    if (!config?.length) return [];
    return config.map((item: any) => {
      const { label, key, json } = item;
      return {
        key,
        label,
        forceRender: true,
        children: (
          <BaseInfoForm
            ref={formRefMap[key]}
            json={json}
            onSubmit={onSubmitForm}
            submitLoading={submitLoading}
          />
        ),
      };
    });
  }, [config, formRefMap, submitLoading]);

  useEffect(() => {
    if (!config?.length) return;
    setActiveTab(config[0].key);
  }, [config]);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  return (
    <div>
      {!hideHeader && (
        <>
          <PageHeader
            title="员工信息"
            onBack={() => {
              history.replace(`/entity/${entityId}/corehr/employee/working/list`)
            }}
            className={`mt-[20px]`}
          />
          <div className="text-[20px] font-bold ml-[18px] mt-[20px]">
            新增在职员工
          </div>
        </>
      )}
      <div className="mt-4 bg-white p-[20px] rounded-4">
        <Tabs
          type="card"
          activeKey={activeTab}
          style={{ marginBottom: 14 }}
          items={tabItems}
          onChange={handleTabChange}
        />
      </div>
    </div>
  );
};

export default NewWorkingEmployee;
