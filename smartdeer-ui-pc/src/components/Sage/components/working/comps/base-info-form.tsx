import { message } from 'antd';
import {
  ForwardRefRenderFunction,
  forwardRef,
  useImperativeHandle,
  useRef,
} from 'react';
import SchemaForm from '../../../../../components/schema-form';
import { FormColumnsType } from '../../../../../components/schema-form/typing';

interface IFormProps {
  submitLoading: boolean;
  onSubmit: (val: any) => void;
  json: FormColumnsType[];
  initialValues?: any;
  formKey?: string;
  formRefs?: any;
}

export const getDefaultMedicalPlanByStaffRank = (staffRank: string) => {
  let medicalPlanValue = '';
  if (staffRank.includes('H1')) {
    medicalPlanValue = '4';
  } else if (staffRank.includes('H2') || staffRank.includes('H3')) {
    medicalPlanValue = '3';
  } else if (staffRank.includes('H4')) {
    medicalPlanValue = '2';
  } else if (staffRank.includes('H5')) {
    medicalPlanValue = '1';
  } else {
    medicalPlanValue = '';
  }
  return medicalPlanValue;
};

const BaseInfoForm: ForwardRefRenderFunction<any, IFormProps> = (
  props,
  ref,
) => {
  const { submitLoading, onSubmit, json, initialValues, formKey, formRefs } =
    props;
  const baseRef = useRef(null);

  useImperativeHandle(ref, () => {
    return baseRef.current;
  });

  const handleSchemaFormFinish = (value: any) => {
    onSubmit(value);
  };

  const handleFailed = () => {
    message.error('请完善表单信息');
  };

  // console.log('json', json)

  return (
    <SchemaForm
      formProps={{
        onValuesChange: (changedValues, allValues) => {
          if (formKey === 'boarding') {
            const staffRank = allValues.staffRank;
            let medicalPlanValue = getDefaultMedicalPlanByStaffRank(staffRank);

            formRefs['base']?.current?.setFieldValue?.(
              'medicalPlan',
              medicalPlanValue,
            );
          }
        },
      }}
      ref={baseRef}
      columns={json}
      size="large"
      layout="vertical"
      labelCol={{ span: 24 }}
      gridProps={{
        col: 2,
      }}
      initialValues={initialValues}
      submitLoading={submitLoading}
      onFinishFailed={handleFailed}
      onFinish={handleSchemaFormFinish}
    />
  );
};

export default forwardRef(BaseInfoForm);
