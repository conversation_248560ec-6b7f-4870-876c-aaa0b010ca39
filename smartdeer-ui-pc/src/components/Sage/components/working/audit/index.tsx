import { history, useParams } from '@umijs/max';
import { <PERSON><PERSON>, Card, DatePicker, Empty, Input, message } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import React, { useEffect, useMemo, useState } from 'react';
import { useFetcher } from '../../../../../components/json-render/version2/hooks/useFetcher';
import PageHeader from '../../../../../components/page-header';
import S from '../../../../../utils/storage';
import styles from './index.less';

dayjs.extend(utc);
dayjs.extend(timezone);

const CONFIG_KEY = 'TIMEZONE';
const AuditEditWorkingEmployee = () => {
  const params = useParams();
  const entityId = params.entityId as string;
  const peopleId = params.id as string;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [entityTimezone, setEntityTimezone] = useState('Asia/Shanghai');
  const [changeReason, setChangeReason] = useState('');
  const { fetcher } = useFetcher({ entityId });

  const formatDate = (val: string, dataType: string) => {
    const timestamp = parseInt(val);
    if (dataType === 'datePicker' && dayjs(timestamp).isValid()) {
      return dayjs(timestamp).format('YYYY-MM-DD HH:ss:mm');
    }
    return val;
  };

  const formatUTCDate = (val: string, dataType: string) => {
    if (dataType === 'datePicker' && dayjs(val).isValid()) {
      const local = dayjs.tz(val, entityTimezone);
      return local.utc().valueOf();
    }
    return val;
  };

  const fetch = async () => {
    const res = await fetcher({
      type: 'function',
      functionKey: 'get_entity_conf_by_key',
      defaultParams: {
        confKey: CONFIG_KEY,
      },
    });
    setEntityTimezone(res.confValue);
  };

  useEffect(() => {
    fetch();
    const data = S.get('people_diff_info') || [];
    const newData = data.map((item: any) => {
      return {
        ...item,
        before: formatDate(item.before, item.dataType),
        after: formatDate(item.after, item.dataType),
        effectTime: dayjs(),
      };
    });
    setDataSource(newData);
  }, []);

  const handleEffectTimeChange = (date: Dayjs) => {
    const data = dataSource.map((item) => {
      return { ...item, effectTime: date };
    });
    setDataSource(data);
  };

  const handleDateChange = (date: Dayjs, key: string) => {
    const data = dataSource.map((item) => {
      if (item.key === key) {
        return {
          ...item,
          effectTime: date,
        };
      }
      return item;
    });
    setDataSource(data);
  };

  const diffDataSource = useMemo(() => {
    return dataSource.filter((item) => !item.hide);
  }, [dataSource]);

  const handleTimezoneChange = (value: string, index: number) => {
    setDataSource(
      dataSource.map((item, i) => {
        if (i === index) {
          return {
            ...item,
            timezone: value,
          };
        }
        return item;
      }),
    );
  };

  const handleChangeReason = (e: any) => {
    setChangeReason(e.target.value);
  };

  const getUtcDate = (time: number, timezone: string) => {
    const formatTime = dayjs(time).format('YYYY-MM-DD');
    const local = dayjs.tz(formatTime, timezone);
    const utcDate = local.utc().valueOf();
    return utcDate;
  };

  const handleUpdate = async () => {
    const timezone = entityTimezone;
    // const timezone = 'America/New_York'
    const data = dataSource.map((item) => {
      return {
        key: item.key,
        value: formatUTCDate(item.after, item.dataType) || '',
        effectTime: getUtcDate(item.effectTime, timezone),
        reason: changeReason,
      };
    });
    // 修改前的值
    const originCommits = dataSource.map((item) => {
      return {
        key: item.key,
        value: item.before || '',
        effectTime: getUtcDate(item.effectTime, timezone),
      };
    });
    // 页面展示的值
    const translateCommits = diffDataSource.map((item) => {
      return {
        key: item.key,
        before: item.before || '',
        after: formatUTCDate(item.after, item.dataType) || '',
        effectTime: getUtcDate(item.effectTime, timezone),
      };
    });
    try {
      await fetcher({
        type: 'function',
        functionKey: 'x_sage_corehr_edit_core_people',
        defaultParams: {
          peopleId,
          commits: data,
          originCommits,
          translateCommits,
          __force__: 'true',
        },
      });

      history.push(
        `/entity/${entityId}/corehr/employee/working/detail/${peopleId}`,
      );
      S.remove('people_diff_info');
    } catch (error: any) {
      message.error(error.message);
    }
  };

  return (
    <div>
      <PageHeader
        title="修改确认"
        onBack={() => {
          history.replace(`/entity/${entityId}/corehr/employee/working/list`);
        }}
        className="mt-[20px]"
      />
      <Card
        title="您已经修改以下字段"
        style={{ marginTop: 10 }}
        extra={
          diffDataSource.length > 0 && (
            <div>
              <span className={`mr-2`}>时区: {entityTimezone}</span>
              <span className="mr-2">统一生效日期</span>
              <DatePicker onChange={handleEffectTimeChange} />
            </div>
          )
        }
      >
        {diffDataSource.length === 0 ? (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ) : (
          <div>
            <div className={`${styles['audit-table-header']}`}>
              <span className={`${styles['audit-table-header-item']}`}>
                字段
              </span>
              <span className={`${styles['audit-table-header-item']}`}>
                修改前
              </span>
              <span className={`${styles['audit-table-header-item']}`}>
                修改后
              </span>
              <span className={`${styles['audit-table-header-item']}`}>
                生效日期
              </span>
              <span className={`${styles['audit-table-header-item']}`}>
                时区
              </span>
            </div>
            <div>
              {diffDataSource.map((item: any, index: number) => {
                return (
                  <div
                    key={index}
                    className={`${styles['audit-table-content']}`}
                  >
                    <span className={`${styles['audit-table-content-item']}`}>
                      {item.title}
                    </span>
                    <span className={`${styles['audit-table-content-item']}`}>
                      {item.before?.toString()}
                    </span>
                    <span className={`${styles['audit-table-content-item']}`}>
                      {item.after.toString()}
                    </span>
                    <div className={`${styles['audit-table-content-item']}`}>
                      <DatePicker
                        value={item.effectTime}
                        onChange={(date) => handleDateChange(date, item.key)}
                      />
                    </div>
                    <span className={`${styles['audit-table-content-item']}`}>
                      {entityTimezone}
                    </span>
                  </div>
                );
              })}
            </div>
            <div className="mt-4">
              <span className="block mb-2 font-semibold">变更理由</span>
              <Input.TextArea
                value={changeReason}
                onChange={handleChangeReason}
              />
            </div>
          </div>
        )}
        {diffDataSource.length > 0 && (
          <div className="text-center mt-6">
            <Button
              type="primary"
              className="text-center"
              onClick={handleUpdate}
            >
              修改
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};

export default AuditEditWorkingEmployee;
