import { Loading, PageHeader } from '@smartdeer-ui/pc';
import { history, useParams } from '@umijs/max';
import { Tabs, message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { isArray, isObject, isString } from 'lodash';
import { transformDataByKeys, transformKeys, transformValueToString } from '../utils';
import BaseInfoForm from '../comps/base-info-form';
import { useFetcher } from '../../../../../components/json-render/version2/hooks/useFetcher';
import S from '../../../../../utils/storage';

const EditWorkingEmployee: React.FC = (props: any) => {
  const { config } = props;
  const params = useParams();
  const entityId = params.entityId as string;
  const peopleId = params.id as string;
  const [activeTab, setActiveTab] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [peopleInfo, setPeopleInfo] = useState<any>();
  const { fetcher } = useFetcher({entityId});

  const formRefMap = useMemo(() => {
    if (!config?.length) return {};
    const refMap: Record<string, React.RefObject<any>> = {};
    config.forEach((item: any) => {
      refMap[item.key] = React.createRef();
    });
    return refMap;
  }, [config]);

  const fetchPeopleInfo = async () => {
    try {
      setIsLoading(true);

     const {rs} =  await fetcher({
      type: 'function',
          functionKey: "x_sage_corehr_get_core_people",
          defaultParams: {
            peopleId,
            toTime: Date.now(),
          }
      })

      const data = JSON.parse(rs.json);

      if (data.system_payroll_allowance) {
        data.system_payroll_allowance = isString(data.system_payroll_allowance) ? JSON.parse(data.system_payroll_allowance) : data.system_payroll_allowance;
      }

      if (data.system_payroll_bonus) {
        data.system_payroll_bonus = isString(data.system_payroll_bonus) ? JSON.parse(data.system_payroll_bonus) : data.system_payroll_bonus;
      }

      if (data.system_payroll_incentive_plan) {
        data.system_payroll_incentive_plan = isString(data.system_payroll_incentive_plan) ? JSON.parse(data.system_payroll_incentive_plan) : data.system_payroll_incentive_plan;
      }

      if (data.system_login_mobile?.includes?.(',')) {
        const [areaCode, mobile] = data.system_login_mobile.split(',');
        data.areaCode = areaCode;
        data.system_login_mobile = mobile;
      }
      data.employeePhoto = data.employeePhoto
        ? data.employeePhoto.split(',')
        : [];
      data.system_admin_permission = data.system_admin_permission
        ? data.system_admin_permission.split(',')
        : [];
      data.system_admin_deny_permission = data.system_admin_deny_permission
        ? data.system_admin_deny_permission.split(',')
        : [];
      data.system_hrbp = data.system_hrbp ? data.system_hrbp.split(',') : [];

      setPeopleInfo({
        ...rs,
        ...data,
      });
    } catch (error: any) {
      message.error(error.message);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchPeopleInfo();
  }, []);

  const findJsonItemByKey = (key: string) => {
    const list = config
      .map((item: any) => {
        return [...item.json];
      })
      .flat();
    let tempKey = key;
    if (tempKey.endsWith('Str')) {
      tempKey = tempKey.slice(0, -3);
    }
    const item = list.find((item: any) => {
      return item.field === tempKey;
    });
    return item;
  };

  const submit = async (values: any) => {
    let data = { ...values };
    data['system_login_mobile'] =
      data.areaCode + ',' + data.system_login_mobile;
    data['nationalityStr'] = data['nationality'];
    data.employeePhoto = data.employeePhoto?.length
      ? data.employeePhoto.join(',')
      : undefined;
    data.system_admin_permission = isArray(data.system_admin_permission)
      ? data.system_admin_permission.join(',')
      : undefined;
    data.system_admin_deny_permission = isArray(
      data.system_admin_deny_permission,
    )
      ? data.system_admin_deny_permission.join(',')
      : undefined;
    data.system_hrbp = isArray(data.system_hrbp)
      ? data.system_hrbp?.join(',')
      : data.system_hrbp;
    delete data.areaCode;
    const transformedData = transformDataByKeys(data, transformKeys, config);
    if (!transformedData) {
      return;
    }
    data = transformedData;
    data = transformValueToString(data);

    try {
      setSubmitLoading(true);

      const {rs: diff} =  await fetcher({
      type: 'function',
          functionKey: "x_sage_corehr_get_core_people_diff",
          defaultParams: {
           ...data,
          peopleId,
          __force__: 'true',
          }
      })
 
      const diffData = diff.map((item: any) => {
        const jsonItem = findJsonItemByKey(item.key);
        const before = isObject(peopleInfo[item.key])
          ? JSON.stringify(peopleInfo[item.key])
          : peopleInfo[item.key];
        // hide为true时，diff时隐藏，当存在fieldStr，或者字段为接口使用而非前端页面使用时设置hide为true
        const hide =
          !!diff.find((diffItem: any) => diffItem.key === item.key + 'Str') ||
          !jsonItem?.title;
        return {
          ...item,
          dataType: jsonItem?.type,
          title: jsonItem?.title,
          before,
          after: data[item.key],
          effectTime: '',
          hide,
        };
      });
      S.set("people_diff_info", diffData);
      history.push(
        `/entity/${entityId}/corehr/employee/working/edit/audit/${peopleId}`,
      );
    } catch (error: any) {
      message.error(error.message);
    }
    setSubmitLoading(false);
  };

  const onSubmitForm = () => {
    const keys = Object.keys(formRefMap);
    let data = {};
    const errorKeys: string[] = [];
    let passed = 0;
    keys.forEach((key: any) => {
      if (formRefMap[key].current) {
        formRefMap[key].current
          .validateFields()
          .then((values: any) => {
            data = { ...data, ...values };
            passed += 1;
            if (passed === keys.length) {
              submit(data);
            }
          })
          .catch(() => {
            message.error('请完善表单信息');
            errorKeys.push(key);
            setActiveTab(errorKeys[0]);
          });
      }
    });
  };

  const tabItems = useMemo(() => {
    if (!config?.length) return [];
    return config.map((item: any) => {
      const { label, key, json } = item;
      return {
        key,
        label,
        forceRender: true,
        children: (
          <BaseInfoForm
            ref={formRefMap[key]}
            json={json}
            initialValues={peopleInfo}
            onSubmit={onSubmitForm}
            submitLoading={submitLoading}
          />
        ),
      };
    });
  }, [config, formRefMap, peopleInfo, submitLoading]);

  useEffect(() => {
    if (!config?.length) return;
    setActiveTab(config[0].key);
  }, [config]);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <>
      <PageHeader
        title="编辑员工"
        onBack={() => {
          history.replace(`/entity/${entityId}/corehr/employee/working/list`)
        }}
        className={`mt-[20px]`}
      />

      <div className="mt-4 bg-white p-[20px] rounded-4">
        <Tabs
          type="card"
          activeKey={activeTab}
          style={{ marginBottom: 14 }}
          items={tabItems}
          onChange={handleTabChange}
        />
      </div>
    </>
  );
};

export default EditWorkingEmployee;
