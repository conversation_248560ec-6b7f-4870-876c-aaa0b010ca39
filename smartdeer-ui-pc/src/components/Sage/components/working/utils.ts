import { mathjs } from '../../../../utils/mathjs';
import { message } from 'antd';

import { isArray, isNil, isNumber, isObject } from 'lodash';

const transformValueToString = (data: Record<string, any>) => {
  let newData: Record<string, any> = {};
  Object.keys(data).forEach((key) => {
    let value = data[key];
    if (isObject(value)) {
      newData[key] = JSON.stringify(value);
    } else if (isNumber(value)) {
      newData[key] = value.toString();
    } else {
      newData[key] = value;
    }
  });
  return newData;
};

const transformKeys = [
  'system_organization',
  'system_position',
  'system_report',
  'system_cost_center',
];

const isFieldRequired = (conf: any[], key: string) => {
  let json: any[] = [];
  conf.forEach((item) => {
    json = [...json, ...item.json];
  });
  const fieldsConf = json.filter((item) => item?.field?.includes?.(key));
  const fieldRequiredIndex = fieldsConf.findIndex(
    (item) => item?.labelProps?.rules?.length > 0,
  );
  return fieldRequiredIndex > -1;
};

const validateKeyMap: Record<string, string> = {
  system_cost_center: 'CostCenter',
  system_organization: 'Department',
};

// 数据格式转换，将特定字段转成接口接收的格式
// 例如：{system_report_0_id: 'xxx', system_report_0_percentage: 70} -> {system_report: [{id: 'xxx', percentage: 0.7}]}
const transformDataByKeys = (data: any, keys: string[], config: any[]) => {
  let errors: string[] = [];
  keys.forEach((transformKey) => {
    let temp = Object.keys(data).filter(
      (key) => key.includes(transformKey) && !key.endsWith('Str'),
    );
    let result: Record<string, any>[] = [];
    temp.forEach((tempKey) => {
      if (data[tempKey]) {
        let keys = tempKey.split('_');
        let key = keys[keys.length - 1];
        let index: number = Number(keys[keys.length - 2]);
        let value = data[tempKey];
        if (key === 'percentage') {
          value = mathjs(Number(value), 100, '/');
          value = Number(value.toFixed(2));
          value = isNaN(value) ? 0 : value;
        }
        result[index] = {
          ...result[index],
          [key]: !isNil(value) ? value.toString() : undefined,
        };

        data[tempKey] = !isNil(data[tempKey])
          ? data[tempKey].toString()
          : undefined;
      } else {
        data[tempKey] = '';
      }
    });
    // 校验 result
    const title = validateKeyMap[transformKey];
    if (validateKeyMap[transformKey] && isFieldRequired(config, transformKey)) {
      const errorIndex = result.findIndex((item) => !item?.id);
      if (errorIndex > -1 && !errors.includes(title)) {
        errors.push(title);
        message.error(
          `${title} ${errorIndex + 1} 信息未填写完整，请检查后再提交`,
        );
      }

      // const errorPercentIndex = result.findIndex(
      //   (item) => !item?.percentage || item?.percentage === '0',
      // );
      // if (errorPercentIndex > -1 && !errors.includes(title)) {
      //   errors.push(title);
      //   message.error(
      //     `${title} ${errorPercentIndex + 1} Percentage 不能为0，请检查后再提交`,
      //   );
      // }
      // let totalPercent = 0;
      // for (let item of result) {
      //   if (item?.percentage) {
      //     let percentage = mathjs(item.percentage, 100, '*');
      //     totalPercent = Number(mathjs(percentage, totalPercent, '+'));
      //   }
      // }
      // totalPercent = mathjs(totalPercent, 100, '/') as number;

      // if (Number(totalPercent) !== 1 && !errors.includes(title)) {
      //   errors.push(title);
      //   message.error(`${title} Percentage 之和须为 100%，请检查后再提交`);
      // }
    } else if (
      validateKeyMap[transformKey] &&
      !isFieldRequired(config, transformKey)
    ) {
      // if (isArray(result) && result.length > 0) {
      //   let hasError = !result.every(
      //     (item) =>
      //       item.hasOwnProperty('id') && item.hasOwnProperty('percentage'),
      //   );
      //   if (hasError && !errors.includes(title)) {
      //     errors.push(title);
      //     message.error(
      //       `${title} 和 Percentage 需要同时填写/不填写，请检查后再提交`,
      //     );
      //   }
      //   let totalPercent = 0;
      //   for (let item of result) {
      //     if (item?.percentage) {
      //       let percentage = mathjs(item.percentage, 100, '*');
      //       totalPercent = Number(mathjs(percentage, totalPercent, '+'));
      //     }
      //   }
      //   totalPercent = mathjs(totalPercent, 100, '/') as number;
      //   if (Number(totalPercent) !== 1 && !errors.includes(title)) {
      //     errors.push(title);
      //     message.error(`${title} Percentage 之和须为 100%，请检查后再提交`);
      //   }
      // }
    }
    data[transformKey] = JSON.stringify(result);
  });
  return errors.length ? false : data;
}

export {
  transformValueToString,
  transformKeys,
  isFieldRequired,
  validateKeyMap,
  transformDataByKeys
};