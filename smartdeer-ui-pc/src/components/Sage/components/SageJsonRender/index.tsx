import {ConfigContext, JsonRender} from '@smartdeer-ui/pc';
import {message} from 'antd';
import React, {useEffect, useRef, useState} from 'react';
import {isArray, isObject} from "lodash";
import {useJsonFetcher} from "../../../json-render/version2/hooks/useFetcher";
import {useParamsDict} from "../../../../utils/hooks";
import {replaceVariablesInTemplate} from "../../../../utils/replaceVariablesInTemplate";
import {convertFunctionToString, resolveRefConfig} from "../../../json-render/utils";
import {cachedSwrFetcher, swrDownloadFile} from "../../../../utils/swrFetcher";
import {ProTable} from "@ant-design/pro-components";
import custom from '../custom'


interface JsonRenderResponse {
  json: string;
  innerScript: string;
}

let buildIns: Record<string, (props: any) => React.ReactNode> = {};

const processedEvents = (events: any[]) => {
  if (!events) {
    return [];
  }
  return events.map((item: any) => {
    if (item.code && typeof item.code === 'function') {
      return {
        ...item,
        code: convertFunctionToString(item.code),
      };
    }
    return item;
  });
};

/**
 * BfmJsonRender - 智能页面渲染组件
 *
 * 功能说明：
 * 1. 支持远程配置和本地mock配置的智能页面渲染
 * 2. 统一管理业务自定义组件的注册
 * 3. 处理页面配置的获取、解析和状态管理
 *
 * @param confKey - 页面配置标识
 * @param productCode - 产品编码
 * @param mock - 本地mock配置，用于开发调试
 */
const SageJsonRender = ({
                          confKey,
                          tableConfKey,
                          productCode,
                          props,
                          confEffect,
                          confObject,
                          confRefObject,
                          components = {},
                          consts = {},
                          builtInComponents = {},
                          defaultRender,
                          showMessage = true
                        }: any) => {

  const [init, setInit] = useState(false)

  const tableActionRef = useRef(null);

  const {jsonFetcher, loading} = useJsonFetcher();
  const [json, setJson] = useState<string>('{}');
  const [events, setEvents] = useState<any[]>([]);
  const [initialState, setInitialState] = useState<any>({});

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict({});
  let appFunctionApi = configContext.appFunctionApi;
  appFunctionApi = replaceVariablesInTemplate(paramsDict, appFunctionApi!);

  buildIns = {
    ...buildIns,
    ...builtInComponents,
  };

  const localComponents = confKey ? buildIns[confKey] : undefined;

  const fetchTableConf = async () => {
    try {
      const res = await jsonFetcher({
        type: 'function',
        functionKey: 'x_sage_memory_get_content',
        defaultParams: {
          key: tableConfKey
        },
      })
      const {content} = res.json.jsonRenderData
      setJson(content.json);
      setEvents(content.innerScripts)
    } catch (error) {
      if (showMessage) message.error(`获取配置失败: ${confKey}`);
      setJson('');
      setEvents([]);
    } finally {
      setInit(true)
    }
  }

  const fetchCommonConf = async () => {
    try {
      const {data} = await cachedSwrFetcher(appFunctionApi, 'POST', {
        functionKey: 'x_sage_get_smd_smart_page_by_key',
        params: {
          pageKey: confKey,
        },
      });

      const {json, innerScript} = data?.rs || {};
      const script = innerScript ? JSON.parse(innerScript) : [];
      setJson(json);
      setEvents(script);
    } catch (error) {
      if (showMessage) message.error(`获取配置失败: ${confKey}`);
      setJson('');
      setEvents([]);
    } finally {
      setInit(true)
    }
  };

  const fetchServiceConf = async () => {
    try {
      const {rs} = (await jsonFetcher({
        type: 'function',
        functionKey: 'x_sage_get_service_ability_smart_page_conf',
        defaultParams: {
          productCode,
          confKey,
          areaCode: 'all',
          confType: '6',
        },
      })) as { rs: JsonRenderResponse };
      const {json, innerScript} = rs;
      const script = innerScript ? JSON.parse(innerScript) : [];
      setJson(json);
      setEvents(script);
    } catch (error) {
      if (showMessage) message.error(`获取配置失败: ${confKey}`);
      setJson('{}');
      setEvents([]);
    } finally {
      setInit(true)
    }
  };

  const fetchConfEffect = async () => {
    try {
      const {rs} = (await jsonFetcher({
        ...confEffect,
      })) as { rs: JsonRenderResponse };
      const {json, innerScript} = rs;
      const script = JSON.parse(innerScript);
      setJson(json);
      setEvents(script);
    } catch (error) {
      if (showMessage) message.error(`获取配置失败: ${confEffect?.functionKey}`);
      setJson('{}');
      setEvents([]);
    } finally {
      setInit(true)
    }
  };

  useEffect(() => {

    // 自定义组件优先使用
    if (!!localComponents || props?.loadLocal) {
      return;
    }
    // 使用配置对象(可用于本地调试)
    if (confObject) {
      console.log("confObject", confObject);

      if (!confObject.components) {
        message.error('配置对象中缺少components');
        return;
      }
      if (confObject.events && !isArray(confObject.events)) {
        message.error('配置对象中events必须是数组格式');
        return;
      }
      setJson(JSON.stringify({components: confObject.components}));
      setEvents(processedEvents(confObject.events));
      setInitialState(confObject.initialState || {});
      return;
    }
    // 如果存在confRefObject，解析confRefObject，可用于本地调试，使用场景是复杂的含有ref引用的配置
    if (confRefObject) {
      const {conf, confMap} = confRefObject;
      const refConfig = resolveRefConfig(conf, confMap);
      setJson(refConfig.json);
      setEvents(refConfig.events);
      // 下方打印结果可以直接粘贴到智能页面 mock 中生成远程配置
      console.log({
        events: refConfig.events,
        components: JSON.parse(refConfig.json).components,
      });
      return;
    }

    if (productCode && confKey) {
      fetchServiceConf();
      return;
    }

    // 如果存在confKey，获取通用智能页面配置
    if (confKey) {
      fetchCommonConf();
      return;
    }
    // 如果存在confEffect，根据confEffect获取配置
    if (confEffect) {
      fetchConfEffect();
      return;
    }

    if (tableConfKey) {
      fetchTableConf();
      return;
    }

  }, [
    tableConfKey,
    confKey,
    productCode,
    confObject,
    confEffect,
    confRefObject,
    !!localComponents,
  ]);

  if (localComponents) {
    return localComponents(props);
  }

  if (init && !loading && (!json || json === '{}')) {
    return defaultRender;
  }

  return (
    <>
      <JsonRender
        version="2"
        json={json}
        events={events || []}
        customComponents={{
          SageJsonRender: confProps => <SageJsonRender {...confProps} props={{...confProps.props, ...props}}/>,
          ProTable: (props: any) => {
            return <ProTable {...props} actionRef={tableActionRef}/>;
          },
          ...custom,
          ...components,
        }}
        consts={{
          productCode,
          ...consts,
        }}
        props={{
          isObject: isObject,
          tableActionRef,
          swrDownloadFile: swrDownloadFile,
          ...props,
        }}
        initialState={initialState}
      />
    </>
  );
};

export default SageJsonRender;
