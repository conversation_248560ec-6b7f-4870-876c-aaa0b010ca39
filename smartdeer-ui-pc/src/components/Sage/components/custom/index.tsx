
import React from 'react';

import {
  ProField,
  ProForm,
  ProFormText,
  ProFormSelect,
  ProFormDatePicker,
  ProFormDigit,
  ProFormTextArea,
  ModalForm
} from '@ant-design/pro-components';
import { Descriptions, DatePicker} from 'antd';

import UploadButton from "../../../../../../src/pages/corehr/comps/upload-button";
import FieldQuill from "../../../schema-form/field-components/quill-wrapper";
import NewWorkingEmployee from '../working/new';
import EditWorkingEmployee from '../working/edit';
import AuditEditWorkingEmployee from '../working/audit';

const RangePicker = DatePicker.RangePicker;

const DescriptionsItem = Descriptions.Item;

export default {
  FieldQuill: (confProps: any) => <FieldQuill {...confProps} />,
  UploadButton: (props: any) => <UploadButton {...props} />,
  ProField: (props: any) => <ProField {...props} />,
  ProForm: ProForm,
  ProFormText: ProFormText,
  ProFormSelect: ProFormSelect,
  ProFormDigit: ProFormDigit,
  ProFormTextArea: ProFormTextArea,
  ProFormDatePicker: ProFormDatePicker,
  ModalForm: ModalForm,
  RangePicker: RangePicker,
  FormGroup: ProForm.Group,
  DescriptionsItem: (props: any) => <DescriptionsItem {...props} />,
  NewWorkingEmployee: NewWorkingEmployee,
  EditWorkingEmployee: EditWorkingEmployee,
  AuditEditWorkingEmployee: AuditEditWorkingEmployee,
}
