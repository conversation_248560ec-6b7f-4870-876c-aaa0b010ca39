import type { SchemaTabsProps } from '@smartdeer-ui/pc';

export const columns: SchemaTabsProps['columns'] = [
  {
    label: 'Basic Information',
    key: 'base',
    type: 'schemaPage',
    columns: [
      {
        type: 'container',
        children: [
          {
            type: 'h3',
            props: {
              style: {
                fontSize: '20px',
                marginBottom: '14px',
              },
            },
            children: 'Personal Infomation',
          },
          {
            type: 'div',
            props: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, minmax(0, 1fr))',
                rowGap: '14px',
              },
            },
            children: [
              {
                type: 'listItem',
                props: {
                  title: 'Staff no.',
                  description:
                    '*{json.system_employee_code | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Surname',
                  description: '*{json.surname | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'First Name',
                  description: '*{json.firstName | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Chinese Name',
                  description: '*{json.chineseName | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Name on HKID',
                  description: '*{json.nameOnHKID | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Employee Name',
                  description: '*{json.employeeName | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Preferred Name',
                  description: '*{json.preferredName | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Company Email',
                  description: '*{json.system_login_email | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Personal Email',
                  description: '*{json.personalEmail | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Staff Rank',
                  description: '*{json.staffRank | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Special Staff Pool',
                  description: '*{json.specialStaffPool | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Contract Entity',
                  description:
                    '*{system_contract_entity_str | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Permission Whitelist',
                  description:
                    '*{json.system_admin_permission | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Permission Blacklist',
                  description:
                    '*{json.system_admin_deny_permission | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Entity Group Key',
                  description:
                    '*{json.system_entity_group_key | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Tags',
                  description: '*{json.system_tag | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Mobile',
                  description:
                    '*{json.system_login_mobile | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Working Location',
                  description: '*{json.workingLocation | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Working Mode',
                  description: '*{json.workingMode | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Contract Type',
                  description: '*{json.contractType | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Employment Type',
                  description: '*{json.employmentTypeStr | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Join Date',
                  description: '*{json.joinDate | formatDate(YYYY-MM-DD)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Original Join Date',
                  description:
                    '*{json.originalJoinDate | formatDate(YYYY-MM-DD)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Probation Period',
                  description: '*{json.probationPeriod | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Probation Period Date',
                  description:
                    '*{json.probationPeriodDate | formatDate(YYYY-MM-DD)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Onboarding Time',
                  description:
                    '*{json.system_onboarding_time | formatDate(YYYY-MM-DD HH:ss:mm)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Contract Start Time',
                  description:
                    '*{json.system_contract_start_time | formatDate(YYYY-MM-DD HH:ss:mm)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Contract End Time',
                  description:
                    '*{json.system_contract_end_time | formatDate(YYYY-MM-DD HH:ss:mm)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'MPF Enrolment Date',
                  description:
                    '*{json.mpfEnrolmentDate | formatDate(YYYY-MM-DD)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'ER First Contribution',
                  description:
                    '*{json.erFirstContribution | formatDate(YYYY-MM-DD)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'EE First Contribution',
                  description:
                    '*{json.eeFirstContribution | formatDate(YYYY-MM-DD)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Date of Birth',
                  description: '*{json.dateOfBirth | formatDate(YYYY-MM-DD)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Age',
                  description: '*{json.age | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'HKID',
                  description: '*{json.hkid | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'SGID/Malay ID/Other ID',
                  description: '*{json.sgidMalayIdOtherId | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'CNID',
                  description: '*{json.cnid | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Japanese ID',
                  description: '*{json.japaneseIdNumber | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Singapore ID',
                  description: '*{json.singaporeIdNumber | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Passport',
                  description: '*{json.passport | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Nationality',
                  description: '*{json.nationality | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Passport Expiry Date',
                  description:
                    '*{json.passportExpiryDate | formatDate(YYYY-MM-DD)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Gender',
                  description: '*{json.genderStr | displayIfEmpty(-)}',
                },
              },
            ],
          },
        ],
      },
    ],
  },
  {
    label: 'Account',
    key: 'account',
    type: 'schemaPage',
    columns: [
      {
        type: 'container',
        children: [
          {
            type: 'h3',
            props: {
              style: {
                fontSize: '20px',
                marginBottom: '14px',
              },
            },
            children: 'Account Information',
          },

          {
            type: 'confirm',
            props: {
              title: 'Add Account',
              description: 'Are you sure you want to add the account?',
              style: {
                position: 'absolute',
                right: '20px',
                top: '20px',
              },
            },
            effect: {
              show: '*{system_has_account}===false',
              fetch: {
                type: 'function',
                functionKey: 'x_sage_corehr_add_account',
                defaultParams: {
                  id: '*{peopleId}',
                },
              },
              onSuccess: {
                type: 'retry',
              },
            },
          },
          {
            type: 'confirm',
            props: {
              title: 'Remove Account',
              description: 'Are you sure you want to remove the account?',
              style: {
                position: 'absolute',
                right: '20px',
                top: '20px',
              },
            },
            effect: {
              show: '*{system_has_account}===true',
              fetch: {
                type: 'function',
                functionKey: 'x_sage_corehr_remove_account',
                defaultParams: {
                  id: '*{peopleId}',
                },
              },
              onSuccess: {
                type: 'retry',
              },
            },
          },
          {
            type: 'div',
            props: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, minmax(0, 1fr))',
                rowGap: '14px',
              },
            },
            children: [
              {
                type: 'listItem',
                props: {
                  title: 'Login Mobile',
                  description: '*{system_login_mobile | displayIfEmpty(-)}',
                },
              },
              {
                type: 'listItem',
                props: {
                  title: 'Login Email',
                  description: '*{system_login_email | displayIfEmpty(-)}',
                },
              },
            ],
          },
        ],
      },
    ],
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'x_sage_corehr_get_account',
        defaultParams: {
          id: '*{peopleId}',
        },
      },
    },
  },
  {
    label: 'Permission',
    key: 'permission',
    type: 'schemaPage',
    columns: [
      {
        type: 'container',
        children: [
          {
            type: 'h3',
            props: {
              style: {
                fontSize: '20px',
                marginBottom: '14px',
              },
            },
            children: 'Permission',
          },
          {
            type: 'crudTable',
            props: {
              columns: [
                {
                  title: 'ID',
                  key: 'id',
                },
                {
                  title: 'Name',
                  key: 'name',
                },
                {
                  title: 'Description',
                  key: 'description',
                },
              ],
              data: 'permissions',
            },
          },
          {
            type: 'h3',
            props: {
              style: {
                fontSize: '20px',
                marginBottom: '14px',
                marginTop: '20px',
              },
            },
            children: 'Roles',
          },
          {
            type: 'crudTable',
            props: {
              columns: [
                {
                  title: 'ID',
                  key: 'id',
                },
                {
                  title: 'Name',
                  key: 'name',
                },
                {
                  title: 'Description',
                  key: 'description',
                },
              ],
              data: 'roles',
            },
          },
        ],
      },
    ],
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'x_sage_corehr_get_core_people_permission',
        defaultParams: {
          id: '*{peopleId}',
        },
      },
    },
  },
  {
    label: 'Leave',
    key: 'leave',
    type: 'schemaPage',
    columns: [
      {
        type: 'container',
        children: [
          {
            type: 'h3',
            props: {
              style: {
                fontSize: '20px',
                marginBottom: '14px',
              },
            },
            children: 'Leave Balence',
          },
          {
            type: 'crudTable',
            props: {
              columns: [
                {
                  title: 'Leave Name',
                  key: 'groupName',
                  type: 'template',
                  template: '<div>*{groupName | displayIfEmpty(-)}</div>',
                },
                {
                  title: 'Used',
                  key: 'had',
                  type: 'template',
                  template: '<div>*{had | displayIfEmpty(-)}</div>',
                },
                {
                  title: 'Balance',
                  key: 'left',
                  type: 'template',
                  template: '<div>*{left | displayIfEmpty(-)}</div>',
                },
                {
                  title: 'Unit',
                  key: 'unitStr',
                  type: 'template',
                  template: '<div>*{unitStr | displayIfEmpty(-)}</div>',
                },
                {
                  title: 'Action',
                  key: 'action',
                  type: 'modal',
                  props: {
                    width: '700',
                    title: 'Leave Details',
                    componentContent: 'leaves',
                    componentName: 'crud-table',
                    tablecColumns: [
                      {
                        title: 'Leave Name',
                        key: 'leaveStr',
                        type: 'template',
                        template: '<div>*{leaveStr | displayIfEmpty(-)}</div>',
                      },
                      {
                        title: 'Quota',
                        key: 'total',
                        type: 'template',
                        template: '<div>*{countStr | displayIfEmpty(-)}</div>',
                      },
                      {
                        title: 'Effective start time',
                        key: 'effectStartTime',
                        type: 'template',
                        template:
                          '<div>*{effectStartTime |formatDate(YYYY-MM-DD)}</div>',
                      },
                      {
                        title: 'Effective end time',
                        key: 'effectEndTime',
                        type: 'template',
                        template:
                          '<div>*{effectEndTime |formatDate(YYYY-MM-DD)}</div>',
                      },
                      {
                        title: 'Expire Time',
                        key: 'expireTime',
                        type: 'template',
                        template:
                          '<div>*{expireTime |formatDate(YYYY-MM-DD)}</div>',
                      },
                      {
                        title: 'Reasons',
                        key: 'reasons',
                        type: 'template',
                        template: '<div>*{reasons | displayIfEmpty(-)}</div>',
                      },
                    ],
                  },
                },
              ],
              data: 'dataInfo',
            },
          },
        ],
      },
    ],
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'x_gs_corehr_get_core_people_leave_mine',
        defaultParams: {
          id: '*{peopleId}',
        },
      },
    },
  },
  {
    label: 'History Log',
    key: 'log',
    type: 'schemaPage',
    columns: [
      {
        type: 'container',
        children: [
          {
            type: 'h3',
            props: {
              style: {
                fontSize: '20px',
                marginBottom: '14px',
              },
            },
            children: 'History Log',
          },
          {
            type: 'crudTable',
            props: {
              columns: [
                {
                  title: 'Employee Name',
                  key: 'employeeName',
                },
                {
                  title: 'HRBP',
                  key: 'system_hrbp',
                },
                {
                  title: 'Create Time',
                  key: 'createTime',
                  type: 'template',
                  template: '<div>*{createTime |formatDate(YYYY-MM-DD)}</div>',
                },
                {
                  title: 'Differences',
                  key: 'commit',
                  type: 'template',
                  template: '<div>*{commit}</div>',
                },
              ],
              data: 'dataInfo',
              pagination: true,
            },
          },
        ],
      },
    ],
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'sage_corehr_search_core_people_history_log',
        defaultParams: {
          current: 1,
          limit: 20,
          size: 20,
          searchInfo: {
            searchItems: [
              {
                key: 'corePeopleId',
                value: '*{peopleId}',
                action: 'eq',
              },
            ],
          },
        },
      },
    },
  },
  {
    label: 'Operation Record',
    key: 'operationRecord',
    type: 'schemaPage',
    columns: [
      {
        type: 'container',
        children: [
          {
            type: 'h3',
            props: {
              style: {
                fontSize: '20px',
                marginBottom: '14px',
              },
            },
            children: 'Operation Record',
          },
          {
            type: 'crudTable',
            props: {
              columns: [
                {
                  title: 'Create Time',
                  key: 'createTime',
                  type: 'template',
                  template: '<div>*{createTime|formatDate(YYYY-MM-DD)}</div>',
                  width: '300px',
                },
                {
                  title: 'Effect Time',
                  key: 'effectTime',
                  type: 'template',
                  template: '<div>*{effectTime|formatDate(YYYY-MM-DD)}</div>',
                  width: '300px',
                },
                {
                  title: 'Important',
                  key: 'effect',
                  type: 'template',
                  template:
                    "<div style='width:10px;height:10px;border-radius:5px;background-color:*{importantColor};'></div>",
                  width: '200px',
                },
                {
                  title: 'Salary',
                  key: 'effect',
                  type: 'template',
                  template:
                    "<div style='width:10px;height:10px;border-radius:5px;background-color:*{salaryChangeColor};'></div>",
                  width: '200px',
                },
                {
                  title: 'Differences',
                  key: 'commit',
                  type: 'template',
                  template: '<div>*{commit}</div>',
                },
              ],
              data: 'dataInfo',
              pagination: true,
            },
          },
        ],
      },
    ],
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'sage_corehr_search_people_operate',
        defaultParams: {
          current: 1,
          pageSize: 20,
          limit: 20,
          entityId: '*{entityId}',
          searchInfo: {
            searchItems: [
              {
                key: 'entityId',
                value: '*{entityId}',
                action: 'eq',
              },
              {
                key: 'peopleId',
                value: '*{peopleId}',
                action: 'eq',
              },
            ],
            orders: [
              {
                key: 'createTime',
                asc: 'desc',
              },
            ],
          },
        },
        transformKey: 'transformSearchPeopleOperate',
        transformIndex: 'dataInfo',
      },
    },
  },
];
