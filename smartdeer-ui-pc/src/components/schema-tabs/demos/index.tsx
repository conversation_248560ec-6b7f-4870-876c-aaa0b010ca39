import { SchemaTabs } from '@smartdeer-ui/pc';
import dayjs from 'dayjs';
import React from 'react';
import { columns } from './columns';

const dataSource = {
  firstChildGenderStr: '',
  specialStaffPoolStr: '',
  system_cost_center_3_id: '',
  genderStr: '',
  system_payroll_groupStr: 'Hong Kong',
  system_report_0_idStr: '',
  system_organization_0_idStr: '錢包管理',
  system_position: '[]',
  system_organization_0_str: '錢包管理',
  system_admin_deny_permissionStr: '',
  system_cost_center_9_percentage: '',
  system_contract_start_time: '1719964800000',
  system_cost_center_9_idStr: '',
  id: 1719,
  system_cost_center_6_idStr: '',
  system_onboarding_time: '1719964800000',
  system_cost_center_6_percentage: '',
  system_cost_center_8_id: '',
  system_service_status: '1',
  uniqueKey: '7a537efc251845b19bb1758fa5271ecd',
  system_contract_entity_str: 'Tokyo Hash Co.,Ltd',
  system_organization_2_percentage: '',
  system_organization_0_id: '221',
  system_cost_center_3_idStr: '',
  system_cost_center_0_str: 'SGITECH',
  system_report_1_id: '',
  system_cost_center_0_percentage: '100',
  deleteTime: 0,
  system_entity_group_keyStr: '',
  system_report: '[]',
  system_cost_center_0_id: '76',
  discretionaryBonusStr: '',
  status: 1,
  system_cost_center_5_id: '',
  system_cost_center_7_idStr: '',
  system_cost_center_8_percentage: '',
  system_organization_1_id: '',
  groupKey: 'hashkey',
  surname: 'tianjia',
  system_organization_1_percentage: '',
  secondChildGenderStr: '',
  system_cost_center_1_idStr: '',
  system_admin_permissionStr: '',
  system_payroll_basic: '100',
  updateTime: 1721107003313,
  system_cost_center_8_idStr: '',
  system_hrbp_str: 'tianjia',
  deleted: 0,
  latestVersion: '075a79af',
  createTime: 1721106969414,
  system_cost_center_2_id: '',
  system_hrbpStr: 'tianjia (<EMAIL>)',
  thirdChildGenderStr: '',
  system_organization_0_percentage: '100',
  system_cost_center_2_idStr: '',
  system_cost_center_7_id: '',
  system_cost_center_4_percentage: '',
  system_organization: [
    {
      percentage: '1',
      id: '221',
    },
  ],
  uuid: '7a537efc251845b19bb1758fa5271ecd',
  system_report_0_id: '',
  fifthChildGenderStr: '',
  system_hrbp: '1719',
  employmentTypeStr: '',
  entityName: 'HashKey',
  system_cost_center_1_percentage: '',
  system_position_0_id: '',
  system_organization_1_idStr: '',
  employeeName: 'tianjia',
  system_contract_entityStr: 'Tokyo Hash Co.,Ltd',
  system_cost_center_4_id: '',
  system_report_1_idStr: '',
  firstName: 'tianjia',
  probationPeriodDate: '1720540800000',
  system_cost_center_7_percentage: '',
  areaCodeStr: '中国大陆',
  system_cost_center_9_id: '',
  system_cost_center_0_idStr: 'SGITECH',
  system_login_email: '<EMAIL>',
  system_cost_center_4_idStr: '',
  system_position_0_idStr: '',
  json: '{"system_organization_0_percentage":"100","firstChildGenderStr":"","specialStaffPoolStr":"","system_cost_center_3_id":"","system_cost_center_2_idStr":"","genderStr":"","system_cost_center_7_id":"","system_payroll_groupStr":"Hong Kong","system_cost_center_4_percentage":"","system_organization":"[{\\"id\\":\\"221\\",\\"percentage\\":\\"1\\"}]","system_report_0_idStr":"","system_organization_0_idStr":"錢包管理","system_position":"[]","system_report_0_id":"","fifthChildGenderStr":"","system_hrbp":"1719","system_admin_deny_permissionStr":"","employmentTypeStr":"","system_cost_center_9_percentage":"","system_contract_start_time":"1719964800000","system_cost_center_9_idStr":"","system_cost_center_6_idStr":"","system_onboarding_time":"1719964800000","system_cost_center_1_percentage":"","system_position_0_id":"","system_organization_1_idStr":"","employeeName":"tianjia","system_contract_entityStr":"Tokyo Hash Co.,Ltd","system_cost_center_4_id":"","system_cost_center_6_percentage":"","system_cost_center_8_id":"","system_service_status":"1","system_organization_2_percentage":"","system_organization_0_id":"221","system_cost_center_3_idStr":"","system_report_1_idStr":"","system_report_1_id":"","system_cost_center_0_percentage":"100","firstName":"tianjia","probationPeriodDate":"1720540800000","system_cost_center_7_percentage":"","system_entity_group_keyStr":"","system_report":"[]","system_cost_center_0_id":"76","areaCodeStr":"中国大陆","discretionaryBonusStr":"","system_cost_center_5_id":"","system_cost_center_7_idStr":"","system_cost_center_8_percentage":"","system_cost_center_9_id":"","system_cost_center_0_idStr":"SGITECH","system_login_email":"<EMAIL>","system_organization_1_id":"","system_cost_center_4_idStr":"","system_position_0_idStr":"","surname":"tianjia","system_cost_center":"[{\\"id\\":\\"76\\",\\"percentage\\":\\"1\\"}]","system_employee_code":"testbinding","system_cost_center_1_id":"","system_cost_center_5_percentage":"","system_organization_1_percentage":"","system_organization_2_idStr":"","secondChildGenderStr":"","system_cost_center_1_idStr":"","system_admin_permissionStr":"","system_cost_center_6_id":"","system_payroll_group":"9","system_payroll_basic":"100","system_cost_center_8_idStr":"","system_cost_center_2_percentage":"","system_login_mobile":"+86,18645174599","fourthChildGenderStr":"","system_contract_entity":"14","system_cost_center_5_idStr":"","system_organization_2_id":"","chineseName":"tianjia","staffRank":"1-2","system_cost_center_2_id":"","system_hrbpStr":"tianjia (<EMAIL>)","system_cost_center_3_percentage":"","thirdChildGenderStr":"","maritalStr":""}',
  system_cost_center: '[{"id":"76","percentage":"1"}]',
  system_employee_code: 'testbinding',
  system_cost_center_1_id: '',
  system_cost_center_5_percentage: '',
  system_organization_2_idStr: '',
  system_cost_center_6_id: '',
  system_payroll_group: '9',
  entityId: 51,
  system_cost_center_2_percentage: '',
  system_login_mobile: '+86,18645174599',
  fourthChildGenderStr: '',
  system_contract_entity: '14',
  system_cost_center_5_idStr: '',
  system_organization_2_id: '',
  chineseName: 'tianjia',
  staffRank: '1-2',
  system_payroll_group_str: 'Hong Kong',
  system_cost_center_3_percentage: '',
  maritalStr: '',
};

dataSource.json = JSON.parse(dataSource.json);

const transformSearchPeopleOperate = (dataInfo: any) => {
  const tryDayjs = (value: any) => {

    const timestampStart = 1000000;
    try {
      const date = dayjs(Number(value));
      if (!date.isValid()) {
        console.log(value)
        return value;
      }

      if (typeof value === 'number' || date.valueOf() < timestampStart) {
        if (value < timestampStart) {
          return value;
        }
      }

      return date.format('YYYY-MM-DD');
    } catch (e) {
      return value;
    }
  };
  const list = dataInfo.map((item: any) => {
    let parsedJson = JSON.parse(item?.translateJson || '[]');
    const result = Object.entries(parsedJson)
      .map(([key, value]:any) => {
        let val;
        try {
          val = JSON.parse(value);
          val = val?.after || '-'
        }catch(err) {
          val = value || '-'
        }
        let originalKey = key.endsWith('Str') ? key.slice(0, -3) : key;
        return `<div class="flex" style="margin-top: 10px"><div style="width: 250px;font-weight: bolder">${originalKey}</div> <div style="width: 150px;">${tryDayjs(val)}</div></div>`
      })
      .join('');
    let importantColor = '#ccc'
    if (item.important.toString() === '1') {
      importantColor = '#f33a3a'
    }

    let salaryChangeColor = '#ccc'
    if (item.salaryChange.toString() === '1') {
      salaryChangeColor = '#f33a3a'
    }
    item.importantColor = importantColor;
    item.salaryChangeColor = salaryChangeColor;

    return {
      ...item,
      commit: result
    };
  })
  return list;
};

export default () => {
  return (
    <SchemaTabs
      columns={columns}
      dataSource={dataSource}
      variables={{
        peopleId: '1719',
        entityId: '51',
      }}
      transforms={{ transformSearchPeopleOperate }}
    />
  );
};
