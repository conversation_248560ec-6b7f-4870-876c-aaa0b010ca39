import { isArray, isObject } from 'lodash';
import React from 'react';

import { ConfigContext } from '../../../../components/config-provider';
import { FetchType } from '../../../../typing';
import { checkKeysExistInObject } from '../../../../utils/checkKeysExistInObject';
import { extractPatternsFromInput } from '../../../../utils/extract';
import { getEffectFetchConfig } from '../../../../utils/getEffectFetchConfig';
import { useParamsDict } from '../../../../utils/hooks';
import swrFetcher from '../../../../utils/swrFetcher';
import SchemaPage from '../../../schema-page';
import { SchemaTabsContext } from '../../context';

interface FieldSchemaPageProps {
  [key: string]: any;
}

const FieldSchemaPage: React.FC<FieldSchemaPageProps> = (props) => {
  const { effect, columns } = props;

  const configContext = React.useContext(ConfigContext);
  const { tabsVariables, dataSource, transforms, onRetry } =
    React.useContext(SchemaTabsContext);
  const paramsDict = useParamsDict(tabsVariables || {});
  const { transformKey = '', transformIndex = '' } = (effect?.fetch ||
    {}) as FetchType;

  const fetchInitRef = React.useRef(true);
  const [isLoading, setIsLoading] = React.useState(false);
  const [fetchData, setFetchData] = React.useState<any>();

  const fetch = async (fetchParams?: Record<string, any>) => {
    if (!effect.fetch) return;
    const fetchDependentValue = extractPatternsFromInput(
      JSON.stringify(effect.fetch),
    );

    if (fetchDependentValue.length > 0) {
      const has = checkKeysExistInObject(paramsDict, fetchDependentValue);

      if (!has) return;
    }

    setIsLoading(true);

    const { api, method, params, dataIndex } = getEffectFetchConfig(
      effect.fetch,
      configContext,
      paramsDict,
    );

    const newDataIndex = dataIndex ? dataIndex : 'root';

    const newParams = fetchParams
      ? { ...params, params: { ...params.params, ...fetchParams } }
      : params;

    const { data } = await swrFetcher(api, method, newParams);

    let list: Record<string, any>;

    if (newDataIndex === 'root') {
      list = data;
    } else {
      list = data[newDataIndex];
    }

    if (typeof list === 'string') {
      try {
        list = JSON.parse(list);
      } catch (e) { }
    }

    if (isArray(list) && transforms?.[transformKey]) {
      list = transforms[transformKey](list);
    }

    if (isObject(list) && !isArray(list) && transforms?.[transformKey]) {
      if (transformIndex && list[transformIndex]) {
        list[transformIndex] = transforms[transformKey](list[transformIndex]);
      }
      if (!transformIndex) {
        list = transforms[transformKey](list);
      }
    }

    if (isObject(list) && !isArray(list)) {
      list = { ...dataSource, ...paramsDict, ...list };
    }

    setFetchData(list);

    setIsLoading(false);

    fetchInitRef.current = false;
  };

  React.useEffect(() => {
    if (effect?.fetch) {
      fetch();
    }
  }, [tabsVariables]);

  const handleRetry = (params?: Record<string, any>) => {
    if (onRetry) return onRetry(params);
    fetch(params);
  };

  return (
    <SchemaPage
      columns={columns}
      dataSource={fetchData || dataSource}
      onRetry={(params?: Record<string, any>) => handleRetry(params)}
    />
  );
};

export default FieldSchemaPage;
