import React from 'react';
import { isDeepEqualReact } from '../../../utils';

import type { FieldComponentProps } from '../typing';

import FieldSchemaPage from './schema-page-wrapper';

const classPrefix = `deer-tab-item`;

const FieldComponent: React.FC<FieldComponentProps> = (props) => {
  const {
    valueType
  } = props;

  let childNode: React.ReactNode = null;

  switch (valueType) {
    case 'schemaPage':
      childNode = (
        <FieldSchemaPage {...props} />
      );
      break;

    default:
      childNode = (
        <div className={`bg-red-500 text-white rounded px-2 py-1 mb-[20px]`}>
          没有实现的组件: {valueType}
        </div>
      );
  }

  return <>{childNode}</>;
};

export default React.memo(FieldComponent, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps);
});
