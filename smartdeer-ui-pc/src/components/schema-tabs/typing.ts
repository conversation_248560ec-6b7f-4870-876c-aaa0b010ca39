import React from 'react';
import type { EffectType } from '../../typing';
import { PageColumnsType as SchemaPageColumnsType } from '../schema-page/typing';

export type TabsColumnsType = {
  // 组件类型
  type: string;

  // 唯一标识
  key: string;

  // 标签的文本
  label?: string | React.ReactElement;

  // 显示隐藏
  hidden?: boolean | 'true';

  // 组件配置
  columns?: SchemaPageColumnsType[];

  paneProps?: {
    destroyInactiveTabPane?: boolean;
    disabled?: boolean;
    forceRender?: boolean;
    closable?: boolean;
  };

  // 组件的 props
  props?: {
    style?: React.CSSProperties;
    className?: string;
    [key: string]: any;
  };

  // 副作用
  effect?: EffectType;

  // 依赖项
  dependencies?: string[];
};

export type FieldProps = {
  className?: string;
  style?: React.CSSProperties;

  [key: string]: any;
};

export type FieldComponentProps = {
  valueType: string;
  fieldProps?: FieldProps;
  effect?: EffectType;
  columns?: SchemaPageColumnsType[];
};
