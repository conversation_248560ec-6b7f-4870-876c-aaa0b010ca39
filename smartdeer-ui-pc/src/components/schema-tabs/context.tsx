import React from 'react'

interface RecordType {
  [key: string]: any;
}

export type SchemaTabsContextType = {
  tabsVariables: RecordType;
  dataSource: RecordType;
  transforms?: RecordType;
  onRetry?: (params?: Record<string, any>) => void;
}

export const defaultSchemaTabsContext: SchemaTabsContextType = {
  tabsVariables: {},
  dataSource: {}
}

export const SchemaTabsContext =
  React.createContext<SchemaTabsContextType>(defaultSchemaTabsContext)
