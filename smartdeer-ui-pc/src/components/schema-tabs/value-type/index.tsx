import React from 'react';
import FieldComponent from '../field-components'
import type { TabsColumnsType } from '../typing';

const renderValueType = (
  item: TabsColumnsType,
  helpers: Record<string, any>,
) => {
  const { index, genItems } = helpers

  const getField = () => {
    return (
      <FieldComponent
        key={[item.key, index || 0].join('_')}
        fieldProps={item.props}
        valueType={item.type}
        columns={item.columns}
        effect={item.effect}
      >
      </FieldComponent>
    );
  }

  return getField();
}

export default renderValueType
