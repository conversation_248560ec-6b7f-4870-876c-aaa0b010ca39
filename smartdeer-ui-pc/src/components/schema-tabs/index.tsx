import { Empty, Tabs, TabsProps } from 'antd';
import classNames from 'classnames';
import { isArray, isString } from 'lodash';
import React from 'react';
import { useDeepCompareMemo } from '../../utils/hooks';
import { omitUndefined } from '../../utils/omitUndefined';
import { mergeProps } from '../../utils/withDefaultProps';
import type { SchemaTabsContextType } from './context';
import { SchemaTabsContext } from './context';
import type { TabsColumnsType } from './typing';
import renderValueType from './value-type';

import './index.less';

const classPrefix = `deer-schema-tabs`;

export { SchemaTabsContext, type SchemaTabsContextType };

export interface SchemaTabsProps extends TabsProps {
  className?: string;
  style?: React.CSSProperties;
  columnsJson?: string;
  columns?: TabsColumnsType[];
  dataSource?: Record<string, any>;
  variables?: Record<string, any>;
  transforms?: Record<string, any>;

  onRetry?: (params?: Record<string, any>) => void;
}

const defaultProps = {
  dataSource: {},
  columns: [],
  variables: {},
  type: 'card',
};

const SchemaTabs: React.FC<SchemaTabsProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const {
    className,
    style,

    columns: propColumns,
    columnsJson,
    onRetry,
    dataSource,
    variables,
    transforms,
    ...restTabsProps
  } = props;

  const [showEmpty, setShowEmpty] = React.useState<boolean>(false);
  const [columns, setColumns] = React.useState<TabsColumnsType[]>([]);

  const handleReplaceKeysWithRegex = (list: any[]) => {
    if (list.length === 0) {
      setShowEmpty(true);
      return;
    }

    setColumns(list);
  };

  React.useEffect(() => {
    if (isArray(propColumns) && propColumns.length > 0) {
      handleReplaceKeysWithRegex(propColumns);
    } else if (isString(columnsJson)) {
      try {
        handleReplaceKeysWithRegex(JSON.parse(columnsJson));
      } catch (err) {
        console.error(`[smartdeer-ui: SchemaTabs] columnsJson 解析错误！`);
      }
    }
  }, [propColumns, columnsJson]);

  /**
   * 生成子项
   *
   * @param items
   */
  const genItems = (items: TabsColumnsType[]) => {
    return items
      .filter((item) => {
        return !item.hidden;
      })
      .map((originItem) => {
        const item = omitUndefined({
          type: originItem.type,
          label: originItem.label,
          key: originItem.key,
          hidden: originItem.hidden,
          effect: originItem.effect,
          columns: originItem.columns,
          props: originItem.props,
        });

        const paneProps = omitUndefined({
          label: originItem.label,
          key: originItem.key,
          destroyInactiveTabPane: originItem.paneProps?.destroyInactiveTabPane,
          disabled: originItem.paneProps?.disabled,
          forceRender: originItem.paneProps?.forceRender,
          closable: originItem.paneProps?.closable,
        });

        const childNode = renderValueType(item, {
          originItem,
          genItems,
        });
        return {
          ...paneProps,
          children: childNode,
        };
      })
      .filter((item) => {
        return Boolean(item);
      });
  };

  const tabItems = useDeepCompareMemo(() => {
    if (columns.length === 0) return [];

    return genItems(columns);
  }, [columns]);

  if (showEmpty) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <SchemaTabsContext.Provider
        value={{
          onRetry,
          tabsVariables: variables,
          dataSource: dataSource,
          transforms
        }}
      >
        <Tabs {...restTabsProps} items={tabItems} />
      </SchemaTabsContext.Provider>
    </div>
  );
};

export default SchemaTabs;
