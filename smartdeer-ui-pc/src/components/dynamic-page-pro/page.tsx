import React from 'react'
import classNames from 'classnames'
import swrFetcher from '../../utils/swrFetcher'
import { ConfigContext } from '../config-provider'
import { devError, prodError } from '../../utils/log'
import Loading from '../loading'
import { useParamsDict } from '../../utils/hooks'
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate';
import SchemaPagePro from '../schema-page-pro'
import type { SchemaPageProProps } from '../schema-page-pro'
import { replaceReferences } from '../../utils/replaceReferences'
import { useDebounceEffect } from 'ahooks'

import './page.less';

const classPrefix = `deer-dynamic-page-pro`

export interface DynamicPageProProps extends SchemaPageProProps {
  className?: string;
  style?: React.CSSProperties;

  conf?: {
    version?: string;
    confKey: string;
    language?: string;
  };
  confObject?: SchemaPageProProps['columns']
}

const DynamicPagePro: React.FC<DynamicPageProProps> = (props) => {
  const {
    className,
    style,

    conf,
    confObject,

    ...restProps
  } = props

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict()

  if (!conf && !confObject) {
    devError('DynamicPagePro', '缺少必要参数 conf ！')
  }

  const [isLoading, setIsLoading] = React.useState(true)
  const [columns, setColumns] = React.useState<SchemaPageProProps['columns']>([])

  const getConf = async () => {
    if (confObject) {
      setColumns(confObject)
      setIsLoading(false)
      return
    }

    if (!conf) {
      setIsLoading(false)
      return
    }

    try {
      const api = replaceVariablesInTemplate(paramsDict, configContext.appFunctionApi!)

      const { data } = await swrFetcher(api!, 'POST', {
        functionKey: configContext.formatterUiByKey!,
        params: {
          confKey: conf?.confKey,
          language: conf?.language || 'gl',
          version: conf?.version || '1'
        }
      })

      const { page, ...rest } = JSON.parse(data.rs)

      const newPage = replaceReferences(page, rest)

      setColumns(newPage as SchemaPageProProps['columns'])

    } catch (err) {
      prodError('DynamicPagePro', '获取配置失败！')
    }

    setIsLoading(false)
  }

  React.useEffect(() => {
    setIsLoading(true)
    setColumns([])
    getConf()
  }, [conf])

  if (isLoading) {
    return <Loading />
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <SchemaPagePro
        {...restProps}
        columns={columns}
      />
    </div>
  );
};

export default DynamicPagePro;
