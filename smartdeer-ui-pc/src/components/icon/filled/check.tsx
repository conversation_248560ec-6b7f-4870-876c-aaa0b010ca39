import React, { type FC } from 'react';
import { FilledProps } from '../typing'

const CheckFilled: FC<FilledProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" fill="#fff" version="1.1" width="1em" height="1em" viewBox="0 0 14 14">
        <g><g><ellipse cx="7" cy="7" rx="7" ry="7" fill="currentColor" fillOpacity="1" /></g><g transform="matrix(0.17472431063652039,-0.9846174120903015,0.9846174120903015,0.17472431063652039,-5.560453951358795,11.365950852632523)"><path d="" fill="#D8D8D8" fillOpacity="1" /><path d="M5.8736,9.341177Q5.95569,9.180466,5.95569,9Q5.95569,8.9261315,5.94128,8.853682Q5.92687,8.781233,5.8986,8.712987Q5.87033,8.644742,5.82929,8.583322Q5.78825,8.521903,5.73602,8.46967Q5.68379,8.417437,5.62237,8.376398Q5.56095,8.335359,5.4927,8.30709Q5.42446,8.278822,5.35201,8.264410999999999Q5.27956,8.25,5.20569,8.25Q5.10205,8.25,5.0023,8.278106Q4.90254,8.306213,4.814144,8.360319Q4.725749,8.414425,4.65534,8.490476Q4.584931,8.566527,4.5377849999999995,8.658823L3.332094,11.01914Q3.331555,11.020199999999999,3.33102,11.02125Q3.263575,11.15432,3.252188,11.30307Q3.2408010000000003,11.45182,3.287204,11.5936Q3.3336069999999998,11.73539,3.430737,11.84862Q3.527866,11.96185,3.660934,12.0293L8.645859999999999,14.55586L8.64621,14.55603Q8.80606,14.63705,8.98527,14.63705Q9.05914,14.63705,9.13159,14.62264Q9.204039999999999,14.608229999999999,9.27228,14.57996Q9.340530000000001,14.55169,9.40195,14.51065Q9.463370000000001,14.46961,9.5156,14.41738Q9.56783,14.36515,9.60887,14.30373Q9.64991,14.24231,9.678180000000001,14.17406Q9.70645,14.10582,9.72086,14.03337Q9.73527,13.96092,9.73527,13.88705Q9.73527,13.78311,9.707,13.68309Q9.678740000000001,13.58306,9.62433,13.494489999999999Q9.56993,13.40593,9.493490000000001,13.33549Q9.41705,13.26506,9.32434,13.21807L9.324,13.2179L5.01016,11.03148L5.8736,9.341177L5.8736,9.341177Z" fillRule="evenodd" fillOpacity="1" /></g></g>
      </svg>
    </span>
  )
}

export default CheckFilled;
