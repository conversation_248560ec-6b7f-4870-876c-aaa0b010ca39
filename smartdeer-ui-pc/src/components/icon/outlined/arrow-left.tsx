import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const ArrowLeftOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 20 20" fill="currentColor">
        <path d="M18.0357 9.15144H4.97548L12.7924 2.36572C12.9174 2.25635 12.8415 2.05322 12.6764 2.05322H10.7009C10.6139 2.05322 10.5313 2.08447 10.4665 2.14028L2.03128 9.45947C1.95402 9.52644 1.89206 9.60923 1.8496 9.70224C1.80713 9.79525 1.78516 9.89629 1.78516 9.99854C1.78516 10.1008 1.80713 10.2018 1.8496 10.2948C1.89206 10.3878 1.95402 10.4706 2.03128 10.5376L10.5157 17.9014C10.5491 17.9305 10.5893 17.9461 10.6317 17.9461H12.6741C12.8393 17.9461 12.9152 17.7407 12.7902 17.6336L4.97548 10.8479H18.0357C18.134 10.8479 18.2143 10.7675 18.2143 10.6693V9.33001C18.2143 9.23179 18.134 9.15144 18.0357 9.15144Z" />
      </svg>
    </span>
  )
}

export default ArrowLeftOutline;
