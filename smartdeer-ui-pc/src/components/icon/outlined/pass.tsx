import React, { type FC } from 'react';
import { OutlineProps } from '../typing';

const PassOutline: FC<OutlineProps> = (props) => {
  const { className, style } = props;

  return (
    <span className={className} style={{ ...style }}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 14 14"
        fill="currentColor"
      >
        <path
          d="M13.2503 1.96875H12.1581C12.0049 1.96875 11.8596 2.03906 11.7659 2.15937L5.32369 10.3203L2.23463 6.40625C2.1879 6.34692 2.12833 6.29895 2.06041 6.26593C1.99249 6.23292 1.91796 6.21572 1.84244 6.21563H0.750253C0.645565 6.21563 0.587753 6.33594 0.651815 6.41719L4.9315 11.8391C5.1315 12.0922 5.51588 12.0922 5.71744 11.8391L13.3487 2.16875C13.4128 2.08906 13.3549 1.96875 13.2503 1.96875V1.96875Z"
        />
      </svg>
    </span>
  );
};

export default PassOutline;
