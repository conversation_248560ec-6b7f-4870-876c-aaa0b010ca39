import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const EmailOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" fill="currentColor">
        <g clipPath="url(#clip0_739_9043)">
          <path d="M13.8192 2.28564C14.3978 2.28564 14.9528 2.51551 15.3619 2.92468C15.7711 3.33385 16.001 3.88881 16.001 4.46746V11.7402C16.001 12.3188 15.7711 12.8738 15.3619 13.283C14.9528 13.6921 14.3978 13.922 13.8192 13.922H2.18279C1.60414 13.922 1.04919 13.6921 0.640016 13.283C0.230846 12.8738 0.000976562 12.3188 0.000976562 11.7402V4.46746C0.000976562 3.88881 0.230846 3.33385 0.640016 2.92468C1.04919 2.51551 1.60414 2.28564 2.18279 2.28564H13.8192ZM1.51734 4.17437C1.47656 4.2667 1.4555 4.36653 1.45552 4.46746V11.7402C1.45552 11.9331 1.53215 12.1181 1.66854 12.2544C1.80493 12.3908 1.98991 12.4675 2.18279 12.4675H13.8192C14.012 12.4675 14.197 12.3908 14.3334 12.2544C14.4698 12.1181 14.5464 11.9331 14.5464 11.7402V4.46746C14.5465 4.36707 14.5257 4.26776 14.4853 4.17583L9.44243 8.63983C9.06017 8.97818 8.57136 9.17178 8.06108 9.18693C7.5508 9.20208 7.05137 9.03782 6.6497 8.72273L6.54789 8.63837L1.51734 4.17437ZM12.7821 3.74019H3.21989L7.5137 7.55037C7.63589 7.65878 7.79101 7.7229 7.95408 7.7324C8.11715 7.7419 8.27867 7.69622 8.41261 7.60274L8.47807 7.5511L12.7821 3.74019Z" strokeWidth="0.342857" />
        </g>
        <defs>
          <clipPath id="clip0_739_9043">
            <rect width="1em" height="1em" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </span>
  )
}

export default EmailOutline;
