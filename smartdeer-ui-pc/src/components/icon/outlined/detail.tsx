import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const DetailOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>

      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 18 18" fill="currentColor" stroke="currentColor">
        <path d="M14.7834 1.79999H3.2166C2.6829 1.79999 2.25 2.23289 2.25 2.76659V15.2334C2.25 15.7671 2.6829 16.2 3.2166 16.2H14.7834C15.3171 16.2 15.75 15.7671 15.75 15.2334V2.76659C15.75 2.23289 15.3171 1.79999 14.7834 1.79999ZM14.85 15.2334C14.85 15.251 14.843 15.268 14.8305 15.2805C14.818 15.293 14.8011 15.3 14.7834 15.3H3.2166C3.20785 15.3 3.19919 15.2983 3.19111 15.2949C3.18303 15.2916 3.17569 15.2867 3.16951 15.2805C3.16332 15.2743 3.15842 15.267 3.15507 15.2589C3.15172 15.2508 3.15 15.2421 3.15 15.2334V2.76659C3.15 2.72969 3.1797 2.69999 3.2166 2.69999H14.7834C14.8203 2.69999 14.85 2.72969 14.85 2.76659V15.2334Z" strokeWidth="0.2" />
        <path d="M12.1688 5.71863H5.83099C5.72147 5.72571 5.61875 5.77421 5.54369 5.85428C5.46863 5.93434 5.42686 6.03998 5.42686 6.14973C5.42686 6.25948 5.46863 6.36511 5.54369 6.44518C5.61875 6.52525 5.72147 6.57375 5.83099 6.58083H12.1688C12.2783 6.57375 12.381 6.52525 12.4561 6.44518C12.5311 6.36511 12.5729 6.25948 12.5729 6.14973C12.5729 6.03998 12.5311 5.93434 12.4561 5.85428C12.381 5.77421 12.2783 5.72571 12.1688 5.71863ZM12.1688 8.59683H5.83099C5.72147 8.60391 5.61875 8.65241 5.54369 8.73248C5.46863 8.81254 5.42686 8.91818 5.42686 9.02793C5.42686 9.13768 5.46863 9.24331 5.54369 9.32338C5.61875 9.40345 5.72147 9.45195 5.83099 9.45903H12.1688C12.2783 9.45195 12.381 9.40345 12.4561 9.32338C12.5311 9.24331 12.5729 9.13768 12.5729 9.02793C12.5729 8.91818 12.5311 8.81254 12.4561 8.73248C12.381 8.65241 12.2783 8.60391 12.1688 8.59683ZM9.97909 11.4183H5.83099C5.71994 11.4234 5.61513 11.4711 5.53834 11.5515C5.46155 11.6318 5.4187 11.7387 5.4187 11.8499C5.4187 11.961 5.46155 12.0679 5.53834 12.1483C5.61513 12.2287 5.71994 12.2764 5.83099 12.2814H9.97909C10.0901 12.2764 10.1949 12.2287 10.2717 12.1483C10.3485 12.0679 10.3914 11.961 10.3914 11.8499C10.3914 11.7387 10.3485 11.6318 10.2717 11.5515C10.1949 11.4711 10.0901 11.4234 9.97909 11.4183Z" strokeWidth="0.3" />
      </svg>
    </span>
  )
}

export default DetailOutline;
