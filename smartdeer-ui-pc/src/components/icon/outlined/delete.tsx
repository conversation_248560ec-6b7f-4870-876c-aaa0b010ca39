import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const DeleteOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 14 14" fill="currentColor">
        <path fillRule="evenodd" clipRule="evenodd" d="M5.28075 2.52716C5.40847 2.4154 5.59441 2.34333 5.80002 2.34333H8.20002C8.40564 2.34333 8.59158 2.4154 8.7193 2.52716C8.84489 2.63705 8.90002 2.77065 8.90002 2.89333V3.22823H5.10002V2.89333C5.10002 2.77065 5.15516 2.63705 5.28075 2.52716ZM4.10002 3.22823V2.89333C4.10002 2.45906 4.29775 2.05852 4.62224 1.77458C4.94461 1.49251 5.36912 1.34333 5.80002 1.34333H8.20002C8.63093 1.34333 9.05544 1.49251 9.3778 1.77458C9.7023 2.05852 9.90002 2.45906 9.90002 2.89333V3.22823H12.25C12.5814 3.22823 12.85 3.49686 12.85 3.82823C12.85 4.1596 12.5814 4.42823 12.25 4.42823H11.7V11.1782C11.7 11.6125 11.5023 12.013 11.1778 12.297C10.8554 12.5791 10.4309 12.7282 10 12.7282H4.00002C3.56912 12.7282 3.14461 12.5791 2.82224 12.297C2.49775 12.013 2.30002 11.6125 2.30002 11.1782V4.42823H1.75002C1.41865 4.42823 1.15002 4.1596 1.15002 3.82823C1.15002 3.49686 1.41865 3.22823 1.75002 3.22823H2.91669H4.10002ZM3.30002 4.42823V11.1782C3.30002 11.3009 3.35516 11.4345 3.48075 11.5444C3.60847 11.6562 3.79441 11.7282 4.00002 11.7282H10C10.2056 11.7282 10.3916 11.6562 10.5193 11.5444C10.6449 11.4345 10.7 11.3009 10.7 11.1782V4.42823H3.30002ZM5.60002 6.12823C5.87617 6.12823 6.10002 6.35209 6.10002 6.62823V9.42823C6.10002 9.70437 5.87617 9.92823 5.60002 9.92823C5.32388 9.92823 5.10002 9.70437 5.10002 9.42823V6.62823C5.10002 6.35209 5.32388 6.12823 5.60002 6.12823ZM8.90002 6.62823C8.90002 6.35209 8.67617 6.12823 8.40002 6.12823C8.12388 6.12823 7.90002 6.35209 7.90002 6.62823V9.42823C7.90002 9.70437 8.12388 9.92823 8.40002 9.92823C8.67617 9.92823 8.90002 9.70437 8.90002 9.42823V6.62823Z" />
      </svg>
    </span>
  )
}

export default DeleteOutline;
