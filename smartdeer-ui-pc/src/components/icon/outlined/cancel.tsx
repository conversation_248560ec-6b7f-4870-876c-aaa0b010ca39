import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const CancelOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 14 14" fill="currentColor">
      <path
      d="M11.3906 2.78114H4.71875V1.62489C4.71875 1.5202 4.59844 1.46239 4.51719 1.52645L2.29844 3.27645C2.2835 3.28814 2.27141 3.30308 2.2631 3.32014C2.2548 3.33719 2.25048 3.35591 2.25048 3.37489C2.25048 3.39386 2.2548 3.41258 2.2631 3.42964C2.27141 3.44669 2.2835 3.46163 2.29844 3.47332L4.51719 5.22332C4.59844 5.28739 4.71875 5.22957 4.71875 5.12489V3.96864H11.2031V11.3124H1.73438C1.66562 11.3124 1.60938 11.3686 1.60938 11.4374V12.3749C1.60938 12.4436 1.66562 12.4999 1.73438 12.4999H11.3906C11.9422 12.4999 12.3906 12.0515 12.3906 11.4999V3.78114C12.3906 3.22957 11.9422 2.78114 11.3906 2.78114Z"
    />
      </svg>
    </span>
  )
}

export default CancelOutline;
