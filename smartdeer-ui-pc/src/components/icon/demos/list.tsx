import React, { useState, ComponentType } from 'react'
import { Icon } from '@smartdeer-ui/pc';

type IconItem = {
  name: string
  component: ComponentType<{ style?: React.CSSProperties }>
}

const items: IconItem[] = []
for (let key in Icon) {
  const component = (Icon as any)[key] as ComponentType

  items.push({
    name: key,
    component,
  })
}

export default () => {
  const [list, setList] = useState<IconItem[]>(items)

  return (
    <div className={`flex flex-wrap`}>
      {list.map(item => {
        return (
          <div key={item.name} className={`w-[120px] h-[120px] flex items-center justify-center`}>
            <div>
              <div className={`flex justify-center text-[40px]`}>
                <item.component style={{ fontSize: '40px', color: '#555' }} />
              </div>
              <div className={`text-[12px] mt-[20px] text-[#55]`}> {item.name} </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
