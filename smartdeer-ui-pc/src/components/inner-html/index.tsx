import React, { useState } from 'react';
import classNames from 'classnames'
import DOMPurify from 'dompurify';
import { Image } from 'antd'
import './index.css'

DOMPurify.addHook('afterSanitizeAttributes', function (node) {
  // set all elements owning target to target=_blank
  if ('target' in node) {
    node.setAttribute('target', '_blank');
    node.setAttribute('rel', 'noopener noreferrer');
  }
});

const classPrefix = 'deer-inner-html'

export interface InnerHtmlProps {
  className?: string;
  style?: React.CSSProperties;
  content?: string;
}

const InnerHtml: React.FC<InnerHtmlProps> = (props) => {
  const {
    className,
    style,
    content,
  } = props

  const [image, setImage] = useState({ visible: false, src: null });

  const contentHtml = React.useMemo(() => {
    let html = content

    if (html?.includes('<p><br></p>')) {
      html = content?.replaceAll('<p><br></p>', '')
    }

    if (html?.includes('<p><br/></p>')) {
      html = content?.replaceAll('<p><br/></p>', '')
    }

    return DOMPurify.sanitize(html || '')
  }, [content])

  const handleImgClick = (event: any) => {
    if (event.target.tagName === "IMG") {
      const src = event.target.src;
      setImage({ visible: true, src });
    }
  };

  return (
    <>
      <div
        className={classNames(classPrefix, className)}
        style={{ ...style }}
        onClick={handleImgClick}
        dangerouslySetInnerHTML={{ __html: contentHtml || '' }}
      />

      {image.src && (
        <Image
          width={200}
          style={{ display: 'none' }}
          src={image.src}
          preview={{
            visible: image.visible,
            src: image.src!,
            onVisibleChange: (value) => {
              setImage({ visible: value, src: null });
            },
          }}
        />
      )}
    </>
  )
}

export default InnerHtml
