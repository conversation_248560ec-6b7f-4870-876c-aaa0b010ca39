
import React from 'react';
import { Form } from 'antd'
import CSSMotion from 'rc-motion';
import classNames from 'classnames';
import type { FormColumnsType } from './typing';
import type { SchemaFormProProps, SchemaFormProRef } from './form'
import SchemaFormPro from './form'
import { mergeProps } from '../../utils/withDefaultProps'
import pageEventBus from '../../bus/pageEventHub'
import { PAGE_EVENT_NAME } from '../../consts'
import { isUndefined, get } from 'lodash';
import { prodError, prodWarning } from '../../utils/log'
import { SchemaFormProStepsContext } from './context'
import type { FetchType } from '../../typing';
import { ConfigContext } from '../config-provider';
import { useParamsDict } from '../../utils/hooks';
import { cachedSwrFetcher } from '../../utils/swrFetcher'
import { getEffectFetchConfig } from '../../utils/getEffectFetchConfig'

import './form-steps.less';

const classPrefix = `deer-schema-form-pro-steps`;

type ItemType = {
  title?: string;
  description?: string;

  destroyInactiveTabPane?: boolean;
  forceRender?: boolean;

  columns?: FormColumnsType[];
  formProps?: Omit<SchemaFormProProps, 'columns'>
}

export interface SchemaFormProStepsProps {
  className?: string;
  style?: React.CSSProperties;
  field?: string;
  destroyInactiveTabPane?: boolean;
  forceRender?: boolean;
  defaultActiveIndex?: number;
  items: ItemType[];
  fetchItems?: FetchType;

  variables?: Record<string, any>;

  formProps?: Omit<SchemaFormProProps, 'columns'>;
  initialValues?: Record<string, any>;

  onFinish?: (values: Record<string, any>) => void;
}

const defaultProps = {
  forceRender: false,
  destroyInactiveTabPane: false,
  defaultActiveIndex: 0,
  initialValues: {},
  variables: {}
}

const SchemaFormProSteps: React.FC<SchemaFormProStepsProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,
    field,
    forceRender,
    destroyInactiveTabPane,
    defaultActiveIndex,
    initialValues,
    variables,

    fetchItems,
    formProps,

    onFinish,

    ...restProps
  } = props

  const configContext = React.useContext(ConfigContext);

  const paramsDict = useParamsDict({ ...variables, ...initialValues })


  const [activeIndex, setActiveIndex] = React.useState(defaultActiveIndex)
  const [items, setItems] = React.useState<any[]>(restProps.items)

  const [formValues, setFormValues] = React.useState<Record<string, any>>(initialValues)

  // useRef 可以提供一个在组件的整个生命周期内都保持不变的引用对象。
  const activeIndexRef = React.useRef(defaultActiveIndex)
  const formValuesRef = React.useRef(initialValues)
  const itemsRef = React.useRef<any[]>(restProps.items)

  const formsRef = React.useRef([1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(() => React.createRef<SchemaFormProRef>())).current;

  /**
   * 根据索引设置活动步骤
   *
   * @param index 索引值，可以是字符串或数字
   */
  const handleTo = (index: string | number) => {
    if (isUndefined(index) || index === '') {
      prodError('SchemaFormPro Steps', 'index is undefined')
      return
    }

    setActiveIndex(Number(index))
  }

  /**
   * 处理下一步操作
   */
  const handleNext = async () => {
    // 获取下一个索引
    const nextIndex = activeIndexRef.current + 1

    // 判断是否超出索引
    if (nextIndex >= itemsRef.current.length) {
      prodWarning('SchemaFormPro Steps', 'index is out of range')
      return
    }

    // 校验当前表单
    await formsRef[activeIndexRef.current].current?.validateFields()

    try {

      // 获取当前表单数据
      const curFormValues = await formsRef[activeIndexRef.current].current?.getFieldsValue()

      formValuesRef.current = {
        ...formValuesRef.current,
        ...curFormValues
      }

      setFormValues(formValuesRef.current)

      activeIndexRef.current = nextIndex
      setActiveIndex(nextIndex)
    } catch (err: any) {
      prodError('SchemaFormPro Steps', err.message)
    }
  }

  /**
 * 异步函数，用于处理步骤的下一页获取逻辑
 */
  const handleFetchNext = async () => {

    if (!fetchItems) {
      prodError('SchemaFormPro Steps', 'fetchItems is undefined or null')
      return
    }

    // 校验当前表单
    await formsRef[activeIndexRef.current].current?.validateFields()

    try {

      const curFormValues = await formsRef[activeIndexRef.current].current?.getFieldsValue()

      formValuesRef.current = {
        ...formValuesRef.current,
        ...curFormValues
      }

      setFormValues(formValuesRef.current)

      const { api, method, params, dataIndex } = getEffectFetchConfig(fetchItems, configContext, { ...paramsDict, ...formValuesRef.current })

      const { data } = await cachedSwrFetcher(api, method, params)

      let result: any = dataIndex ? get(data, dataIndex) : data

      try {
        result = JSON.parse(result)
      } catch (err) {
      }

      itemsRef.current = [
        ...itemsRef.current,
        ...result
      ]

      setItems(JSON.parse(JSON.stringify(itemsRef.current)))

      handleNext()

    } catch (err: any) {
      prodError('SchemaFormPro Steps', err.message)
    }
  }

  /**
   * 处理后退按钮点击事件
   *
   * 当点击后退按钮时，将当前活动索引减一，并检查新索引是否越界。
   * 如果新索引小于0，则显示警告信息并返回。
   * 否则，更新活动索引和组件状态。
   */
  const handleBack = () => {
    const backIndex = activeIndexRef.current - 1

    if (backIndex < 0) {
      prodWarning('SchemaFormPro Steps', 'index is out of range')
      return
    }

    activeIndexRef.current = backIndex
    setActiveIndex(backIndex)
  }

  /**
   * 异步提交表单处理函数
   */
  const handleSubmit = async () => {
    console.log('handleSubmit')

    if (activeIndexRef.current !== itemsRef.current.length - 1) {
      prodWarning('SchemaFormPro Steps', 'is not last step')
      return
    }

    // 校验当前表单
    await formsRef[activeIndexRef.current].current?.validateFields()

    try {
      // 获取当前表单数据
      const curFormValues = await formsRef[activeIndexRef.current].current?.getFieldsValue()

      formValuesRef.current = {
        ...formValuesRef.current,
        ...curFormValues
      }

      setFormValues(formValuesRef.current)

      onFinish?.(formValuesRef.current)

    } catch (err: any) {
      prodError('SchemaFormPro Steps', err.message)
    }
  }

  React.useEffect(() => {
    if (!field) return

    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME['form-steps-to']}`, handleTo)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME['form-steps-next']}`, handleNext)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME['form-steps-fetch-next']}`, handleFetchNext)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME['form-steps-back']}`, handleBack)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME['form-steps-submit']}`, handleSubmit)

    return () => {
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME['form-steps-to']}`, handleTo)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME['form-steps-next']}`, handleNext)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME['form-steps-fetch-next']}`, handleFetchNext)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME['form-steps-back']}`, handleBack)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME['form-steps-submit']}`, handleSubmit)
    }
  }, [])

  return (
    <div
      className={classNames(classPrefix, className)}
      style={{ ...style }}
    >
      <SchemaFormProStepsContext.Provider
        value={{
          formStepsValues: formValues
        }}
      >
        <Form.Provider
          onFormFinish={(formName: string, { forms, values }) => { }}
          onFormChange={(formName: string, info) => { }}
        >
          {items?.map((item, index) => {
            const {
              title,
              description,
              forceRender: itemForceRender,
              destroyInactiveTabPane: itemDestroyInactiveTabPane,
              ...rest
            } = item;

            const active = index === activeIndex;

            return (
              <CSSMotion
                key={title || index}
                visible={active}
                forceRender={!!(forceRender || itemForceRender)}
                removeOnLeave={!!(destroyInactiveTabPane || itemDestroyInactiveTabPane)}
                leavedClassName={`${classPrefix}-hidden`}
              >
                {({ style: motionStyle, className: motionClassName }, ref) => (
                  <div
                    ref={ref}
                    className={classNames(`${classPrefix}-pane`, motionClassName)}
                    style={{ ...motionStyle }}
                    data-pane-title={title || index}
                    data-pane-description={description || index}
                  >
                    <SchemaFormPro
                      key={index}
                      variables={variables}
                      {...formProps}
                      {...item.formProps}
                      columns={item?.columns || []}
                      ref={formsRef[index]}
                      initialValues={initialValues}
                    />
                  </div>
                )}
              </CSSMotion>
            )
          })}
        </Form.Provider>
      </SchemaFormProStepsContext.Provider>

    </div>
  )
}

export default SchemaFormProSteps;


