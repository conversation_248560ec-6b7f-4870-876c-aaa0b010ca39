import React from 'react';
import FormField from '../form-field'
import FormDependencies from '../form-dependencies'
import type { ItemType, RenderValueTypeHelpers } from '../typing';
import { omitUndefined } from '../../../utils'
import FormShow from '../form-show'

const field = (
  item: ItemType,
  helpers: RenderValueTypeHelpers,
) => {

  const labelProps = omitUndefined({
    ...item?.labelProps,
    label: item?.title,
    name: item.field,
    rules: item?.labelProps?.rules?.map((item: any) => {
      return omitUndefined({
        ...item,
        pattern: item.pattern ? new RegExp(item.pattern) : undefined
      })
    })
  })

  const formFieldProps = {
    ...item,
    labelProps
  }

  const getField = (dependenciesValues = {}) => {
    const { effect = {} } = formFieldProps
    const { show } = effect

    let node = <FormField
      key={[item.field, helpers.index || 0].join('_1')}
      {...formFieldProps}
      dependenciesValues={dependenciesValues}
    />

    if (show) {
      node = <FormShow effectShow={show} dependenciesValues={dependenciesValues}>
        {node}
      </FormShow>
    }

    return node
  }

  if (item.dependencies && Array.isArray(item.dependencies) && item.dependencies.length > 0) {
    return (
      <FormDependencies
        names={item.dependencies}
        key={[item.field, helpers.index || 0].join('-')}
      >
        {getField}
      </FormDependencies>
    )
  }

  return getField()
};

export default field
