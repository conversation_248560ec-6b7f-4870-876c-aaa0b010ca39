.deer-schema-form-pro {
  &-pane {
    height: 100%;
    width: 100%;
  }

  &-hidden {
    display: none;
  }

  .switch {
    &-appear,
    &-enter {
      transition: none;

      &-start {
        opacity: 0;
      }

      &-active {
        opacity: 1;
        transition: all 0.3s;
      }
    }

    &-leave {
      position: absolute;
      transition: none;
      inset: 0;

      &-start {
        opacity: 1;
      }

      &-active {
        opacity: 0;
        transition: all 0.3s;
      }
    }
  }

  .deer-form-pro-label {
    padding: 0 0 8px;

    label {
      &::before {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
      }
    }
  }
}
