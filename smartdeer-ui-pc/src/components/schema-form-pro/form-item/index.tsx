import React from 'react';
import { Form } from 'antd';
import type { FormColumnsType, ItemType } from '../typing'
import { mergeProps } from '../../../utils/withDefaultProps'
import { isDeepEqualReact } from '../../../utils'
import { isArray } from 'lodash'

const classPrefix = `deer-form-pro-item`

interface FormItemProps extends ItemType {
  labelProps: FormColumnsType['labelProps'];
  dependenciesValues?: Record<string, any>;
  children?: React.ReactNode
}

const defaultProps = {
  labelProps: {},
  dependenciesValues: {}
}

const normFile = (e: any) => {
  if (isArray(e)) {
    return e;
  }
  return e?.fileList;
};

const FormItem: React.FC<FormItemProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    children,
    labelProps,
    type,
    dependenciesValues,
    ...extra
  } = props

  return (
    <Form.Item
      {...labelProps}
      className={classPrefix}
    >
      {children}
    </Form.Item>
  )
};

export default React.memo(FormItem, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
