import React from 'react';
import { EffectType } from '../../typing';

export type ComponentType =
  | 'row'
  | 'col'
  | 'space'
  | 'flex'
  | 'divider'
  | 'title'
  | 'div'
  | 'text'
  | 'label'
  | 'button'
  | 'next'
  | 'submit'
  | 'input'
  | 'password'
  | 'select'
  | 'radio'
  | 'textArea'
  | 'datePicker'
  | 'schemaPage';

export type FormColumnsType = {
  // 组件类型
  type: ComponentType;

  // 唯一标识
  field: string | string[];

  // 标签的文本
  title?: string;

  // 显示隐藏
  hidden?: boolean;

  // 组件的 props
  props?: {
    className?: string;
    style?: React.CSSProperties;
    placeholder?: string;
    extendedFields?: {
      fromKey?: string | string[];
      toKey?: string | string[];
    }[];
  } & Record<string, any>;

  // label props
  labelProps?: {
    noStyle?: boolean;
    initialValue?: string;
  } & Record<string, any>;

  // 副作用
  effect?: EffectType;

  // 孩子
  children?: string | FormColumnsType | FormColumnsType[];

  // 依赖项 field
  dependencies?: Array<string> | Array<string[]>;
};

export type ItemType = Omit<
  FormColumnsType,
  'children' | 'labelProps' | 'props'
> & {
  children?: React.ReactNode | React.ReactNode[];
  fieldProps: FormColumnsType['props'];
  labelProps: FormColumnsType['labelProps'];
};

export type RenderValueTypeHelpers = {
  originItem: FormColumnsType;
  index: number;
  generateItems: (items: FormColumnsType[]) => React.ReactNode[];
};

export type ProFieldFC<T = {}> = React.ForwardRefRenderFunction<
  any,
  {
    children?: React.ReactNode | React.ReactNode[];
    fieldProps?: FormColumnsType['props'] & {
      buttonLoading?: boolean;
      responseLoading?: boolean;
      responseData?: any;
      extendedFields?: {
        fromKey?: string | string[];
        toKey?: string | string[];
      }[];
    };
    value?: any;
    onChange?: (...args: any) => void;
    onClick?: (...args: any) => void;
  } & T
>;

/**
 * 指示要使用的验证器类型。公认的类型值是：
 *
 * string: Must be of type string. This is the default type.
 * number: Must be of type number.
 * boolean: Must be of type boolean.
 * method: Must be of type function.
 * regexp: Must be an instance of RegExp or a string that does not generate an exception when creating a new RegExp.
 * integer: Must be of type number and an integer.
 * float: Must be of type number and a floating point number.
 * array: Must be an array as determined by Array.isArray.
 * object: Must be of type object and not Array.isArray.
 * enum: Value must exist in the enum.
 * date: Value must be valid as determined by Date
 * url: Must be of type url.
 * hex: Must be of type hex.
 * email: Must be of type email.
 * any: Can be any type.
 * */
export type FormValidateType =
  | 'string'
  | 'array'
  | 'number'
  | 'integer'
  | 'float'
  | 'object'
  | 'date'
  | 'url'
  | 'hex'
  | 'email';

export type FormRuleType = {
  message: string;
  required: boolean;
  pattern?: string | RegExp;

  type?: 'customValidator';
  condition?: '===' | '!==' | '>' | '>=' | '<' | '<=';
  fieldToCompare?: string;
};

export type FormLabelType = {
  label: string;
  name: string;
  tooltip: string;
  rules: FormRuleType[];
  noStyle?: boolean;
};

export type SchemaRenderValueTypeFunction = (
  item: ItemType,
  helpers: Record<string, any>,
) => React.ReactNode;
