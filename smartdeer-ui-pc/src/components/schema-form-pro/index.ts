import InternalSchemaFormPro from './form';
import FormSteps from './form-steps';

export const classPrefix = `deer-schema-form-pro`;

export type { SchemaFormProProps, SchemaFormProRef } from './form';
export type { SchemaFormProStepsProps } from './form-steps';

type InternalSchemaFormProType = typeof InternalSchemaFormPro;

type CompoundedComponent = InternalSchemaFormProType & {
  FormSteps: typeof FormSteps;
};

const SchemaFormPro = InternalSchemaFormPro as CompoundedComponent;

SchemaFormPro.FormSteps = FormSteps;

export default SchemaFormPro;
