/**
 * 获取网格映射值
 *
 * @param globalVal 全局值，默认为 '2'
 * @param curVal 当前值，默认为 ''
 * @returns 返回网格映射的数值结果
 */
export const getGridMapping = (
  globalVal: string = '2',
  curVal: string = '',
) => {
  // 确保 globalVal 是一个有效的值（'1' 到 '4' 之间），否则默认为 '2'
  const validGlobalVal = globalVal > '0' && globalVal <= '4' ? globalVal : '2';

  // 如果提供了 curVal，则进行以下处理
  if (curVal) {
    // 确保 curVal 是一个有效的值（'1' 到 validGlobalVal 之间），否则使用 validGlobalVal
    const validCurVal =
      curVal > '0' && curVal <= validGlobalVal ? curVal : validGlobalVal;

    // 根据 globalVal 和 curVal 计算网格映射值
    const gridMapping = (24 / Number(validGlobalVal)) * Number(validCurVal);

    return Math.round(gridMapping);
  }

  // 如果没有提供 curVal，则只根据 globalVal 计算网格映射值
  const gridMapping = 24 / Number(validGlobalVal);
  return gridMapping;
};
