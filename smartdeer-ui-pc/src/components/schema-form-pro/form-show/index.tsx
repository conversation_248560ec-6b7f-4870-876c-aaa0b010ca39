import React from 'react';
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'
import { isEmpty } from 'lodash'
import { SchemaFormProContext } from '../context'
import { useParamsDict } from '../../../utils/hooks'

interface FormShowProps {
  effectShow: string;
  dependenciesValues: Record<string, any>;
  children: React.ReactNode;
}

const FormShow: React.FC<FormShowProps> = (props) => {
  console.log('FormShow', props)
  const { effectShow, dependenciesValues, children } = props

  const paramsDict = useParamsDict()
  const { formVariables } = React.useContext(SchemaFormProContext)


  // 判断副作用 show 以及依赖数据是否存在
  if (!isEmpty(dependenciesValues) || !isEmpty(formVariables) || !isEmpty(paramsDict)) {
    const variables = { ...paramsDict, ...formVariables, ...dependenciesValues }

    // 判断是否需要显示
    const show = evaluateLogicalExpression(variables, effectShow)

    if (!show) return <></>
  }

  return children
};

export default FormShow
