import React from 'react';
import { SchemaFormPro } from '@smartdeer-ui/pc'
import type { SchemaFormProRef } from '@smartdeer-ui/pc'
import { Form } from 'antd';
import { columns } from './layout'

export default () => {
  const formRef = React.useRef<SchemaFormProRef>(null)

  const initialValues = {
    "avatar": "https://global-image.smartdeer.work/test/images/0x4b93aaf0fd6b48739bc4afe7f09e3517.png",
  }

  const onFinish = (values: any) => {
    console.log('onFinish --- layout --- > ', values)
    console.log('onFinish --- layout --- > ', JSON.stringify(values))
  }

  return (
    <SchemaFormPro
      field='layout'
      ref={formRef}
      columns={columns}
      initialValues={initialValues}
      size='large'
      layout='horizontal'
      variables={{
        entityId: 2,
      }}
      onFinish={onFinish}
    />
  )
}
