import type { SchemaFormProStepsProps } from '@smartdeer-ui/pc';

export const items: SchemaFormProStepsProps['items'] = [
  {
    title: 'k1',
    columns: [
      {
        type: 'title',
        field: 'k1.title',
        props: {
          level: '2',
        },
        children: 'Your organization details',
      },
      {
        type: 'div',
        field: 'k1.div.description',
        props: {},
        children:
          'Please provide your Organization information accurately, it will be used in all your communications on the platform.',
      },
      {
        type: 'input',
        field: 'organizationName',
        title: 'Organization name',
        props: {
          placeholder: '请输入',
        },
        labelProps: {
          rules: [
            {
              required: true,
              message: 'Please input your organization name',
            },
          ],
        },
      },
      {
        type: 'input',
        field: 'organizationSize',
        title: 'Organization size',
        props: {
          placeholder: '请输入',
        },
      },
      {
        type: 'input',
        field: 'headquartersCountry',
        title: 'Headquarters country',
        props: {
          placeholder: '请输入',
        },
      },

      {
        type: 'flex',
        field: 'button-wrapper',
        props: {
          justify: 'center',
        },
        children: [
          {
            type: 'button',
            field: 'k1.button',
            props: {
              type: 'primary',
            },
            children: 'Next',
            effect: {
              publish: 'steps:form-steps-next',
            },
          },
        ],
      },
    ],
  },
  {
    title: 'k2',
    columns: [
      {
        type: 'title',
        field: 'k2.title',
        props: {
          level: '2',
        },
        children: 'Personal Details',
      },
      {
        type: 'div',
        field: 'k2.div.description',
        props: {},
        children:
          'Please provide your personal details, they will be used to complete your profile on ICB.',
      },
      {
        type: 'input',
        field: 'legalFirstName',
        title: 'Legal first name',
        props: {},
      },
      {
        type: 'input',
        field: 'legalLastName',
        title: 'Legal last name',
        props: {},
      },
      {
        type: 'input',
        field: 'preferredName',
        title: 'Preferred name (optional)',
        props: {},
      },
      {
        type: 'input',
        field: 'citizenOf',
        title: 'Citizen of',
        props: {},
      },
      {
        type: 'input',
        field: 'dateOfBirth',
        title: 'Date of birth (MM/DD/YYYY)',
        props: {},
      },
      {
        type: 'row',
        field: 'k2.row_1',
        children: [
          {
            type: 'col',
            field: 'k2.col_1',
            props: {
              span: '8',
            },
            children: [
              {
                type: 'input',
                field: 'dialCode',
                title: 'Dial Code',
                props: {
                  placeholder: '请输入内容',
                },
              },
            ],
          },
          {
            type: 'col',
            field: 'k2.col_2',
            props: {
              span: '16',
            },
            children: [
              {
                type: 'input',
                field: 'phoneNumber',
                title: 'Phone number',
                props: {
                  placeholder: '请输入',
                },
              },
            ],
          },
        ],
      },
      {
        type: 'input',
        field: 'department',
        title: 'Department',
        props: {},
      },
      {
        type: 'input',
        field: 'jobTitle',
        title: 'Job title',
        props: {},
      },
      {
        type: 'input',
        field: 'seniorityLevel',
        title: 'Seniority level',
        props: {},
      },

      {
        type: 'flex',
        field: 'button-wrapper',
        props: {
          justify: 'center',
        },
        children: [
          {
            type: 'button',
            field: 'step2.back1',
            props: {},
            children: '返回',
            effect: {
              publish: 'steps:form-steps-back',
            },
          },
          {
            type: 'button',
            field: 'k2.submit',
            props: {
              type: 'primary',
            },
            children: 'Submit',
            effect: {
              publish: 'steps:form-steps-submit',
            },
          },
        ],
      },
    ],
  },
];
