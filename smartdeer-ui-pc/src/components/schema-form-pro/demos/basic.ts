import type { SchemaFormProProps } from '@smartdeer-ui/pc';

export const columns: SchemaFormProProps['columns'] = [
  {
    type: 'input',
    field: 'input',
    title: 'input',
    props: {},
  },
  {
    type: 'datePicker',
    field: 'datePicker',
    title: 'datePicker',
    props: {},
  },
  {
    type: 'flex',
    field: 'button-wrapper',
    props: {
      justify: 'center',
    },
    children: [
      {
        type: 'button',
        field: 'reset',
        props: {},
        children: 'Reset',
        effect: {
          publish: 'basic:form-reset',
        },
      },
      {
        type: 'button',
        field: 'submit',
        props: {
          type: 'primary',
        },
        children: 'Submit',
        effect: {
          publish: 'basic:form-submit',
        },
      },
    ],
  },
];
