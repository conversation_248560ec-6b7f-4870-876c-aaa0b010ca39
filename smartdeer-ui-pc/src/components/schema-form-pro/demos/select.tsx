import React from 'react';
import { SchemaFormPro } from '@smartdeer-ui/pc'
import { columns } from './select'

export default () => {
  const onFinish = (values: any) => {
    console.log('onFinish --- select --- > ', values)
    console.log('onFinish --- select --- > ', JSON.stringify(values))
  }

  return (
    <SchemaFormPro
      field='select'
      columns={columns}
      size='large'
      layout='horizontal'
      variables={{
        entityId: 2,
      }}
      onFinish={onFinish}
    />
  )
}
