import React from 'react';
import { SchemaFormPro } from '@smartdeer-ui/pc'
import { items } from './steps.conf'

const { FormSteps } = SchemaFormPro

export default () => {

  const onFinish = (values: any) => {
    console.log('onFinish --- steps --- > ', values)
    console.log('onFinish --- steps --- > ', JSON.stringify(values))
  }

  return (
    <>
      <FormSteps
        field='steps'
        items={items}
        formProps={{
          size: 'large',
          layout: 'vertical'
        }}
        onFinish={onFinish}
      />
    </>
  )
}
