import type { SchemaFormProProps } from '@smartdeer-ui/pc';

export const columns: SchemaFormProProps['columns'] = [
  {
    type: 'select',
    field: 'select',
    title: 'select',
    props: {
      extendedFields: [
        { fromKey: 'value', to<PERSON><PERSON>: ['json', 'executeType'] },
        { fromKey: 'label', toKey: ['json', 'executeTypeStr'] },
      ],
      options: [
        {
          value: '1',
          label: 'month',
        },
        {
          value: '2',
          label: 'weekly',
        },
      ],
    },
  },

  {
    type: 'flex',
    field: 'button-wrapper',
    props: {
      justify: 'center',
    },
    children: [
      {
        type: 'button',
        field: 'reset',
        props: {},
        children: 'Reset',
        effect: {
          publish: 'select:form-reset',
        },
      },
      {
        type: 'button',
        field: 'submit',
        props: {
          type: 'primary',
        },
        children: 'Submit',
        effect: {
          publish: 'select:form-submit',
        },
      },
    ],
  },
];
