import type { SchemaFormProProps } from '@smartdeer-ui/pc';

export const columns: SchemaFormProProps['columns'] = [
  {
    type: 'row',
    field: 'row_1',
    children: [
      {
        type: 'col',
        field: 'col_1',
        props: {
          span: '12',
        },
        children: [
          {
            type: 'input',
            field: 'col_1_input',
            title: 'input1',
            props: {
              placeholder: '请输入内容',
            },
          },
        ],
      },
      {
        type: 'col',
        field: 'col_2',
        props: {
          span: '12',
        },
        children: [
          {
            type: 'input',
            field: 'col_2_input',
            title: 'input2',
            props: {
              placeholder: '请输入内容',
            },
          },
        ],
      },
    ],
  },

  {
    type: 'flex',
    field: 'button-wrapper',
    props: {
      justify: 'center',
    },
    children: [
      {
        type: 'button',
        field: 'reset',
        props: {},
        children: 'Reset',
        effect: {
          publish: 'layout:form-reset',
        },
      },
      {
        type: 'button',
        field: 'submit',
        props: {
          type: 'primary',
        },
        children: 'Submit',
        effect: {
          publish: 'layout:form-submit',
        },
      },
    ],
  },
];
