import React from 'react';
import { SchemaFormPro } from '@smartdeer-ui/pc'
import { columns } from './basic'

export default () => {
  const onFinish = (values: any) => {
    console.log('onFinish --- basic --- > ', values)
    console.log('onFinish --- basic --- > ', JSON.stringify(values))
  }

  return (
    <SchemaFormPro
      field='basic'
      columns={columns}
      size='large'
      layout='horizontal'
      variables={{
        entityId: 2,
      }}
      onFinish={onFinish}
    />
  )
}
