import React from 'react';
import { Select, Form, Input } from 'antd';
import type { SelectProps } from 'antd';
import { isString, get, isEmpty } from 'lodash';
import type { ProFieldFC } from '../../typing'
import { SchemaFormProContext } from '../../context'
import omit from '../../../../utils/omit'

interface FieldSelectProps {
  fieldProps: SelectProps
}

const FieldSelect: ProFieldFC<FieldSelectProps> = (props) => {
  const { value, onChange, fieldProps, field } = props

  const {
    disabled,
    options,
    responseLoading,
    responseData,
    extendedFields = [],

    ...restFieldProps
  } = fieldProps

  const { fieldNames } = restFieldProps

  const { form } = React.useContext(SchemaFormProContext)

  const handleClear = () => {

  }

  // 自定义搜索
  const filterOption = (input: string, option?: Record<string, any>) => {
    if (!option) return false

    let label = option.label

    if (fieldNames && fieldNames.label) {
      label = option[fieldNames.label]
    }

    return label.toLowerCase().includes(input.toLowerCase())
  }

  const handleChange = (value: string) => {
    onChange?.(value)

    // 扩展字段赋值
    if (extendedFields.length > 0) {
      const newOptions = responseData || options

      const option = newOptions.find((item: any) => item.value === value || (fieldNames && fieldNames.value && item[fieldNames.value] === value))

      extendedFields.forEach((item: any) => {
        const toValue = get(option, item.fromKey)
        form?.setFieldValue(item.toKey, toValue)
      })
    }
  }

  const isDisabled = isString(disabled) ? disabled === 'true' : disabled

  const selectProps = omit(restFieldProps, ['responseLoading', 'responseData', 'extendedFields'])

  return (
    <>
      <Select
        {...selectProps}
        showSearch={true}
        value={value}
        disabled={isDisabled}
        style={{ width: '100%' }}
        options={responseData || options}
        filterOption={fieldProps.remoteSearch ? false : filterOption}
        onChange={handleChange}
        loading={responseLoading}
        // notFoundContent={fetching ? <Spin size="small" /> : null}
        // onSearch={debounceFetcher}
        onClear={handleClear}
      />

      {/* 扩展字段渲染 */}
      {!isEmpty(extendedFields) && extendedFields.map((item, index) => {
        return (
          <Form.Item name={item.toKey} hidden key={index}>
            <Input />
          </Form.Item>
        )
      })}
    </>
  );
};


export default FieldSelect

