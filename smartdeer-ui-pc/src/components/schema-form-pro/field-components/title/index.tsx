import React from 'react';
import { Typography } from 'antd';
import type { TypographyProps } from 'antd';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'
import type { TitleProps } from 'antd/lib/typography/Title';


const { Title } = Typography;

const classPrefix = `deer-form-pro-title`

const defaultFieldProps = {
  level: '1'
}

interface FieldRowProps {
  fieldProps?: TypographyProps['Title']
}

const FieldTitle: ProFieldFC<FieldRowProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    level,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <Title
      {...restProps}
      level={Number(level) as any}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {children}
    </Title>
  );
};

export default FieldTitle
