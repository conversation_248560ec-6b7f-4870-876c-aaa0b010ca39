import React from 'react';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'
import SchemaPagePro from '../../../schema-page-pro'
import { SchemaFormProContext } from '../../context'
import { SchemaFormProStepsContext } from '../../context'

const classPrefix = `deer-form-pro-schema-page`

const defaultFieldProps = {
}

interface FieldRowProps {
  // fieldProps?: TypographyProps['Paragraph']
}

const FieldParagraph: ProFieldFC<FieldRowProps> = (props) => {
  const { fieldProps } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  const { form, formVariables, formInitialValues } = React.useContext(SchemaFormProContext)
  const { formStepsValues } = React.useContext(SchemaFormProStepsContext)

  const [show, setShow] = React.useState(false)

  const [dataSource, setDataSource] = React.useState({})

  React.useEffect(() => {
    const formValues = form?.getFieldsValue()

    setDataSource({
      ...formVariables,
      ...formInitialValues,
      ...formValues,
      ...formStepsValues
    })

    setShow(true)
  }, [])

  if (!show) {
    return null
  }

  return (
    <SchemaPagePro
      {...restProps}
      dataSource={dataSource}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    />
  );
};

export default FieldParagraph
