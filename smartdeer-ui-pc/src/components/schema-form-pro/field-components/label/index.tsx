import React from 'react';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const classPrefix = `deer-form-pro-label`

const defaultFieldProps = {
}

interface FieldLabelProps {
  // fieldProps?: ParagraphProps
}

const FieldLabel: ProFieldFC<FieldLabelProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <div
      {...restProps}
      className={`${classPrefix} ${className} `}
      style={{ ...style }}
    >
      <label >
        {children}
      </label>
    </div>
  );
};

export default FieldLabel
