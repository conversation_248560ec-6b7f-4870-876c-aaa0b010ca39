import React from 'react';
import { DatePicker } from 'antd';
import type { ProFieldFC } from '../../typing'
import { isString } from 'lodash';
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs';

const FieldDatePicker: ProFieldFC = (props) => {
  const { value, onChange, fieldProps } = props

  const handleChange = (date: Dayjs) => {

    onChange?.(date.valueOf() + '')
  }

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <DatePicker
      placeholder=' '
      {...fieldProps}
      value={value ? dayjs(Number(value)) : undefined}
      disabled={isDisabled}
      style={{ width: '100%', ...fieldProps?.style }}
      onChange={handleChange}
    />
  );
};

export default FieldDatePicker
