import React from 'react';
import { Button } from 'antd';
import type { ProFieldFC } from '../../typing'
import { isString } from 'lodash';

const FieldNext: ProFieldFC = (props) => {
  const { children, fieldProps } = props

  const {
    responseLoading,
    ...restFieldProps
  } = fieldProps || {}

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled


  const handleClick = () => {
    // props.onClick?.()

    console.log('next')

  }

  return (
    <Button
      {...restFieldProps}
      disabled={isDisabled}
      onClick={handleClick}
    >
      {children}
    </Button>
  );
};

export default FieldNext
