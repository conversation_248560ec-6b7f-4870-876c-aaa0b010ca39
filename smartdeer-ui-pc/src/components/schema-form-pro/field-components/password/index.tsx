import React from 'react';
import { Input } from 'antd';
import type { ProFieldFC } from '../../typing'
import { isString } from 'lodash';

const FieldPassword: ProFieldFC = (props) => {
  const { value, onChange, fieldProps } = props

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Input.Password
      {...fieldProps}
      disabled={isDisabled}
      value={value}
      onChange={onChange}
    />
  );
};

export default FieldPassword
