import React from 'react';
import { Button } from 'antd';
import type { ProFieldFC } from '../../typing'
import { isString } from 'lodash';

const FieldButton: ProFieldFC = (props) => {
  const { children, fieldProps } = props

  const {
    buttonLoading,
    ...restFieldProps
  } = fieldProps || {}

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Button
      {...restFieldProps}
      loading={buttonLoading}
      disabled={isDisabled}
    >
      {children}
    </Button>
  );
};

export default FieldButton
