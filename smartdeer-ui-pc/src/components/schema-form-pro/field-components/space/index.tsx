import React from 'react';
import classNames from 'classnames';
import { Space } from 'antd'
import type { SpaceProps } from 'antd'
import { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const classPrefix = `deer-form-pro-space`

interface FieldSpaceProps {
  fieldProps: SpaceProps
}

const defaultFieldProps = {
  size: 'middle',
}

const FieldSpace: ProFieldFC<FieldSpaceProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restFieldProps
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <Space
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restFieldProps}
    >
      {children}
    </Space >
  );
};

export default FieldSpace
