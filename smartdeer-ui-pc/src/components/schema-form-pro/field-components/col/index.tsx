import React from 'react';
import { Col } from 'antd';
import type { ColProps } from 'antd';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const classPrefix = `deer-form-pro-item`

interface FieldColProps {
  fieldProps?: ColProps
}

const defaultFieldProps = {
  span: 12
}

const FieldCol: ProFieldFC<FieldColProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <Col
      {...restProps}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {children}
    </Col>
  );
};

export default FieldCol
