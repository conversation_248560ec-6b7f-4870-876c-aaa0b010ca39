import React from 'react';
import { Typography } from 'antd';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'
import type { ParagraphProps } from 'antd/es/typography/Paragraph';

const { Paragraph } = Typography;

const classPrefix = `deer-form-pro-paragraph`

const defaultFieldProps = {
}

interface FieldParagraphProps {
  fieldProps?: ParagraphProps
}

const FieldParagraph: ProFieldFC<FieldParagraphProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <Paragraph
      {...restProps}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {children}
    </Paragraph>
  );
};

export default FieldParagraph
