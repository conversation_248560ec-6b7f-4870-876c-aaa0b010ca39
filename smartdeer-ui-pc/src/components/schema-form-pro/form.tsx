import React from 'react';
import { Form, FormProps } from 'antd';
import type { FormInstance } from 'antd';
import { isEmpty } from 'lodash';
import renderValueType from './value-type';
import { mergeProps } from '../../utils/withDefaultProps';
import { omitUndefined } from '../../utils/omitUndefined';
import { isArray, isObject, isString } from 'lodash';
import { SchemaFormProContext } from './context';
import type { SchemaFormProContextType } from './context';
import { useRefFunction, useDeepCompareMemo } from '../../utils/hooks';
import pageEventBus from '../../bus/pageEventHub'
import { PAGE_EVENT_NAME } from '../../consts'
import { delay } from '../../utils';

import type { FormColumnsType, RenderValueTypeHelpers } from './typing';

import './form.less';

const classPrefix = `deer-schema-form-pro`;

export {
  SchemaFormProContext,
  type SchemaFormProContextType,
};

export interface SchemaFormProRef extends FormInstance {
  submit: () => void;
}


export interface SchemaFormProProps extends Omit<FormProps, 'form'> {
  field?: SchemaFormProContextType['field'];
  columns?: FormColumnsType[];
  submitText?: string;
  resetText?: string;
  variables?: SchemaFormProContextType['formVariables'];
  onReset?: () => void;
  initialValues?: Record<string, any>;
}

const defaultProps = {
  columns: [],
  size: 'large',
  layout: 'vertical',
  labelAlign: 'right',
  labelWrap: false,
  variables: {},
  initialValues: {}
};

interface SchemaFormContentProps {
  columns: FormColumnsType[]
}

const SchemaFormContent: React.FC<SchemaFormContentProps> = (props) => {
  const { columns } = props;

  /**
   * 生成子项
   *
   * @param items
   */
  const generateItems: RenderValueTypeHelpers['generateItems'] = useRefFunction((items: FormColumnsType[]) => {
    return items
      .map((originItem, index) => {
        const item = omitUndefined({
          type: originItem.type,
          field: originItem.field,
          title: originItem.title,
          hidden: originItem.hidden,
          fieldProps: omitUndefined({
            ...originItem.props,
          }) || {},
          labelProps: omitUndefined({
            ...originItem.labelProps
          }) || {},
          effect: originItem.effect,
          children: isArray(originItem.children) ? generateItems(originItem.children) : isObject(originItem.children) ? generateItems([originItem.children]) : isString(originItem.children) ? originItem.children : undefined,
          dependencies: originItem.dependencies,
        });

        return renderValueType(item, {
          originItem,
          index,
          generateItems,
        });

      }).filter((field) => {
        return Boolean(field);
      });
  });

  const childNode = useDeepCompareMemo(() => {
    if (columns.length === 0) return null;

    return generateItems(columns);
  }, [columns]);


  return (
    <> {childNode}</>
  );
}

const SchemaFormPro = React.forwardRef<FormInstance, SchemaFormProProps>((p, ref) => {
  const props = mergeProps(defaultProps, p);

  const {
    field,
    columns,
    initialValues,
    variables,

    onFinish,
    onFinishFailed,
    onReset,
    ...restProps
  } = props;

  const [form] = Form.useForm();

  React.useImperativeHandle(ref, () => form);

  const handleFinish = async () => {
    form.submit()

    await delay(1000)
  };

  const handleReset = () => {
    form.resetFields();

    onReset?.();
  };

  React.useEffect(() => {
    if (!field) return

    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME['form-submit']}`, handleFinish)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME['form-reset']}`, handleReset)

    return () => {
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME['form-submit']}`, handleFinish)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME['form-reset']}`, handleReset)
    }
  }, [])


  const preventEnterPress = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter' && (e.target as HTMLElement)?.tagName === 'BUTTON') {
      e.preventDefault();
    }
  };

  const formContextValue = useDeepCompareMemo(() => {
    return {
      field,
      form,
      formInitialValues: initialValues,
      formVariables: variables,
    };
  }, [field, variables]);

  return (
    <Form
      autoComplete='off'
      form={form}
      initialValues={initialValues}
      className={classPrefix}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      onKeyDown={preventEnterPress}
      {...restProps}
    >
      <SchemaFormProContext.Provider
        value={formContextValue}
      >
        {!isEmpty(columns) && <SchemaFormContent columns={columns} />}
      </SchemaFormProContext.Provider>
    </Form>
  );
});

export default SchemaFormPro;
