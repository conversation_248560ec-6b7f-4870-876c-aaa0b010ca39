
import React from 'react';
import isDeepEqualReact from '../../../utils/isDeepEqualReact'
import { omitUndefined } from '../../../utils/omitUndefined'
import type { ItemType, RenderValueTypeHelpers } from '../typing'
import FormItem from '../form-item'
import EffectHoc from './effect-hoc'

// 布局组件
import FieldRow from '../field-components/row'
import FieldCol from '../field-components/col'
import FieldDivider from '../field-components/divider'
import FieldFlex from '../field-components/flex'
import FieldSpace from '../field-components/space'

// 元素组件
import FieldTitle from '../field-components/title'
import FieldParagraph from '../field-components/div'
import FieldText from '../field-components/text'
import FieldLabel from '../field-components/label'

// 通用组件
import FieldButton from '../field-components/button'
import FieldNext from '../field-components/next'

// form 组件
import FieldInput from '../field-components/input'
import FieldPassword from '../field-components/password'
import FieldSelect from '../field-components/select'
import FieldRadio from '../field-components/radio'
import FieldTextArea from '../field-components/text-area'
import FieldDatePicker from '../field-components/date-picker'

// 展示组件
import FieldSchemaPage from '../field-components/schema-page'

interface FormFieldProps extends ItemType {
  children?: React.ReactNode | React.ReactNode[];
  dependenciesValues?: Record<string, any>,
}

const defaultRenderComponent = (
  valueType: string,
  props: Omit<FormFieldProps, 'type'>,
) => {
  const { children, effect, ...restProps } = props

  let childNode: React.ReactNode = null;
  switch (valueType) {
    // ---------------------------- 布局组件 ----------------------------

    case 'row':
      childNode = <FieldRow {...restProps}>{children}</FieldRow>
      break;

    case 'col':
      childNode = <FieldCol {...restProps}>{children}</FieldCol>;
      break;

    case 'divider':
      childNode = <FieldDivider {...restProps as any}>{children}</FieldDivider>;
      break;

    case 'space':
      childNode = <FieldSpace {...restProps as any}>{children}</FieldSpace>;
      break;

    case 'flex':
      childNode = <FieldFlex {...restProps as any}>{children}</FieldFlex>;
      break;

    // ---------------------------- 元素组件 ----------------------------
    case 'title':
      childNode = <FieldTitle {...restProps as any}>{children}</FieldTitle>;
      break;

    case 'div':
      childNode = <FieldParagraph {...restProps as any}>{children}</FieldParagraph>;
      break;

    case 'text':
      childNode = <FieldText {...restProps as any}>{children}</FieldText>;
      break;

    case 'label':
      childNode = <FieldLabel {...restProps as any}>{children}</FieldLabel>;
      break;

    // ---------------------------- 通用组件 ----------------------------

    case 'button':
      childNode = <FieldButton {...restProps}>{children}</FieldButton>;
      break;

    case 'next':
      childNode = <FieldNext {...restProps}>{children}</FieldNext>;
      break;

    // ---------------------------- 表单组件 ----------------------------

    case 'input':
      childNode = (
        <FieldInput {...restProps}>{children}</FieldInput>
      );
      break;

    case 'password':
      childNode = (
        <FieldPassword {...restProps}>{children}</FieldPassword>
      );
      break;

    case 'select':
      childNode = (
        <FieldSelect {...restProps as any}>{children}</FieldSelect>
      );
      break;

    case 'radio':
      childNode = (
        <FieldRadio {...restProps as any}>{children}</FieldRadio>
      );
      break;

    case 'textArea':
      childNode = (
        <FieldTextArea {...restProps as any}>{children}</FieldTextArea>
      );
      break;

    case 'datePicker':
      childNode = (
        <FieldDatePicker {...restProps as any}>{children}</FieldDatePicker>
      );
      break;

    // ---------------------------- 展示组件 ----------------------------

    case 'schemaPage':
      childNode = (
        <FieldSchemaPage {...restProps as any}>{children}</FieldSchemaPage>
      );
      break;

    default:
      childNode = <div style={{ background: 'red', color: '#fff', borderRadius: '8px', padding: '5px', margin: '24px 0' }} >没有实现的组件: {valueType}</div>;
  }

  if (effect) {
    childNode = <EffectHoc effect={effect}>{childNode}</EffectHoc>
  }

  return <>{childNode}</>;
};

const fromComponentValue = ['input', 'password', 'select', 'datePicker']

const FormField: React.FC<FormFieldProps> = (props) => {
  const {
    type,
    effect,
    ...restProps
  } = props;

  const renderedDom = defaultRenderComponent(type, omitUndefined({
    ...restProps,
    effect: omitUndefined({
      fetch: effect?.fetch,
      value: effect?.value,
      switchValue: effect?.switchValue,
      getValue: effect?.getValue,
      disabled: effect?.disabled,
      publish: effect?.publish,
      click: effect?.click,
    })
  }))

  if (fromComponentValue.includes(type)) {
    const formItemProps = omitUndefined({
      ...props,
      effect: omitUndefined({
        required: effect?.required,
        labelDescription: effect?.labelDescription,
      })
    })

    return (
      <FormItem {...formItemProps}>
        {renderedDom}
      </FormItem>
    );
  }

  return (
    <>{renderedDom}</>
  );
};

export default React.memo(FormField, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
