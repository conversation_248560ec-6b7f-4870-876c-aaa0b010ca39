
import React from 'react';
import pageEventBus from '../../../bus/pageEventHub'
import { isNull, isString } from 'lodash';
import { extractParenthesesContent } from '../../../utils/extract'
import { omitUndefined } from '../../../utils/omitUndefined'
import { debounce, get } from 'lodash'
import { EffectType } from '../../../typing';
import { getEffectFetchConfig } from '../../../utils/getEffectFetchConfig'
import { ConfigContext } from '../../config-provider'
import { useParamsDict } from '../../../utils/hooks'
import { cachedSwrFetcher } from '../../../utils/swrFetcher'

type RecordType = Record<string, any>

interface EffectHocProps {
  effect: EffectType
  children: React.ReactElement
  dependenciesValues?: RecordType
  value?: any;
  onChange?: (...args: any) => void;
}

const EffectHoc: React.FC<EffectHocProps> = (props) => {
  const { effect, children, dependenciesValues, value, onChange } = props

  const { publish, fetch: fetchConfig } = effect

  const paramsDict = useParamsDict(dependenciesValues || {})

  const configContext = React.useContext(ConfigContext)

  const [buttonLoading, setButtonLoading] = React.useState<boolean>(false)
  const [responseLoading, setResponseLoading] = React.useState<boolean>(false)
  const [responseData, setResponseData] = React.useState<RecordType | RecordType[]>()

  const fetch = async () => {
    setResponseLoading(true)

    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(fetchConfig!, configContext, paramsDict)

      const { data } = await cachedSwrFetcher(api, method, params)

      const result = dataIndex ? get(data, dataIndex) : data

      setResponseData(result)
    } catch (error) {
      console.error('fetch error', error)
    }

    setResponseLoading(false)
  }

  React.useEffect(() => {
    if (!fetchConfig) return

    fetch()
  }, [])

  // console.log('effect hoc', children?.props)

  const handleClick = debounce(async () => {
    if (!publish) return;

    setButtonLoading(true);

    const eventList = isString(publish) ? [publish] : publish;

    try {
      // 创建一个包含所有异步事件的 Promise 数组
      const eventPromises = eventList.map(item => {
        const eventName = item.includes('(') ? item.split('(')[0] : item;

        let params: null | string | string[] = extractParenthesesContent(item.replace(/\s+/g, ''));

        if (isString(params)) {
          params = params.split(',').map(item => item.trim());
        } else if (isNull(params)) {
          params = [];
        }

        // 返回 pageEventBus.publish 的 Promise
        return pageEventBus.publish(eventName, ...params);
      });

      // 等待所有事件完成
      await Promise.all(eventPromises);
    } catch (error) {
      console.error('Error publishing events:', error);
    } finally {
      // 无论成功或失败，最后都关闭 loading 状态
      setButtonLoading(false);
    }
  }, 1000, { leading: true, trailing: false });

  const fieldProps = omitUndefined({
    ...children?.props?.fieldProps,
    onClick: publish ? handleClick : undefined,
    responseData: fetchConfig ? responseData : undefined,
    responseLoading: fetchConfig ? responseLoading : undefined,
    buttonLoading: buttonLoading,
  })

  return (
    <React.Fragment>
      {React.cloneElement(children, {
        ...children?.props,
        fieldProps: fieldProps || {},
        value,
        onChange
      })}
    </React.Fragment>
  )
}

export default EffectHoc
