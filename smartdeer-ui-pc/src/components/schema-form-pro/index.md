---
toc: content
group: 
  title: 建设
  order: 19
---

# SchemaFormPro 高级表单

开发中

## 示例

<code src="./demos/basic.tsx">基本使用</code>
<code src="./demos/layout.tsx">布局</code>
<code src="./demos/select.tsx">Select高级用法</code>
<code src="./demos/steps.tsx">Steps</code>

## API

### Form

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| variables | 全局变量 | `object` | `--` | |
| columns | 表单的定义，一般是 json 对象 | `object` | `--` | |
| initialValues | 表单默认值，只有初始化以及重置时生效	| `object` | `--` | |
| size |	设置字段组件的尺寸（仅限 antd 组件） | `small` \| `middle` \| `large	` | `large` | |
| layout | 表单布局	 | `horizontal` \| `vertical` \| `inline` | `horizontal` | |
| onFinish | 提交表单且数据验证成功后回调事件		| `function(values)	` | `--` | |

支持原生 [antd form](https://ant-design.antgroup.com/components/form-cn) 的所有属性。

## Steps

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| items | 全局变量 | `itemType[]` | `--` | |
| forceRender | 被隐藏时是否渲染 DOM 结构	 | `boolean` | `false` | |
| destroyInactiveTabPane | 被隐藏时是否销毁 DOM 结构 | `boolean` | `false` | |
| form | 参数 from 配置	| `Form` | `--` | |
| onFinish | 提交表单且数据验证成功后回调事件		| `function(values)	` | `--` | |

### ItemType

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| key | 唯一标识 | `string` | `--` |  |
| forceRender | 被隐藏时是否渲染 DOM 结构	 | `boolean` | `false` | |
| destroyInactiveTabPane | 被隐藏时是否销毁 DOM 结构 | `boolean` | `false` | |
| form | 参数 from 配置	| `Form` | `--` | |

