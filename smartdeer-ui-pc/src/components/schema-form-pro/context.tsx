import React from 'react'
import type { FormInstance } from 'antd';

type RecordType = Record<string, any>

export type SchemaFormProContextType = {
  field?: string;
  form?: FormInstance<any>;
  formInitialValues: RecordType;
  formVariables: RecordType;
}

export const defaultFormContext: SchemaFormProContextType = {
  formVariables: {},
  formInitialValues: {},
}

export const SchemaFormProContext =
  React.createContext<SchemaFormProContextType>(defaultFormContext)


export type SchemaFormProStepsType = {
  formStepsValues: RecordType;
}

export const defaultFormStepsContext: SchemaFormProStepsType = {
  formStepsValues: {}
}

export const SchemaFormProStepsContext =
  React.createContext<SchemaFormProStepsType>(defaultFormStepsContext)

