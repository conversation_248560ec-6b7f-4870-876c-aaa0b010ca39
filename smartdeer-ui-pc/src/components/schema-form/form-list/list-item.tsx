import React from 'react';
import { Row, Popconfirm } from 'antd'
import type { FormListFieldData, FormListOperation } from 'antd'
import { FormListContext } from '.';
import Item from '../../icon'
import { ConfigContext } from '../../config-provider'

const classPrefix = `deer-form-list-item`

interface FormListItemProps {
  children: React.ReactNode;
  field: FormListFieldData;
  fields: FormListFieldData[];
  action: FormListOperation;
  index: number;
  count: number;
  originName?: string | string[];

  title?: string | React.ReactElement;
  bodyStyle?: React.CSSProperties;
  popconfirm?: {
    title?: string;
    description?: string;
  };
  readonly?: boolean;
}

const FormListItem: React.FC<FormListItemProps> = (props) => {
  const {
    field,
    fields,
    children,
    originName,
    title,
    index,
    count,
    action,
    bodyStyle,
    popconfirm,
    readonly = false,
    ...extra
  } = props;

  const { language } = React.useContext(ConfigContext);
  const listContext = React.useContext(FormListContext);

  const childrenArray = React.Children.map(children, (item, itemIndex) => {
    if (React.isValidElement(item)) {
      return React.cloneElement(item, {
        key: item.key || item?.props?.name || itemIndex,
        formListField: field,
        ...(item?.props || {}),
      });
    }
    return item;
  });

  return (
    <FormListContext.Provider
      value={{
        ...field,
        index: field.name,
        prevIndex: listContext.index,
        prev: listContext,
        listName: [listContext.listName, originName, field.name]
          .filter((item) => item !== undefined)
          .flat(1),
      }}
    >
      <div className={classPrefix}>
        <div className={`${classPrefix}-title`}>
          <strong>{title} {index !== 0 ? index + 1 : ''}</strong>

          {!readonly && count > 1 && (
            <Popconfirm
              placement='top'
              icon={<></>}
              title={popconfirm?.title || language.form.popconfirm.title}
              description={popconfirm?.description || ''}
              okText={language.form.popconfirm.okText}
              cancelText={language.form.popconfirm.cancelText}
              onConfirm={() => action.remove(index)}
            >
              <a className={`${classPrefix}-del`} >
                <Item.DeleteOutline />
                <span style={{ marginLeft: '6px' }}>
                  {language.form.deleteText}
                </span>
              </a>
            </Popconfirm>
          )}
        </div>

        <div style={bodyStyle}>
          <Row gutter={24}>
            {childrenArray}
          </Row>
        </div>
      </div>
    </FormListContext.Provider>
  )
};

export default FormListItem;
