import React from 'react';
import classNames from 'classnames'
import { Form, Col } from 'antd';
import type { ItemType } from '../typing'
import { mergeProps } from '../../../utils/withDefaultProps'
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'
import { omit } from '../../../utils'
import { isDeepEqualReact } from '../../../utils'
import { SchemaFormContext } from '../context'
import { isEmpty } from 'lodash';
import FormListContainer from './list-container'
import type { NamePath } from 'antd/lib/form/interface';
import type { FormListFieldData } from 'antd'
import { useDeepCompareMemo } from '../../../utils/hooks'

import './index.less'

const classPrefix = `deer-form-list`

export type FormListProps = ItemType & {
  dependenciesValues?: Record<string, any>,
  initialValues?: Record<string, any>,
  children?: React.ReactNode,
}

const defaultProps = {
  labelProps: {},
  dependenciesValues: {}
}

export const FormListContext = React.createContext<
  | (FormListFieldData & {
    listName: NamePath;
    index: number;
  })
  | Record<string, any>
>({});


const FormList: React.FC<FormListProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    field,
    title,
    labelProps,
    dependenciesValues,
    children,
    colProps,
    formListField,
    ...extra
  } = props

  // 副作用
  const effect = extra?.effect

  const { formVariables } = React.useContext(SchemaFormContext);

  const name = useDeepCompareMemo(() => {
    return formListField ? [formListField.name, field] : field as NamePath
  }, [field])

  // 判断副作用 show 以及依赖数据是否存在
  if (!!effect?.show && (!isEmpty(dependenciesValues) || !isEmpty(formVariables))) {
    const variables = { ...formVariables, ...dependenciesValues }

    // 判断是否需要显示
    const show = evaluateLogicalExpression(variables, effect?.show)

    if (!show) return <></>
  }

  const omitKeys = ['className', 'style', 'rules', 'dependencies']

  const formListProps = omit(labelProps, omitKeys)

  return (
    <Col {...colProps}>
      <div
        className={classNames(classPrefix)}
        style={{ ...labelProps?.style }}
      >
        <Form.List
          name={name}
          {...formListProps}
        >
          {(fields, action, meta) => {
            return (
              <FormListContainer
                fields={fields}
                action={action}
                meta={meta}
                originName={field}
                title={title}
                bodyStyle={labelProps?.bodyStyle}
                maxCount={labelProps?.maxCount}
                addText={labelProps?.addText}
                popconfirm={labelProps?.popconfirm}
                readonly={labelProps?.readonly}
              >
                {children}
              </FormListContainer>
            )
          }}
        </Form.List>
      </div>
    </Col>
  )
};

export default React.memo(FormList, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
