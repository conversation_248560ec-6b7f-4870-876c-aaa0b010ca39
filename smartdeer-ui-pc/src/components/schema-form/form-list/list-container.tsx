import React from 'react';
import type { FormListFieldData, FormListOperation } from 'antd'
import { nanoid } from '@ant-design/pro-utils';
import FormListItem from './list-item';
import {
  PlusOutlined,
} from '@ant-design/icons';
import { ConfigContext } from '../../config-provider'

const classPrefix = `deer-form-list`

interface FormListContainerProps {
  children: React.ReactNode;
  fields: FormListFieldData[];
  action: FormListOperation;
  originName?: string | string[];
  meta: {
    errors?: React.ReactNode[];
    warnings?: React.ReactNode[];
  };
  title?: string | React.ReactElement;
  bodyStyle?: React.CSSProperties;
  maxCount?: number | string;
  addText?: string;
  popconfirm?: {
    title?: string;
    description?: string;
  };
  //  只读
  readonly?: boolean;
}

const FormListContainer: React.FC<FormListContainerProps> = (props) => {
  const {
    fields,
    children,
    action,
    maxCount = 5,
    addText,
    readonly = false,
  } = props;

  const { language } = React.useContext(ConfigContext);

  const fieldKeyMap = React.useRef(new Map<string, string>());

  const uuidFields = React.useMemo(() => {
    return fields.map((field) => {
      if (!fieldKeyMap.current?.has(field.key.toString())) {
        fieldKeyMap.current?.set(field.key.toString(), nanoid());
      }
      const uuid = fieldKeyMap.current?.get(field.key.toString());
      return {
        ...field,
        uuid,
      };
    });
  }, [fields]);

  const itemList = React.useMemo(() => {
    return uuidFields.map((field, index) => {
      return (
        <FormListItem
          {...props}
          key={field.uuid}
          field={field}
          index={index}
          count={uuidFields.length}
          readonly={readonly}
        >
          {children}
        </FormListItem>
      );
    });
  }, [children, props, uuidFields]);

  const handleClickAdd = () => {
    if (maxCount !== undefined && uuidFields.length >= Number(maxCount)) {
      return;
    }

    action.add()
  }

  return (
    <>
      {itemList}
      {
        (!readonly && itemList.length < Number(maxCount)) && <div className={`${classPrefix}-add`}>
          <a onClick={handleClickAdd}>
            <PlusOutlined />
            <span>{addText || language.form.addText}</span>
          </a>
        </div>
      }
    </>
  )
};

export default FormListContainer;

