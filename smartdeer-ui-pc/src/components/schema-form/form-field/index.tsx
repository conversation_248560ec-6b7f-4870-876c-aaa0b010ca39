
import React from 'react';
import { Col, Flex, Form, Space } from 'antd';
import { isEmpty } from 'lodash';
import isDeepEqualReact from '../../../utils/isDeepEqualReact'
import { SchemaFormContext } from '../context'
import { useParamsDict } from '../../../utils/hooks'
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'
import { replaceVariablesInTemplate } from '../../../utils/replaceVariablesInTemplate'
import type { EffectType } from '../../../typing';
import InnerHtml from '../../inner-html';
import type { ItemType } from '../typing'
import { FormListContext } from '../form-list';

interface FormFieldProps extends Omit<ItemType, 'children'> {
  children: React.ReactNode;
  helpers: Record<string, any>,
}

const classPrefix = `deer-form-item`

const FormField: React.FC<FormFieldProps> = (props) => {
  const {
    dependenciesValues,
    children,
    effect,
    helpers,
    ...restProps
  } = props;
  const formListContext = React.useContext(FormListContext);

  const { formVariables } = React.useContext(SchemaFormContext);

  const paramsDict = useParamsDict()

  // 判断副作用 show 以及依赖数据是否存在
  if (!!effect?.show && (!isEmpty(dependenciesValues) || !isEmpty(formVariables) || !isEmpty(paramsDict))) {
    const variables = { ...paramsDict, ...formVariables, ...dependenciesValues, index: formListContext.name }

    const effectShow = replaceVariablesInTemplate(variables, effect?.show, /\*<([^}]+)>/g)

    // 判断是否需要显示
    const show = evaluateLogicalExpression(variables, effectShow)

    if (!show) return <></>
  }

  let childNode: React.ReactNode = null;

  if (restProps.type === 'space') {

    if (!restProps.childrenColumns || !Array.isArray(restProps.childrenColumns)) return null;

    let labelView;

    if (restProps.title) {
      labelView = <div>
        <div className={`${classPrefix}-title`}>{restProps.title}</div>
        {!!restProps.descriptionStr && (
          <div className={`${classPrefix}-description`}>
            {restProps.descriptionStr.includes('div') ? <InnerHtml content={restProps.descriptionStr} /> : restProps.descriptionStr}
          </div>
        )}
      </div>;
    }

    childNode = (
      <Form.Item label={labelView} className={'deer-form-restProps-title'}>
        <Space {...restProps.props} className={'smart-deer-form-restProps-space'}>
          {helpers.genItems(restProps.childrenColumns)}
        </Space>
      </Form.Item>
    );
  }

  if (restProps.type === 'label') {
    childNode = (
      <div className={`deer-form-item-customize-label `}{...restProps.props}>
        <div>
          <h4 className={`${restProps.props?.required !== false && 'deer-form-item-customize-label-required'}`}>
            {restProps.title}
          </h4>
          {restProps?.labelProps?.description && (
            <div style={{ color: "#C2C2C2", "fontSize": "12px" }}>
              {restProps?.labelProps?.description}
            </div>
          )}
        </div>
      </div>
    )
  }

  if (restProps.type === 'h1') {
    childNode = <h1  {...restProps.props}>{restProps.title}</h1>
  }

  if (restProps.type === 'h2') {
    childNode = <h2  {...restProps.props}>{restProps.title}</h2>
  }

  if (restProps.type === 'h3') {
    childNode = <h3  {...restProps.props}>{restProps.title}</h3>
  }

  if (restProps.type === 'h4') {
    childNode = <h4  {...restProps.props}>{restProps.title}</h4>
  }

  if (restProps.type === 'h5') {
    childNode = <h5  {...restProps.props}>{restProps.title}</h5>
  }

  if (restProps.type === 'h6') {
    childNode = <h5  {...restProps.props}>{restProps.title}</h5>
  }

  if (restProps.type === 'div') {
    childNode = <div  {...restProps.props}>{restProps.title}</div>
  }

  if (restProps.type === 'p') {
    childNode = <p  {...restProps.props}>{restProps.title}</p>
  }

  if (restProps.type === 'span') {
    childNode = <span  {...restProps.props}>{restProps.title}</span>
  }

  if (restProps.type === 'html') {
    childNode = <InnerHtml  {...restProps.props} content={restProps?.content || restProps?.title || ''} />
  }

  if (restProps.type === 'grayInfo') {
    childNode = (
      <Flex
        justify={`space-between`}
        className={`bg-gray-100 text-black rounded px-2 p-3 ${restProps.props?.className}`}
      >
        <div className={``}>{restProps.props?.label}</div>
        <div className={`font-bold`}>{restProps.props?.value}</div>
      </Flex>
    )
  }

  if (childNode) {
    return <Col span={helpers.colProps?.span}>{childNode}</Col>
  }

  return <>{children}</>;
};

export default React.memo(FormField, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
