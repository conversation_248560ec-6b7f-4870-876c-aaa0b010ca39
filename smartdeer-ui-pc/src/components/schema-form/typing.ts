import React from 'react';

import type { EffectType } from '../../typing';

import type {
  FieldModalType,
  FieldOptionType,
  FieldPropsType,
  FieldValueType,
} from './field-components/typing';

export type DependenciesValuesType = {
  disabled?: boolean;
  show?: boolean;
  hide?: boolean;
  required?: boolean;
};

export type FormColumnsValidateType = {
  trigger?: string;
  mode?: string;
  message?: string;
  pattern?: string;
  type?: FormValidateType;
};

export type ChildrenType = {
  type: 'col';
  props: {
    span: number;
  };
  children: FormColumnsType[];
  [key: string]: any;
};

type FieldHtmlVlaueType =
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'div'
  | 'p'
  | 'span'
  | 'grayInfo'
  | 'html';

export type FormColumnsType = {
  // 组件类型
  type: FieldValueType | FieldHtmlVlaueType;

  // 唯一标识
  field?: string | string[];

  // 转行成需要的真实提交的字段
  transformField?: string;

  // 标签的文本
  title?: string | React.ReactElement;

  // 显示隐藏
  hidden?: boolean;

  // 组件的 props
  props?: {
    options?: FieldOptionType[];
    addonAfter?: any;
    fieldNames?: {
      label?: string;
      value?: string;
      description?: string;
      key?: string;
      children?: string;
      extra?: string;
      templateKey?: string;
    };

    [key: string]: any;
  };

  // label props
  labelProps?: {
    hideTitle?: boolean;
    noStyle?: boolean;

    initialValue?: string;

    // 描述
    description?: string;

    className?: string;

    style?: React.CSSProperties;
    bodyStyle?: React.CSSProperties;

    rules?: FormRuleType[];

    maxCount?: number | string;

    [key: string]: any;

    popconfirm?: {
      title?: string;
      description?: string;
    };
  };

  // 副作用
  effect?: EffectType;

  // 孩子
  children?: FormColumnsType[];

  fieldModalSteps?: FieldModalType[];
  fieldModal?: FieldModalType;

  // 依赖项 field 正在被舍弃，请改用 dependencies，但是不要删除，已保证旧的代码可以正常执行
  dependencys?: string[];

  // 依赖项 field
  dependencies?: Array<string> | Array<string[]>;

  // 必填
  required?: boolean;

  // 校验
  validate?: [];

  //  Col组件Props
  colProps?: { span: number; key: string };

  [key: string]: any;
};

export type ItemType = FormColumnsType & {
  childrenColumns?: FormColumnsType[];
  columns?: FormColumnsType[];
};

/**
 * 指示要使用的验证器类型。公认的类型值是：
 *
 * string: Must be of type string. This is the default type.
 * number: Must be of type number.
 * boolean: Must be of type boolean.
 * method: Must be of type function.
 * regexp: Must be an instance of RegExp or a string that does not generate an exception when creating a new RegExp.
 * integer: Must be of type number and an integer.
 * float: Must be of type number and a floating point number.
 * array: Must be an array as determined by Array.isArray.
 * object: Must be of type object and not Array.isArray.
 * enum: Value must exist in the enum.
 * date: Value must be valid as determined by Date
 * url: Must be of type url.
 * hex: Must be of type hex.
 * email: Must be of type email.
 * any: Can be any type.
 * */
export type FormValidateType =
  | 'string'
  | 'array'
  | 'number'
  | 'integer'
  | 'float'
  | 'object'
  | 'date'
  | 'url'
  | 'hex'
  | 'email';

export type FormRuleType = {
  message: string;
  required: boolean;
  pattern?: string | RegExp;

  type?: 'customValidator';
  condition?: '===' | '!==' | '>' | '>=' | '<' | '<=';
  fieldToCompare?: string;
};

export type FormLabelType = {
  label: string;
  name: string;
  tooltip: string;
  rules: FormRuleType[];
  noStyle?: boolean;
};

export type FormFieldPropsType = FieldPropsType;

export type SchemaRenderValueTypeFunction = (
  item: ItemType,
  helpers: Record<string, any>,
) => React.ReactNode;
