import React from 'react'
import type { FormInstance } from 'antd';
import type { FormColumnsType } from './typing'

type RecordType = Record<string, any>

export type SchemaFormContextType = {
  form?: FormInstance<any>;
  formVariables: RecordType;
  supplementingTheString?: boolean;
  middleware: Record<string, (values: RecordType) => string>;
  fileExpire: boolean;
  onAppend: (field: string, columns: FormColumnsType[]) => void;
}

export const defaultFormContext: SchemaFormContextType = {
  formVariables: {},
  supplementingTheString: true,
  middleware: {},
  fileExpire: false,
  onAppend: () => { },
}

export const SchemaFormContext =
  React.createContext<SchemaFormContextType>(defaultFormContext)


const transformFinishValues = [
  {
    "from": [
      "organizationName",
      "organizationLogo",
      "shortDescription"
    ],
    "to": "organization",
    "format": "join"
  }
]

const transform = (values: RecordType) => {
  transformFinishValues.forEach(({ from, to, format }) => {
    if (format === 'join') {
      values[to] = from.map(key => values[key]).join(' ')
    }
  })
};

