import type { FormInstance } from 'antd';
import { Button, Form, FormProps, Row, Space } from 'antd';
import classNames from 'classnames';
import { isEmpty, isObject, isString } from 'lodash';
import React from 'react';
import { deepMergeImmutable } from '../../utils/deepMerge';
import { useDeepCompareMemo, useRefFunction } from '../../utils/hooks';
import { omitUndefined } from '../../utils/omitUndefined';
import { mergeProps } from '../../utils/withDefaultProps';
import { ConfigContext } from '../config-provider';
import type { SchemaFormContextType } from './context';
import { SchemaFormContext } from './context';
import { getGridMapping } from './utils';
import renderValueType from './value-type';

import type { FormColumnsType } from './typing';

import './form.less';

const classPrefix = `deer-schema-form`;

type RecordType = Record<string, any>;

export { SchemaFormContext, type SchemaFormContextType };

export interface SchemaFormRef extends FormInstance {
  submit: () => void;
}

export interface SchemaFormProps {
  className?: string;
  columns: FormColumnsType[];
  hideButton?: boolean;
  size?: 'small' | 'middle' | 'large';
  layout?: 'horizontal' | 'vertical' | 'inline';
  submitText?: string;
  submitLoading?: boolean;
  resetText?: string;
  labelAlign?: 'left' | 'right';
  labelWrap?: boolean;
  labelCol?: RecordType;
  initialValues?: RecordType;
  fileExpire?: boolean;
  gridProps?: {
    gutter?: number | string;
    col?: number | string;
  };
  defaultValues?: RecordType;

  footer?: (
    submitButton: React.ReactNode,
    restButton: React.ReactNode,
  ) => React.ReactNode;

  onFinish?: (values: RecordType, fileToken?: string) => void;
  onFinishFailed?: (errorInfo: any) => void;
  onReset?: () => void;

  supplementingTheString?: SchemaFormContextType['supplementingTheString'];
  apiDependencyData?: SchemaFormContextType['formVariables'];
  apiVariables?: SchemaFormContextType['formVariables'];
  variables?: SchemaFormContextType['formVariables'];
  middleware?: SchemaFormContextType['middleware'];

  formProps?: FormProps;
  insertNodes?: Array<{
    component: React.ReactNode;
    index?: number; // 索引，用于插入到表单中的位置, 默认插入到最后
  }>;
}

const defaultProps = {
  columns: [],
  size: 'middle',
  layout: 'horizontal',
  submitLoading: false,
  // labelCol: { span: 6 },
  labelAlign: 'right',
  labelWrap: false,
  middleware: {},
  supplementingTheString: true,
  fileExpire: false,
  defaultValues: {},

  gridProps: {
    gutter: '24',
    col: '2',
  },
};

const SchemaForm = React.forwardRef<
  FormInstance,
  React.PropsWithChildren<SchemaFormProps>
>((p, ref) => {
  const props = mergeProps(defaultProps, p);

  const {
    className,
    // columns,
    onFinish,
    onFinishFailed,
    onReset,
    size,
    layout,
    submitLoading,
    labelCol,
    labelAlign,
    labelWrap,
    initialValues,
    middleware,
    gridProps,
    supplementingTheString,
    footer,
    fileExpire,
    defaultValues,
    formProps,
    insertNodes,
    ...restProps
  } = props;

  const configContext = React.useContext(ConfigContext);

  const [form] = Form.useForm();

  const [columns, setColumns] = React.useState<FormColumnsType[]>([]);

  React.useEffect(() => {
    setColumns(restProps.columns as FormColumnsType[])
  }, [restProps.columns])

  const [curSubmitLoading, setCurSubmitLoading] = React.useState(false);

  const submitText =
    restProps.submitText || configContext.language?.form.submitText;
  const resetText =
    restProps.hideButton || configContext.language?.form.resetText;

  React.useImperativeHandle(ref, () => form);

  if (restProps.apiDependencyData) {
    console.warn(
      `[smartdeer-ui: SchemaForm] apiDependencyData 属性正在被舍弃，请改为使用 apiVariables`,
    );
  }

  const formVariables =
    restProps.variables ||
    restProps.apiDependencyData ||
    restProps.apiVariables ||
    {};

  const transformFields: Record<string, any> = useDeepCompareMemo(() => {
    const map: Record<string, any> = {};
    columns
      .filter((item) => !!item.transformField && !!item.field)
      .forEach((item) => {
        const field = item.field || '';

        if (isString(field)) {
          map[field] = item?.transformField;
        }
      });

    return map;
  }, [columns]);

  const handleAppend = (field: string, values: FormColumnsType[]) => {
    const newColumns: FormColumnsType[] = columns.filter(
      (item) => item.dataEffectAppendField !== field,
    );

    const index = newColumns.findIndex((item) => item.field === field);

    if (index !== -1) {
      const newValues = values.map((item) => {
        return {
          ...item,
          dataEffectAppendField: field,
        };
      });

      newColumns.splice(index + 1, 0, ...newValues);

      setColumns(newColumns as FormColumnsType[]);
    }
  };

  const handleFinish = async (values: any) => {
    let newValues = JSON.parse(JSON.stringify(values));

    if (!isEmpty(transformFields)) {
      for (const oldKey in transformFields) {
        if (newValues[oldKey]) {
          const newKey = transformFields[oldKey];

          newValues[newKey] = newValues[oldKey];
          delete newValues[oldKey];
        }
      }
    }

    newValues = deepMergeImmutable(defaultValues, newValues);

    onFinish?.(newValues);
  };

  const handleFinishFailed = (errorInfo: any) => {
    onFinishFailed?.(errorInfo);
  };

  const handleReset = () => {
    form.resetFields();

    onReset?.();
  };

  const formContextValue = React.useMemo(() => {
    return {
      form,
      formVariables,
      supplementingTheString,
      middleware,
      fileExpire,
      onAppend: handleAppend,
    };
  }, [formVariables, supplementingTheString, middleware, fileExpire]);

  /**
   * 生成子项
   *
   * @param items
   */
  const genItems: any = useRefFunction((items: FormColumnsType[]) => {
    return items
      .map((originItem, index) => {
        const item: any = omitUndefined({
          type: originItem.type,
          field: originItem.field,
          title: originItem.title,
          hidden: originItem.hidden,
          props: omitUndefined({
            ...originItem.props,
            addonAfter: isObject(originItem?.props?.addonAfter)
              ? renderValueType(
                originItem?.props?.addonAfter as FormColumnsType,
                { index: 0 },
              )
              : originItem?.props?.addonAfter,
          }),
          labelProps: originItem.labelProps,
          effect: originItem.effect,
          childrenColumns: originItem.children,
          fieldModal: originItem.fieldModal,
          fieldModalSteps: originItem.fieldModalSteps,
          dependencies: originItem.dependencies || originItem.dependencys,
          required: originItem.required,
          validate: originItem.validate,
          dataEffectAppendField: originItem.dataEffectAppendField,
        });

        // if (originItem.columns) {
        //   item.columns = genItems(originItem.columns);
        // }

        const colProps = {
          key: [item.field, index || 0].join('_'),
          span: getGridMapping(
            gridProps.col.toString(),
            originItem?.col ? originItem?.col.toString() : '',
          ),
        };

        return renderValueType(item, {
          originItem,
          index,
          initialValues,
          genItems,
          colProps,
        });
      })
      .filter((field) => {
        return Boolean(field);
      });
  });

  const preventEnterPress = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter' && (e.target as HTMLElement)?.tagName === 'BUTTON') {
      e.preventDefault();
    }
  };

  const childNode = useDeepCompareMemo(() => {
    if (columns.length === 0) return null;
    const newColumns = genItems(columns);
    if (insertNodes?.length) {
      insertNodes.forEach((item) => {
        if (item.index === 0) {
          newColumns.unshift(item.component);
        } else {
          const index = item.index || newColumns.length;
          newColumns.splice(index, 0, item.component);
        }
      });
    }
    return newColumns;
  }, [columns]);

  const restButton = (
    <Button htmlType="button" onClick={handleReset}>
      {resetText}
    </Button>
  );

  const submitButton = (
    <Button
      loading={curSubmitLoading || submitLoading}
      type="primary"
      htmlType="submit"
    >
      {submitText}
    </Button>
  );

  return (
    <Form
      layout={layout}
      size={size}
      form={form}
      labelCol={labelCol}
      labelAlign={labelAlign}
      labelWrap={labelWrap}
      onFinish={handleFinish}
      onFinishFailed={handleFinishFailed}
      onKeyDown={preventEnterPress}
      initialValues={initialValues}
      className={classNames(classPrefix, className)}
      autoComplete="off"
      {...formProps}
    >
      <SchemaFormContext.Provider value={formContextValue}>
        <Row gutter={gridProps.gutter ? Number(gridProps.gutter) : 24}>
          {childNode}
          {props.children}
        </Row>
      </SchemaFormContext.Provider>

      {footer ? (
        <>{footer(submitButton, restButton)}</>
      ) : (
        <div style={{ textAlign: 'center' }}>
          <Space align="center">
            <Button
              loading={curSubmitLoading || submitLoading}
              type="primary"
              htmlType="submit"
            >
              {submitText}
            </Button>
            <Button htmlType="button" onClick={handleReset}>
              {resetText}
            </Button>
          </Space>
        </div>
      )}
    </Form>
  );
});

export default SchemaForm;
