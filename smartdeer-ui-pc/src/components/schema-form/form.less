.deer-schema-form.ant-form-vertical {
  .ant-form-item-label {
    height: auto !important;

    label {
      height:  auto !important;
    }

    .title{
      font-weight: 700;
    }
  }
}

.deer-schema-form.ant-form-horizontal {
  .ant-form-item-label {
    width: 120px;
    text-align: right;
  }
}

.deer-form-item {
  &-title {
    font-weight: 700;
  }

  &-description {
    font-size: 400 !important;
  }

  .ant-form-item-label {

    .ant-form-item-required {
      &::before {
        display: none !important;
      }

      .deer-form-item-title {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: "*";
        }
      }
    }
  }

  &-hide-required {
    .ant-form-item-label >label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none !important;
    }
  }
  &-hide-title {
    .ant-form-item-label {
      display: none !important;
    }
  }

 .ant-input-textarea-show-count .ant-input-data-count {
  font-size: 14px;
  bottom: -24px;
 }
}

.deer-form-item-customize-label {
  margin-bottom: 8px;

  h4 {
    display: inline-flex;
    align-items: center;
    font-weight: 700;
  }

  &-required {
    &::before {
      display: inline-block;
      margin-right: 4px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: "*";
    }
  }
}

.deer-schema-form {
  .ant-transfer {
    display: flex;

    .ant-transfer-list {
      flex: 1;
    }
  }
}

.smart-deer-form-item-container {
}

.smart-deer-form-item-space .ant-col-12 {
  max-width: fit-content !important;
  font-weight: normal;
}
