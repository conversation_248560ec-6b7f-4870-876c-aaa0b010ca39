import React from 'react';
import { Form } from 'antd';
import { get, isArray, isEmpty } from 'lodash'
import { isDeepEqualReact } from '../../../utils'
import { FormListContext } from '../form-list';
import { replaceVariablesInTemplate } from '../../../utils/replaceVariablesInTemplate'
import { useDeepCompareMemo } from '../../../utils/hooks';

export interface FormDependenciesProps {
  names: Array<string> | Array<string[]>;
  children: (values: { [key: string]: any }) => React.ReactNode;
};

const FormDependencies: React.FC<FormDependenciesProps> = (props) => {

  const formListContext = React.useContext(FormListContext);

  const { children, names } = props

  const nameList = useDeepCompareMemo(() => {

    return names.map((name) => {
      if (isArray(name)) {

        let nameStr = name.join('.')

        if (!isEmpty(formListContext)) {
          nameStr = replaceVariablesInTemplate(formListContext as Record<string, any>, nameStr)
        }

        return nameStr.split('.');
      }

      return name
    });
  }, [names, formListContext])

  return (
    <Form.Item
      noStyle
      shouldUpdate={(prevValues, nextValues) => {
        return nameList.some((name) => {
          return !isDeepEqualReact(
            get(prevValues, name),
            get(nextValues, name),
          );
        });
      }}
    >
      {(form) => {
        const values: Record<string, any> = {} as Record<string, any>;

        for (let i = 0; i < nameList.length; i++) {
          const itemName = nameList[i]

          const value = form?.getFieldValue?.(itemName);
          if (isArray(itemName)) {
            values[itemName.join('.')] = value;
            values[itemName.join(',')] = value;
            values[itemName.join('-')] = value;
          } else {
            values[itemName] = value
          }
        }

        return <>{children(values)}</>
      }}
    </Form.Item>
  )
};

export default FormDependencies
