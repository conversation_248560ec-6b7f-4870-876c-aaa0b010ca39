import React from 'react';
import { SchemaForm } from '@smartdeer-ui/pc'
import type { FormColumnsType, SchemaFormRef } from '@smartdeer-ui/pc'
import { Button, Form, Input, Space } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';

const columns: FormColumnsType[] = [
  // {
  //   "type": "select",
  //   "field": "holidayType",
  //   "title": "假期类型",
  //   "props": {
  //     "className": "",
  //     "style": {},
  //     "options": [
  //       {
  //         "label": "年假",
  //         "value": "1"
  //       },
  //       {
  //         "label": "事假",
  //         "value": "2"
  //       },
  //       {
  //         "label": "婚嫁",
  //         "value": "5"
  //       },
  //       {
  //         "label": "婚嫁",
  //         "value": "6"
  //       }
  //     ]
  //   },
  //   // "effect": {
  //   //   "fetch": {
  //   //     "action": "/saas/v1/entity/2/leave/conf/1",
  //   //     // "action": "/saas/v1/entity/2/leave/group/key",
  //   //     "method": "GET"
  //   //   },
  //   // },
  //   "labelProps": {
  //     "description": "",
  //     "rules": [{
  //       "required": true,
  //       "message": "请选择客户！"
  //     }]
  //   },
  //   "children": []
  // },

  // {
  //   "type": "gs.vacation.startTime",
  //   "field": "fromTime",
  //   "title": "开始时间",
  //   "effect": {
  //     "fetch": {
  //       "action": "/saas/v1/entity/*{entityId}/leave/conf/*{vacationType}",
  //       // "action": "/saas/v1/entity/2/leave/conf/1",
  //       "method": "GET"
  //     }
  //   },
  //   "labelProps": {
  //     "rules": [{
  //       "required": true,
  //       "message": "请选择开始时间！"
  //     }]
  //   }
  // },
  // {
  //   "type": "gs.vacation.endTime",
  //   "field": "toTime",
  //   "title": "结束时间",
  //   "effect": {
  //     "fetch": {
  //       "action": "/saas/v1/entity/*{entityId}/leave/conf/*{vacationType}",
  //       // "action": "/saas/v1/entity/2/leave/conf/1",
  //       "method": "GET"
  //     }
  //   },
  //   "labelProps": {
  //     "description": "",
  //     "dependencies": ['fromTime'],
  //     "rules": [{
  //       "required": true,
  //       "message": "请选择结束时间！"
  //     }, {
  //       "required": true,
  //       "message": "结束时间 不能早于 开始时间！",
  //       "type": "customValidator",
  //       "condition": ">",
  //       "fieldToCompare": "fromTime",
  //     }]
  //   },
  // },
  // {
  //   "type": "gs.vacation.duration",
  //   "field": "duration",
  //   "title": "时长（小时）",
  //   "dependencies": ['fromTime', 'toTime'],
  //   "props": {
  //     "disabled": true
  //   },
  //   "effect": {
  //     "fetch": {
  //       "action": "/saas/v1/entity/*{entityId}/leave/conf/*{vacationType}",
  //       // "action": "/saas/v1/entity/2/leave/conf/1",
  //       "method": "GET"
  //     },
  //     "value": "*{toTime | dateDiffHour(*<fromTime>)}"
  //   },
  //   "labelProps": {
  //     "rules": [{
  //       "required": true,
  //       "message": "请假市场应该大于 0"
  //     }]
  //   }
  // },

  // {
  //   "type": "gs.vacation.dateRangePicker",
  //   "field": "fromTime",
  //   "title": "开始时间",
  //   "effect": {
  //     "fetch": {
  //       "action": "/saas/v1/entity/*{entityId}/leave/conf/*{vacationType}",
  //       // "action": "/saas/v1/entity/2/leave/conf/1",
  //       "method": "GET"
  //     }
  //   },
  //   "labelProps": {
  //     "noStyle": true,
  //     "rules": [{
  //       "required": true,
  //       "message": "请选择开始时间！"
  //     }]
  //   }
  // },
  // {
  //   "type": "textArea",
  //   "field": "reason",
  //   "title": "请假理由",
  //   "labelProps": {
  //     "rules": [{
  //       "required": true,
  //       "message": "请输入请假理由！"
  //     }]
  //   }
  // }

  // {
  //   "type": "quill",
  //   "field": "quill",
  //   "title": "quill",
  //   "props": {
  //     "style": {
  //       "height": "800px"
  //     }
  //   },
  //   "labelProps": {
  //     "rules": [{
  //       "required": true,
  //       "message": "请输入请假理由！"
  //     }]
  //   }
  // },

  // {
  //   "type": "upload",
  //   "field": "upload",
  //   "title": "upload",
  //   "labelProps": {
  //     "rules": [{
  //       "required": true,
  //       "message": "请输入请假理由！"
  //     }]
  //   }
  // }

  // {
  //   "type": "upload",
  //   "field": "avatar",
  //   "title": "头像",
  //   "props": {
  //     "maxCount": 1,
  //   },
  //   "labelProps": {
  //     "rules": [
  //       {
  //         "required": true,
  //         "message": "请上传头像"
  //       }
  //     ]
  //   }
  // }

  // {
  //   "type": "selectQuillTemplate",
  //   "field": "workOrderType",
  //   "title": "签证申请工单",
  //   "props": {
  //     "quillField": "quill",
  //     "options": [
  //       {
  //         "label": "签证申请工单",
  //         "value": "1",
  //         "template": "<p>员工姓名：</p><p>员工国籍：</p><p>雇佣国家：</p><p>员工居住地国家：</p><p>员工最高学历：</p><p>受抚养者数量：</p>"
  //       },
  //       {
  //         "label": "员工入职工单",
  //         "value": "2"
  //       }
  //     ]
  //   }
  // },
  // {
  //   "type": "quill",
  //   "field": "quill",
  //   "title": "quill",
  //   "props": {

  //   }
  // },
  // {
  //   "type": "codeEditor",
  //   "field": "codeEditor",
  //   "title": "codeEditor",
  //   "props": {
  //     "defaultLang": "java",
  //     "height": "400px"
  //   }
  // },

  // {
  //   "type": "select",
  //   "field": "show",
  //   "title": "show",
  //   "labelProps": {
  //     "initialValue": "1"
  //   },
  //   "props": {
  //     "options": [
  //       {
  //         "label": "隐藏",
  //         "value": "1"
  //       },
  //       {
  //         "label": "显示",
  //         "value": "2"
  //       },
  //     ]
  //   }
  // },
  // {
  //   "type": "select",
  //   "field": "k1",
  //   "title": "k1",
  //   "props": {
  //     "options": [
  //       {
  //         "label": "v1",
  //         "value": "1"
  //       },
  //       {
  //         "label": "v2",
  //         "value": "2"
  //       },
  //       {
  //         "label": "v3",
  //         "value": "3"
  //       },
  //       {
  //         "label": "v4",
  //         "value": "4"
  //       }
  //     ]
  //   }
  // },
  // {
  //   "type": "select",
  //   "field": "k2",
  //   "title": "k2",
  //   "props": {
  //     "options": [
  //       {
  //         "label": "v1",
  //         "value": "1"
  //       },
  //       {
  //         "label": "v2",
  //         "value": "2"
  //       },
  //       {
  //         "label": "v3",
  //         "value": "3"
  //       },
  //       {
  //         "label": "v4",
  //         "value": "4"
  //       }
  //     ]
  //   }
  // },
  // {
  //   "type": "input",
  //   "field": "input",
  //   "title": "input",
  //   "dependencies": ["show", "k1", "k2"],
  //   "effect": {
  //     "show": "*{show} === 2",
  //     "switchValue": [
  //       {
  //         "case": "*{k1} === 2 && *{k2} === 2",
  //         "value": "4"
  //       },
  //       {
  //         "case": "*{k1} === 1",
  //         "value": "1"
  //       },
  //       {
  //         "case": "*{k2} === 2",
  //         "value": "2"
  //       }
  //     ]
  //   }
  // },

  // {
  //   "type": "input",
  //   "field": "t",
  //   "title": 't',
  //   "labelProps": {
  //     "rules": [
  //       {
  //         "required": true,
  //         "message": 'required'
  //       }
  //     ]
  //   }
  // },
  // {
  //   "type": "select",
  //   "field": "t1",
  //   "title": 't1',
  //   "effect": {
  //     "fetch": {
  //       "type": "api",
  //       "action": "/saas/gs/v1/dict/employingState",
  //       "method": "GET",
  //     }
  //   },
  //   "labelProps": {
  //     "rules": [
  //       {
  //         "required": true,
  //         "message": 'required'
  //       }
  //     ]
  //   }
  // },
  // {
  //   "type": "select",
  //   "field": "t2",
  //   "title": 't2',
  //   "effect": {
  //     "fetch": {
  //       "type": "api",
  //       "action": "/saas/gs/v1/dict/employingState",
  //       "method": "GET",
  //     }
  //   },
  //   "labelProps": {
  //     "rules": [
  //       {
  //         "required": true,
  //         "message": 'required'
  //       }
  //     ]
  //   }
  // },
  // {
  //   "type": "select",
  //   "field": "t3",
  //   "title": 't3',
  //   "effect": {
  //     "fetch": {
  //       "type": "api",
  //       "action": "/saas/gs/v1/dict/employingState",
  //       "method": "GET",
  //     }
  //   },
  //   "labelProps": {
  //     "rules": [
  //       {
  //         "required": true,
  //         "message": 'required'
  //       }
  //     ]
  //   }
  // },

  // {
  //   "type": "select",
  //   "field": "count",
  //   "title": "计算",
  //   "labelProps": {
  //     "initialValue": "1"
  //   },
  //   "props": {
  //     "options": [
  //       {
  //         "label": "隐藏",
  //         "value": "1"
  //       },
  //       {
  //         "label": "显示",
  //         "value": "2"
  //       },
  //     ]
  //   }
  // },

  // {
  //   "type": "input",
  //   "field": "num",
  //   "title": "计算显示",
  //   "dependencies": ["count"],
  //   "effect": {
  //     "show": "*{count} * 2 === 4 && *{entityId} === 2"
  //   }
  // },


  // {
  //   "type": "select",
  //   "field": "groupKey",
  //   "title": "groupKey",
  //   "labelProps": {
  //     "description": "description description",
  //   },
  //   "props": {
  //     "options": [
  //       {
  //         "label": "maternity_Leave",
  //         "value": "maternity_Leave"
  //       },
  //       {
  //         "label": "paternity_Leave",
  //         "value": "paternity_Leave"
  //       },
  //       {
  //         "label": "paid_sick_leave",
  //         "value": "paid_sick_leave"
  //       },
  //       {
  //         "label": "annual_leave_2024",
  //         "value": "annual_leave_2024"
  //       },
  //     ]
  //   }
  // },

  // {
  //   "type": "datePicker",
  //   "field": "fromTime",
  //   "title": "开始时间",
  // },

  // {
  //   "type": "datePicker",
  //   "field": "toTime",
  //   "title": "结束时间",
  //   "dependencies": ["fromTime", "groupKey"],
  //   "effect": {
  //     "disabled": "*{groupKey} === maternity_Leave",
  //     "switchValue": [
  //       {
  //         "case": "*{groupKey} === maternity_Leave && *{fromTime} !== ",
  //         "value": "*{fromTime | addWeek(14) | subtractDay(1) | addHour(10)}"
  //       }
  //     ],
  //   }
  // },
  // {
  //   "type": "upload",
  //   "field": "files",
  //   "title": "病假证明",
  //   "props": {
  //     "format": "object"
  //   },
  //   "dependencies": [
  //     "groupKey",
  //     "toTime",
  //     "fromTime"
  //   ],
  //   "effect": {
  //     "show": "*{toTime} - *{fromTime} > 46800000 && *{groupKey} === paid_sick_leave"
  //   }
  // },
  // {
  //   "type": "radio",
  //   "field": "isBlockLeave",
  //   "title": "Block leave",
  //   "labelProps": {
  //     "initialValue": "2"
  //   },
  //   "props": {
  //     "options": [
  //       {
  //         "label": "是",
  //         "value": '1'
  //       },
  //       {
  //         "label": "否",
  //         "value": '2'
  //       },
  //     ]
  //   },
  //   "dependencies": ["groupKey"],
  //   "effect": {
  //     "show": "*{groupKey | includes(annual)} === true"
  //   }
  // },

  // {
  //   "type": "input",
  //   "field": "r1",
  //   "title": "校验1"
  // },
  // {
  //   "type": "input",
  //   "field": "r2",
  //   "title": "校验2",
  //   "dependencies": ["r1"],
  //   "effect": {
  //     "required": "*{r1} === r"
  //   }
  // },

  // {
  //   "type": "input",
  //   "field": "group",
  //   "title": "组名",
  // },

  // {
  //   "type": "formList",
  //   "field": "formList",
  //   "title": "用户",
  //   "col": "2",
  //   "labelProps": {
  //     "bodyStyle": {
  //       "backgroundColor": "#F8FAFC",
  //       "padding": "12px",
  //     },
  //     "maxCount": "5",
  //     "addText": "添加费用明细",
  //     "popconfirm": {
  //       "title": "确定删除这组费用明细吗？"
  //     }
  //   },
  //   "children": [
  //     {
  //       "type": "input",
  //       "field": "name",
  //       "title": "姓名",
  //     },
  //     {
  //       "type": "input",
  //       "field": "age",
  //       "title": "年龄",
  //     }
  //   ]
  // },

  // {
  //   "type": "input",
  //   "field": "other",
  //   "title": "其他",
  // },

  // {
  //   "type": "select",
  //   "field": "type",
  //   "title": "类型",
  //   "col": "2",
  //   "props": {
  //     "supplementingTheTemplateKey": true,
  //     "supplementingTheTemplateName": 'templateKey',
  //     "options": [
  //       {
  //         "label": "员工入职",
  //         "value": "1",
  //         "templateKey": 'aaa'
  //       },
  //       {
  //         "label": "员工离职",
  //         "value": "2",
  //         "templateKey": 'bbb'
  //       }
  //     ]
  //   },
  // },
  // {
  //   "type": "effect.append",
  //   "field": "templateJson",
  //   "title": "测试",
  //   "dependencies": ["templateKey"],
  //   "col": "2",
  //   "props": {
  //   },
  //   "effect": {
  //     // "show": "*{templateKey} !== ",
  //     "fetch": {
  //       "type": "function",
  //       "defaultParams": {
  //         "key": "*{templateKey}",
  //       },
  //     }
  //   },
  //   "labelProps": {
  //     "noStyle": true
  //   }
  // },

  // {
  //   type: 'schemaPage',
  //   field: 'schemaPage',
  //   col: 2,
  //   props: {
  //     columns: [
  //       {
  //         "type": "container",
  //         "children": [
  //           {
  //             "type": "h2",
  //             "children": "*{applyRealNameStr}"
  //           },
  //           {
  //             "type": "div",
  //             "children": "*{companyName}",
  //             "props": {
  //               "style": {
  //                 "marginTop": "10px",
  //                 "fontSize": "12px",
  //                 "color": "#757575"
  //               }
  //             }
  //           },
  //           {
  //             "type": "div",
  //             "children": "审批中",
  //             "props": {
  //               "style": {
  //                 "marginTop": "10px",
  //                 "fontSize": "16px",
  //                 "color": "#fb923c"
  //               }
  //             },
  //             "effect": {
  //               "show": "*{system_status} === 0"
  //             }
  //           },
  //           {
  //             "type": "div",
  //             "children": "已审批通过",
  //             "props": {
  //               "style": {
  //                 "marginTop": "10px",
  //                 "fontSize": "16px",
  //                 "color": "#4ade80"
  //               }
  //             },
  //             "effect": {
  //               "show": "*{system_status} === 1"
  //             }
  //           },
  //           {
  //             "type": "div",
  //             "children": "已拒绝审批",
  //             "props": {
  //               "style": {
  //                 "marginTop": "10px",
  //                 "fontSize": "16px",
  //                 "color": "#f87171"
  //               }
  //             },
  //             "effect": {
  //               "show": "*{system_status} === 2"
  //             }
  //           },
  //           {
  //             "type": "div",
  //             "children": "已取消",
  //             "props": {
  //               "style": {
  //                 "marginTop": "10px",
  //                 "fontSize": "16px",
  //                 "color": "#9ca3af"
  //               }
  //             },
  //             "effect": {
  //               "show": "*{system_status} === 3"
  //             }
  //           }
  //         ]
  //       }
  //     ]
  //   },
  //   labelProps: {
  //     noStyle: true
  //   }
  // }

  // {
  //   "type": "checkbox",
  //   "field": "checkbox",
  //   "title": "其他",
  //   "col": "2",
  //   props: {
  //     supplementingTheString: true,
  //     options: [
  //       { label: '11', value: '1' },
  //       { label: '22', value: '2' },
  //       { label: '33', value: '3' }
  //     ]
  //   }
  // },

  // {
  //   "type": "radio",
  //   "field": ['account', 'showPassword2'],
  //   "title": "显示确认密码",
  //   "col": "2",
  //   props: {
  //     supplementingTheString: true,
  //     options: [
  //       { label: '不显示', value: '1' },
  //       { label: '显示', value: '2' },
  //     ]
  //   }
  // },
  // {
  //   "type": "formList",
  //   "field": "accountList",
  //   "title": "账号密码列表",
  //   "col": "2",
  //   "labelProps": {
  //     "bodyStyle": {
  //       "backgroundColor": "#F8FAFC",
  //       "padding": "12px",
  //     },
  //     "maxCount": "5",
  //     "addText": "添加密码",
  //     "popconfirm": {
  //       "title": "确定删除这组密码吗？"
  //     }
  //   },
  //   "children": [
  //     {
  //       "type": "input",
  //       "field": "alias1",
  //       "title": "账号1",
  //     },
  //     {
  //       "type": "input",
  //       "field": "alias2",
  //       "title": "账号2",
  //       "dependencies": [['accountList', '*{index}', 'alias1']],
  //       "effect": {
  //         "value": "*{accountList,*<index>,alias1}"
  //       }
  //     },

  //     {
  //       "type": "input",
  //       "field": "password1",
  //       "title": "密码",
  //     },
  //     {
  //       "type": "input",
  //       "field": "password2",
  //       "title": "确认密码",
  //       "dependencies": [['account', 'showPassword2']],
  //       "effect": {
  //         "show": "*{account,showPassword2} === 2"
  //       }
  //     },
  //     {
  //       "type": "input",
  //       "field": "tip",
  //       "title": "密码提示",
  //       "dependencies": [['accountList', '*{index}', 'password1']],
  //       "col": "2",
  //       "effect": {
  //         "show": "*{accountList,*<index>,password1} !== "
  //       }
  //     }
  //   ]
  // },

  // {
  //   "type": "upload",
  //   "field": "upload",
  //   "title": "upload",
  //   "props": {
  //     "uploadType": ['image', 'video', 'file', 'compressedFile'],
  //   },
  //   "labelProps": {
  //     "rules": [{
  //       "required": true,
  //       "message": "请上传"
  //     }]
  //   }
  // }

  // {
  //   "type": "formList",
  //   "field": "questions",
  //   "title": "添加题目",
  //   "col": "2",
  //   "labelProps": {
  //     "bodyStyle": {
  //       "padding": "12px",
  //     },
  //     "maxCount": "5",
  //     "addText": "添加",
  //     "popconfirm": {
  //       "title": "确定删除这组题目吗？"
  //     }
  //   },
  //   "children": [
  //     {
  //       "type": "input",
  //       "field": "question",
  //       "title": "标题",
  //     },
  //     {
  //       "type": "formList",
  //       "field": "options",
  //       "title": "答案选项",
  //       "col": "2",
  //       "labelProps": {
  //         "style": {
  //           "padding": "20px",
  //         },
  //         "bodyStyle": {
  //           "backgroundColor": "#F8FAFC",
  //           "padding": "20px",
  //         },
  //         "maxCount": "5",
  //         "addText": "添加选项",
  //         "popconfirm": {
  //           "title": "确定删除这组答案吗？"
  //         }
  //       },
  //       "children": [
  //         {
  //           "type": "input",
  //           "field": "content",
  //           "title": "选项标题",
  //         },
  //         {
  //           "type": "input",
  //           "field": "option",
  //           "title": "选项内通",
  //           "props": {}
  //         }
  //       ]
  //     },
  //     {
  //       "type": "radio",
  //       "field": "answer",
  //       "title": "答案",
  //       "props": {
  //         "options": [
  //           { label: 'a', value: '1' },
  //           { label: 'b', value: '2' },
  //           { label: 'c', value: '3' },
  //           { label: 'd', value: '4' },
  //         ]
  //       }
  //     }
  //   ]
  // },

  // {
  //   "type": "transfer",
  //   "field": "questions",
  //   "title": "权限列表",
  //   "col": "2",
  //   "props": {
  //     "columns": [
  //       {
  //         dataIndex: 'label',
  //         title: '题目',
  //       },
  //       {
  //         dataIndex: 'description',
  //         title: '答案',
  //       },
  //       {
  //         dataIndex: 'extra',
  //         title: '额外的',
  //       },
  //     ],

  //     "options": [
  //       {
  //         "value": "1",
  //         "label": "这是问题1",
  //         "description": "这是答案1",
  //         "extra": "额外的1"
  //       },
  //       {
  //         "value": "2",
  //         "label": "这是问题2",
  //         "description": "这是答案2",
  //         "extra": "额外的2"
  //       }
  //     ],

  //     "fieldNames": {
  //       "value": "name",
  //       "label": "name",
  //       "description": "description",
  //       "extra": "extra"
  //     }
  //   },
  // },

  // {
  //   "type": "datePicker",
  //   "field": "contractStartTime",
  //   "title": "合同开始时间",
  //   "labelProps": {
  //     "rules": [
  //       {
  //         "required": true,
  //         "message": 'required'
  //       }
  //     ]
  //   }
  // },
  // {
  //   "type": "select",
  //   "field": "trialPeriodType",
  //   "title": "试用期类型",
  //   "col": "2",
  //   props: {
  //     options: [
  //       { label: '天', value: '1' },
  //       { label: '月', value: '2' },
  //     ]
  //   }
  // },
  // {
  //   "type": "input",
  //   "field": "trialPeriod",
  //   "title": '试用期',
  //   "labelProps": {
  //     "rules": [
  //       {
  //         "required": true,
  //         "message": 'required'
  //       }
  //     ]
  //   }
  // },
  // {
  //   "type": "datePicker",
  //   "field": "contractEndTime",
  //   "title": "合同结束时间",
  //   props: {
  //     disabled: 'true'
  //   },
  //   "dependencies": ["trialPeriodType", "contractStartTime", "trialPeriod"],
  //   "effect": {
  //     "switchValue": [
  //       {
  //         "case": "*{trialPeriodType} === 1 && *{contractStartTime} !==  && *{trialPeriod} !== ",
  //         "value": "*{contractStartTime | addDay(*<trialPeriod>)}"
  //       },
  //       {
  //         "case": "*{trialPeriodType} === 2 && *{contractStartTime} !==  && *{trialPeriod} !== ",
  //         "value": "*{contractStartTime | addMonth(*<trialPeriod>)}"
  //       }
  //     ],
  //   }
  // },

  {
    type: 'dateRangePicker',
    title: 'dateRangePicker',
    field: 'dateRangePicker',
    props: {},
    "labelProps": {
      "rules": [
        {
          "required": true,
          "message": "请选择费用发生时间"
        }
      ]
    }
  }
]

export default () => {
  const formRef = React.useRef<SchemaFormRef>(null)

  const [form] = Form.useForm();

  const [isSubmitLoading, setIsSubmitLoading] = React.useState(false)

  const initialValues = {
    "quill": "<div>adsa</div>",
    "avatar": "https://global-image.smartdeer.work/test/images/0x4b93aaf0fd6b48739bc4afe7f09e3517.png",
    // "show": "2",
    "account": {
      "showPassword2": "1",
    },
    "questions": [
      {
        "number": "1",  //编号
        "question": "",// 题干
        "options": [
          {
            "content": "",//选项内容
            "option": ""//选项
          }
        ],
        "answer": "A" //答案
      }
    ],
    "accountList": [
      {
        "alias1": "",
        "alias2": "",
        "password1": "",
        "password2": "",
      },
    ]
  }

  const onFinish = (values: any) => {
    setIsSubmitLoading(true)

    console.log('onFinish ---> ', values)
    console.log('onFinish ---> ', JSON.stringify(values))

    setTimeout(() => {
      setIsSubmitLoading(false)
    }, 1000)
  }

  const handleClickSubmit = () => {
    formRef.current?.submit()
  }

  return (
    <>
      <SchemaForm
        ref={formRef}
        columns={columns}
        initialValues={initialValues}
        size='large'
        // layout='vertical'
        layout='horizontal'
        // labelCol={{ span: 24 }}
        gridProps={{
          col: 1
        }}
        variables={{
          entityId: 2,
        }}
        onFinish={onFinish}
      />

      <Button loading={isSubmitLoading} onClick={handleClickSubmit}>Ref 提交</Button>
    </>
  )
}
