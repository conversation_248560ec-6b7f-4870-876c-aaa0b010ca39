import React from 'react';
import { Form, Select, Input } from 'antd';
import { SchemaFormContext } from '../../context'
import { isNil } from 'lodash';

import type { FieldProps } from '../typing'

import { isString } from 'lodash'
import { mergeProps } from '../../../../utils/withDefaultProps'
import omit from '../../../../utils/omit'
import { usePropsOptions } from '../../../../utils/hooks'

// 兼容代码-----------
import 'antd/lib/select/style';
//------------

const filterOption = (input: string, option?: { label: string; value: string }) =>
  (option?.label ?? '').toLowerCase().includes(input.toLowerCase());

const defaultProps = {
  fieldProps: {
    showSearch: true
  }
}

const FieldSelectQuillTemplate: React.FC<FieldProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const { value, fieldProps, onChange, field } = props

  const newFieldProps = omit(fieldProps, ['quillField'])

  const schemaFormContext = React.useContext(SchemaFormContext)

  const { isLoading, options } = usePropsOptions(props)

  const handleChange = (value: string) => {
    onChange?.(value)
  }

  React.useEffect(() => {
    if (!schemaFormContext.supplementingTheString) return

    const option = options.find(option => option.value === value)

    if (!option) {
      schemaFormContext.form?.setFieldValue(`${field}Str`, '')
      return
    }


    if (fieldProps?.quillField) {
      const forceUpdateTemplate = `<div data-update="force" />`

      const template = forceUpdateTemplate + (option.template || '')

      schemaFormContext.form?.setFieldValue(fieldProps.quillField, template)
    }

    schemaFormContext.form?.setFieldValue(`${field}Str`, option.label)
  }, [value, options])

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <>
      <Select
        {...newFieldProps}
        value={
          !isNil(value)
            ? Array.isArray(value)
              ? value
              : value.toString()
            : undefined
        }
        disabled={isDisabled}
        style={{ width: '100%' }}
        loading={isLoading}
        options={options}
        filterOption={filterOption}
        onChange={handleChange}
      />

      {schemaFormContext.supplementingTheString && (
        <Form.Item hidden noStyle name={`${field}Str`}>
          <Input />
        </Form.Item>
      )}
    </>
  );
};


export default FieldSelectQuillTemplate

