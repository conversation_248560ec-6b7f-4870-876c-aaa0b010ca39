import React from 'react';
import { Alert } from 'antd';
import type { FieldProps } from '../typing'
import InnerHtml from '../../../inner-html'

import './index.less'

const FieldAlert: React.FC<FieldProps> = (props) => {
  const { fieldProps = {} } = props

  return (
    <Alert
      {...fieldProps}
      description={<InnerHtml content={fieldProps.description || ''} />}
    />
  );
};

export default FieldAlert
