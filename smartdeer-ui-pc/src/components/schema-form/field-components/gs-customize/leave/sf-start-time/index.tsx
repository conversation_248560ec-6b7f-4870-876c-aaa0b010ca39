import React from 'react';
import { SchemaFormContext } from '../../../../context';
import type { FieldProps } from '../../../typing';

import DateRangePicker from '../common/date-range-picker';

const FieldSfLeaveStartTime: React.FC<FieldProps> = (props) => {
  const { fieldProps } = props;

  const { form } = React.useContext(SchemaFormContext);

  const { type = 'hour', workHours, workMinutes } = fieldProps || {};

  const handleChange = (value: any) => {
    props?.onChange(value);
    let newValue;
    // 如果选择时间为12:00，将toTime设置为13:00；如果选择时间为5点半，将toTime设置为6点
    const date = new Date(Number(value));
    if (date.getHours() === 17 && date.getMinutes() === 30) {
      newValue = Number(value) + 1800000;
    } else if (date.getHours() === 12 && date.getMinutes() === 0) {
      newValue = Number(value) + 3600000;
    } else if (date.getHours() === 12 && date.getMinutes() === 30) {
      newValue = Number(value) + 1800000;
    } else {
      newValue = value;
    }
    form?.setFieldValue('fromTime', newValue);
  };

  return (
    <DateRangePicker
      disabled={props?.disabled}
      value={props?.value}
      type={type}
      timePickertype="PM"
      validHours={workHours}
      validMinutes={workMinutes}
      onChange={(value) => handleChange(value)}
      showTimePicker={true}
    />
  );
};

export default FieldSfLeaveStartTime;
