import React from 'react';
import type { GetProps } from 'antd';
import { Row, Col, DatePicker, TimePicker, Spin, Form, Alert, Select } from 'antd';
import type { Dayjs } from 'dayjs';
import { ConfigContext } from '../../../../../config-provider'
import type { FieldProps } from '../../../typing'
import { useDateRangePicker, useFetchRule } from '../hooks'
import type { TimezoneType } from '../../../../../..';
import {
  convertUTCToTimezone
} from '../../../../../../utils/time';

type RangePickerProps = GetProps<typeof DatePicker.RangePicker>;

interface DateRangePickerProps {
  type: 'hour' | 'halfDay',
  timePickertype: 'AM' | 'PM',
  value?: any;
  onChange?: (...args: any) => void;
  validHours?: number[];
  validMinutes?: number[];
  disabledDate?: RangePickerProps['disabledDate']
}

export const DateRangePicker: React.FC<DateRangePickerProps> = (props) => {
  const {
    type,
    timePickertype,
    validHours = [],
    validMinutes = [],
    disabledDate = () => false,
  } = props

  const configContext = React.useContext(ConfigContext)

  const { value, handleDateChange, handleTimeChange } = useDateRangePicker(
    props,
    configContext.timezone as TimezoneType,
    validHours,
    validMinutes
  )

  const showTimePicker = !!validHours.length

  let timeFormat = 'HH:mm';

  if (!!validHours.length && !!validMinutes.length) {
    timeFormat = 'HH:mm';
  } else if (!!validHours.length) {
    timeFormat = 'HH';
  }

  const cellRender = React.useCallback((current: string | number | Dayjs, info: any): React.ReactNode => {
    let childNode: React.ReactNode = ''

    if (info.subType === 'hour' && typeof current === 'number') {
      childNode = validHours.includes(current) ? current : ''
    }

    if (info.subType === 'minute' && typeof current === 'number') {
      childNode = validHours.includes(current) ? current : ''
    }

    if (childNode) {
      childNode = <div className={'ant-picker-time-panel-cell-inner'}>{childNode}</div>
    }

    return childNode
  }, [validHours]);

  const handleChangeDatePicker = (value: string) => {

    if (type === 'hour') {
      handleDateChange(value)
      return
    }

    let hour: number = 0

    if (timePickertype === 'AM') {
      hour = validHours[0]
    } else {
      hour = validHours[Math.floor(validHours.length / 2)]
    }

    handleDateChange(value, hour.toString())
  }

  const handleChangeHalfDay = (value: string) => {
    let hour: number = 0

    if (timePickertype === 'AM') {
      if (value === '1') {
        hour = validHours[0]
      } else if (value === '2') {
        hour = validHours[Math.floor(validHours.length / 2)]
      }
    } else {
      if (value === '1') {
        hour = validHours[Math.floor(validHours.length / 2)]
      } else if (value === '2') {
        hour = validHours[validHours.length - 1] + 1
      }
    }

    handleTimeChange(hour.toString())
  }

  const halfDayValue = React.useMemo(() => {
    if (!value) return ''

    const hour = value.hour()

    let half = Math.ceil(validHours.length / 2);

    if (timePickertype === 'PM') {
      half = half + 1
    }

    const [firstHalf, secondHalf] = [validHours.slice(0, half), validHours.slice(half)];

    if (firstHalf.includes(hour)) {
      return '1'
    }

    return '2'
  }, [value])

  return (
    <Row gutter={16}>
      <Col span={showTimePicker ? 16 : 24}>
        <DatePicker
          inputReadOnly
          value={value}
          placeholder=' '
          style={{ width: '100%' }}
          disabledDate={disabledDate}
          onChange={(_, dateString) => handleChangeDatePicker(dateString as string)}
        />
      </Col>
      {showTimePicker && (
        <Col span={8}>
          {type === 'halfDay' ? (
            <Select
              value={halfDayValue}
              style={{ width: '100%' }}
              onChange={handleChangeHalfDay}
              options={[
                { value: '1', label: 'AM' },
                { value: '2', label: 'PM' },
              ]}
            />
          ) : (
            <TimePicker
              inputReadOnly
              needConfirm={false}
              showNow={false}
              value={value}
              placeholder=' '
              format={timeFormat}
              cellRender={cellRender}
              style={{ width: '100%' }}
              onChange={(_, timeString) => handleTimeChange(timeString as string)}
            />
          )}
        </Col>
      )}
    </Row>
  )
}

const FieldVacationDateRangePicker: React.FC<FieldProps> = (props) => {
  const {
    fieldProps
  } = props

  // tyep = halfDay（半日） ｜ hour（小时）
  const { type = 'hour' } = fieldProps || {}

  const { language, timezone } = React.useContext(ConfigContext)

  const { conf, isLoading } = useFetchRule(props)

  const startValidStartHours = React.useMemo(() => {
    let hours = []
    if (type === 'halfDay）') {
      hours = conf?.workHours || []
    } else {
      hours = conf?.startValidStartHours || []
    }

    return hours
  }, [type, conf])

  const startValidMinutes = React.useMemo(() => {
    return conf?.startValidMinutes || []
  }, [conf])

  const endValidStartHours = React.useMemo(() => {
    let hours = []

    if (type === 'halfDay') {
      hours = conf?.workHours || []
    } else {
      const topHour = conf?.endValidStartHours ? conf?.endValidStartHours[0] : 0
      hours = conf?.endValidStartHours ? conf?.endValidStartHours.map(item => item + 1) : []
      if (!!hours.length) {
        hours.unshift(topHour)
      }
    }

    return hours
  }, [type, conf])

  const endValidMinutes = React.useMemo(() => {
    return conf?.endValidMinutes || []
  }, [conf])

  const [data, setData] = React.useState({
    fromTime: '',
    fromTimeStr: '',
    toTime: '',
    toTimeStr: '',
    actualFromTime: '',
    actualFromTimeStr: '',
    actualToTime: '',
    actualToTimeStr: '',
  })

  const [errorMsg, setErrorMsg] = React.useState('')

  const handleChange = (name: string, value: any) => {
    const newValue = value ? convertUTCToTimezone(Number(value), timezone as TimezoneType, 'YYYY-MM-DD HH:mm') : value

    setData(values => {
      return {
        ...values,
        [name]: value,
        [`${name}Str`]: newValue
      }
    })
  }

  return (
    <Spin spinning={isLoading}>
      <Form.Item
        name='fromTime'
        label={language?.form.defaultItemLabel.startTime}
        rules={[{
          required: true
        }]}
      >
        <DateRangePicker
          type={type}
          timePickertype='AM'
          validHours={startValidStartHours}
          validMinutes={startValidMinutes}
          onChange={(value) => handleChange('fromTime', value)}
        />
      </Form.Item>

      <Form.Item
        name='toTime'
        label={language?.form.defaultItemLabel.endTime}
        rules={[{
          required: true
        }]}
      >
        <DateRangePicker
          type={type}
          timePickertype='PM'
          validHours={endValidStartHours}
          validMinutes={endValidMinutes}
          onChange={(value) => handleChange('toTime', value)}
        />
      </Form.Item>

      {!!data.fromTimeStr && !!data.toTimeStr && !!data.actualFromTimeStr && !!data.actualToTimeStr && (
        <Alert
          style={{ marginBottom: '24px' }}
          message={`你选择的时间是 ${data.fromTimeStr} 到 ${data.toTimeStr}，但是根据公司要求，真正的请假时间是 ${data.actualFromTimeStr} 到 ${data.actualToTimeStr}。`}
        />
      )}

      {!!errorMsg && (
        <Alert
          type='error'
          style={{ marginBottom: '24px' }}
          message={errorMsg}
        />
      )}
    </Spin>
  );
};

export default FieldVacationDateRangePicker
