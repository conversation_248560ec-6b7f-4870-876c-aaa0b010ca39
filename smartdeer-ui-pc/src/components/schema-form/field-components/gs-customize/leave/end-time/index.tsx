import React from 'react';
import { Spin } from 'antd';
import type { FieldProps } from '../../../typing'
import { useFetchRule } from '../hooks'
import { evaluateLogicalExpression } from '../../../../../../utils/evaluateLogicalExpression'
import { SchemaFormContext } from '../../../../context'

import DateRangePicker from '../common/date-range-picker'

const FieldLeaveEndTime: React.FC<FieldProps> = (props) => {
  const {
    fieldProps
  } = props

  const { formVariables } = React.useContext(SchemaFormContext)

  // tyep = halfDay（半日） ｜ hour（小时）
  const { type = 'hour', hideTimePicker } = fieldProps || {}

  const showTimePicker = React.useMemo(() => {
    if (hideTimePicker) {
      return !evaluateLogicalExpression(formVariables, hideTimePicker)
    } else {
      return true
    }
  }, [hideTimePicker])

  const { conf, isLoading } = useFetchRule(props)

  const endValidStartHours = React.useMemo(() => {
    let hours = []

    if (type === 'halfDay') {
      hours = conf?.workHours || []
    } else {
      const topHour = conf?.endValidStartHours ? conf?.endValidStartHours[0] : 0
      hours = conf?.endValidStartHours ? conf?.endValidStartHours.map(item => item + 1) : []
      if (!!hours.length) {
        hours.unshift(topHour)
      }
    }

    return hours
  }, [type, conf])

  const endValidMinutes = React.useMemo(() => {
    return conf?.endValidMinutes || []
  }, [conf])


  const handleChange = (value: any) => {
    props?.onChange(value)
  }

  return (
    <Spin spinning={isLoading}>
      <DateRangePicker
        disabled={props?.disabled}
        value={props?.value}
        type={type}
        timePickertype='PM'
        validHours={endValidStartHours}
        validMinutes={endValidMinutes}
        onChange={(value) => handleChange(value)}
        showTimePicker={!!endValidStartHours.length && showTimePicker}
      />
    </Spin>
  );
};

export default FieldLeaveEndTime
