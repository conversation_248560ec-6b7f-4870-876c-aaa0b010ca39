import React from 'react';
import type { GetProps } from 'antd';
import { Row, Col, DatePicker, TimePicker, Select } from 'antd';
import type { Dayjs } from 'dayjs';
import { ConfigContext } from '../../../../../../config-provider'
import { useDateRangePicker } from '../../hooks'
import type { TimezoneType } from '../../../../../../..';

type RangePickerProps = GetProps<typeof DatePicker.RangePicker>;

interface DateRangePickerProps {
  type: 'hour' | 'halfDay',
  timePickertype: 'AM' | 'PM',
  value?: any;
  onChange?: (...args: any) => void;
  validHours?: number[];
  validMinutes?: number[];
  disabled?: boolean;
  disabledDate?: RangePickerProps['disabledDate'],
  showTimePicker?: boolean;
  defaultEndTime?: boolean;
}

export const DateRangePicker: React.FC<DateRangePickerProps> = (props) => {
  const {
    type,
    timePickertype,
    validHours = [],
    validMinutes = [],
    disabledDate = () => false,
    showTimePicker = true,
    defaultEndTime = false
  } = props

  const configContext = React.useContext(ConfigContext)

  const { value, handleDateChange, handleTimeChange } = useDateRangePicker(
    props,
    configContext.timezone as TimezoneType,
    validHours,
    validMinutes,
    defaultEndTime,
  )

  let timeFormat = 'HH:mm';

  if (!!validHours.length && !!validMinutes.length) {
    timeFormat = 'HH:mm';
  } else if (!!validHours.length) {
    timeFormat = 'HH';
  }

  const cellRender = React.useCallback((current: string | number | Dayjs, info: any): React.ReactNode => {
    let childNode: React.ReactNode = ''

    if (info.subType === 'hour' && typeof current === 'number') {
      childNode = validHours.includes(current) ? current : ''
    }

    if (info.subType === 'minute' && typeof current === 'number') {
      const cur = current === 0 ? '00' : current
      childNode = validMinutes.includes(current) ? cur : ''
    }

    if (childNode) {
      childNode = <div className={'ant-picker-time-panel-cell-inner'}>{childNode}</div>
    }

    return childNode
  }, [validHours]);

  const handleChangeDatePicker = (value: string) => {

    if (type === 'hour') {
      handleDateChange(value)
      return
    }

    let hour: number = 0

    if (timePickertype === 'AM') {
      hour = validHours[0]
    } else {
      hour = validHours[Math.floor(validHours.length / 2)]
    }

    handleDateChange(value, hour.toString())
  }

  const handleChangeHalfDay = (value: string) => {
    let hour: number = 0

    if (timePickertype === 'AM') {
      if (value === '1') {
        hour = validHours[0]
      } else if (value === '2') {
        hour = validHours[Math.floor(validHours.length / 2)]
      }
    } else {
      if (value === '1') {
        hour = validHours[Math.floor(validHours.length / 2)]
      } else if (value === '2') {
        hour = validHours[validHours.length - 1] + 1
      }
    }

    handleTimeChange(hour.toString())
  }

  const halfDayValue = React.useMemo(() => {
    if (!value) return ''

    const hour = value.hour()

    let half = Math.ceil(validHours.length / 2);

    if (timePickertype === 'PM') {
      half = half + 1
    }

    const [firstHalf, secondHalf] = [validHours.slice(0, half), validHours.slice(half)];

    if (firstHalf.includes(hour)) {
      return '1'
    }

    return '2'
  }, [value])

  const isDisabled = props.disabled

  return (
    <Row gutter={16}>
      <Col span={showTimePicker ? 16 : 24}>
        <DatePicker
          disabled={isDisabled}
          inputReadOnly
          value={value}
          placeholder=' '
          style={{ width: '100%' }}
          disabledDate={disabledDate}
          onChange={(_, dateString) => handleChangeDatePicker(dateString as string)}
        />
      </Col>
      {showTimePicker && (
        <Col span={8}>
          {type === 'halfDay' ? (
            <Select
              disabled={isDisabled}
              value={halfDayValue}
              style={{ width: '100%' }}
              onChange={handleChangeHalfDay}
              options={[
                { value: '1', label: 'AM' },
                { value: '2', label: 'PM' },
              ]}
            />
          ) : (
            <TimePicker
              inputReadOnly
              needConfirm={false}
              showNow={false}
              value={value}
              placeholder=' '
              format={timeFormat}
              cellRender={cellRender}
              style={{ width: '100%' }}
              onChange={(_, timeString) => handleTimeChange(timeString as string)}
            />
          )}
        </Col>
      )}
    </Row>
  )
}

export default DateRangePicker
