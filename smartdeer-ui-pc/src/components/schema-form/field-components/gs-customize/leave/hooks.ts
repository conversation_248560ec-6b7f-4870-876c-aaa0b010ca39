import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import type { TimezoneType } from '../../../../..';
import { getEffectFetchConfig } from '../../../../../utils/getEffectFetchConfig';
import { useParamsDict } from '../../../../../utils/hooks';
import { cachedSwrFetcher } from '../../../../../utils/swrFetcher';
import {
  convertTimezoneToUTC,
  convertUTCToTimezone,
  getTimeInTimeZone,
} from '../../../../../utils/time';
import { ConfigContext } from '../../../../config-provider';
import { SchemaFormContext } from '../../../context';
import type { FieldEffectType } from '../../typing';

type ConfType = {
  startValidStartHours?: number[];
  startValidMinutes?: number[];
  endValidStartHours?: number[];
  endValidMinutes?: number[];
  workHours?: number[];
};

const hours24: number[] = [];
for (let index = 0; index <= 24; index++) {
  hours24.push(index);
}

interface TimePickerProps {
  value?: any;
  onChange?: (...args: any) => void;
  effect?: FieldEffectType;
}

export const useFetchRule = (props: TimePickerProps) => {
  const { effect } = props;

  const configContext = React.useContext(ConfigContext);
  const schemaFormContext = React.useContext(SchemaFormContext);
  const formVariables = schemaFormContext.formVariables;
  const paramsDict = useParamsDict();
  const [isLoading, setIsLoading] = useState(false);

  const [conf, setConf] = useState<ConfType>({});

  const fetch = async () => {
    if (!effect?.fetch) return;

    const { api, method, params } = getEffectFetchConfig(
      effect.fetch,
      configContext,
      { ...paramsDict, ...formVariables },
    );

    setIsLoading(true);
    const { data } = await cachedSwrFetcher(api, method, params);
    setConf(data);

    setIsLoading(false);
  };

  useEffect(() => {
    fetch();
  }, []);

  return {
    isLoading,
    conf,
  };
};

export const useDateRangePicker = (
  props: TimePickerProps,
  timezone: TimezoneType,
  validStartHours: number[],
  validMinutes: number[],
  defaultEndTime = false, // 是否将默认时+分设置为结束时间
) => {
  const { value, onChange } = props;

  let newValue: any = value ? dayjs(Number(value)) : '';

  if (newValue && timezone) {
    newValue = convertUTCToTimezone(newValue, timezone);
  }

  // 获取时间字符串的逻辑
  const getTimeString = (newValue?: any): string => {
    if (newValue) {
      return dayjs(newValue).format('HH:mm');
    }

    let defaultHour = validStartHours[0] ? validStartHours[0] : '00';
    let defaultMinute = validMinutes[0] ? validMinutes[0] : '00';
    if (defaultEndTime) {
      defaultHour = validStartHours?.length
        ? validStartHours[validStartHours.length - 1]
        : '00';
      defaultMinute = validMinutes?.length
        ? validMinutes[validMinutes.length - 1]
        : '00';
    }
    return `${defaultHour}:${defaultMinute}`;
  };

  // 转换日期字符串为UTC毫秒值的逻辑
  const convertDateStringToUTC = (
    dateString: string,
    timezone?: TimezoneType,
  ): string => {
    if (timezone) {
      // 假设 convertTimezoneToUTC 是一个处理时区转换的函数
      // 注意：这里需要确保 convertTimezoneToUTC 函数能够正确处理各种情况
      return convertTimezoneToUTC(dateString, timezone, 'x') + '';
    }
    return dayjs(dateString).valueOf() + '';
  };

  const handleDateChange = (dateString: string, timeString?: string) => {
    let newDateString: string = '';
    const newTimeString = timeString || getTimeString(newValue); // 假设 newValue 是从外部传入的变量

    if (dateString) {
      newDateString = convertDateStringToUTC(
        `${dateString} ${newTimeString}`,
        timezone,
      );
    }

    onChange?.(newDateString);
  };

  const handleTimeChange = (timeString: string) => {
    let newDateString: string = newValue
      ? (dayjs(newValue).format('YYYY-MM-DD') as string)
      : (getTimeInTimeZone(timezone, 'YYYY-MM-DD') as string);
    let newTimeString: string = '';

    if (timeString) {
      newTimeString = convertDateStringToUTC(
        `${newDateString} ${timeString}`,
        timezone,
      );
    }

    onChange?.(newTimeString);
  };

  return {
    value: newValue,
    handleDateChange,
    handleTimeChange,
  };
};
