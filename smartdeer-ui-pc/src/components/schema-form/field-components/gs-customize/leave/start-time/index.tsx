import React from 'react';
import { Spin } from 'antd';
import type { FieldProps } from '../../../typing'
import { useFetchRule } from '../hooks'
import DateRangePicker from '../common/date-range-picker'
import { evaluateLogicalExpression } from '../../../../../../utils/evaluateLogicalExpression'
import { SchemaFormContext } from '../../../../context'

const FieldLeaveStartTime: React.FC<FieldProps> = (props) => {
  const {
    fieldProps
  } = props

  const { formVariables } = React.useContext(SchemaFormContext)

  // tyep = halfDay（半日） ｜ hour（小时）
  const { type = 'hour', hideTimePicker } = fieldProps || {}

  const showTimePicker = React.useMemo(() => {
    if (hideTimePicker) {
      return !evaluateLogicalExpression(formVariables, hideTimePicker)
    } else {
      return true
    }
  }, [hideTimePicker])

  const { conf, isLoading } = useFetchRule(props)

  const startValidStartHours = React.useMemo(() => {
    let hours = []
    if (type === 'halfDay）') {
      hours = conf?.workHours || []
    } else {
      hours = conf?.startValidStartHours || []
    }

    return hours
  }, [type, conf])

  const startValidMinutes = React.useMemo(() => {
    return conf?.startValidMinutes || []
  }, [conf])

  const handleChange = (value: any) => {
    props?.onChange(value)
  }

  return (
    <Spin spinning={isLoading}>
      <DateRangePicker
        value={props?.value}
        type={type}
        timePickertype='AM'
        validHours={startValidStartHours}
        validMinutes={startValidMinutes}
        onChange={(value) => handleChange(value)}
        showTimePicker={!!startValidStartHours.length && showTimePicker}
      />
    </Spin>
  );
};

export default FieldLeaveStartTime
