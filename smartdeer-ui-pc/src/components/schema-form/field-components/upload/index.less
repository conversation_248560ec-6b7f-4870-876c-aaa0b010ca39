
.ant-upload-rtl.deer-form-upload-picture .ant-upload-list-item {
  float: right;
}

.deer-form-upload {
  &-picture{
    .ant-upload-list-item {
      float: left;
      width: 200px !important;
      margin-inline-end: 8px;
    }
  }

  &-text {
    .ant-upload-list-item-action {
      opacity: 1 !important;
    }

    .ant-upload-list-item {
      background: #F8FAFC !important;
      padding: 12px;
      border-radius: 8px;
      height: auto !important;

      // .ant-upload-list-item-actions {

      //   .ant-btn {
      //     &:hover {
      //       background: #F8FAFC !important;
      //     }
      //   }

      //   .ant-btn-icon .anticon {
      //     color: var(--icb-color-p01) !important;
      //     transition: all 0.2s;

      //     &:hover {
      //       opacity: 0.6;
      //     }
      //   }
      // }
    }

  }
}
