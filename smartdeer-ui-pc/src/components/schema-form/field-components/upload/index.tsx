import React from 'react';
import { Upload, Button, message } from 'antd';
import type { UploadProps, UploadFile } from 'antd';
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import { isArray, isObject, isString } from 'lodash';
import type { FieldProps } from '../typing'
import { omit } from '../../../../utils'
import S from '../../../../utils/storage'
import { ConfigContext } from '../../../config-provider'
import { useFileAccessToken } from '../../../../utils/hooks'
import { useParamsDict } from '../../../../utils/hooks'
import { replaceVariablesInTemplate } from '../../../../utils/replaceVariablesInTemplate';
import { checkFileType } from '../../../../utils/checkFileType'

// 兼容代码-----------
import 'antd/lib/upload/style';
//------------

import './index.less'

const classPrefix = `deer-form-upload`


const FieldUpload: React.FC<FieldProps> = (props) => {

  const { value, onChange, fieldProps = {} } = props

  const { uploadType, sizeLimit } = fieldProps

  const { appApiBaseUrl, language, isFileToken, appDefaultEntityId, appFileTokenUploadApi, appFileTokenCommonUploadApi } = React.useContext(ConfigContext)

  const paramsDict = useParamsDict()

  const { getTheCompleteFileUrl } = useFileAccessToken()

  const [fileList, setFileList] = React.useState<UploadFile[]>([]);

  const extraProps = omit(fieldProps, ['value', 'id', 'onChange', 'uploadType', 'isPrivate'])

  const token = S.getAuthToken()

  const actionUrl = React.useMemo(() => {
    let apiUrl = ''

    if (fieldProps?.api) {
      apiUrl = fieldProps?.api.includes('http') ? fieldProps?.api : `${appApiBaseUrl}${fieldProps?.api}`
    } else if (isFileToken) {
      if (paramsDict.entityUUID) {
        apiUrl = `${appApiBaseUrl}${appFileTokenUploadApi}`
        apiUrl = replaceVariablesInTemplate(paramsDict, apiUrl)
      } else if (paramsDict.entityId !== appDefaultEntityId) {
        apiUrl = `${appApiBaseUrl}${appFileTokenUploadApi}`
        apiUrl = replaceVariablesInTemplate(paramsDict, apiUrl)
      } else {
        apiUrl = `${appApiBaseUrl}${appFileTokenCommonUploadApi}`
      }
    } else {
      apiUrl = `${appApiBaseUrl}/v1/common/file/uploadImage`
    }

    return apiUrl
  }, [fieldProps?.api])

  React.useEffect(() => {
    const init = async () => {
      const newValue: any[] = isArray(value) ? value : (value ? [value] : [])

      const fileList: any[] = newValue.map(async (item: string | Record<string, string>, index: number) => {
        let name = ''
        let url = ''
        let fileKey = ''
        if (isString(item)) {
          const i = item.lastIndexOf('/')
          name = item.substring(i + 1)
          url = item
          fileKey = item
        } else if (isObject(item)) {
          name = item?.name
          url = item?.url
          fileKey = item?.url
        }

        if (isFileToken) {
          url = await getTheCompleteFileUrl(url)
        }

        return {
          name: name,
          thumbUrl: url,
          fileKey: fileKey,
          uid: name + index,
          status: 'done'
        }
      })

      const newfileList = await Promise.all(fileList)

      setFileList(newfileList)
    }

    init()
  }, [])

  const handleBeforeUpload: UploadProps['beforeUpload'] = (file: File) => {
    const check = checkFileType(file, uploadType)

    if (!check) {
      message.error(`${file.name} ${language.upload.defaultValidateMessages.isNotSupported}`);
    }

    let isLtSizeLimit = true

    if (sizeLimit > 0) {
      isLtSizeLimit = file.size / 1024 / 1024 < sizeLimit; // 动态大小限制
      if (!isLtSizeLimit) {
        let msg = language.upload.defaultValidateMessages.fileSizeError
        msg = msg.replace('{size}', sizeLimit);
        message.error(msg);
      }
    }
    return (check && isLtSizeLimit) || Upload.LIST_IGNORE;
  }

  const handleChange: UploadProps['onChange'] = (info) => {
    let oldFileList = [...info.fileList];

    const maxCount = Number(fieldProps?.maxCount)

    if (maxCount) {
      oldFileList = oldFileList.slice(-maxCount);
    }

    const newFileList = oldFileList.filter(file => !!file.status).map((file) => {
      if (!file.response) {
        return file;
      }

      let fileKey = file.response.data.absoluteFileUrl

      if (isFileToken) {
        fileKey = file.response.data
      } else {
        fileKey = file.response.data.absoluteFileUrl
      }

      if (fileKey.includes('_median')) {
        fileKey = fileKey.substring(0, fileKey.length - 7)
      }

      return {
        ...file,
        fileKey: fileKey
      }
    });

    setFileList(newFileList);

    const retFileList = newFileList.filter(item =>
      item.status === 'done'
    ).map((item: any) => {

      if (fieldProps?.format === 'object') {
        return {
          url: item.fileKey || item.thumbUrl,
          name: item.name
        }
      }

      return item.fileKey || item.thumbUrl
    })

    if ((!value || value.length === 0) && retFileList.length === 0) return

    const retValue = Number(fieldProps?.maxCount) === 1 ? retFileList[0] : retFileList

    onChange?.(retValue)
  }

  const handleCLickDownload = async (file: any) => {
    let url = ''

    if (fieldProps?.format === 'object') {
      const currentFile = value.find((item: any) => {
        return item.name === file.name
      })

      if (!currentFile) return

      url = currentFile.url

    } else {
      url = value.find((item: any) => {
        return item === file.fileKey
      })
    }

    if (!url) return

    if (isFileToken && !url.includes('http')) {
      url = await getTheCompleteFileUrl(url)
    }

    window.open(url)
  }

  return (
    <Upload
      accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.xls,.xlsx,.zip,.rar,.7z,.tar,.mp4,.avi,.mov,.mkv"
      listType='picture'
      {...extraProps}
      className={`${classPrefix} ${classPrefix}-${extraProps.listType || 'picture'}`}
      action={actionUrl}
      headers={{ 'Authorization': token }}
      beforeUpload={handleBeforeUpload}
      onChange={handleChange}
      fileList={fileList}
      showUploadList={{
        showDownloadIcon: true,
        downloadIcon: <DownloadOutlined />
      }}
      onDownload={handleCLickDownload}
    >
      <Button
        icon={<UploadOutlined />}
      >
        {language.upload.onClickText}
      </Button>
    </Upload>
  );
};

export default FieldUpload
