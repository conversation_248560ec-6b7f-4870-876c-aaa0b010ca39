import React from 'react';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import { ConfigContext } from '../../../config-provider'
import type { FieldProps } from '../typing'
import { convertTimezoneToUTC, convertUTCToTimezone } from '../../../../utils/time';
import { isString } from 'lodash';

const FieldDatePicker: React.FC<FieldProps> = (props) => {
  const {
    value,
    onChange,
    fieldProps
  } = props

  const { timezone } = React.useContext(ConfigContext)

  const handleChange = (_: any, timeString: string | string[]) => {
    let newTimeString: string = '';

    if (timeString) {
      if (timezone) {
        newTimeString = convertTimezoneToUTC(timeString, timezone, 'x') + ''
      } else {
        newTimeString = dayjs(timeString as string).valueOf() + ''
      }

      if (fieldProps?.defaultHours) {
        newTimeString = dayjs(timeString as string).add(fieldProps?.defaultHours, 'hour').valueOf() + ''
      }
    }

    onChange?.(newTimeString)
  }

  let newValue: any = value ? dayjs(Number(value)) : ''

  if (newValue && timezone) {
    newValue = convertUTCToTimezone(newValue, timezone)
  }

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  // console.log('fieldProps', fieldProps)
  // console.log('fieldProps?.defaultPickerValue', fieldProps?.defaultPickerValue)
  return (
    <DatePicker
      value={newValue}
      placeholder=' '
      {...fieldProps}
      disabled={isDisabled}
      style={{ width: '100%', ...fieldProps?.style }}
      defaultPickerValue={fieldProps?.defaultPickerValue ? dayjs(fieldProps?.defaultPickerValue) : undefined}
      onChange={handleChange}
    />
  );
};

export default FieldDatePicker
