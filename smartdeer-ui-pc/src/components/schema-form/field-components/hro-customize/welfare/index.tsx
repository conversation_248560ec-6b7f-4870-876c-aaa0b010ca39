import React from "react";
import { FieldProps } from "../../typing";
import { Input, Flex, Alert, Button } from 'antd';
import { BorderBottomOutlined } from '@ant-design/icons';

const FieldHROWelfare: React.FC<FieldProps> = (props) => {
  const { fieldProps, ...rest } = props
  return (
    <div className={'hro.welfare'}>
      <Flex justify="space-between" className={`mb-[20px] bg-gray-100 px-3 py-3 rounded`}>
        <Flex>
          <Flex className={`mr-6`}>
            <BorderBottomOutlined className={`mr-4`} />
          </Flex>
          <Flex vertical>
            <div className={`font-bold`}>{fieldProps?.title}</div>
            {fieldProps?.subTitle && (
              <div className={`text-gray-500`}>{fieldProps?.subTitle}</div>
            )}
          </Flex>
        </Flex>
        <Flex>
          {fieldProps?.addDisable ? (
            <Button type="primary" size={'small'} disabled>{fieldProps?.addText}</Button>
          ) : (
            <Button type="primary" size={'small'}>{fieldProps?.addText}</Button>
          )}
        </Flex>
      </Flex>
      <Flex vertical>
        {fieldProps?.description && (
          <div className={`mb-[20px]`}>{fieldProps?.description}</div>
        )}
        {fieldProps?.alert && (
          <Alert message={'医疗保险是香港的一项强制性福利'} showIcon className={`w-full`} />
        )}
      </Flex>
    </div>
  )
}

export default FieldHROWelfare