import React from "react";
import { FieldProps } from "../../typing";
import { Flex, Button } from 'antd';

const FieldHROWorkChecker: React.FC<FieldProps> = (props) => {
  return (
    <div className='hro.work.checker'>
      <Flex justify="space-between" className={`mb-[20px] bg-gray-100 px-3 py-3 rounded`}>
        <Flex vertical>
          <div className={`mb-[10px] font-bold`}>检查工作范围</div>
          <div className={`text-gray-500`}>通过我们的人工智能检查验证您的工作范围是否适用于 SmartDeer EOR 模型，以跳过审核并立即生成报价。</div>
        </Flex>
        <Flex>
          <Button type="primary" size="small">检查</Button>
        </Flex>
      </Flex>
    </div>
  )
}

export default FieldHROWorkChecker