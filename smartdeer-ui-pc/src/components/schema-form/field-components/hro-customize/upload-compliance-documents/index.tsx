import React from "react";
import { Input, Flex, Alert, Button, Modal, Tag, Space } from 'antd';
import { EditOutlined, DeleteOutlined, CloseCircleOutlined, CheckCircleOutlined, DownloadOutlined } from '@ant-design/icons';
import { SchemaForm } from '../../../../../index';
import DownloadFiles from '../../../../download-files';
import { ConfigContext } from '../../../../config-provider'
import InnerHtml from '../../../../inner-html'
import { replaceVariablesInTemplate } from '../../../../../utils/replaceVariablesInTemplate'

import { FieldProps } from "../../typing";

type ModalStateType = {
  title?: string;
  open: boolean;
  type?: 'add' | 'download';
}

const FieldHroUploadComplianceDocuments: React.FC<FieldProps> = (props) => {
  const {
    value = {},
    fieldModalSteps,
    fieldProps = {},
    formItemTitle,
    onChange,
    fieldValueType
  } = props

  const configContext = React.useContext(ConfigContext)
  const urlParams = configContext.useUrlParams()

  const [stepIndex, setStepIndex] = React.useState(1)
  const [list, setList] = React.useState<any[]>(Array.isArray(value.list) ? value.list : [])
  const [modalFromValues, setModalFromValues] = React.useState<Record<string, any>>({})

  const [initialValues, setInitialValues] = React.useState({})

  const [modalState, setModalState] = React.useState<ModalStateType>({
    open: false,
    type: 'download'
  });

  const uploadAction = fieldProps.uploadApi ?
    fieldProps.uploadApi.replace('*{flowToken}', urlParams?.flowToken) :
    `/saas/gs/v1/flow/token/${urlParams?.flowToken}/upload`

  const modalFormColumns = [
    {
      "type": "draggerUpload",
      "title": <InnerHtml content={fieldProps?.description} />,
      "field": "fileList",
      "props": {
        "action": uploadAction,
      },
      "labelProps": {
        "valuePropName": "fileList",
        "rules": [{
          "required": true,
          "message": configContext && configContext?.language ? configContext?.language?.form.defaultValidateMessages.upload.default : '请上传文件！'
        }]
      }
    }
  ]

  const handleClickAdd = () => {
    setStepIndex(1)

    setInitialValues({
      fileList: value?.list ? value?.list : []
    })

    setModalState({
      open: true,
      type: 'add'
    })
  }

  const handleClicShowDownload = () => {
    setModalState({
      open: true,
      type: 'download'
    })
  }

  const handleFinish = (values: Record<string, any>) => {
    if (stepIndex === 1) {
      setStepIndex(stepIndex + 1)
      return
    }

    onChange({
      key: fieldValueType,
      groupKey: fieldProps?.groupKey || '',
      groupKeyStr: fieldProps?.groupKeyStr || '',
      list: values.fileList
    })

    setModalState({
      open: false
    })
  }

  const handleReset = () => {
    setModalState({
      open: false
    })
    setInitialValues({})
  }

  const handleCancel = () => {
    setModalState({
      open: false
    })
    setInitialValues({})
  }

  const renderFooter = (onClick?: (values: any) => void) => {
    return (
      <div className={`flex justify-end items-center`}>
        <div className={`text-gray-500 text-12 mr-4`}>
          <div>Step {stepIndex} of 2</div>
          <div>Next: {stepIndex === 1 ? 'Upload documents' : 'Submit'}</div>
        </div>

        <Button
          size='middle'
          type='primary'
          htmlType='submit'
          onClick={() => {
            if (onClick && typeof onClick === 'function') {
              onClick?.({})
            }
          }}
        >
          {stepIndex === 1 ? 'Next' : 'Submit'}
        </Button>
      </div>
    )
  }

  return (
    <div className='hro.uploadComplianceDocuments'>
      <div className={`bg-gray-100 rounded-8 px-3 py-3 flex items-center   justify-between`}>
        <div className={`pr-4`}>
          <Space>
            <div className={`font-bold`}>{fieldProps?.title}</div>

            {value?.list ? (
              <>
                <Tag icon={<CheckCircleOutlined />} color='success'>
                  SUBMITTED
                </Tag>

                <span className={`cursor-pointer`} onClick={handleClicShowDownload}>
                  <DownloadOutlined />
                </span>
              </>
            ) : (
              <Tag icon={<CloseCircleOutlined />} color='error'>
                NOT SUBMITTED
              </Tag>
            )}

          </Space>

          <InnerHtml className="text-gray-500 mt-[10px]" content={fieldProps?.description} />
        </div>

        <Button size='small' type='primary' onClick={handleClickAdd}>Upload</Button>
      </div>

      <Modal
        title={`Upload compliance document`}
        open={modalState.open}
        destroyOnClose={true}
        onCancel={handleCancel}
        footer={null}
      >
        <div className={`font-bold text-center`}>{fieldProps?.title}</div>

        {modalState.type === 'add' ? (
          <>
            <div className={`py-6`}>
              {stepIndex === 1 ? (
                <>
                  <InnerHtml className={`mb-[24px]`} content={fieldProps?.modalPrompt} />
                  {renderFooter(handleFinish)}
                </>
              ) : (
                <SchemaForm
                  columns={modalFormColumns}
                  initialValues={initialValues}
                  size='large'
                  layout='vertical'
                  labelCol={{ span: 24 }}
                  gridProps={{
                    col: 1
                  }}
                  onFinish={handleFinish}
                  onReset={handleReset}
                  resetText='关闭'
                  submitText={'确定'}
                  footer={renderFooter as any}
                />
              )}
            </div>

            <div className={`text-center text-12  text-gray-700`}>
              <div>Issues with this compliance document? Provide feedback</div>
              <div>Or contact support if you need assistance.</div>
            </div>
          </>
        ) : (
          <div className={`py-6`}>
            <DownloadFiles list={value?.list || []} />
          </div>
        )}

      </Modal>
    </div>
  )
}

export default FieldHroUploadComplianceDocuments
