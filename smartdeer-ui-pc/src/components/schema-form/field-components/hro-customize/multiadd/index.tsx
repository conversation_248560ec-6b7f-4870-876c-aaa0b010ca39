import React from "react";
import { Input, <PERSON>lex, <PERSON><PERSON>, But<PERSON>, Modal } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { SchemaForm } from '../../../../../index';

import { FieldProps } from "../../typing";

type ModalStateType = {
  title?: string;
  open: boolean;
  isAdd?: boolean;
  editKey?: string;
  editIndex?: number
}

const FieldHROMultiadd: React.FC<FieldProps> = (props) => {
  const { value = {}, childrenColumns, fieldProps, formItemTitle, onChange, fieldValueType } = props

  const [list, setList] = React.useState<any[]>(Array.isArray(value.list) ? value.list : [])

  const [initialValues, setInitialValues] = React.useState({})

  const [modalState, setModalState] = React.useState<ModalStateType>({
    open: false,
  });

  const handleClickAdd = () => {
    setModalState({
      title: '添加',
      open: true,
      isAdd: true
    })
  }

  const handleClickItemEdit = (index: number, item: Record<string, any>) => {
    setInitialValues(item)

    setModalState({
      title: '编辑',
      open: true,
      isAdd: false,
      editKey: item.key,
      editIndex: index
    })
  }

  const handleClickItemDel = (index: number, key: string) => {
    const newList = [...list]
    newList.splice(index, 1)
    setList(newList)

    if (newList.length === 0) {
      onChange?.(undefined)
    } else {
      onChange?.({
        key: fieldValueType,
        groupKey: fieldProps?.groupKey || '',
        groupKeyStr: fieldProps?.groupKeyStr || '',
        template: fieldProps?.template || '',
        list: newList
      })
    }
  }

  const handleFinish = (values: Record<string, any>) => {
    console.log('values', values)

    setModalState({
      open: false,
    })
    setInitialValues({})

    const newList = [...list]

    if (modalState.isAdd) {
      newList.push({
        ...values,
        key: Date.now() + ''
      })

      setList(newList)
    } else {
      newList[modalState?.editIndex!] = { ...newList[modalState?.editIndex!], ...values }
      setList(newList)
    }

    onChange({
      key: fieldValueType,
      groupKey: fieldProps?.groupKey || '',
      groupKeyStr: fieldProps?.groupKeyStr || '',
      template: fieldProps?.template || '',
      list: newList
    })
  }

  const handleReset = () => {
    setModalState({
      open: false
    })
    setInitialValues({})
  }

  const handleCancel = () => {
    setModalState({
      open: false
    })
    setInitialValues({})
  }

  return (
    <div className='hro.multiadd'>
      <Button size='small' type='primary' onClick={handleClickAdd}>
        添加
      </Button>

      {!!list.length && (
        <div className={`mt-[10px]`}>
          {list.map((item, index) => {
            return (
              <Flex key={item.key} justify="space-between" className={`mb-[20px] bg-gray-100 px-3 py-3 rounded-8 last:mb-0`}>
                <Flex vertical>
                  <div className={`mb-[5px] font-bold`}>{item[fieldProps?.titleKey]}</div>
                  <div className={`text-gray-500`}>
                    {Array.isArray(fieldProps?.descriptionKey) ? (
                      <>
                        {fieldProps?.descriptionKey.map((key, index) => {
                          return (
                            <span key={key}>
                              {item[key]}
                              {fieldProps?.descriptionKey.length - 1 !== index && '/'}
                            </span>
                          )
                        })}
                      </>
                    ) : (
                      <span>
                        {item[fieldProps?.descriptionKey]}
                      </span>
                    )}
                  </div>
                </Flex>

                <Flex align='center' gap='10px'>
                  <div className={`font-bold`}>{item[fieldProps?.valueKey]} {fieldProps?.valueUnitKey ? item[fieldProps?.valueUnitKey] : ''}</div>
                  <EditOutlined onClick={() => handleClickItemEdit(index, item)} className={`cursor-pointer text-#p1`} style={{ fontSize: '18px' }} />
                  <DeleteOutlined onClick={() => handleClickItemDel(index, item.key)} className={`cursor-pointer text-#p1`} style={{ fontSize: '18px' }} />
                </Flex>
              </Flex>
            )
          })}
        </div>
      )}

      <Modal
        title={`增加${formItemTitle}`}
        open={modalState.open}
        destroyOnClose={true}
        onCancel={handleCancel}
        footer={null}
      >
        {!!childrenColumns && (
          <SchemaForm
            columns={childrenColumns}
            initialValues={initialValues}
            size='large'
            layout='vertical'
            labelCol={{ span: 24 }}
            onFinish={handleFinish}
            onReset={handleReset}
            resetText='关闭'
            submitText={'确定'}
            gridProps={{
              col: 1
            }}
          />
        )}
      </Modal>
    </div>
  )
}

export default FieldHROMultiadd
