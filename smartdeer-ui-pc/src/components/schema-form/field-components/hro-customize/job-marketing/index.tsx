import React from "react";
import { FieldProps } from "../../typing";
import { Input, <PERSON>lex, <PERSON><PERSON>, But<PERSON> } from 'antd';

const FieldHROJobMarketing: React.FC<FieldProps> = (props) => {
  return (
    <div className='eor.job.marketing'>
      <Flex justify="space-between" className={`mb-[20px] bg-gray-100 px-3 py-3 rounded`}>
        <Flex vertical>
          <div className={`mb-[10px] font-bold`}>市场洞察</div>
          <div className={`text-gray-500`}>暂时我们没有关于该职位的市场洞察信息。</div>
        </Flex>
      </Flex>
    </div >
  )
}

export default FieldHROJobMarketing