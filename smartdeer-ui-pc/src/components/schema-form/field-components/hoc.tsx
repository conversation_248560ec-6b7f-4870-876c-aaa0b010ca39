
import React from 'react';
import { SchemaFormContext } from '../context'
import type { FieldProps, FieldComponentProps, FieldValueType } from './typing'
import { replaceTemplateWithPipes } from '../../../utils/replaceTemplateWithPipes'
import { isArray } from 'lodash';
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'
import { isSpecialValue } from '../../../utils/isSpecialValue'


interface HOCEffectValueProps extends FieldProps {
  children: React.ReactElement
}

export const HOCEffectValue: React.FC<HOCEffectValueProps> = (props) => {
  const { children, effect, field, originFormItemName, dependenciesValues } = props

  const { form, middleware, formVariables } = React.useContext(SchemaFormContext)

  React.useEffect(() => {
    const value = effect?.value
    const getValue = effect?.getValue
    const switchValue = effect?.switchValue

    const variables = {
      ...formVariables,
      ...dependenciesValues,
      [field]: props.value || '',
      index: props.index
    }

    let newValue: any

    if (!!value) {
      newValue = replaceTemplateWithPipes(value || '', variables, /\*<([^}]+)>/g)
      newValue = replaceTemplateWithPipes(newValue || '', variables)
    }

    if (!!getValue) {
      const fn = middleware[getValue]

      if (fn) {
        newValue = fn(variables)
      }
    }

    if (isArray(switchValue)) {
      const current = switchValue.find(item => {
        return evaluateLogicalExpression(variables, item.case)
      })

      if (current) {
        newValue = replaceTemplateWithPipes(current.value || '', variables)
      }
    }

    if (newValue && !isSpecialValue(newValue)) {
      form?.setFieldValue(originFormItemName, newValue)
    }

  }, [dependenciesValues, formVariables])

  return (
    <>
      {React.cloneElement(children, {
        ...children.props
      })}
    </>
  )
}

export const HOCEffectDisable: React.FC<HOCEffectValueProps> = (props) => {
  const { children, effect, dependenciesValues, fieldProps } = props

  const { formVariables } = React.useContext(SchemaFormContext)

  const isDisabled = React.useMemo(() => {
    const disabled = effect?.disabled

    const variables = { ...formVariables, ...dependenciesValues }

    return evaluateLogicalExpression(variables, disabled || '')

  }, [dependenciesValues, formVariables])

  return (
    <>
      {React.cloneElement(children, {
        ...children.props,
        fieldProps: {
          ...fieldProps,
          disabled: isDisabled
        }
      })}
    </>
  )
}
