import { Form, Input, TreeSelect } from 'antd';
import { isNil, isArray, isString, isEmpty } from 'lodash';
import React, { useMemo } from 'react';
import { SchemaFormContext } from '../../context';
import omit from '../../../../utils/omit'
import type { FieldProps } from '../typing';
import { usePropsOptions } from '../../../../utils/hooks';
import { mergeProps } from '../../../../utils/withDefaultProps';
import { generatingSupplementingTheName } from '../utils'
import { useDeepCompareEffect } from 'ahooks'

const defaultProps = {
  fieldProps: {
    showSearch: true,
    treeDefaultExpandAll: true,
    allowClear: true,
  },
};

type RecordType = Record<string, any>;

const findNodeByValue = (
  tree: RecordType[],
  value: string,
): RecordType | null => {
  for (let node of tree) {
    if (node.value === value) {
      return node;
    }
    if (node.children) {
      let result = findNodeByValue(node.children, value);
      if (result) {
        return result;
      }
    }
  }
  return null;
};


const buildValueMap = (list: RecordType[]) => {
  const valueMap: RecordType = {}

  const traverse = (nodes: RecordType[], parent: RecordType | null): void => {
    nodes.forEach(({ children, value, label }) => {
      const node: RecordType = { parent, label, value };
      valueMap[value] = node;
      if (children) {
        traverse(children, node);
      }
    });
  };

  traverse(list, null);
  return valueMap;
};

const FieldTreeSelect: React.FC<FieldProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const { value, fieldProps, onChange, field, originFormItemName } = props;

  const [valueMap, setValueMap] = React.useState<RecordType>({});
  const [prefixValue, setPrefixValue] = React.useState<string>();

  const {
    fieldNames,
    autoPrefix,
    supplementingTheString,
    supplementingTheKey,
  } = fieldProps || {};

  const schemaFormContext = React.useContext(SchemaFormContext);

  const newSupplementingTheString = supplementingTheString ?? schemaFormContext.supplementingTheString

  const { isLoading, options } = usePropsOptions(props);

  React.useEffect(() => {
    if (!options) return
    if (!autoPrefix) return

    const valueMap = buildValueMap(options);

    setValueMap(valueMap)

  }, [options]);

  const getPathLabel = (value: string) => {
    const path = [];
    let current = valueMap[value];
    while (current) {
      path.unshift(current.label);
      current = current.parent;
    }

    path.pop()

    return path.join(' / ') + ' / ';
  }

  React.useEffect(() => {
    if (!autoPrefix) return
    if (!value) return
    if (!options) return
    if (isEmpty(valueMap)) return

    setPrefixValue(getPathLabel(value))
  }, [value, options, valueMap]);

  const handleChange = (value: string) => {
    const val = !isNil(value)
      ? Array.isArray(value)
        ? value
        : value.toString()
      : undefined;

    onChange?.(val);
  };

  React.useEffect(() => {
    if (!newSupplementingTheString && !supplementingTheKey) return

    const option = findNodeByValue(options, value);

    if (newSupplementingTheString) {
      const name = generatingSupplementingTheName(originFormItemName, 'Str')

      let label = (option && option.label) ? option.label : ''

      if (autoPrefix) {
        label = getPathLabel(value) + label
      }

      schemaFormContext.form?.setFieldValue(name, label);
    }

    if (supplementingTheKey) {
      const name = generatingSupplementingTheName(originFormItemName, 'Key')

      schemaFormContext.form?.setFieldValue(name, option && option.enumKey ? option.enumKey : '');
    }
  }, [value, options, fieldNames]);

  const isDisabled = isString(fieldProps?.disabled)
    ? fieldProps?.disabled === 'true'
    : fieldProps?.disabled;

  const treeSelectProps = React.useMemo(() => {
    return omit(fieldProps, ['autoPrefix', 'supplementingTheString', 'supplementingTheKey', 'fieldNames', 'parentSelectable'])
  }, [fieldProps])

  return (
    <>
      <TreeSelect
        treeDefaultExpandAll
        {...treeSelectProps}
        value={
          !isNil(value)
            ? Array.isArray(value)
              ? value
              : value.toString()
            : undefined
        }
        fieldNames={{
          value: 'value',
          label: 'label',
          children: 'children',
        }}
        loading={isLoading}
        disabled={isDisabled}
        treeData={options}
        style={{ width: '100%' }}
        prefix={prefixValue}
        onChange={handleChange}
      />

      {newSupplementingTheString && (
        <Form.Item hidden noStyle name={generatingSupplementingTheName(originFormItemName, 'Str')}>
          <Input />
        </Form.Item>
      )}

      {supplementingTheKey && (
        <Form.Item hidden noStyle name={generatingSupplementingTheName(originFormItemName, 'Key')}>
          <Input />
        </Form.Item>
      )}
    </>
  );
};

export default FieldTreeSelect;
