import React from 'react';
import { Input, Spin } from 'antd';
import dayjs from 'dayjs'
import type { FieldProps } from '../../../typing'
import { getEffectFetchConfig } from '../../../../../../utils/getEffectFetchConfig'
import swrFetcher from '../../../../../../utils/swrFetcher'
import { ConfigContext } from '../../../../../config-provider'
import { SchemaFormContext } from '../../../../context'


const FieldHashkeyReallyWantTime: React.FC<FieldProps> = (props) => {
  const configContext = React.useContext(ConfigContext)
  const { language } = configContext
  const { formVariables } = React.useContext(SchemaFormContext)

  const [day, setDay] = React.useState('0')
  const [errorMsg, setErrorMsg] = React.useState('')

  const [loading, setLoading] = React.useState(true)

  const fetch = async () => {
    setLoading(true)
    setErrorMsg('')

    try {
      const variables = { ...formVariables, ...props?.dependenciesValues }

      const { isBlockLeave } = variables

      const { api, method, params } = getEffectFetchConfig(props?.effect?.fetch!, configContext, variables)

      const { data } = await swrFetcher(api, method, params)

      const userReallyWant = data.userReallyWant ? data.userReallyWant + '' : '0'

      if (isBlockLeave === '1' && Number(userReallyWant) < 5) {
        setErrorMsg(language.form.defaultValidateMessages.hashkeyGsLeave.blockLeave)
      } else {
        setDay(userReallyWant)
      }

    } catch (err: any) {
      console.log('err', err)
      setErrorMsg(err.message)
    }

    setLoading(false)
  }

  React.useEffect(() => {
    if (errorMsg) {
      props?.onChange(undefined)
      return
    }
    props?.onChange(day)
  }, [day, errorMsg])

  React.useEffect(() => {
    fetch()
  }, [props?.dependenciesValues])

  if (errorMsg) {
    return <div style={{ color: 'red' }}>{errorMsg}</div>
  }

  return (
    <Spin spinning={loading}>
      <Input value={day} disabled />
    </Spin>
  );
};

export default FieldHashkeyReallyWantTime
