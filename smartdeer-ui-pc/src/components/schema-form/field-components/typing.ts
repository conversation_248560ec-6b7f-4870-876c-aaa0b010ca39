import type { EffectType } from '../../../typing';
import type { FormColumnsType } from '../typing';

export type FieldValueType =
  | 'input'
  | 'password'
  | 'textArea'
  | 'inputNumber'
  | 'radio'
  | 'checkbox'
  | 'select'
  | 'switch'
  | 'timePicker'
  | 'timeRangePicker'
  | 'datePicker'
  | 'dateRangePicker'
  | 'dateTimePicker'
  | 'cascader'
  | 'upload'
  | 'draggerUpload'
  | 'alert'
  | 'FcRow'
  | any;

export type FieldModalType = {
  stepName: string;
  form: FormColumnsType[];
};

export type FieldEffectType = EffectType;

export type FieldComponentProps = {
  index?: number;

  field: string;

  formItemName: string | string[];

  originFormItemName: string | string[];

  valueType: FieldValueType;

  fieldProps?: FieldPropsType;

  effect?: FieldEffectType;

  dependenciesValues: Record<string, any>;

  childrenColumns?: FormColumnsType[];

  formItemTitle?: string | React.ReactElement;

  fieldModalSteps?: FieldModalType[];
  fieldModal?: FieldModalType;

  onChange?: (...args: any) => void;
  value?: any;

  columns?: any[];
};

export type FieldOptionExtraType = {
  elementKey?: string;
};

export type FieldOptionType = {
  value: string;
  label: string;
  description?: string;
  children?: FieldOptionType[];
  extra?: FieldOptionExtraType | string | null;
  template?: string;
  templateKey?: string;
};

export type FieldProps = {
  field: string;

  formItemName: string | string[];

  originFormItemName: string | string[];

  fieldProps?: FieldPropsType;

  effect?: FieldEffectType;

  dependenciesValues: Record<string, any>;

  childrenColumns?: FormColumnsType[];

  formItemTitle?: string | React.ReactElement;

  fieldValueType: string;

  fieldModalSteps?: FieldModalType[];
  fieldModal?: FieldModalType;

  value?: any;
  onChange?: any;
  defaultValue?: any;

  [key: string]: any;
};

export type FieldPropsType = {
  className?: string;
  style?: React.CSSProperties;
  effect?: FieldEffectType;
  options?: FieldOptionType[];

  [key: string]: any;
};
