import React from 'react';
import type { FieldProps } from '../typing'
import Quill from '../../../quill'
import { ConfigContext } from '../../../config-provider'
import { hasImageTag } from '../../../../utils'
import { evaluateLogicalExpression } from '../../../../utils/evaluateLogicalExpression'

import './index.less'
import { isArray } from 'lodash';

const classPrefix = `deer-field-quill`

const FieldQuill: React.FC<FieldProps> = (props) => {
  const { value, onChange, field, fieldProps, dependenciesValues = {} } = props

  const { language } = React.useContext(ConfigContext)

  const { showFileExpirePrompt = '' } = fieldProps || {}

  const handleChange = (value: string) => {
    onChange?.(value)
  }

  const showFileExpire = React.useMemo(() => {
    if (!showFileExpirePrompt) return false

    return hasImageTag(value) && evaluateLogicalExpression(dependenciesValues, showFileExpirePrompt)
  }, [showFileExpirePrompt, dependenciesValues, value])

  return (
    <div className={`${classPrefix}`}>
      <Quill
        {...fieldProps}
        id={isArray(field) ? field.join('-') : field}
        className={`${classPrefix}-content`}
        value={value}
        onChange={handleChange}
      />

      {showFileExpire && (
        <div className={`${classPrefix}-expire-tip`}>
          <p>{language.quill.fileExpire.tip}</p>
        </div>
      )}
    </div>
  )
};

export default FieldQuill
