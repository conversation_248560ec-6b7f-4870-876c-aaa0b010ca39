import React from 'react';
import { Form, Select, Input, Spin } from 'antd';
import { SchemaFormContext } from '../../context'
import { isArray, isNil, isString, debounce } from 'lodash';
import type { FieldProps } from '../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'
import { usePropsOptions } from '../../../../utils/hooks'
import omit from '../../../../utils/omit'
import { generatingSupplementingTheName } from '../utils'
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes'

// 兼容代码-----------
import 'antd/lib/select/style';
//------------

const filterOption = (input: string, option?: { label: string; value: string }) =>
  (option?.label ?? '').toLowerCase().includes(input.toLowerCase());

const defaultProps = {
  fieldProps: {
    showSearch: true
  }
}

const FieldSelect: React.FC<FieldProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const { value, fieldProps, onChange, field, originFormItemName } = props

  const {
    mode,
    supplementingTheString,
    supplementingTheKey,
    supplementingTheTemplateKey,
    supplementingTheTemplateName,
    remoteSearch,
    remoteSearchParams,
    defaultSelected = false,
  } = fieldProps || {};

  const schemaFormContext = React.useContext(SchemaFormContext)

  const { isLoading, options, fetch } = usePropsOptions(props)

  const [fetching, setFetching] = React.useState(false);

  const newSupplementingTheString = supplementingTheString ?? schemaFormContext.supplementingTheString

  // const initRef = React.useRef(true)

  const debounceFetcher = debounce((keyword: string) => {
    if (!remoteSearch || !remoteSearchParams) return

    const searchParams = JSON.stringify(remoteSearchParams)

    const newSearchParams = keyword ? JSON.parse(replaceTemplateWithPipes(searchParams, { keyword })) : {}

    fetch(newSearchParams)
  }, 800)

  const handleClear = () => {
    if (!remoteSearch || !remoteSearchParams) return

    fetch({})
  }

  React.useEffect(() => {
    if (props.value || !defaultSelected) return;
    if (!options || options.length !== 1) return;

    // if (initRef.current) {
    //   initRef.current = false;
    // }

    let value = ''

    const selected = options.find(option => option.default === true)

    if (selected) {
      value = selected.value
    } else {
      value = options[0].value
    }

    if (mode !== 'multiple' || mode !== 'tags') {
      schemaFormContext.form?.setFieldValue(originFormItemName, value)
    }
  }, [options]);

  React.useEffect(() => {
    if (!newSupplementingTheString && !supplementingTheKey && !supplementingTheTemplateKey) return

    if ((mode === 'multiple' || mode === 'tags') && isArray(value)) {

      const optionList = value.map((item: any) => {
        const option = options.find(option => option.value === item)
        return option || { value: item, label: item }
      })

      if (newSupplementingTheString) {
        const name = generatingSupplementingTheName(originFormItemName, 'Str')

        if (!optionList?.length) {
          schemaFormContext.form?.setFieldValue(name, '')
        } else {
          const fieldStr = optionList.map(item => item.label).join(',')

          schemaFormContext.form?.setFieldValue(name, fieldStr)
        }
      }

      if (supplementingTheKey) {
        const name = generatingSupplementingTheName(originFormItemName, 'Key')

        if (!optionList?.length) {
          schemaFormContext.form?.setFieldValue(name, '')
        } else {
          const fieldKey = optionList.map(item => item.enumKey).join(',')
          schemaFormContext.form?.setFieldValue(name, fieldKey)
        }
      }

    } else {
      const option = options.find(option => option.value === value)

      if (newSupplementingTheString) {
        const name = generatingSupplementingTheName(originFormItemName, 'Str')

        schemaFormContext.form?.setFieldValue(name, option ? option?.label : '')
      }

      if (supplementingTheKey) {
        const name = generatingSupplementingTheName(originFormItemName, 'Key')

        schemaFormContext.form?.setFieldValue(name, option ? option?.enumKey : '')
      }

      if (supplementingTheTemplateKey && supplementingTheTemplateName) {
        schemaFormContext.form?.setFieldValue(supplementingTheTemplateName, option ? option?.templateKey : undefined)
      }
    }
  }, [value, options])


  const handleChange = (value: string) => {
    onChange?.(value)
  }

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  const selectedValue = React.useMemo(() => {
    if (isNil(value)) return undefined;
    if (mode === 'multiple' || mode === 'tags') {
      let newValue;
      if (isArray(value)) {
        return value;
      }
      try {
        newValue = value.split(',');
      } catch (e) {
        newValue = [];
      }
      return newValue;
    }
    return isArray(value) ? value : value.toString();
  }, [value, mode]);

  const selectProps = React.useMemo(() => {
    return omit(fieldProps, ['defaultOptions', 'remoteSearch', 'remoteSearchParams', 'supplementingTheString', 'supplementingTheKey', 'supplementingTheTemplateKey', 'supplementingTheTemplateName'])
  }, [fieldProps])

  return (
    <>
      <Select
        {...selectProps}
        showSearch={true}
        value={selectedValue}
        fieldNames={{
          value: 'value',
          label: 'label',
        }}
        disabled={isDisabled}
        style={{ width: '100%' }}
        loading={isLoading}
        options={options}
        filterOption={fieldProps.remoteSearch ? false : filterOption}
        onChange={handleChange}
        notFoundContent={fetching ? <Spin size="small" /> : null}
        onSearch={debounceFetcher}
        onClear={handleClear}
      />

      {newSupplementingTheString && (
        <Form.Item hidden noStyle name={generatingSupplementingTheName(originFormItemName, 'Str')}>
          <Input />
        </Form.Item>
      )}

      {supplementingTheKey && (
        <Form.Item hidden noStyle name={generatingSupplementingTheName(originFormItemName, 'Key')}>
          <Input />
        </Form.Item>
      )}

      {(supplementingTheTemplateKey && supplementingTheTemplateName) && (
        <Form.Item hidden noStyle name={supplementingTheTemplateName}>
          <Input />
        </Form.Item>
      )}
    </>
  );
};


export default FieldSelect

