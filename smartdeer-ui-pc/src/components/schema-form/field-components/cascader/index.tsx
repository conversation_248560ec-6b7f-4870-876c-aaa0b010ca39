import React from 'react';
import { FieldProps } from '../typing'
import DefaultCascader from './default-cascader'
import EffectCascader from './effect-cascader'

// 兼容代码-----------
import 'antd/lib/cascader/style';
//------------

const FieldCascader: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps } = props

  return (
    <>
      FieldCascader
      {/* {isObject(effect?.fetch) ? (
        <EffectCascader {...props} />
      ) : (
        <DefaultCascader {...props} />
      )} */}
    </>
  );
};

export default FieldCascader
