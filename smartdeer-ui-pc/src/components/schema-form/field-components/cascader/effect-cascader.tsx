import React, { useState } from 'react';
import { <PERSON><PERSON>, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { SWRResponse } from 'swr';
import type { FieldProps } from '../typing'
import { omit } from '../../../../utils'
import { useFetcher } from '../../../../utils/hooks'
import { swrMiddlewareAfter } from '../../../../utils/swrFetcher'

const EffectCascader: React.FC<FieldProps> = (props) => {
  const { effect } = props

  // const middleware = swrMiddlewareAfter((swr: SWRResponse) => {

  //   const { data: res } = swr

  //   const fnStr = effect?.fetch?.parse

  //   const fn = new Function(`return ${fnStr}`);

  //   const result = fn()(res.data)

  //   return {
  //     ...swr,
  //     data: result
  //   }
  // })

  // const { data = [], error, isLoading } = useFetcher(effect?.fetch?.action!, effect?.fetch?.method!, effect?.fetch?.defaultParams, middleware)

  // if (error) return <div className={`text-center py-4`}>failed to load</div>
  // if (isLoading) return <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />


  // const extra = omit(props, ['value', 'id', 'effect', 'options'])

  return <>Cascader 待完善</>

  return (
    <Cascader
    // options={data}
    // maxTagCount={3}
    // showCheckedStrategy={Cascader.SHOW_CHILD}
    // {...extra}
    />
  );
};

export default EffectCascader

