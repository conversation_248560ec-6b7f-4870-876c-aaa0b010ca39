import React from 'react';
import type { FieldProps } from '../typing'
// import { omit } from '../../../utils'

const FieldSpacing: React.FC<FieldProps> = (props) => {
  // console.log('FieldSpacing -> ', props)

  const { fieldProps } = props

  // console.log('valueType', valueType)

  // const extra = omit(props, ['id', 'valueType'])

  return (
    <div {...fieldProps} />
  );
};

export default FieldSpacing
