import React from 'react';
import { Input } from 'antd';
import type { FieldProps } from '../typing'

// 兼容代码-----------
import 'antd/lib/input/style';
//------------

const FieldTextArea: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps } = props

  return (
    <Input.TextArea
      {...fieldProps}
      value={value}
      onChange={onChange}
    />
  );
};

export default FieldTextArea
