import React from 'react';
import type { FieldProps } from '../typing'
import JsonEditor from '../../../json-editor'
import './index.less'

const classPrefix = `deer-field-json-editor`

const FieldJsonEditor: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps } = props

  const handleChange = (value: string) => {
    onChange?.(value)
  }

  return (
    <div className={`${classPrefix}`}>
      <JsonEditor {...fieldProps} className={`${classPrefix}-content`} value={value} onChange={handleChange} />
    </div>
  )
};

export default FieldJsonEditor
