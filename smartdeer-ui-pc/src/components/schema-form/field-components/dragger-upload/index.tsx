import React from 'react';
import { InboxOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { message, Upload } from 'antd';
import type { FieldProps } from '../typing'
import S from '../../../../utils/storage'
import { ConfigContext } from '../../../config-provider'
import { useDownloadFile } from '../../../../utils/hooks'

const { Dragger } = Upload;

const fileTypes = [
  'image/apng',
  "image/bmp",
  'image/gif',
  'image/jpeg',
  "image/pjpeg",
  'image/png',
  "image/svg+xml",
  "image/tiff",
  "image/webp",
  "image/x-icon",

  'application/pdf',
  'application/doc',
  'application/msword',
  'application/docx',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
]

const FieldDraggerUpload: React.FC<FieldProps> = (props) => {
  const { fileList = [], onChange, fieldProps } = props

  const { appApiBaseUrl } = React.useContext(ConfigContext)

  const token = S.getAuthToken()

  const { handleDownload } = useDownloadFile()

  const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
    const check = fileTypes.includes(file.type)

    if (!check) {
      message.error(`${file.name} 不支持此文件类型！`);
    }

    return check
  }

  const handleChange: UploadProps['onChange'] = ({ file, fileList }) => {
    if (file.response && file.response.code !== 0) {
      message.error(file.response.message)
      return
    }

    if (file.status === 'done' || file.status === 'removed') {

      const newFileList = fileList.map(item => {
        let fileUrl = ''

        if (item.thumbUrl || item.url) {
          fileUrl = (item.thumbUrl || item.url) as string
        } else {
          fileUrl = item.response.data
        }

        return {
          url: fileUrl,
          uid: item.uid,
          type: item.type,
          name: item.name,
          size: item.size
        }
      })

      onChange?.(Number(fieldProps?.maxCount) === 1 ? newFileList[0] : newFileList)
    }
  }

  const handleClickDownload = async (item: any) => {

    await handleDownload(item.thumbUrl || item.url, item.name)
  }

  const action = fieldProps?.action && fieldProps?.action.includes('http') ? fieldProps?.action : `${appApiBaseUrl}${fieldProps?.action}`

  return (
    <Dragger
      // {...fieldProps}
      multiple={false}
      accept=".doc,.docx,.pdf,.jpg,.jpeg,.png,.xls,.xlsx"
      defaultFileList={fileList.map((item: any, index: number) => {
        return {
          name: item.name,
          thumbUrl: item.thumbUrl || item.url,
          uid: item.uid || item.url || index,
          status: "done",
        }
      })}
      showUploadList={{
        showDownloadIcon: true,
      }}
      action={action}
      headers={{ 'Authorization': token }}
      beforeUpload={handleBeforeUpload}
      onChange={handleChange}
      onDownload={handleClickDownload}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">
        Click or drag file to this area to upload
      </p>
      <p className="ant-upload-hint">
        Support formats: JPG, PNG, PDF, XLS, XLSX, DOC, DOCX, PPT.
      </p>
    </Dragger>
  )
};

export default FieldDraggerUpload;
