import React from 'react';
import { Skeleton, Input } from 'antd'
import type { FieldProps } from '../../typing'
import { SchemaFormContext } from '../../../context'
import { getEffectFetchConfig } from '../../../../../utils/getEffectFetchConfig'
import { extractPatternsFromInput } from '../../../../../utils/extract'
import { checkKeysExistInObject } from '../../../../../utils/checkKeysExistInObject'
import { cachedSwrFetcher } from '../../../../../utils/swrFetcher'
import { useParamsDict } from '../../../../../utils/hooks'
import { ConfigContext } from '../../../../config-provider'
import { useDeepCompareEffect } from 'ahooks';


const FieldAppend: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps = {}, field, effect, dependenciesValues } = props

  // document.append

  const configContext = React.useContext(ConfigContext)

  const { formatterElementByKey } = configContext

  const { onAppend, formVariables } = React.useContext(SchemaFormContext)

  const paramsDict = useParamsDict({ ...formVariables, ...dependenciesValues })

  const [isSkeleton, setIsSkeleton] = React.useState(false)

  const fetch = async () => {
    if (!effect) return

    const fetchDependentValue = extractPatternsFromInput(JSON.stringify((effect.fetch)))

    if (fetchDependentValue.length > 0) {
      const has = checkKeysExistInObject(paramsDict, fetchDependentValue)

      onChange?.('')

      onAppend(field, [])

      if (!has) return
    }

    setIsSkeleton(true)

    try {

      const { api, method, params, dataIndex } = getEffectFetchConfig(effect.fetch!, configContext, paramsDict)

      if (!params.functionKey || !params.processKey) {
        Object.assign(params, { functionKey: formatterElementByKey })
      }

      const newDataIndex = dataIndex ? dataIndex : 'root'

      const { data } = await cachedSwrFetcher(api, method, params)

      const columns = JSON.parse(data[newDataIndex])

      onChange?.({ form: data[newDataIndex] })

      onAppend(field, columns)

    } catch (err: any) {

    }

    setIsSkeleton(false)
  }

  useDeepCompareEffect(() => {
    fetch()
  }, [dependenciesValues])

  return (
    <>
      {isSkeleton && <Skeleton active />}

      <div style={{ display: 'none' }}>
        <Input.TextArea value={value} />
      </div>
    </>
  );
};

export default FieldAppend
