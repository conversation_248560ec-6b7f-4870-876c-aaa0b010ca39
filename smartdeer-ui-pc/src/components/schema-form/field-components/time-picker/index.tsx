import React from 'react';
import { TimePicker } from 'antd';
import type { FieldProps } from '../typing'

const FieldTimePicker: React.FC<FieldProps> = (props) => {
  const {
    value,
    onChange,
    fieldProps
  } = props

  return (
    <TimePicker
      {...fieldProps}
      placeholder=' '
      style={{ width: '100%', ...fieldProps?.style }}
      value={value}
      onChange={onChange}
    />
  );
};

export default FieldTimePicker
