import React, { useEffect, useState } from 'react';
import { getEffectFetchConfig } from '../../../../../utils/getEffectFetchConfig';
import { useParamsDict } from '../../../../../utils/hooks';
import swrFetcher from '../../../../../utils/swrFetcher';
import { ConfigContext } from '../../../../config-provider';
import { SchemaFormContext } from '../../../context';
import { FieldProps } from '../../typing';

export const useFetchRule = (props: FieldProps) => {
  const { effect } = props;

  const configContext = React.useContext(ConfigContext);
  const schemaFormContext = React.useContext(SchemaFormContext);
  const formVariables = schemaFormContext.formVariables;
  const paramsDict = useParamsDict();
  const [isLoading, setIsLoading] = useState(false);

  const [conf, setConf] = useState<Record<string, any>>({});

  const fetch = async () => {
    if (!effect?.fetch) return;

    const { api, method, params } = getEffectFetchConfig(
      effect.fetch,
      configContext,
      { ...paramsDict, ...formVariables },
    );

    setIsLoading(true);
    const { data } = await swrFetcher(api, method, params);
    setConf(data);

    setIsLoading(false);
  };

  useEffect(() => {
    fetch();
  }, []);

  return {
    isLoading,
    conf,
  };
};
