import { Form, Input, Select } from 'antd';
import React from 'react';
import { evaluateLogicalExpression } from '../../../../../../utils/evaluateLogicalExpression';
import { SchemaFormContext } from '../../../../context';
import type { FieldProps } from '../../../typing';
import { useFetchRule } from '../hooks';

const FieldSSCFlowSelect: React.FC<FieldProps> = (props) => {
  const { fieldProps, effect, value, field } = props;

  const schemaFormContext = React.useContext(SchemaFormContext);

  const { fieldNames } = fieldProps || {};

  const show = evaluateLogicalExpression({}, effect?.show || '');

  const { conf, isLoading } = useFetchRule(props);

  const currentIndex = React.useMemo(() => {
    let list: any[] = [];
    if (!conf?.rs?.json) {
      return -1;
    }
    try {
      list = JSON.parse(conf.rs.json);

      const currentFinishedIndex = list.findLastIndex((node: any) => {
        return Number(node.status) === 1;
      });
      const currentStartIndex = list.findLastIndex((node: any) => {
        return Number(node.status) === 0;
      });
      const currentIndex = Math.max(
        currentFinishedIndex + 1,
        currentStartIndex,
      );

      return currentIndex;
    } catch (e) {
      return -1;
    }
  }, [conf]);

  const getItemDisabled = (item: any, index: number, list: any[]) => {
    let disabled = false;
    const canNotSkipableIndex = list.findIndex((node: any) => {
      return Number(node.status) !== 1 && node.skipable === 'false';
    });

    if (index < currentIndex) {
      disabled = true;
    } else {
      disabled = canNotSkipableIndex !== -1 && index > canNotSkipableIndex;
    }

    return disabled;
  };

  let options = React.useMemo(() => {
    let result: any[] = [];
    if (!conf?.rs?.json) {
      return [];
    }
    try {
      result = JSON.parse(conf.rs.json);

      result = result.map((item: any, index: number) => {
        return {
          ...item,
          label: item[fieldNames.label],
          value: item[fieldNames.value],
          disabled: getItemDisabled(item, index, result),
        };
      });
    } catch (e) {
      return [];
    }
    return result;
  }, [conf]);

  React.useEffect(() => {
    const option = options.find((option: any) => option?.value === value);
    schemaFormContext.form?.setFieldValue('flowStepKeyStr', option?.label);
    schemaFormContext.form?.setFieldValue('flowStep', option?.step);
  }, [value, options]);

  React.useEffect(() => {
    schemaFormContext.form?.setFieldValue(
      field,
      options[currentIndex]?.[fieldNames.value],
    );
  }, [currentIndex, options]);

  const handleChange = (value: any) => {
    props?.onChange(value);
  };

  return show ? (
    <>
      <Select
        {...fieldProps}
        value={value}
        fieldNames={{
          value: 'value',
          label: 'label',
        }}
        style={{ width: '100%' }}
        loading={isLoading}
        options={options}
        onChange={handleChange}
      />
      <Form.Item hidden noStyle name={'flowStepKeyStr'}>
        <Input />
      </Form.Item>
      <Form.Item hidden noStyle name={'flowStep'}>
        <Input />
      </Form.Item>
    </>
  ) : null;
};

export default FieldSSCFlowSelect;
