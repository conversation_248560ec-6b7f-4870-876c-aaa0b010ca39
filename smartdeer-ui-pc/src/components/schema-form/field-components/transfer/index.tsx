import React from 'react';
import { Table, Transfer, Skeleton } from 'antd';
import type { GetProp, TableColumnsType, TableProps, TransferProps } from 'antd';

import type { FieldProps } from '../typing'
import { usePropsOptions } from '../../../../utils/hooks'

type TransferItem = GetProp<TransferProps, 'dataSource'>[number];
type TableRowSelection<T extends object> = TableProps<T>['rowSelection'];

interface DataType {
  key: string;
  label: string;
  description?: string;
}

interface TableTransferProps extends TransferProps<TransferItem> {
  dataSource: DataType[];
  leftColumns: TableColumnsType<DataType>;
  rightColumns: TableColumnsType<DataType>;
}

// Customize Table Transfer
const TableTransfer: React.FC<TableTransferProps> = (props) => {
  const { leftColumns, rightColumns, ...restProps } = props;
  return (
    <Transfer style={{ width: '100%' }} {...restProps}>
      {({
        direction,
        filteredItems,
        onItemSelect,
        onItemSelectAll,
        selectedKeys: listSelectedKeys,
        disabled: listDisabled,
      }) => {
        const columns = direction === 'left' ? leftColumns : rightColumns;
        const rowSelection: TableRowSelection<TransferItem> = {
          getCheckboxProps: () => ({ disabled: listDisabled }),
          onChange(selectedRowKeys) {
            onItemSelectAll(selectedRowKeys, 'replace');
          },
          selectedRowKeys: listSelectedKeys,
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
        };

        return (
          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={filteredItems}
            size="small"
            style={{ pointerEvents: listDisabled ? 'none' : undefined }}
            onRow={({ key, disabled: itemDisabled }) => ({
              onClick: () => {
                if (itemDisabled || listDisabled) {
                  return;
                }
                onItemSelect(key, !listSelectedKeys.includes(key));
              },
            })}
          />
        );
      }}
    </Transfer>
  );
};

const filterOption = (input: string, item: DataType) =>
  item.label?.includes(input)

const defaultColumns: TableColumnsType<DataType> = [
  {
    dataIndex: 'label',
    title: '名称',
  },
  {
    dataIndex: 'description',
    title: '介绍',
  },
];

const FieldTransfer: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps = {} } = props

  const { columns = defaultColumns } = fieldProps

  const { isLoading, options } = usePropsOptions(props)

  const handleChange: TransferProps['onChange'] = (nextTargetKeys, direction, moveKeys) => {
    onChange(nextTargetKeys);
  };

  if (isLoading) return <Skeleton />

  return (

    <TableTransfer
      dataSource={options.map((item) => ({ label: item.label, description: item.description, key: item.value, extra: item.extra }))}
      targetKeys={value ? value : []}
      showSearch
      showSelectAll={false}
      onChange={handleChange}
      filterOption={filterOption}
      leftColumns={columns}
      rightColumns={columns}
    />
  );
};

export default FieldTransfer
