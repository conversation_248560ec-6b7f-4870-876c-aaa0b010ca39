import React from 'react';
import { Form, Checkbox, Input, Spin } from 'antd';
import type { RadioChangeEvent } from 'antd';
import type { FieldProps } from '../typing'
import { SchemaFormContext } from '../../context'
import { usePropsOptions } from '../../../../utils/hooks'
import { generatingSupplementingTheName } from '../utils'
import omit from '../../../../utils/omit'

// 兼容代码-----------
import 'antd/lib/checkbox/style';
//------------

const FieldCheckbox: React.FC<FieldProps> = (props) => {

  const { value, onChange, fieldProps = {}, field, originFormItemName } = props

  const schemaFormContext = React.useContext(SchemaFormContext)

  const { isLoading, options } = usePropsOptions(props)

  const newSupplementingTheString = fieldProps.supplementingTheString ?? schemaFormContext.supplementingTheString

  React.useEffect(() => {
    if (!newSupplementingTheString) return

    if (!value) return

    const optionList: any[] = value.map((item: any) => options.find(option => option.value === item))

    const name = generatingSupplementingTheName(originFormItemName, 'Str')

    if (!optionList?.length) {
      schemaFormContext.form?.setFieldValue(name, '')
    } else {
      const fieldStr = optionList.map(item => item.label).join(',')
      schemaFormContext.form?.setFieldValue(name, fieldStr)
    }

  }, [value, options])

  const checkboxProps = React.useMemo(() => {
    return omit(fieldProps, ['supplementingTheString', 'supplementingTheKey', 'supplementingTheTemplateKey', 'supplementingTheTemplateName'])
  }, [fieldProps])

  return (
    <Spin spinning={isLoading}>
      <Checkbox.Group
        {...checkboxProps}
        value={value}
        onChange={onChange}
        options={options}
      />

      {newSupplementingTheString && (
        <Form.Item hidden noStyle name={generatingSupplementingTheName(originFormItemName, 'Str')}>
          <Input />
        </Form.Item>
      )}
    </Spin>
  );
};

export default FieldCheckbox
