import React from 'react';
import { Skeleton } from 'antd'
import type { FieldProps } from '../typing'
import AuditTimeline from '../../../audit-timeline'
import { useGetEffectFetch } from '../../../../utils/hooks'

const FieldAuditTimeline: React.FC<FieldProps> = (props) => {
  const { fieldProps, effect, dependenciesValues } = props

  const { processNameMap } = fieldProps || {}

  const { data, isLoading } = useGetEffectFetch(effect?.fetch, dependenciesValues)

  if (isLoading) {
    return <Skeleton active paragraph={{ rows: 3 }} />;
  }

  return (
    <AuditTimeline
      dataInfo={data.version === 'PRO_FLOW' ? data : JSON.stringify({
        auditMap: data,
        system_status: 0
      })}
      steps={data.version === 'PRO_FLOW' ? JSON.stringify(data.steps) : '[]'}
      showAvatar={true}
      version='2.0'
      hideApprover={true}
      {...fieldProps}
      processNameMap={processNameMap}
    />
  );
};

export default FieldAuditTimeline

