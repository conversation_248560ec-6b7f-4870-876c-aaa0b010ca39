import React from 'react';
import { transformDataBasedOnRules } from '../../../../utils/transformDataBasedOnRules';
import SchemaPage from '../../../schema-page';
import type { FieldProps } from '../typing';

const FieldSchemaPage: React.FC<FieldProps> = (props) => {
  const { fieldProps, dependenciesValues = {} } = props;

  const { transforms = [], ...restFieldProps } = fieldProps || {};

  let dataSource = {
    ...dependenciesValues,
  };

  dataSource = transforms
    ? transformDataBasedOnRules(dataSource, transforms)
    : dataSource;

  return <SchemaPage {...restFieldProps} dataSource={dataSource} />;
};

export default FieldSchemaPage;
