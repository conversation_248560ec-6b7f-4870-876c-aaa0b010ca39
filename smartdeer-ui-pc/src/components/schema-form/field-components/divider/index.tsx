import React from 'react';
import { Divider } from 'antd';
import type { DividerProps } from 'antd';
import type { FieldProps } from '../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const classPrefix = `deer-form-pro-divider`

const defaultFieldProps = {
}

const FieldDivider: React.FC<FieldProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  console.log('style', props)

  return (
    <Divider
      {...restProps}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {children}
    </Divider>
  );
};

export default FieldDivider
