import React from 'react';
import { InputNumber } from 'antd';
import type { FieldProps } from '../typing'

// 兼容代码-----------
import 'antd/lib/input-number/style';
//------------

const FieldInputNumber: React.FC<FieldProps> = (props) => {

  const { value, onChange, fieldProps } = props

  return (
    <InputNumber
      {...fieldProps}
      value={value}
      style={{ width: '100%' }}
      onChange={onChange}
    />
  );
};

export default FieldInputNumber
