import React from 'react';
import { Input } from 'antd';
import type { FieldProps } from '../typing'

// 兼容代码-----------
import 'antd/lib/input/style';
import { isString } from 'lodash';
//------------

const FieldInput: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps } = props

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Input
      {...fieldProps}
      disabled={isDisabled}
      value={value}
      onChange={onChange}
    />
  );
};

export default FieldInput
