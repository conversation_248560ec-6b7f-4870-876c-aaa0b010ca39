import { Input } from 'antd';
import { isString } from 'lodash';
import React from 'react';
import { getEffectFetchConfig } from '../../../../utils/getEffectFetchConfig';
import { cachedSwrFetcher } from '../../../../utils/swrFetcher';
import type { FieldProps } from '../typing';

// 兼容代码-----------
import { useDeepCompareEffect } from 'ahooks';
import 'antd/lib/input/style';
import { ConfigContext } from '../../../../components/config-provider';
import { FetchType } from '../../../../typing';
import { evaluateLogicalExpression } from '../../../../utils/evaluateLogicalExpression';
import useParamsDict from '../../../../utils/hooks/useParamsDict';
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes';
import { transformDataBasedOnRules } from '../../../../utils/transformDataBasedOnRules';
import { SchemaFormContext } from '../../context';

//------------

const FieldInputDefaultValue: React.FC<FieldProps> = (props) => {
  const {
    value,
    onChange,
    fieldProps,
    effect = {},
    dependenciesValues,
    originFormItemName,
  } = props;

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict(dependenciesValues || {});
  const schemaFormContext = React.useContext(SchemaFormContext);

  const isDisabled = isString(fieldProps?.disabled)
    ? fieldProps?.disabled === 'true'
    : fieldProps?.disabled;

  const fetch = async () => {
    if (effect.fetch?.if) {
      const variables = { ...paramsDict, ...dependenciesValues };

      // 判断是否需要显示
      const show = evaluateLogicalExpression(variables, effect.fetch.if);

      if (!show) return;
    }
    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(
        effect.fetch as FetchType,
        configContext,
        paramsDict,
      );

      const dataPath = dataIndex ? dataIndex : '';

      const { data = {} } = await cachedSwrFetcher(api, method, params);

      let result = dataPath ? data[dataPath] : data;

      if (effect.fetch?.transform) {
        result = transformDataBasedOnRules(result, effect.fetch.transform);
      }

      const defaultValue = replaceTemplateWithPipes(
        effect.defaultValue,
        result,
      );

      schemaFormContext.form?.setFieldValue(originFormItemName, defaultValue);
    } catch (error) {
      // console.log('error', error);
    }
  };

  useDeepCompareEffect(() => {
    if (effect?.fetch) {
      fetch();
    }
  }, [dependenciesValues]);

  return (
    <Input
      {...fieldProps}
      disabled={isDisabled}
      value={value}
      onChange={onChange}
    />
  );
};

export default FieldInputDefaultValue;
