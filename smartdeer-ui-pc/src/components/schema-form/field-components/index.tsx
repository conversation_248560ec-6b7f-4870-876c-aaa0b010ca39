import React from 'react';
import { isArray } from 'lodash';
import { isDeepEqualReact, omitUndefined, omit } from '../../../utils'
import { replaceTemplateWithPipes } from '../../../utils/replaceTemplateWithPipes';
import type { FieldProps, FieldComponentProps, FieldValueType } from './typing'
import { HOCEffectValue, HOCEffectDisable } from './hoc';
import { ComponentsContext, type RenderComponentsType } from '../../../components/components-provider';
import { isFunction } from 'lodash';

// 表单组件
import FieldInput from './input'
import FieldPassword from './password'
import FieldTextArea from './text-area'
import FieldInputNumber from './input-number'
import FieldRadio from './radio'
import FieldCheckbox from './checkbox'
import FieldSelect from './select'
import FieldTreeSelect from './tree-select'
import FieldSwitch from './switch'
import FieldTimePicker from './time-picker'
import FieldTimeRangePicker from './time-range-picker'
import FieldDatePicker from './date-picker'
import FieldDateRangePicker from './date-range-picker'
import FieldCascader from './cascader'
import FieldUpload from './upload'
import FieldDraggerUpload from './dragger-upload'
import FieldJsonEditor from './json-editor-wrapper'
import FieldCodeEditor from './code-editor-wrapper'
import FieldQuill from './quill-wrapper'
import FieldTransfer from './transfer'
import FieldInputDefaultValue from './input-default-value'

// 辅助组件
import FieldAlert from './alert'
import FieldDivider from './divider'

// 自定义组件
import SelectQuillTemplate from './select-quill-template'


// 布局组件
// import FieldSpacing from './spacing'

// hro 自定义组件
import FieldHROWorkChecker from './hro-customize/work-checker'
import FieldHROWelfare from './hro-customize/welfare'
import FieldHROJobMarketing from './hro-customize/job-marketing'
import FieldHROMultiadd from './hro-customize/multiadd'
import FieldHroUploadComplianceDocuments from './hro-customize/upload-compliance-documents'


// gs 自定义组件
import FieldLeaveDateRangePicker from './gs-customize/leave/date-range-picker'
import FieldLeaveStartTime from './gs-customize/leave/start-time'
import FieldLeaveEndTime from './gs-customize/leave/end-time'
import FieldSfLeaveEndTime from './gs-customize/leave/sf-end-time'
import FieldSfLeaveStartTime from './gs-customize/leave/sf-start-time'
import FieldReallyWantTime from './gs-customize/leave/really-want-time'
import FieldHashkeyReallyWantTime from './hashkey-gs-customize/leave/really-want-time'

// sage 自定义组件
import FieldSSCFlowSelect from './sage-customize/ssc/flow-select';

// 展示组件
import FieldViewAuditTimeline from './view/audit-timeline'
import FieldSchemaPage from './view/schema-page'

// 插入组件
import FieldAppend from './effect/append';

const classPrefix = `deer-form-field`;


function formatChildNode(props: any, childNode: any) {

  let newChildNode: any = childNode

  const hasDisabled = props.effect?.disabled;

  if (hasDisabled) {
    newChildNode = <HOCEffectDisable {...props}>{childNode}</HOCEffectDisable>;
  }

  const hasEffectValue = props.effect?.value || props.effect?.getValue || isArray(props.effect?.switchValue);

  if (hasEffectValue) {
    newChildNode = <HOCEffectValue {...props}>{childNode}</HOCEffectValue>;
  }

  return newChildNode;
}

const defaultRenderComponent = (
  valueType: FieldValueType,
  props: FieldProps,
  formRenderComponents?: RenderComponentsType[],
) => {

  // 自定义组件
  if (formRenderComponents?.length) {
    const component = formRenderComponents.find(
      (item) => item.type === valueType,
    )?.render;

    if (component) {
      return isFunction(component)
        ? component({ ...props })
        : React.cloneElement(component as React.ReactElement, {
          ...props,
        });
    }
  }

  let childNode: React.ReactNode = null;

  switch (valueType) {
    case 'input':
      childNode = <FieldInput {...props} />;
      break;
    case 'password':
      childNode = <FieldPassword {...props} />;
      break;

    case 'textArea':
      childNode = <FieldTextArea {...props} />;
      break;

    case 'inputNumber':
      childNode = <FieldInputNumber {...props} />;
      break;

    case 'radio':
      childNode = <FieldRadio {...props} />;
      break;

    case 'checkbox':
      childNode = <FieldCheckbox {...props} />;
      break;

    case 'select':
      childNode = <FieldSelect {...props} />;
      break;

    case 'treeSelect':
      childNode = <FieldTreeSelect {...props} />;
      break;

    case 'switch':
      childNode = <FieldSwitch {...props} />;
      break;

    case 'timePicker':
      childNode = <FieldTimePicker {...props} />;
      break;

    case 'timeRangePicker':
      childNode = <FieldTimeRangePicker {...props} />;
      break;

    case 'datePicker':
      childNode = <FieldDatePicker {...props} />;
      break;

    case 'dateRangePicker':
      childNode = <FieldDateRangePicker {...props} />;
      break;

    case 'cascader':
      childNode = <FieldCascader {...props} />;
      break;

    case 'transfer':
      childNode = <FieldTransfer {...props} />;
      break;

    case 'upload':
      childNode = <FieldUpload {...props} />;
      break;

    case 'draggerUpload':
      childNode = <FieldDraggerUpload {...props} />;
      break;

    case 'alert':
      childNode = <FieldAlert {...props} />;
      break;

    case 'divider':
      childNode = <FieldDivider {...props} />;
      break;

    case 'jsonEditor':
      childNode = <FieldJsonEditor {...props} />;
      break;

    case 'codeEditor':
      childNode = <FieldCodeEditor {...props} />;
      break;

    case 'quill':
      childNode = <FieldQuill {...props} />;
      break;

    case 'selectQuillTemplate':
      childNode = <SelectQuillTemplate {...props} />;
      break;

    case 'view.auditTimeline':
      childNode = <FieldViewAuditTimeline {...props} />
      break;

    case 'hro.work.checker':
      childNode = <FieldHROWorkChecker {...props} />;
      break;

    case 'hro.welfare':
      childNode = <FieldHROWelfare {...props} />;
      break;

    case 'eor.job.marketing':
      childNode = <FieldHROJobMarketing {...props} />;
      break;

    case 'hro.multiadd':
      childNode = <FieldHROMultiadd {...props} />;
      break;

    case 'hro.uploadComplianceDocuments':
      childNode = <FieldHroUploadComplianceDocuments {...props} />;
      break;

    case 'gs.leave.dateRangePicker':
      childNode = <FieldLeaveDateRangePicker {...props} />;
      break;

    case 'gs.leave.startTime':
      childNode = <FieldLeaveStartTime {...props} />;
      break;

    case 'gs.leave.endTime':
      childNode = <FieldLeaveEndTime {...props} />;
      break;

    case 'gs.leave.sf.startTime':
      childNode = <FieldSfLeaveStartTime {...props} />;
      break;

    case 'gs.leave.sf.endTime':
      childNode = <FieldSfLeaveEndTime {...props} />;
      break;

    case 'gs.leave.reallyWantTime':
      childNode = <FieldReallyWantTime {...props} />;
      break;

    case 'hashkey.gs.leave.reallyWantTime':
      childNode = <FieldHashkeyReallyWantTime {...props} />;
      break;

    case 'effect.append':
      childNode = <FieldAppend {...props} />;
      break;

    case 'sage.ssc.flow.select':
      childNode = <FieldSSCFlowSelect {...props} />;
      break;

    case 'schemaPage':
      childNode = <FieldSchemaPage {...props} />;
      break;

    case 'inputDefaultValue':
      childNode = <FieldInputDefaultValue {...props} />;
      break;

    // case 'group':
    //   return props.columns?.map((item: any) => formatChildNode(props, item))

    default:
      childNode = <div className={`bg-red-500 text-white rounded px-2 py-1 mb-[20px]`}>没有实现的组件: {valueType}</div>;
  }

  return <>{formatChildNode(props, childNode)}</>;
};

const FieldComponent: React.FC<FieldComponentProps> = (props) => {
  const {
    valueType,
    fieldProps,
    dependenciesValues,
    ...extra
  } = props

  const { formRenderComponents } = React.useContext(ComponentsContext);

  const newFiledProps: any = fieldProps && omit(fieldProps, ['className', 'style']);


  if (fieldProps?.addonBefore && dependenciesValues) {
    newFiledProps.addonBefore = replaceTemplateWithPipes(fieldProps.addonBefore, dependenciesValues)
  }

  if (fieldProps?.addonAfter && dependenciesValues) {
    newFiledProps.addonAfter = replaceTemplateWithPipes(fieldProps.addonAfter, dependenciesValues)
  }

  const renderedDom = defaultRenderComponent(
    valueType,
    omitUndefined({
      fieldProps: newFiledProps,
      dependenciesValues,
      ...extra,
      fieldValueType: valueType
    }),
    formRenderComponents
  )

  return (
    <div className={`${classPrefix} ${fieldProps?.className}`} style={{ ...fieldProps?.style }}>
      {renderedDom}
    </div>
  )
}

export default React.memo(FieldComponent, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
