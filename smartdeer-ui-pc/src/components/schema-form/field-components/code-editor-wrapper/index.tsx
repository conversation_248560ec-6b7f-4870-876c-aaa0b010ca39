import React from 'react';
import type { FieldProps } from '../typing'
import CodeEditor from '../../../code-editor'
import './index.less'

const classPrefix = `deer-field-json-editor`

const FieldCodeEditor: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps } = props

  const handleChange = (value: string) => {
    onChange?.(value)
  }

  return (
    <CodeEditor
      className={classPrefix}
      {...fieldProps}
      value={value}
      onChange={handleChange}
    />
  )
};

export default FieldCodeEditor
