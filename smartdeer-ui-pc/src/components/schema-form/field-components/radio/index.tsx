import React from 'react';
import { Form, Radio, Input, Spin } from 'antd';
import type { RadioChangeEvent } from 'antd';
import type { FieldProps } from '../typing'
import { SchemaFormContext } from '../../context'
import { usePropsOptions } from '../../../../utils/hooks'
import { generatingSupplementingTheName } from '../utils'
import omit from '../../../../utils/omit'

// 兼容代码-----------
import 'antd/lib/radio/style';
//------------

const FieldRadio: React.FC<FieldProps> = (props) => {

  const { value, onChange, fieldProps = {}, field, originFormItemName } = props

  const schemaFormContext = React.useContext(SchemaFormContext)

  const { isLoading, options } = usePropsOptions(props)

  const newSupplementingTheString = fieldProps.supplementingTheString ?? schemaFormContext.supplementingTheString


  const handleChange = (e: RadioChangeEvent) => {
    onChange?.(e)
  }

  React.useEffect(() => {
    if (!newSupplementingTheString) return

    const checked = options.find(option => option.value === value)
    const name = generatingSupplementingTheName(originFormItemName, 'Str')

    if (!checked) {
      schemaFormContext.form?.setFieldValue(name, '')
      return
    }
    schemaFormContext.form?.setFieldValue(name, checked.label)
  }, [value, options])

  const radioProps = React.useMemo(() => {
    return omit(fieldProps, ['supplementingTheString', 'supplementingTheKey', 'supplementingTheTemplateKey', 'supplementingTheTemplateName'])
  }, [fieldProps])

  return (
    <Spin spinning={isLoading}>
      <Radio.Group
        {...radioProps}
        value={value}
        options={options}
        onChange={handleChange}
      />

      {newSupplementingTheString && (
        <Form.Item hidden noStyle name={generatingSupplementingTheName(originFormItemName, 'Str')}>
          <Input />
        </Form.Item>
      )}
    </Spin>
  );
};

export default FieldRadio
