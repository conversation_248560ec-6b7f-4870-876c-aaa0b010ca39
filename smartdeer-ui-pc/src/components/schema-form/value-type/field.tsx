import React from 'react';
import FormItem from '../form-item'
import <PERSON><PERSON>ield from '../form-field'
import FormDependencies from '../form-dependencies'
import type { ItemType } from '../typing';
import { omitUndefined } from '../../../utils'

const field = (
  item: ItemType,
  helpers: Record<string, any>,
) => {

  const labelProps = omitUndefined({
    ...item?.labelProps,
    rules: item?.labelProps?.rules?.map(item => {
      return omitUndefined({
        ...item,
        pattern: item.pattern ? new RegExp(item.pattern) : undefined
      })
    })
  })

  const formFieldProps = {
    ...item,
    labelProps
  }

  const getField = (values = {}) => {
    return (
      <FormField
        key={[item.field, helpers.index || 0].join('_1')}
        {...formFieldProps}
        dependenciesValues={values}
        helpers={helpers}
      >
        <FormItem
          {...formFieldProps}
          colProps={helpers.colProps}
          dependenciesValues={values}
          initialValues={helpers.initialValues}
        />
      </FormField>
    );
  }

  if (item.dependencies && Array.isArray(item.dependencies) && item.dependencies.length > 0) {
    return (
      <FormDependencies
        names={item.dependencies}
        key={[item.field, helpers.index || 0].join('-')}
      >
        {getField}
      </FormDependencies>
    )
  }

  return getField();
};

export default field
