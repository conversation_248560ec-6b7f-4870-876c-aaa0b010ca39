import React from 'react';
import ProFormList from '../form-list';
import type { SchemaRenderValueTypeFunction } from '../typing';

const formList: SchemaRenderValueTypeFunction = (
  item,
  { genItems, colProps, index }
) => {
  if (item.type === 'formList') {
    if (!item.childrenColumns || !Array.isArray(item.childrenColumns)) return null;
    return (
      <ProFormList
        key={[item.field, index || 0].join('_')}
        colProps={colProps}
        {...item}
      >
        {genItems(item.childrenColumns)}
      </ProFormList>
    );
  }

  return true;
};

export default formList;
