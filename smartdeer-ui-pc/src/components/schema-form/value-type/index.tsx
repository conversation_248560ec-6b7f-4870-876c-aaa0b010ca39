import React from 'react';
import type { ItemType, SchemaRenderValueTypeFunction } from '../typing';
import field from './field';
import formList from './form-list';

const tasks: SchemaRenderValueTypeFunction[] = [
  formList,
];

const renderValueType = (
  item: ItemType,
  helpers: Record<string, any>
) => {

  for (let cur = 0; cur < tasks.length; cur++) {
    const task = tasks[cur];
    const dom = task(item, helpers);

    // False 不再遍历
    // if (dom === false) {
    //   return false;
    if (dom === true) {
      // True 继续下一次
      continue;
    } else {
      // Other Is Dom
      return dom;
    }
  }

  return field(item, helpers);
};

export default renderValueType
