import React from 'react';
import classNames from 'classnames'
import { Col, Form } from 'antd';
// import type { Rule } from 'antd';
import type { ItemType } from '../typing'
import { mergeProps } from '../../../utils/withDefaultProps'
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'
import { omit } from '../../../utils'
import { omitUndefined, isDeepEqualReact } from '../../../utils'
import FieldComponent from '../field-components'
import { evaluateCondition } from '../../../utils/evaluateCondition';
import { SchemaFormContext } from '../context'
import { get, isArray, isUndefined } from 'lodash'
import { useDeepCompareMemo, useParamsDict } from '../../../utils/hooks'
import { isEmpty } from 'lodash';
import { FormListContext } from '../form-list';
import InnerHtml from '../../inner-html'
import { replaceVariablesInTemplate } from '../../../utils/replaceVariablesInTemplate'
import { isSpecialValue } from '../../../utils/isSpecialValue'
import { replaceTemplateWithPipes } from '../../../utils/replaceTemplateWithPipes';

const classPrefix = `deer-form-item`

const formComponents = [
  'input',
  'password',
  'textArea',
  'inputNumber',
  'radio',
  'checkbox',
  'select',
  'switch',
  'timePicker',
  'timeRangePicker',
  'datePicker',
  'dateRangePicker',
  'cascader',
  'upload',
  'hro.multiadd',
]

export type FormItemProps = ItemType & {
  dependenciesValues?: Record<string, any>,
  initialValues?: Record<string, any>,
}

const defaultProps = {
  labelProps: {},
  dependenciesValues: {}
}

const normFile = (e: any) => {
  if (isArray(e)) {
    return e;
  }
  return e?.fileList;
};

const FormItem: React.FC<FormItemProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    field,
    title,
    labelProps,
    type,
    dependenciesValues,
    childrenColumns,
    initialValues,
    colProps,
    ...extra
  } = props

  const formListContext = React.useContext(FormListContext);

  const { formVariables } = React.useContext(SchemaFormContext);

  const paramsDict = useParamsDict()

  // 副作用
  const effect = extra?.effect

  const name = React.useMemo(() => {
    if (field === undefined) return field;
    if (formListContext.name !== undefined) {
      return [formListContext.name, field].flat(1) as string[];
    }
    return field as string;
  }, [formListContext.name, field]);

  const descriptionStr = useDeepCompareMemo(() => {
    const labelDescription = extra.effect?.labelDescription
    let str = labelProps?.description

    if (isArray(labelDescription)) {
      const current = labelDescription.find(item => {
        return evaluateLogicalExpression(formVariables, item.case)
      })

      if (current) {
        str = current.value
      }
    }

    return str
  }, [formVariables])

  const rules = useDeepCompareMemo(() => {
    let newRules: any[] = labelProps?.rules || []

    if (!!effect?.required && (!isEmpty(dependenciesValues) || !isEmpty(formVariables))) {
      const variables = { ...formVariables, ...dependenciesValues }

      const required = evaluateLogicalExpression(variables, effect?.required)

      if (newRules.length === 0) {
        newRules.push({
          required: required
        })
      } else {
        newRules = newRules.map(item => {
          return {
            ...item,
            required: required
          }
        })
      }
    }

    return newRules
  }, [effect?.required, dependenciesValues, formVariables])

  const itemTitle = React.useMemo(() => {
    if (!title) return <div style={{ opacity: 0 }}>null</div>;

    return (
      <div>
        <div className={`${classPrefix}-title`}>{title}</div>
        {!!descriptionStr && (
          <div className={`${classPrefix}-description`}>
            {descriptionStr.includes('div') ? <InnerHtml content={descriptionStr} /> : descriptionStr}
          </div>
        )}
      </div>
    )

  }, [title, descriptionStr])

  const originFormItemName = React.useMemo(() => {
    if (formListContext.listName) {
      return [formListContext.listName, field].flat(1) as string[]
    }

    return field as string
  }, [field, formListContext.listName])

  const omitKeys = ['className', 'style', 'rules', 'dependencies', 'hideTitle']

  // 判断 initialValues 和  labelProps.initialValue 是否同时存在 Form.Item 的默认值。
  const isOmitInitialValue = initialValues && labelProps.initialValue && !isUndefined(get(initialValues, field || 'xxxxxxxx'))

  if (isOmitInitialValue) {
    omitKeys.push('initialValue')
  } if (labelProps.initialValue && isSpecialValue(labelProps.initialValue)) {
    labelProps.initialValue = replaceVariablesInTemplate({
      ...paramsDict,
      ...formVariables,
    }, labelProps.initialValue)
  }

  const formItemProps: any = omit(labelProps, omitKeys)

  if (formItemProps.extra && (!isEmpty(dependenciesValues) || !isEmpty(formVariables) || !isEmpty(paramsDict))) {
    const variables = { ...paramsDict, ...formVariables, ...dependenciesValues }
    formItemProps.extra = replaceTemplateWithPipes(formItemProps.extra, variables)
  }

  if (type === 'draggerUpload') {
    Object.assign(formItemProps, {
      getValueFromEvent: normFile
    })
  }

  const dependencies = labelProps.dependencies || []

  return (
    <Col span={colProps?.span}>
      <div
        className={classNames(classPrefix, labelProps?.className, {
          [`${classPrefix}-hide-required`]: !title,
          [`${classPrefix}-hide-title`]: labelProps?.hideTitle
        })}
        style={{ ...labelProps?.style }}
      >
        <Form.Item
          {...formItemProps}
          name={name}
          label={itemTitle}
          dependencies={[]}
          preserve={false}
          rules={rules.map(item => {
            if (item.type === 'customValidator') {
              return ({ getFieldValue }) => ({
                validator(_, value) {
                  const startTime = getFieldValue(item.fieldToCompare)

                  const check = evaluateCondition(item.condition!, value, startTime)

                  if (!value || !startTime || check) {
                    return Promise.resolve();
                  }

                  return Promise.reject(new Error(item.message));
                },
              })
            }

            return omitUndefined({
              required: item.required,
              message: item.message,
              pattern: item?.pattern
            }) as any
          })}
        >
          <FieldComponent
            index={formListContext.name}
            formItemTitle={title}
            formItemName={name || field!}
            originFormItemName={originFormItemName}
            field={field as any}
            valueType={type}
            fieldProps={extra.props}
            effect={extra.effect}
            fieldModal={extra?.fieldModal}
            fieldModalSteps={extra?.fieldModalSteps}
            columns={extra?.columns}
            childrenColumns={childrenColumns}
            dependenciesValues={dependenciesValues}
          />
        </Form.Item>
      </div>
    </Col>
  )
};

export default React.memo(FormItem, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
