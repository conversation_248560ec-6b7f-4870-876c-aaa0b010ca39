import { message, Skeleton } from 'antd';
import { isArray, isString } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { ConfigContext } from '../../../components/config-provider';
import { getEffectFetchConfig } from '../../../utils/getEffectFetchConfig';
import { useParamsDict } from '../../../utils/hooks';
import { replaceKeysWithRegex } from '../../../utils/replaceKeysWithRegex';
import swrFetcher from '../../../utils/swrFetcher';
import { transformDataBasedOnRules } from '../../../utils/transformDataBasedOnRules';
import { replaceTemplateWithPipes } from '../../../utils/replaceTemplateWithPipes';
import { SchemaPageContext } from '../context';
import { PageColumnsType } from '../typing';
import type { SchemaPageEffectContextType } from './context';
import { SchemaPageEffectContext } from './context';
import { useDebounceEffect } from 'ahooks'
import { devLog } from '../../../utils/log'

const classPrefix = `deer-field-effect`;

export interface EffectProps extends PageColumnsType {
  genItems: (items: PageColumnsType[]) => React.JSX.Element[];
}

export { SchemaPageEffectContext, type SchemaPageEffectContextType };

type RecordType = Record<string, any>;

const Effect: React.FC<EffectProps> = (params) => {
  const { effect, props, children, genItems } = params;
  const { variables = {} } = props || {};

  const [data, setData] = useState<any>(null);

  const [isLoading, setIsLoading] = useState(false);

  const paramsDict = useParamsDict();
  const configContext = React.useContext(ConfigContext);
  const { dataSource, pageVariables } = React.useContext(SchemaPageContext);

  const effectFetch = effect?.fetch;

  const replaceName = React.useMemo(() => {
    const newName = props?.replaceName;
    if (!newName) {
      return ['props'];
    }
    if (isArray(newName)) {
      return newName;
    }
    if (isString(newName)) {
      return newName.split(',');
    }
    return newName;
  }, [props?.replaceName]);

  const { transform } = effectFetch || {};

  const fetch = async (fetchParams = {}, needLoading = true) => {
    setIsLoading(needLoading);

    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(
        effectFetch!,
        configContext,
        { ...paramsDict, ...pageVariables, ...dataSource, ...variables },
      );

      const { data } = await swrFetcher(api, method, {
        ...params,
        params: {
          ...(params?.params || {}),
          ...fetchParams,
        },
      });

      let newData = dataIndex ? data[dataIndex] : data;

      if (transform) {
        newData = transformDataBasedOnRules(newData, transform);
      }

      devLog('SchemaPage Effect', params?.functionKey || params.processKey)
      devLog('SchemaPage Effect', newData)

      setData(newData);
    } catch (err: any) {
      // console.log('err', err);
      // message.error(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useDebounceEffect(() => {
    if (!effectFetch) return;
    fetch();
  }, [dataSource]);

  const handleRetry = (params = {}, loading = true) => {
    fetch(params, loading);
  };

  const columns = useMemo(() => {
    if (!data || !isArray(children)) return null;

    const newChildren: PageColumnsType[] = isArray(children)
      ? children
      : [children];

    let result = newChildren.map((child) => {
      const newChild = JSON.parse(JSON.stringify(child));

      replaceName.forEach((replaceKey: string) => {
        if (isString(newChild[replaceKey])) {
          newChild[replaceKey] = replaceTemplateWithPipes(newChild[replaceKey], { ...paramsDict, ...data })
        } else {
          newChild[replaceKey] = replaceKeysWithRegex(
            newChild[replaceKey] as RecordType | RecordType[],
            { ...paramsDict, ...data },
          ) as any;
        }
      });

      return {
        ...newChild,
      };
    });

    return result;
  }, [data, paramsDict]);

  if (isLoading)
    return (
      <div style={{ marginTop: '20px' }}>
        <Skeleton active paragraph={{ rows: 3 }} />
      </div>
    );

  return (
    <SchemaPageEffectContext.Provider
      value={{
        onEffectRetry: (retryParams?: RecordType, loading?: boolean) =>
          handleRetry(retryParams, loading),
        effectDataSource: dataSource,
      }}
    >
      {columns && <>{genItems(columns)}</>}
    </SchemaPageEffectContext.Provider>
  );
};

export default Effect;
