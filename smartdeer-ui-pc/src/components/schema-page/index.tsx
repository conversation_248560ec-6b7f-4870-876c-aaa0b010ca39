import React from "react"
import classNames from "classnames"
import { Empty } from 'antd'
import { mergeProps } from '../../utils/withDefaultProps'
import renderValueType from './value-type'
import { omitUndefined } from '../../utils/omitUndefined'
import type { PageColumnsType } from './typing'
import { replaceKeysWithRegex } from '../../utils/replaceKeysWithRegex'
import { SchemaPageContext } from './context'
import type { SchemaPageContextType } from './context'
import { isArray, isEmpty, isString } from "lodash"
import { useDeepCompareMemo } from '../../utils/hooks'
import { useParamsDict } from '../../utils/hooks';

import './index.less'

const classPrefix = `deer-schema-page`

export {
  SchemaPageContext,
  type SchemaPageContextType,
};

export type SchemaPageProps = {
  className?: string;
  style?: React.CSSProperties;

  variables?: Record<string, any>;

  columnsJson?: string;
  columns?: PageColumnsType[];
  dataSource?: Record<string, any>;
  forceUpdate?: boolean;
  onRetry?: (params?: Record<string, any>) => void

  mock?: SchemaPageContextType['mock']
}

const defaultProps = {
  variables: {},
  dataSource: {},
  columns: [],
  forceUpdate: false,
}

const SchemaPage: React.FC<SchemaPageProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,

    variables,
    columns: propColumns,
    columnsJson,
    dataSource,
    onRetry,
    mock,
    ...restProps
  } = props

  const [showEmpty, setShowEmpty] = React.useState<boolean>(false)
  const [columns, setColumns] = React.useState<PageColumnsType[]>([])
  const [update, setUpdate] = React.useState(false);

  const paramsDict = useParamsDict();

  const handleRetry = (forceUpdate?: boolean) => {
    if (forceUpdate || restProps.forceUpdate) {
      setUpdate(true)
      setTimeout(() => {
        setUpdate(false)
      }, 10)
    }
    onRetry?.()
  }

  const handleReplaceKeysWithRegex = (list: any[]) => {
    if (list.length === 0) {
      setShowEmpty(true)
      return
    }

    if (isEmpty(dataSource)) {
      setColumns(list);
      return;
    }

    const columns = replaceKeysWithRegex(list, { ...paramsDict, ...dataSource }) as any[]

    setColumns(columns)
  }

  React.useEffect(() => {
    if (isArray(propColumns) && propColumns.length > 0) {
      handleReplaceKeysWithRegex(propColumns)
    } else if (isString(columnsJson)) {

      try {
        handleReplaceKeysWithRegex(JSON.parse(columnsJson))
      } catch (err) {
        console.error(`[smartdeer-ui: SchemaPage] columnsJson 解析错误！`);
      }
    }

  }, [propColumns, columnsJson, dataSource])

  /**
   * 生成子项
   *
   * @param items
   */
  const genItems = (items: PageColumnsType[]) => {
    return items.filter(item => {
      return !item.hidden
    }).map((originItem, index) => {
      const item = omitUndefined({
        scope: originItem.scope,
        type: originItem.type,
        field: originItem.field,
        title: originItem.title,
        hidden: originItem.hidden,
        props: originItem.props,
        effect: originItem.effect,
        children: originItem.children
      })

      return (
        renderValueType(item, {
          originItem,
          index,
          genItems,
          dataSource
        })
      );
    }).filter((field) => {
      return Boolean(field);
    });
  };

  const childNode = useDeepCompareMemo(() => {
    if (columns.length === 0) return null
    return genItems(columns);
  }, [columns]);

  if (showEmpty) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <SchemaPageContext.Provider value={{
        onRetry: handleRetry,
        pageVariables: variables,
        dataSource,
        mock: mock
      }}>
        {!update && (
          <>{childNode}</>
        )}
      </SchemaPageContext.Provider>
    </div>
  )
}

export default SchemaPage
