import React from 'react';
import type { FetchType, OnFail, OnSuccessType } from '../../typing';

export type EffectType = {
  show?: string;
  fetch?: FetchType;
  onSuccess?: OnSuccessType;
  onFail?: OnFail;
};

export type PageColumnsType = {
  // 作用域
  scope?: true;

  // 组件类型
  type: string;

  // 唯一标识
  field?: string | string[];

  // 标签的文本
  title?: string | React.ReactElement;

  // 显示隐藏
  hidden?: boolean | 'true';

  // 组件的 props
  props?: {
    style?: React.CSSProperties;
    bodyStyle?: React.CSSProperties;
    className?: string;
    displayIfEmpty?: boolean;
    displayIfEmptySymbol?: string;
    [key: string]: any;
  };

  // 副作用
  effect?: EffectType;

  // 孩子
  children?: string | PageColumnsType | PageColumnsType[];

  // fieldModalSteps?: FieldModalType[];
  // fieldModal?: FieldModalType;

  // 依赖项 field
  // dependencies?: string[];
};

export type FieldProps = {
  className?: string;
  style?: React.CSSProperties;

  [key: string]: any;
};

export type FieldComponentProps = {
  valueType: string;
  fieldProps?: FieldProps;

  renderChildren?: React.ReactNode;
  effect?: EffectType;
};

export type ColumnType = {
  type: string;
  field: string;
  children?: string | ColumnType | ColumnType[];
  props?: Record<string, any>;
  effect?: EffectType;
  hidden?: boolean;
};

export type SchemaRenderValueTypeFunction = (
  item: PageColumnsType,
  helpers: Record<string, any>,
) => React.ReactNode;
