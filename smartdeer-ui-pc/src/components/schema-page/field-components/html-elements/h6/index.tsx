import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-h6`

const FieldH6: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,

    children,
  } = props

  return (
    <h6 className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </h6>
  );
};

export default FieldH6
