import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-h3`

const FieldH3: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,

    children,
  } = props

  return (
    <h3 className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </h3>
  );
};

export default FieldH3
