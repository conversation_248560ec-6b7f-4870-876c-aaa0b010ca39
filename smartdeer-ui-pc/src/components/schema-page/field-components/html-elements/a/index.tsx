import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'
import { useParamsDict } from '../../../../../utils/hooks'
import { ConfigContext } from '../../../../config-provider'
import { SchemaPageContext } from '../../../context'

import { replaceVariablesInTemplate } from '../../../../../utils/replaceVariablesInTemplate'
import { message } from 'antd';
import copyToClipboard from 'copy-to-clipboard';

const classPrefix = `deer-field-a`

const FieldA: React.FC<FieldHtmlElementProps & {
  copy?: boolean | string,
  name?: string,
}> = (props) => {
  const {
    className,
    style,
    url,
    children,
    copy = false,
    name = '',
  } = props

  const { router } = React.useContext(ConfigContext)
  const { dataSource } = React.useContext(SchemaPageContext)
  const paramsDict = useParamsDict(dataSource)

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!url) {
      console.warn('url is not defined')
      return
    }

    if (copy === true || copy === 'true') {
      try {
        copyToClipboard(window.location.host + url);
        message.success('拷贝成功');
      } catch (e) {
        console.error('拷贝失败：', e);
      }
      return;
    }

    if (!router) {
      console.error('router is not defined')
    }

    const path = replaceVariablesInTemplate(paramsDict, url)

    router?.push(path)
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <a onClick={handleClick}>
        {name}{children}
      </a>
    </div>
  );
};

export default FieldA
