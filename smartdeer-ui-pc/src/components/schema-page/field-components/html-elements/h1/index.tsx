import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-h1`

const FieldH1: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,

    children,
  } = props

  return (
    <h1 className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </h1>
  );
};

export default FieldH1
