import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-h5`

const FieldH5: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,

    children,
  } = props

  return (
    <h5 className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </h5>
  );
};

export default FieldH5
