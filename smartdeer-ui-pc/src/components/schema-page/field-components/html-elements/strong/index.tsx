import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-strong`

const FieldStrong: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,

    children,
  } = props

  return (
    <strong className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </strong>
  );
};

export default FieldStrong
