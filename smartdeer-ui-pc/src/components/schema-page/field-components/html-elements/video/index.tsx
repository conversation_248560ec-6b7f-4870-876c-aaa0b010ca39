import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'
import { isArray } from 'lodash';

const classPrefix = `deer-field-video`

const FieldVideo: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,
    url,
  } = props

  const list: any = React.useMemo(() => {
    if (!url) return []

    try {
      return JSON.parse(url)
    } catch (err) {
      return isArray(url) ? url : [url]
    }
  }, [url])

  return (
    <>
      {list.map((item: any, index: number) => {
        let src = item

        if (item.url) {
          src = item.url
        }

        return (
          <video
            key={index}
            src={src}
            controls
            className={classNames(classPrefix, className)}
            style={{ ...style }}
          >
          </video>
        )
      })}
    </>
  );
};

export default FieldVideo
