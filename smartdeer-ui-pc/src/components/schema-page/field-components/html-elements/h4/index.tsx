import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-h4`

const FieldH4: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,

    children,
  } = props

  return (
    <h4 className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </h4>
  );
};

export default FieldH4
