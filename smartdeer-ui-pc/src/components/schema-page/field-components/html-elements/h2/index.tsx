import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-h2`

const FieldH2: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,

    children,
  } = props

  return (
    <h2 className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </h2>
  );
};

export default FieldH2
