import classNames from 'classnames';
import React from 'react';
import type { FieldHtmlElementProps } from '../typing';

const classPrefix = `deer-field-image`;

const FieldImage: React.FC<FieldHtmlElementProps> = (props) => {
  const { className, style, children, ...restProps } = props;

  return (
    <img
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restProps}
    >
      {children}
    </img>
  );
};

export default FieldImage;
