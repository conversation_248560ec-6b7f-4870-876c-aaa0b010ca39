import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-div`

const FieldDiv: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,
    children
  } = props

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </div>
  );
};

export default FieldDiv
