import React from 'react';
import classNames from 'classnames';
import type { FieldHtmlElementProps } from '../typing'

const classPrefix = `deer-field-span`

const FieldSpan: React.FC<FieldHtmlElementProps> = (props) => {
  const {
    className,
    style,

    children,
  } = props

  return (
    <span className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </span>
  );
};

export default FieldSpan
