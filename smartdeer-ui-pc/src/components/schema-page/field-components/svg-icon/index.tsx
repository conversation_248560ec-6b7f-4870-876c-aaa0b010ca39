import React from 'react';
import * as Icons from '@ant-design/icons';

export type IconNames = keyof typeof Icons;

export interface SvgIconProps {
  style?: React.CSSProperties;
  name: IconNames;
}

/**
 * ANTD ICON SVG组件
 * @param style
 * @param name
 * @constructor
 */
const SvgIcon: React.FC<SvgIconProps> = ({ style, name }) => {
  const IconComponent:any = Icons[name];

  if (!IconComponent) {
    return <span style={{color: 'red'}}>{'Icon with name "${name}" does not exist in @ant-design/icons.'}</span>;
  }
  return <IconComponent style={{width: '24px', height: '24px', ...style}} />;
};

export default SvgIcon;
