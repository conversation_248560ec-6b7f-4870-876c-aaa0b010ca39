import React, { FC, useMemo } from "react";
import classNames from 'classnames';
import pipe from "../../../../../utils/pipe";
import './index.less'

export interface ReimburseDetailProps {
  style?: React.CSSProperties;
  className?: string;
  children?: React.ReactNode;
  list?: string
  columns?: { label: string, key: string }[]
}

const classPrefix = `deer-reimburse-detail`

const ReimburseDetail: FC<ReimburseDetailProps> = (props) => {
  const {
    className,
    style,
  } = props;
  const { columns, listArr } = useMemo(() => {
    let list = []
    try {
      list = props.list ? JSON.parse(props.list) : []
    } catch (error) {
    }
    return {
      columns: props.columns || [],
      listArr: list
    }
  }, [props])

  const getValue = (item: any, key: string) => {
    const hasPipe = key.includes('|');
    if (!hasPipe) {
      return item[key]
    }
    const [rawKey, ...pipeFuncsStr] = key.split('|');
    const value = item[rawKey.trim()];
    return pipe.render(
      String(value),
      pipeFuncsStr.map((item: any) => item.trim()),
      item,
    );
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {listArr.map((listItem: any, index: number) => {
        return <div key={index} className="deer-reimburse-detail-con">
          {
            columns.map((column: any, columnIndex: number) => {
              return <div key={columnIndex} className="deer-reimburse-detail-item">
                <div className="deer-reimburse-detail-item-title">{column.label}</div>
                <div className="deer-reimburse-detail-item-description">{
                  getValue(listItem, column.key)
                }</div>
              </div>
            })
          }
        </div>
      })}
    </div>
  );
}

export default ReimburseDetail
