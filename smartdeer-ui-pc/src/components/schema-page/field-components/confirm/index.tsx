import { Icon } from '@smartdeer-ui/pc';
import { <PERSON><PERSON>, <PERSON><PERSON>, message } from 'antd';
import classNames from 'classnames';
import React from 'react';
import { ConfigContext } from '../../../config-provider';
import SchemaForm from '../../../schema-form';
import type { SchemaFormProps, SchemaFormRef } from '../../../schema-form';
import { transformDataBasedOnRules } from '../../../../utils/transformDataBasedOnRules';
import SchemaPage, { SchemaPageProps } from '../..';
import type { TransformDataRules } from '../../../../typing';
import { delay } from '../../../../utils';

const classPrefix = `deer-field-confirm`;

interface FieldConfirmProps {
  className?: string;
  style?: React.CSSProperties;
  children?: SchemaFormProps['columns'];
  content?: SchemaPageProps['columns'];

  delayTime?: string;

  type?: 'primary' | 'dashed' | 'link' | 'text' | 'default';
  size?: 'large' | 'middle' | 'small';
  title?: string;
  description?: string;
  danger?: boolean;
  iconType?: string;
  destroyOnClose?: boolean;
  modalWidth?: string;
  transformFinishValues?: { from: string; to: string; format?: string }[];
  transformInitialValues?: TransformDataRules;

  onEffect?: (values: Record<string, any>) => Promise<void>;

  //  初始化参数
  formInitialValues?: string;
  gridProps?: any
}

const FieldConfirm: React.FC<FieldConfirmProps> = (props) => {
  const {
    className,
    style,

    delayTime,

    type = 'primary',
    size,
    title,
    description,
    children,
    danger,
    iconType,
    destroyOnClose = false,
    content,
    transformFinishValues,
    modalWidth = '416px',
    onEffect,
    formInitialValues,
    transformInitialValues,
    gridProps,
  } = props;

  const configContext = React.useContext(ConfigContext);

  const okText = configContext.language.modal.okText;
  const cancelText = configContext.language.modal.cancelText;

  const formRef = React.useRef<SchemaFormRef>(null)

  const [isModalOpen, setIsModalOpen] = React.useState(false);

  const [buttonLoading, setButtonLoading] = React.useState(false);

  const handleClick = () => {
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    let values = {}
    if (formRef.current) {
      await formRef.current?.validateFields()

      values = formRef.current.getFieldsValue()
      values = transformFinishValues
        ? transformDataBasedOnRules(values, transformFinishValues)
        : values
    }

    setButtonLoading(true);
    try {

      if (delayTime) {
        await delay(Number(delayTime));
      }

      await onEffect?.(values);
    } catch (err: any) {
      message.error(err.message);
    }
    setButtonLoading(false);

    formRef.current?.resetFields()
    setIsModalOpen(false)
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const buttonIcon = React.useMemo(() => {
    if (!iconType) return null;

    let Component = Icon[iconType as keyof typeof Icon];

    return Component ? (
      <Component style={{ display: 'inline-block', verticalAlign: 'middle' }} />
    ) : null;
  }, [iconType]);

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <Button
        type={type}
        size={size}
        danger={danger}
        loading={buttonLoading}
        onClick={handleClick}
        icon={buttonIcon}
      >
        {title}
      </Button>

      <Modal
        title={title}
        open={isModalOpen}
        okText={okText}
        cancelText={cancelText}
        onOk={handleOk}
        onCancel={handleCancel}
        width={modalWidth}
        className={`${classPrefix}-modal`}
        confirmLoading={buttonLoading}
        destroyOnClose={destroyOnClose}
      >
        {description}

        {!!content && <SchemaPage columns={content} />}

        {!!children && (
          <div style={{ marginTop: '20px' }}>
            <SchemaForm
              ref={formRef}
              columns={children}
              size='large'
              layout='vertical'
              labelCol={{ span: 24 }}
              gridProps={gridProps || {
                col: 1
              }}
              footer={() => null}
              initialValues={formInitialValues ? transformDataBasedOnRules(JSON.parse(formInitialValues), transformInitialValues || []) : {}}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FieldConfirm;
