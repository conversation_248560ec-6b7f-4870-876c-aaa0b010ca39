import React from 'react'
import classNames from 'classnames'
import { Skeleton } from 'antd'
import swrFetcher from '../../../../utils/swrFetcher'
import { ConfigContext } from '../../../config-provider'
import { devError } from '../../../../utils/log'
import { useParamsDict } from '../../../../utils/hooks'
import { replaceVariablesInTemplate } from '../../../../utils/replaceVariablesInTemplate';
import SchemaPage from '../../../schema-page'
import type { SchemaPageProps } from '../../../schema-page'
import { isString } from 'lodash'

const classPrefix = `deer-field-dynamic-page`;

export interface FieldDynamicElementProps extends Omit<SchemaPageProps, 'dataSource'> {
  className?: string;
  style?: React.CSSProperties;

  entityId?: string;
  conf?: {
    elementKey: string;
    version?: string;
    language?: string;
  };
  confString?: string;
  dataSource?: SchemaPageProps['dataSource'] | string;
}

const FieldDynamicElement: React.FC<FieldDynamicElementProps> = (props) => {
  const {
    className,
    style,

    entityId,
    conf,
    confString,

    ...restProps
  } = props

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict(entityId ? { entityId } : {})

  if (!confString && !conf) {
    devError('FieldDynamicElement', '缺少必要参数 conf ！')
  }

  const [isLoading, setIsLoading] = React.useState(true)
  const [columns, setColumns] = React.useState<SchemaPageProps['columns']>([])

  const getConf = async () => {
    if (confString) {
      setColumns(JSON.parse(confString) as SchemaPageProps['columns'])
      setIsLoading(false)
      return
    }

    if (!conf) {
      setIsLoading(false)
      return
    }

    try {
      const api = replaceVariablesInTemplate(paramsDict, configContext.appFunctionApi!)

      const { data } = await swrFetcher(api!, 'POST', {
        functionKey: configContext.formatterElementByKey!,
        params: {
          elementKey: conf?.elementKey,
          language: conf?.language || 'gl',
          version: conf?.version || '1'
        }
      })

      const list = JSON.parse(data.rs)

      setColumns(list as SchemaPageProps['columns'])

    } catch (err) {
      console.error(err)

      throw new Error(`[smartdeer-ui: FieldDynamicElement] Conf 配置错误！`)
    }

    setIsLoading(false)
  }

  React.useEffect(() => {
    getConf()
  }, [])


  const dataSource: Record<string, any> = React.useMemo(() => {
    let dataSource = restProps.dataSource

    if (isString(dataSource)) {
      try {
        dataSource = JSON.parse(dataSource)
      } catch (err) {
        dataSource = {}
      }
    }

    return dataSource as Record<string, any>

  }, [restProps.dataSource])

  if (isLoading) {
    <div style={{ marginTop: '20px' }}>
      <Skeleton active paragraph={{ rows: 3 }} />
    </div>
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <SchemaPage
        {...restProps}
        columns={columns}
        dataSource={dataSource}
      />
    </div>
  );
};

export default FieldDynamicElement;
