import classNames from 'classnames';
import React, { useMemo } from 'react';
import FileView from '../../../file-view'
import type { FileViewProps } from '../../../file-view'

import './index.less';

const classPrefix = `deer-field-list-item-files`;

export interface FieldListItemFilesProps {
  className?: string;
  style?: React.CSSProperties;

  title?: string;
  description?: string;
  children?: React.ReactNode;
  layout?: 'horizontal' | 'vertical';
  isPrivate?: boolean;
  type?: FileViewProps['layout'];
}

const FieldListItemFiles: React.FC<FieldListItemFilesProps> = (props) => {
  const {
    className,
    style,
    title,
    description,
    children,
    layout = 'vertical',
    isPrivate = false,
    type = 'default',
  } = props;

  const list = useMemo<string[]>(() => {
    let arr = []
    try {
      arr = description ? JSON.parse(description) : []
      arr = Array.isArray(arr) ? arr : [arr];
    } catch (error) {
    }

    return arr
  }, [props])

  const containerClass = `${classPrefix}-${layout}`;

  return (
    <div className={classNames(classPrefix, `${classPrefix}-${layout}`, className)} style={{ ...style }}>
      <div className={`${containerClass}-title`}>{title}</div>

      <div className={`${containerClass}-description`}>
        {list.length === 0 ? '-' : (
          <FileView.Group
            list={list}
            isPrivate={isPrivate}
            layout={type}
          />
        )}
      </div>

      {!!children && (
        <div className={`${containerClass}-content`}>{children}</div>
      )}
    </div>
  );
};

export default FieldListItemFiles;
