import { Avatar as AntdAvatar } from 'antd';
import { AvatarSize } from 'antd/es/avatar/AvatarContext';
import classNames from 'classnames';
import React from 'react';

const classPrefix = `deer-field-avatar`;

interface FieldAvatarProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  size?: AvatarSize;
  src?: string;
  id?: string;
  name?: string;
}

const FieldAvatar: React.FC<FieldAvatarProps> = (props) => {
  const {
    className,
    style,

    size = 32,
    src,
    id = '',
    name = '',
  } = props;

  if (src) {
    return (
      <div className={classNames(classPrefix, className)} style={{ ...style }}>
        <AntdAvatar size={size} src={src} />
      </div>
    );
  }

  let colors = [
    '#FFA500',
    '#FF8060',
    '#F45B89',
    '#00CED1',
    '#4386EA',
    '#7C6DDB',
    '#5F9EA0',
    '#C5822F',
    '#32CD32',
    '#71B11D',
  ];
  const index = Number(id.toString().charAt(id.length - 1));

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <AntdAvatar
        size={size}
        style={{ background: colors[index] || '#FF812A' }}
      >
        {name.slice(0, 1)}
      </AntdAvatar>
    </div>
  );
};

export default FieldAvatar;
