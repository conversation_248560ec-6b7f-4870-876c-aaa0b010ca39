import React from 'react';
import { isDeepEqualReact } from '../../../utils'
import { isFunction } from 'lodash';

import type { FieldProps, FieldComponentProps, EffectType } from '../typing'

import { ComponentsContext, RenderComponentsType } from '../../../components/components-provider';

// 展示组件
import FieldListItem from './list-item'
import FieldListItemCard from './list-item-card'
import FieldAvatar from './avatar'
import FieldApprovalTimeline from '../../approval-timeline'
import FieldCrudTable from '../../crud-table'
import FieldAuditTimeline from '../../audit-timeline'
import FieldListItemFiles from './list-item-files'
import FieldSchemaTabs from '../../schema-tabs'
import FieldJsonEditor from '../../json-editor';
import FieldFollowUp from '../../follow-up'
import FieldQuillView from '../../quill-view'
import FieldPageHeader from '../../page-header'
import FieldFlowStep from '../../flow-step'
import FieldDynamicElement from './dynamic-element'
import FieldTimeline from '../../timeline'
import Image from '../../image'
import FieldTable from '../../../components/schema-page-pro/field-components/table';

// 排版组件
import FieldTypographyTitle from './title'
import FieldTypographyText from './text'
import FieldSpace from './space'
import FieldRow from './row'
import FieldCol from './col'

// 容器组件
import FieldContainer from './container'

// html 元素
import FieldH1 from './html-elements/h1'
import FieldH2 from './html-elements/h2'
import FieldH3 from './html-elements/h3'
import FieldH4 from './html-elements/h4'
import FieldH5 from './html-elements/h5'
import FieldH6 from './html-elements/h6'
import FieldDiv from './html-elements/div'
import FieldSpan from './html-elements/span'
import FieldStrong from './html-elements/strong'
import FieldA from './html-elements/a'
import FieldVideo from './html-elements/video'
import FieldImage from './html-elements/image'

// HOC 包
import WrapperApprovalTimeline from './wrappers/approval-timeline'
import WrapperConfirm from './wrappers/confirm'
import WrapperCrudTable from './wrappers/crud-table'
import WrapperSSCFollowUp from './wrappers/ssc-follow-up'
import WrapperSSCFlowStep from './wrappers/ssc-flow-step'

// 行为组件
import FieldConfirm from './confirm'
import FieldButton from './button'

// 自定义组件
import ReimburseDetail from './custom/reimburse-detail'
import UserTags from './user-tags';
import SvgIcon, { SvgIconProps } from './svg-icon';
import { FormatJson } from '../../../components/common';

const classPrefix = `deer-form-field`

type DefaultRenderComponentExtraType = {
  renderChildren?: React.ReactNode;
  effect?: EffectType;
  pageRenderComponents?: Array<RenderComponentsType>;
}

const defaultRenderComponent = (
  valueType: string,
  props: FieldProps,
  extra: DefaultRenderComponentExtraType,
) => {
  const { renderChildren, effect, pageRenderComponents } = extra

  let childNode: React.ReactNode = null;

  switch (valueType) {
    case 'container':
      childNode = <FieldContainer {...props}>{renderChildren}</FieldContainer>;
      break;

    case 'space':
      childNode = <FieldSpace {...props}>{renderChildren}</FieldSpace>;
      break;

    case 'title':
      childNode = <FieldTypographyTitle {...props}>{renderChildren}</FieldTypographyTitle>;
      break;

    case 'text':
      childNode = <FieldTypographyText {...props}>{renderChildren}</FieldTypographyText>;
      break;

    case 'h1':
      childNode = <FieldH1 {...props}>{renderChildren}</FieldH1>;
      break;

    case 'h2':
      childNode = <FieldH2 {...props}>{renderChildren}</FieldH2>;
      break;

    case 'h3':
      childNode = <FieldH3 {...props}>{renderChildren}</FieldH3>;
      break;

    case 'h4':
      childNode = <FieldH4 {...props}>{renderChildren}</FieldH4>;
      break;

    case 'h5':
      childNode = <FieldH5 {...props} >{renderChildren}</FieldH5>;
      break;

    case 'h6':
      childNode = <FieldH6 {...props}>{renderChildren}</FieldH6>;
      break;

    case 'div':
      childNode = <FieldDiv {...props}>{renderChildren}</FieldDiv>;
      break;

    case 'span':
      childNode = <FieldSpan {...props}>{renderChildren}</FieldSpan>;
      break;

    case 'strong':
      childNode = <FieldStrong {...props}>{renderChildren}</FieldStrong>;
      break;

    case 'a':
      childNode = <FieldA {...props}>{renderChildren}</FieldA>;
      break;

    case 'link':
      childNode = <FieldA {...props}>{renderChildren}</FieldA>;
      break;

    case 'video':
      childNode = <FieldVideo {...props}>{renderChildren}</FieldVideo>;
      break;

    case 'image':
      childNode = <FieldImage {...props}>{renderChildren}</FieldImage>;
      break;


    case 'button':
      childNode = <FieldButton {...props}>{renderChildren}</FieldButton>;
      break;

    case 'row':
      childNode = <FieldRow {...props}>{renderChildren}</FieldRow>;
      break;

    case 'col':
      childNode = <FieldCol {...props}>{renderChildren}</FieldCol>;
      break;

    case 'avatar':
      childNode = <FieldAvatar {...props}>{renderChildren}</FieldAvatar>;
      break;

    case 'listItem':
      childNode = <FieldListItem {...props}>{renderChildren}</FieldListItem>;
      break;

    case 'listItemCard':
      childNode = <FieldListItemCard {...props}>{renderChildren}</FieldListItemCard>;
      break;

    case 'approvalTimeline':
      childNode = (
        <WrapperApprovalTimeline effect={effect}>
          <FieldApprovalTimeline  {...props} />
        </WrapperApprovalTimeline>
      );
      break;

    case 'auditTimeline':
      childNode = <FieldAuditTimeline  {...props} />;
      break;

    case 'confirm':
      childNode = (
        <WrapperConfirm effect={effect}>
          <FieldConfirm  {...props} />
        </WrapperConfirm>
      );
      break;

    case 'listItemFiles':
      childNode = <FieldListItemFiles {...props}>{renderChildren}</FieldListItemFiles>;
      break;

    case 'gs.reimburse.detail':
      childNode = <ReimburseDetail {...props}>{renderChildren}</ReimburseDetail>
      break;

    case 'crudTable':
      childNode = (
        <WrapperCrudTable effect={effect} {...props}>
          <FieldCrudTable {...props} />
        </WrapperCrudTable>
      );
      break;

    case 'schemaTabs':
      childNode = <FieldSchemaTabs {...props} />
      break;

    case 'jsonEditor':
      childNode = <FieldJsonEditor {...props} />
      break;

    case 'formatJson':
      childNode = <FormatJson {...props as any} />
      break;

    case 'sscFollowUp':
      childNode = (
        <WrapperSSCFollowUp {...props}>
          <FieldFollowUp {...props} />
        </WrapperSSCFollowUp>
      );
      break;

    case 'sscFlowStep':
      childNode = (
        <WrapperSSCFlowStep effect={effect} {...props}>
          <FieldFlowStep {...props} />
        </WrapperSSCFlowStep>
      );
      break;

    case 'flowStep':
      childNode = <FieldFlowStep {...props} />;
      break;

    case 'quillView':
      childNode = <FieldQuillView {...props} />;
      break;

    case 'pageHeader':
      childNode = <FieldPageHeader {...props} />;
      break;

    case 'dynamicElement':
      childNode = <FieldDynamicElement {...props} />;
      break;

    case 'userTags':
      childNode = <WrapperConfirm effect={effect}>
        <UserTags {...props} />
      </WrapperConfirm>;
      break;

    case 'svgIcon':
      childNode = <SvgIcon {...props as SvgIconProps} />
      break;

    case 'timeline':
      childNode = <FieldTimeline {...props} />
      break;

    case 'img':
      childNode = <img alt={""} {...props} />
      break;

    case 'image':
      childNode = <Image {...props} />
      break;

    case 'table':
      childNode = <FieldTable fieldProps={props} />
      break;

    default: {
      if (pageRenderComponents?.length) {
        // 自定义组件
        const component = pageRenderComponents.find(
          (item) => item.type === valueType,
        )?.render;
        if (component) {
          childNode = isFunction(component)
            ? component({ ...props })
            : React.cloneElement(component as React.ReactElement, {
              ...props,
            });
        }
      } else {
        childNode = (
          <div className={`bg-red-500 text-white rounded px-2 py-1 mb-[20px]`}>
            没有实现的组件: {valueType}
          </div>
        );
      }
    }

  }

  return <>{childNode}</>
}

const FieldComponent: React.FC<FieldComponentProps> = (props) => {
  const {
    valueType,
    fieldProps = {},
    renderChildren,
    effect,
  } = props

  const { pageRenderComponents } = React.useContext(ComponentsContext);

  const renderedDom = defaultRenderComponent(
    valueType,
    fieldProps,
    { renderChildren, effect, pageRenderComponents }
  )

  return (
    <React.Fragment>
      {renderedDom}
    </React.Fragment>
  )
}

export default React.memo(FieldComponent, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
