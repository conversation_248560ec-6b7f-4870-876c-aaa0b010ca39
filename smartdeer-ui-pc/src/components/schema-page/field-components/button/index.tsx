import { Button } from 'antd';
import classNames from 'classnames';
import React from 'react';
import { ConfigContext } from '../../../config-provider';

const classPrefix = `deer-field-button`;

interface FieldButtonProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  type?: 'primary' | 'dashed' | 'link' | 'text' | 'default';
  size?: 'large' | 'middle' | 'small';
  danger?: boolean;
  iconType?: string;
  url?: string;
}

const FieldButton: React.FC<FieldButtonProps> = (props) => {
  const {
    className,
    style,

    type = 'primary',
    size,
    children,
    danger,
    url,
  } = props;

  const { router } = React.useContext(ConfigContext);

  const [buttonLoading, setButtonLoading] = React.useState(false);

  const handleClick = () => {
    if (url) {
      router?.push(url)
    }
  };

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <Button
        type={type}
        size={size}
        danger={danger}
        loading={buttonLoading}
        onClick={handleClick}
      >
        {children}
      </Button>
    </div>
  );
};

export default FieldButton;
