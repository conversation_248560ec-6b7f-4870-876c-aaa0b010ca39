import React from 'react';
import classNames from 'classnames';

const classPrefix = `deer-field-container`

import './index.less'

interface FieldContainerProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode
}

const FieldContainer: React.FC<FieldContainerProps> = (props) => {
  const {
    className,
    style,
    children,
  } = props

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </div>
  );
};

export default FieldContainer
