import React from 'react';
import classNames from 'classnames';
import { Space } from 'antd'

const classPrefix = `deer-field-space`

import './index.less'

interface FieldSpaceProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode
}

const FieldSpace: React.FC<FieldSpaceProps> = (props) => {
  const {
    className,
    style,
    children,
  } = props

  return (
    <Space className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </Space>
  );
};

export default FieldSpace
