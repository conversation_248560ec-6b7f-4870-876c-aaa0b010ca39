.deer-field-list-item-vertical {
  &-title {
    font-size: 12px;
    line-height: 12px;
    color: #757575;
  }

  &-description {
    font-size: 14px;
    line-height: 18px;
    margin-top: 10px;
    color: #474747;
    white-space: normal; /* 确保空白字符正常处理 */
    overflow-wrap: break-word; /* 允许长单词或URL断行 */
    word-break: break-word; /* 对于非CJK文本，确保长单词正确断行 */
  }

  &-emoji {
    margin-right: 4px;
    display: inline-block;
    font-size: 20px;
    vertical-align: middle;
  }

  &-content {
    // display: none;
  }
}

.deer-field-list-item-horizontal {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-title {
    font-size: 14px;
    line-height: 1px;
    color: #323233;
  }

  &-emoji {
    margin-right: 4px;
    display: inline-block;
    font-size: 20px;
    vertical-align: middle;
  }

  &-description {
    font-size: 14px;
    line-height: 14px;
    color: #969799;
    white-space: normal; /* 确保空白字符正常处理 */
    overflow-wrap: break-word; /* 允许长单词或URL断行 */
    word-break: break-word; /* 对于非CJK文本，确保长单词正确断行 */
  }

  &-content {
    // display: none;
  }
}
