import classNames from 'classnames';
import React from 'react';

import './index.less';

const classPrefix = `deer-field-list-item`;

export interface FieldListItemProps {
  className?: string;
  style?: React.CSSProperties;

  title?: string;
  description?: string;
  children?: React.ReactNode;
  layout?: 'horizontal' | 'vertical';
  emoji?: string;
}

const FieldListItem: React.FC<FieldListItemProps> = (props) => {
  const {
    className,
    style,
    title,
    description,
    children,
    layout = 'vertical',
    emoji,
  } = props;

  const containerClass = `${classPrefix}-${layout}`;

  return (
    <div className={classNames(containerClass, className)} style={{ ...style }}>
      <div className={`${containerClass}-title`}>{title}</div>

      {description && (
        <div className={`${containerClass}-description`}>
          {emoji && (
            <span role="img" aria-label={description} className={`${containerClass}-emoji`}>
              {emoji}
            </span>
          )}

          {description}
        </div>
      )}
      {children && (
        <div className={`${containerClass}-content`}>{children}</div>
      )}
    </div>
  );
};

export default FieldListItem;
