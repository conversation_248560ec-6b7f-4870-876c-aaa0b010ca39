import React from 'react';
import classNames from 'classnames';
import { Row } from 'antd'
import type { RowProps } from 'antd'

const classPrefix = `deer-field-row`

interface FieldRowProps extends RowProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode
}

const FieldRow: React.FC<FieldRowProps> = (props) => {
  const {
    className,
    style,
    children,
    ...restProps
  } = props

  return (
    <Row
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restProps}
    >
      {children}
    </Row>
  );
};

export default FieldRow
