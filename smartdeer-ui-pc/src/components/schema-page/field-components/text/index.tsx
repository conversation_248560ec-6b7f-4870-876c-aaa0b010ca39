import React from 'react';
import classNames from 'classnames';
import { Typography } from 'antd';

const { Text } = Typography;

const classPrefix = `deer-field-typography-text`

interface FieldTypographyTextProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  type?: 'secondary' | 'success' | 'warning' | 'danger';
}

const FieldTypographyText: React.FC<FieldTypographyTextProps> = (props) => {
  const {
    className,
    style,
    children,

    ...extra
  } = props

  return (
    <Text
      {...extra}
      className={classNames(classPrefix, className)}
      style={{ ...style }}
    >
      {children}
    </Text>
  );
};

export default FieldTypographyText
