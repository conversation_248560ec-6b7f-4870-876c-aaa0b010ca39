import React, { useEffect, useRef, useState } from 'react';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Avatar, Button, Flex, InputRef, Popover, Select, SelectProps, Tag, Tooltip } from 'antd';
import swrFetcher from '../../../../utils/swrFetcher';
import { replaceVariablesInTemplate } from '../../../../utils/replaceVariablesInTemplate';
import { ConfigContext } from '../../../config-provider';
import { useParamsDict } from '../../../../utils/hooks';

import SmartDeerAvatar from '../../../follow-up/avatar';


type TagType = { subscribe: string, subscribeJson: { avatar: string, email: string, userName: string } }

type UserTagsProps = {
  //  选中的list
  list?: string
  //  禁用的list
  disabledList?: string[]
  //  展示类型
  type?: 'avatar' | 'tags'
  style?: React.CSSProperties;
  selectProps?: SelectProps
  onEffect?: (values: any) => Promise<void>
}

const UserTags: React.FC<UserTagsProps> = (props) => {

  const { list, disabledList = [], style, selectProps, onEffect, type = 'avatar' } = props;

  const [tags, setTags] = useState<TagType[]>([]);
  const [inputVisible, setInputVisible] = useState(false);
  const inputRef = useRef<InputRef>(null);
  const [loading, setLoading] = useState(false);
  const [userOptions, setUserOptions] = useState<{ label: string, value: number }[]>([]);
  const [value, setValue] = useState<any[]>([]);

  useEffect(() => {
    if (inputVisible) {
      inputRef.current?.focus();
    }
  }, [inputVisible]);

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict();


  async function initUsers() {

    setLoading(true);

    const api = replaceVariablesInTemplate(paramsDict, configContext.appFunctionApi!);

    //  请求用户列表
    const { data } = await swrFetcher(api!, 'POST', {
      functionKey: 'search_sage_account',
      params: {
        entityId: '0',
        current: '1',
        size: '1000',
        limit: '1000',
      },
      data: 'dataInfo',
    });

    //  写入options
    setUserOptions(data['dataInfo'].map((item: { accountId: number, alias: string }) => ({
      label: item.alias,
      value: item.accountId,
      disabled: disabledList?.findIndex((accountId: string) => Number(accountId) === item.accountId) > -1,
    })));
    setLoading(false);
  }

  const show = async () => {
    if (userOptions.length === 0) {
      await initUsers();
    }
    setValue(tags.map(item => Number(item.subscribe)));
    setInputVisible(true);
  };

  useEffect(() => {
    if (list) {
      const tags: TagType[] = JSON.parse(list);
      setTags(tags);
    } else {
      setTags([]);
      setValue([]);
    }
  }, [list]);

  const handleCancel = () => {
    setInputVisible(false);
  };

  const handleSubmit = async (values: number[]) => {
    const subscriber: string[] = [];
    const subscriberStr: string[] = [];
    //  格式化
    values.forEach(value => {
      const find = userOptions.find(find => find.value === value);
      if (find) {
        subscriber.push(value.toString());
        subscriberStr.push(find?.label);
      }
    });

    await onEffect!({
      subscriber: subscriber.join(','),
      subscriberStr: subscriberStr.join(','),
    });
    handleCancel();
  };

  const handleDelete = (targetIndex: number) => {
    const filter = tags.filter((_find, index) => index !== targetIndex);
    //  更新组件
    setTags(filter);
    //  更新数据
    handleSubmit(filter.map(item => Number(item.subscribe)));
  };

  return (
    <>
      <Flex gap="8px 0" wrap style={style} align={'center'} justify={'start'}>
        {
          type === 'tags' ? tags.map<React.ReactNode>((tag, index) => {
            const { userName } = tag.subscribeJson;
            const name = userName;
            const isLongTag = userName.length > 20;
            const tagElem = (
              <Tag
                key={name}
                closable={!inputVisible}
                style={{ userSelect: 'none', height: '24px', lineHeight: '24px' }}
                onClose={() => handleDelete(index)}
              >
                {isLongTag ? `${name.slice(0, 20)}...` : name}
              </Tag>
            );
            return isLongTag ? (
              <Tooltip title={name} key={name}>
                {tagElem}
              </Tooltip>
            ) : (
              tagElem
            );
          }) : (
            <Avatar.Group>
              {tags.map(({ subscribe, subscribeJson }) => {
                const { avatar, userName, email } = subscribeJson;
                return <Popover content={<a href={`mailto:${email}`}>{`@${userName}`}</a>} key={subscribe}>
                  <div><SmartDeerAvatar src={avatar} name={userName} id={subscribe} /></div>
                </Popover>;
              })}
            </Avatar.Group>
          )}
        {!inputVisible && <Button size={'small'} icon={<PlusCircleOutlined />} onClick={show} type={'link'} />}
      </Flex>
      {inputVisible && (
        <div style={{ marginTop: '16px', display: 'flex' }}>
          <Select
            size={'small'}
            style={{ width: '100%' }}
            placeholder={'请选择'}
            options={userOptions}
            showSearch
            loading={loading}
            optionFilterProp="label"
            value={value}
            onChange={setValue}
            {...selectProps}
          />
          <a style={{ marginLeft: '8px', flexShrink: 0, height: '24px', lineHeight: '24px' }}
             onClick={() => handleSubmit(value)}>确定</a>
          <a style={{ marginLeft: '4px', flexShrink: 0, height: '24px', lineHeight: '24px' }}
             onClick={handleCancel}>取消</a>
        </div>
      )}
    </>
  );
};

export default UserTags;
