import classNames from 'classnames';
import React from 'react';

import SvgIcon, { IconNames } from '../svg-icon';
import './index.less';

const classPrefix = `deer-field-list-item-card`;

export interface FieldListItemCardProps {
  className?: string;
  style?: React.CSSProperties;

  title?: string;
  description?: string;
  children?: React.ReactNode;
  icon?: IconNames;
}

const FieldListItemCard: React.FC<FieldListItemCardProps> = (props) => {
  const { className, style, title, description, children, icon } = props;


  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {icon ? (
        <div className={`${classPrefix}-icon`}>
          <SvgIcon
            name={icon}
            style={{ fontSize: '24px', width: '18px', height: '18px' }}
          />
        </div>
      ) : null}
      <div className={`${classPrefix}-content`}>
        <div className={`${classPrefix}-content-title`}>{title}</div>
        <div className={`${classPrefix}-content-desc`}>{description}</div>
      </div>
      <div></div>
      {children && <div className={`${classPrefix}-children`}>{children}</div>}
    </div>
  );
};

export default FieldListItemCard;
