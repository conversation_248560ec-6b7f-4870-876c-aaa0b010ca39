import React from 'react';
import { Skeleton } from 'antd'
import type { EffectType } from '../../../typing'
import { useGetEffectFetch } from '../../../../../utils/hooks'

interface FieldWrapperApprovalTimelineProps {
  effect?: EffectType;
  children?: React.ReactElement;
}

const FieldWrapperApprovalTimeline: React.FC<FieldWrapperApprovalTimelineProps> = (props) => {
  const {
    effect,
    children
  } = props

  const effectFetch = effect?.fetch

  const { data, isLoading } = useGetEffectFetch(effectFetch)

  if (isLoading) return <Skeleton active paragraph={{ rows: 3 }} />

  let childNode: React.ReactNode = null;

  if (children) {
    childNode = React.cloneElement(children, {
      dataInfo: data['dataInfo']
    })
  }

  return (
    <>
      {childNode}
    </>
  );
};

export default FieldWrapperApprovalTimeline
