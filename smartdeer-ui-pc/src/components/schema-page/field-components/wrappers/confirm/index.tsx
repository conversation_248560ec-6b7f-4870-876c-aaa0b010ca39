import React from 'react';
import type { EffectType } from '../../../typing'
import { useParamsDict, useSuccess } from '../../../../../utils/hooks';
import { replaceTemplateWithPipes } from '../../../../../utils/replaceTemplateWithPipes';
import { replaceVariablesInTemplate } from '../../../../../utils/replaceVariablesInTemplate';
import { SchemaPageContext } from '../../../context'
import { ConfigContext } from '../../../../config-provider'
import swrFetcher from '../../../../../utils/swrFetcher';
import { SchemaPageEffectContext } from '../../../effect/context';
import { mergeNestedObjectValues } from '../../../../../utils/mergeNestedObjectValues';

interface FieldWrapperConfirmProps {
  effect?: EffectType;
  children?: React.ReactElement;
}

const FieldWrapperConfirm: React.FC<FieldWrapperConfirmProps> = (props) => {
  const {
    effect,
    children,
  } = props

  const { onRetry, dataSource, pageVariables } = React.useContext(SchemaPageContext)
  const { onEffectRetry } = React.useContext(SchemaPageEffectContext)
  const { appProcessApi, appFunctionApi } = React.useContext(ConfigContext)

  const paramsDict = useParamsDict({ ...pageVariables, ...dataSource });

  const onSuccess = useSuccess({
    onRetry,
    onEffectRetry,
  })

  const handleEffectOnSuccess = async (res: any) => {
    const effectOnSuccess = effect?.onSuccess
    if (!effectOnSuccess) return
    await onSuccess(effectOnSuccess, { ...res })
  }

  const handleEffectFetch = async (values?: Record<string, any>) => {
    const effectFetch = effect?.fetch
    const effectFetchType = effect?.fetch?.type
    const paramsInjectionLocation = effect?.fetch?.paramsInjectionLocation || [];

    const defaultParams = effectFetch?.defaultParams
      ? JSON.parse(
        replaceTemplateWithPipes(
          JSON.stringify(effectFetch?.defaultParams),
          { ...paramsDict, ...values }
        ),
      )
      : {};

    let fetchUrl = ''
    let fetchParams = {}

    switch (effectFetchType) {
      case 'api':
        if (paramsInjectionLocation?.length) {
          fetchParams = mergeNestedObjectValues(
            defaultParams,
            { ...values },
            paramsInjectionLocation,
          );
        } else {
          fetchParams = { ...defaultParams, ...values };
        }
        fetchUrl = effectFetch?.api as string;
        break;

      case 'function':
        fetchParams = {
          functionKey: effectFetch?.functionKey,
          params: { ...defaultParams, ...values }
        };

        fetchUrl = appFunctionApi!
        break;

      case 'process':
        fetchParams = {
          processKey: effectFetch?.processKey,
          params: { ...defaultParams, ...values }
        };

        fetchUrl = appProcessApi!
        break;

      default:
        break;
    }

    const url = replaceVariablesInTemplate(paramsDict, fetchUrl)

    const res = await swrFetcher(url, 'POST', fetchParams)

    await handleEffectOnSuccess(res.data)
  }

  let childNode: React.ReactNode = null;

  if (children) {
    childNode = React.cloneElement(children, {
      onEffect: handleEffectFetch
    })
  }

  return (
    <>
      {childNode}
    </>
  );
};

export default FieldWrapperConfirm
