import { Skeleton } from 'antd';
import { isArray } from 'lodash';
import React from 'react';
import { evaluateLogicalExpression } from '../../../../../utils/evaluateLogicalExpression';
import { useGetEffectFetch } from '../../../../../utils/hooks';
import type { EffectType } from '../../../typing';

interface FieldWrapperSSCFlowStepProps {
  effect?: EffectType;
  children?: React.ReactElement;
  finish?: string;
}

const FieldWrapperSSCFlowStep: React.FC<FieldWrapperSSCFlowStepProps> = (
  props,
) => {
  const { effect, children, finish = '' } = props;

  const isFinished = evaluateLogicalExpression({}, finish);

  const effectFetch = effect?.fetch;

  const { data, isLoading } = useGetEffectFetch(effectFetch);

  const getItemStatus = (item: any, index: number, list: any[]) => {
    const currentFinishedIndex = list.findLastIndex((node: any) => {
      return Number(node.status) === 1;
    });
    const currentStartIndex = list.findLastIndex((node: any) => {
      return Number(node.status) === 0;
    });
    const currentIndex = Math.max(currentFinishedIndex + 1, currentStartIndex);

    if (index < currentIndex || isFinished) {
      return 'finish';
    }
    if (index === currentIndex) {
      return 'process';
    }
    return 'wait';
  };

  const steps = React.useMemo(() => {
    let json = data?.json;
    if (!isArray(json)) {
      try {
        json = JSON.parse(json);
      } catch (e) {
        json = [];
      }
    }

    return json.map((item: any, index: number) => {
      return {
        title: item.stepName,
        status: getItemStatus(item, index, json),
      };
    });
  }, [data]);

  if (isLoading) return <Skeleton active paragraph={{ rows: 3 }} />;

  let childNode: React.ReactNode = null;

  if (children) {
    childNode = React.cloneElement(children, {
      steps,
    });
  }

  return <>{childNode}</>;
};

export default FieldWrapperSSCFlowStep;
