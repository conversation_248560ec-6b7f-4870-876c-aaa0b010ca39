import { message, Spin, TablePaginationConfig, TableProps } from 'antd';
import { assign } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { ConfigContext } from '../../../../../components/config-provider';
import TableSearch from '../../../../../components/table-search';
import { getEffectFetchConfig } from '../../../../../utils/getEffectFetchConfig';
import { transformDataBasedOnRules } from '../../../../../utils/transformDataBasedOnRules';
import { useParamsDict } from '../../../../../utils/hooks';
import swrFetcher from '../../../../../utils/swrFetcher';
import type { TableSearchProps } from '../../../../table-search/table-search';
import type { EffectType } from '../../../typing';
import { SchemaPageContext } from '../../../context';

interface FieldWrapperCrudTableProps {
  effect?: EffectType;
  children?: React.ReactElement;
  pagination?: boolean;
  loadingSpin?: boolean;
  tableSearchProps?: TableSearchProps;
  rowKey?: string;
  sortDirections?: Array<'descend' | 'ascend'>
}

type RecordType = Record<string, any>;

const defaultPaginationConfig = {
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  hideOnSinglePage: true,
};

function transformSorter(sorter: any) {
  const origin = Array.isArray(sorter) ? sorter : [sorter];
  const arr: { key: string, asc: string }[] = [];
  origin.forEach((item: any) => {
    if (item.order) {
      arr.push({ key: item.field || item.columnKey, asc: item.order === 'ascend' ? 'asc' : 'desc' });
    }
  });
  return arr;
}

const FieldWrapperCrudTable: React.FC<FieldWrapperCrudTableProps> = (props) => {
  const {
    effect,
    children,
    tableSearchProps,
    pagination = false,
    loadingSpin = true,
    rowKey,
    sortDirections,
  } = props;


  const configContext = React.useContext(ConfigContext);
  const { mock, pageVariables } = React.useContext(SchemaPageContext);
  const paramsDict = useParamsDict(pageVariables);

  const [data, setData] = React.useState<any>(null);
  const [paginationConfig, setPaginationConfig] =
    useState<TablePaginationConfig>(defaultPaginationConfig);

  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [filterParams, setFilterParams] = React.useState<Record<string, any>>(
    {},
  );
  const [sorter, setSorter] = useState<{ key: string, asc: string }[]>([]);

  const effectFetch = effect?.fetch;

  const {
    data: dataIndex,
    sortKey,
    sortOrder = 'descend',
    transform,
  } = effectFetch || {};

  const showSearch = !!tableSearchProps;

  const tableSearchType = tableSearchProps?.type || 'searchItems';

  const fetchTableData = async () => {

    const mockKey = effectFetch?.functionKey || effectFetch?.processKey
    // 如果mock存在，并且mockKey存在，则使用mock数据
    if (mock && mockKey) {
      const data = mock[mockKey];
      setData(data);
      return
    }

    setIsLoading(true);

    const newEffectFetch = {
      ...effectFetch!,
      defaultParams: {
        ...effectFetch?.defaultParams,
      },
    };

    if (showSearch && tableSearchType === 'expressions') {
      newEffectFetch.defaultParams = assign(newEffectFetch.defaultParams, {
        expressions: [
          ...effectFetch?.defaultParams?.expressions,
          ...filterParams?.expressions || [],
        ],
      });
    }

    if (showSearch && tableSearchType === 'searchItems') {
      newEffectFetch.defaultParams = assign(newEffectFetch.defaultParams, {
        searchInfo: {
          ...effectFetch?.defaultParams?.searchInfo,
          ...filterParams,
        },
      });
    }

    if (showSearch && tableSearchType === 'params') {
      newEffectFetch.defaultParams = assign(newEffectFetch.defaultParams, {
        ...filterParams,
      });
    }

    if (sorter?.length) {
      //  这里需要去掉重复的Key
      const defaultOrders = (effectFetch?.defaultParams?.searchInfo?.orders || []);
      const orders: any[] = [...sorter]
      defaultOrders.forEach((item: { key: string }) => {
        const findIndex = sorter.findIndex(find => find.key === item.key);
        if (findIndex === -1) orders.push(item);
      });
      //  写入
      newEffectFetch.defaultParams = assign(newEffectFetch.defaultParams, {
        searchInfo: {
          ...effectFetch?.defaultParams?.searchInfo,
          ...filterParams,
          orders,
        },
      });
    }

    if (pagination) {
      newEffectFetch.defaultParams = assign(newEffectFetch.defaultParams, {
        current: paginationConfig.current,
        limit: paginationConfig.pageSize,
        size: paginationConfig.pageSize,
      });
    }

    try {
      const { api, method, params } = getEffectFetchConfig(
        newEffectFetch!,
        configContext,
        paramsDict,
      );

      const { data } = await swrFetcher(api, method, params);

      setData(data);

      if (pagination) {
        setPaginationConfig({
          ...paginationConfig,
          total: data?.total || 0,
        });
      }
    } catch (err: any) {
      // message.error(err.message);
    }

    setIsLoading(false);
  };

  const dataSource = useMemo(() => {
    let result;
    if (!effectFetch) {
      return [];
    }
    result = dataIndex && data ? data[dataIndex] : data;

    if (sortKey && Array.isArray(result)) {
      const isDescend = sortOrder === 'descend';
      result.sort((a: RecordType, b: RecordType) => {
        return isDescend ? b[sortKey] - a[sortKey] : a[sortKey] - b[sortKey];
      });
    }

    if (transform) {
      result = transformDataBasedOnRules(result, transform);
    }
    return result;
  }, [effectFetch, dataIndex, data, sortKey, sortOrder]);

  useEffect(() => {
    if (!effectFetch) {
      return;
    }
    fetchTableData();
  }, [
    pagination,
    effectFetch,
    paginationConfig.current,
    paginationConfig.pageSize,
    filterParams,
    sorter
  ]);


  const handleChangeTable: TableProps<any>['onChange'] = (pagination, _filters, sorter, extra) => {
    //  分页触发
    if (extra.action === 'paginate') {
      setPaginationConfig({ ...pagination });
    }
    //  排序触发
    if (extra.action === 'sort') {
      setSorter(transformSorter(sorter));
    }
  };

  const handleTableSearch = (values: Record<string, any>) => {
    console.log('values', values);
    setPaginationConfig({ ...paginationConfig, current: 1 });
    setFilterParams(values);
  };

  let childNode: React.ReactNode = null;

  if (children) {
    childNode = React.cloneElement(children, {
      dataSource,
      pagination: pagination ? paginationConfig : undefined,
      onChange: pagination ? handleChangeTable : undefined,
      onRefresh: fetchTableData,
      rowKey: rowKey,
      sortDirections: sortDirections,
      mock: mock,
    });
  }

  return (
    <>
      {showSearch && (
        <TableSearch {...tableSearchProps} onSearch={handleTableSearch} />
      )}
      <Spin spinning={effectFetch && isLoading && loadingSpin}>
        {childNode}
      </Spin>
    </>
  );
};

export default FieldWrapperCrudTable;
