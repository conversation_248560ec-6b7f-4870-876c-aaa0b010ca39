import { PaginationProps } from 'antd';
import { isArray, isObject, isString } from 'lodash';
import React from 'react';
import { SchemaPageEffectContext } from '../../../effect/context';
import type { EffectType } from '../../../typing';

interface FieldWrapperSSCFollowUpProps {
  effect?: EffectType;
  children?: React.ReactElement;
  pagination?: PaginationProps | boolean;
  total?: number;
  dataInfo?: string;
  actionMap?: Record<string, any>;
}

const defaultPaginationConf: PaginationProps = {
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: false,
  hideOnSinglePage: true,
};

const FieldWrapperSSCFollowUp: React.FC<FieldWrapperSSCFollowUpProps> = (
  props,
) => {
  const {
    dataInfo,
    pagination: paginationConf = true,
    total,
    actionMap,
    children,
  } = props;

  const { onEffectRetry } = React.useContext(SchemaPageEffectContext);

  const defaultPagination = React.useMemo(() => {
    if (!paginationConf) return {};
    if (isObject(paginationConf)) {
      return { ...defaultPaginationConf, ...paginationConf, total };
    }
    return { ...defaultPaginationConf, total };
  }, [paginationConf]);

  const dataSource: any = React.useMemo(() => {
    let result;
    if (!dataInfo) return [];

    try {
      result = JSON.parse(dataInfo);
    } catch (e) {
      result = isArray(dataInfo) ? dataInfo : [];
    }

    return result.map((item: any) => {
      let json: any = {};
      try {
        json = JSON.parse(item.json);
        json = isObject(json) ? json : {};
      } catch (e) {
        json = {};
      }

      json.text =
        item.flowStepKeyStr && item.flowStepKeyStr !== '-'
          ? `<p>${json.text || ''}</p>流程处理: ${item?.flowStepKeyStr}${
              item?.flowStatus === 1 ? '已完成' : '进行中'
            }`
          : json.text;

      if (!json.text) {
        // 添加默认操作备注
        json.text = actionMap?.[item?.type] ? actionMap[item.type] : '';
        // 添加处理完结/撤销/关闭时
        json.text = item.html
          ? `<p>${json.text}</p>备注: ${item?.html}`
          : json.text;
      }

      if (!json.text) {
        json.text = '没有填写跟进信息';
      }

      return {
        ...item,
        json,
        notifyName:
          item.notifyName && isString(item.notifyName)
            ? item.notifyName.replace(/,/g, ', ')
            : item.notifyName,
      };
    });
  }, [dataInfo]);

  const handleChangePage = (current: number, pageSize: number) => {
    onEffectRetry?.(
      {
        current,
        limit: pageSize,
        size: pageSize,
      },
      false,
    );
  };

  let childNode: React.ReactNode = null;

  if (children) {
    childNode = React.cloneElement(children, {
      ...props,
      dataSource,
      pagination: !paginationConf ? false : defaultPagination,
      onPageChange: handleChangePage,
    });
  }

  return <>{childNode}</>;
};

export default FieldWrapperSSCFollowUp;
