import React from 'react';
import classNames from 'classnames';
import { Col } from 'antd'
import type { ColProps } from 'antd'

const classPrefix = `deer-field-col`

interface FieldColProps extends ColProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode
}

const FieldCol: React.FC<FieldColProps> = (props) => {
  const {
    className,
    style,
    children,
    ...restProps
  } = props

  return (
    <Col
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restProps}
    >
      {children}
    </Col>
  );
};

export default FieldCol
