import React from 'react';
import classNames from 'classnames';
import { Typography } from 'antd';

const { Title } = Typography;

const classPrefix = `deer-field-typography-title`

interface FieldTypographyTitleProps {
  className?: string;
  style?: React.CSSProperties;

  level?: 3 | 5 | 1 | 2 | 4;
  children?: React.ReactNode;
}

const FieldTypographyTitle: React.FC<FieldTypographyTitleProps> = (props) => {
  const {
    className,
    style,

    level = 3,
    children,
  } = props

  return (
    <Title level={level} className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </Title>
  );
};

export default FieldTypographyTitle
