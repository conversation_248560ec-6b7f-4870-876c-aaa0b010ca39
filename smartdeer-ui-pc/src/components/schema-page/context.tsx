import React from 'react'

export type SchemaPageContextType = {
  onRetry?: (forceUpdate?: boolean) => void,
  dataSource: Record<string, any>
  pageVariables: Record<string, any>
  mock?: Record<string, any>
}

export const defaultSchemaPageContext: SchemaPageContextType = {
  dataSource: {},
  pageVariables: {}
}

export const SchemaPageContext =
  React.createContext<SchemaPageContextType>(defaultSchemaPageContext)
