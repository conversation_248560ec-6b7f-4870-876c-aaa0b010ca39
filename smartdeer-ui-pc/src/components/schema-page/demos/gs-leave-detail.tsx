import React from 'react';
import { SchemaPage } from '@smartdeer-ui/pc'
import type { PageColumnsType } from '@smartdeer-ui/pc'

const columns: PageColumnsType[] = [
  {
    "type": "pageHeader",
    "props": {
      "title": ["请假列表", "请假详情"]
    }
  },

  {
    "type": "container",
    "props": {
      "style": {
        "marginTop": "20px",
      }
    },
    "children": [
      {
        "type": "h2",
        "children": "*{applyRealNameStr}"
      },
      {
        "type": "div",
        "children": "*{companyName}",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "12px",
            "color": "#757575"
          }
        }
      },
      {
        "type": "div",
        "children": "${审批中|leave.audit.default}",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "16px",
            "color": "#fb923c"
          }
        },
        "effect": {
          "show": "*{system_status} === 0"
        }
      },
      {
        "type": "div",
        "children": "${已审批通过|leave.audit.pass}",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "16px",
            "color": "#4ade80"
          }
        },
        "effect": {
          "show": "*{system_status} === 1"
        }
      },
      {
        "type": "div",
        "children": "${已拒绝审批|leave.audit.reject}",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "16px",
            "color": "#f87171"
          }
        },
        "effect": {
          "show": "*{system_status} === 2"
        }
      },
      {
        "type": "div",
        "children": "${已取消|leave.audit.cancel}",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "16px",
            "color": "#9ca3af"
          }
        },
        "effect": {
          "show": "*{system_status} === 3"
        }
      }
    ]
  },
  {
    "type": "container",
    "props": {
      "style": {
        "marginTop": "20px"
      }
    },
    "children": [
      {
        "type": "h3",
        "children": "${审批详情|audit.detail}"
      },
      {
        "type": "listItem",
        "props": {
          "title": "${所在部门|department}",
          "description": "*{department}",
          "style": {
            "marginTop": "18px"
          }
        }
      },
      {
        "type": "listItem",
        "props": {
          "title": "${休假类型|leaveType}",
          "description": "*{groupKeyStr}",
          "style": {
            "marginTop": "16px"
          }
        }
      },
      {
        "type": "listItem",
        "props": {
          "title": "${开始时间|leave.startTime}",
          "description": "*{startFrom | formatTime(YYYY-MM-DD HH:mm)}",
          "style": {
            "marginTop": "16px"
          }
        }
      },
      {
        "type": "listItem",
        "props": {
          "title": "${结束时间|leave.endTime}",
          "description": "*{endTo | formatTime(YYYY-MM-DD HH:mm)}",
          "style": {
            "marginTop": "16px"
          }
        }
      },
      {
        "type": "listItem",
        "props": {
          "title": "${请假原因|leave.reason}",
          "description": "*{reason}",
          "style": {
            "marginTop": "16px"
          }
        }
      }
    ]
  },
  // {
  //   "type": "container",
  //   "props": {
  //     "style": {
  //       "marginTop": "20px"
  //     }
  //   },
  //   "children": [
  //     {
  //       "type": "h3",
  //       "children": "${审批记录|audit.record}"
  //     },
  //     {
  //       "type": "approvalTimeline",
  //       "props": {
  //         "style": {
  //           "marginTop": "20px"
  //         }
  //       },
  //       "effect": {
  //         "fetch": {
  //           "type": "function",
  //           "functionKey": "search_leave",
  //           "defaultParams": {
  //             "entityId": "*{entityId}",
  //             "expressions": [
  //               "parentFlowId:*{flowId} OR id:*{flowId}"
  //             ],
  //             "orders": [
  //               {
  //                 "key": "createTime",
  //                 "asc": "asc"
  //               }
  //             ],
  //             "current": 1,
  //             "size": 50,
  //             "start": 0,
  //             "limit": 50
  //           }
  //         }
  //       }
  //     }
  //   ]
  // },
  {
    "type": "confirm",
    "props": {
      "title": "撤销",
      "description": "您确定要撤销休假吗？",
      "style": {
        "marginTop": "20px",
        "textAlign": "center"
      }
    },
    "effect": {
      "show": "*{system_status} === 0",
      "fetch": {
        "type": "process",
        "processKey": "gs_abolish_leave",
        "defaultParams": {
          "entityId": "*{entityId}",
          "flowId": "*{flowId}"
        }
      },
      "onSuccess": {
        "type": "retry"
      }
    }
  }
]

const dataSource = {
  department: '技术部',
  groupKeyStr: '年假',
  system_status: '0'
}

// console.log(JSON.stringify(columns))

export default () => {
  return (
    <SchemaPage
      // columns={columns}
      columnsJson={JSON.stringify(columns)}
      dataSource={dataSource}
      onRetry={() => console.log('retry')}
    />
  )
}
