import React from 'react';
import { SchemaPage } from '@smartdeer-ui/pc'
import type { PageColumnsType } from '@smartdeer-ui/pc'

const columns: PageColumnsType[] = [
  {
    "type": "container",
    "children": [
      {
        "type": "h2",
        "children": "*{applyRealNameStr}"
      },
      {
        "type": "div",
        "children": "*{companyName}",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "12px",
            "color": "#757575"
          }
        }
      },
      {
        "type": "div",
        "children": "审批中",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "16px",
            "color": "#fb923c"
          }
        },
        "effect": {
          "show": "*{system_status} === 0"
        }
      },
      {
        "type": "div",
        "children": "已审批通过",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "16px",
            "color": "#4ade80"
          }
        },
        "effect": {
          "show": "*{system_status} === 1"
        }
      },
      {
        "type": "div",
        "children": "已拒绝审批",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "16px",
            "color": "#f87171"
          }
        },
        "effect": {
          "show": "*{system_status} === 2"
        }
      },
      {
        "type": "div",
        "children": "已取消",
        "props": {
          "style": {
            "marginTop": "10px",
            "fontSize": "16px",
            "color": "#9ca3af"
          }
        },
        "effect": {
          "show": "*{system_status} === 3"
        }
      }
    ]
  },
  {
    "type": "container",
    "props": {
      "style": {
        "marginTop": "20px"
      }
    },
    "children": [
      {
        "type": "h3",
        "children": "审批详情"
      },
      {
        "type": "listItem",
        "props": {
          "title": "所在部门",
          "description": "*{department}",
          "style": {
            "marginTop": "18px"
          }
        }
      },
      {
        "type": "listItem",
        "props": {
          "title": "休假类型",
          "description": "*{groupKeyStr}",
          "style": {
            "marginTop": "16px"
          }
        }
      },
      {
        "type": "listItem",
        "props": {
          "title": "开始时间",
          "description": "*{startFrom | formatTime(YYYY-MM-DD HH:mm)}",
          "style": {
            "marginTop": "16px"
          }
        }
      },
      {
        "type": "listItem",
        "props": {
          "title": "结束时间",
          "description": "*{endTo | formatTime(YYYY-MM-DD HH:mm)}",
          "style": {
            "marginTop": "16px"
          }
        }
      },
      {
        "type": "listItem",
        "props": {
          "title": "请假原因",
          "description": "*{reason}",
          "style": {
            "marginTop": "16px"
          }
        }
      }
    ]
  },

  {
    "type": "confirm",
    "props": {
      "title": "确认审核",
      // "description": "您确定要确认审核休假吗？",
      "style": {
        "marginTop": "20px",
        "textAlign": "center"
      },
      "children": [
        {
          "type": "datePicker",
          "field": "startTime",
          "title": "开始时间",
          "labelProps": {
            "rules": [{
              "required": true,
              "message": "请选择开始时间！"
            }]
          }
        },
        {
          "type": "textArea",
          "field": "remarks",
          "title": "备注",
        }
      ]
    },
    "effect": {
      "show": "*{system_status} === 0",
      "fetch": {
        "type": "process",
        "processKey": "gs_abolish_leave",
        "defaultParams": {
          "entityId": "*{entityId}",
          "flowId": "*{flowId}"
        }
      },
      "onSuccess": {
        "type": "retry"
      }
    }
  }
]

const dataSource = {
  department: '技术部',
  groupKeyStr: '年假',
  system_status: '0'
}

// console.log(JSON.stringify(columns))

export default () => {
  return (
    <SchemaPage
      // columns={columns}
      columnsJson={JSON.stringify(columns)}
      dataSource={dataSource}
    />
  )
}
