import React from 'react';
import ProGroup from '../group';
import type { SchemaRenderValueTypeFunction } from '../typing';
import { replaceKeysWithRegex } from '../../../utils/replaceKeysWithRegex'
import { isArray } from 'mathjs';

const classPrefix = `deer-field-group`;

const Group: SchemaRenderValueTypeFunction = (
  item,
  { genItems, index }
) => {

  if (item.type === 'group') {

    if (!item.children || !Array.isArray(item.children)) return null;

    const list = item?.props?.list || '[]'
    const children = item?.children || []
    const separator = item?.props?.separator || ''
    const bodyStyle = item?.props?.bodyStyle || {}

    let newList;
    try {
      newList = JSON.parse(list);
    } catch (e) {
      newList = []
    }

    if (newList.length === 0) return item.props?.empty ? <div key={index} dangerouslySetInnerHTML={{ __html: item.props?.empty }} /> : '-'

    return (
      <div
        key={index}
        className={classPrefix}
        style={{
          ...item.props?.style,
        }}
      >
        {item.props?.title && <div className={`${classPrefix}-title`}>{item.props?.title}</div>}

        {newList.map((row: any, index: number) => {
          const columns = replaceKeysWithRegex(children, { index: index + 1, ...row }) as any[];

          return (
            <ProGroup key={index} index={index} bodyStyle={bodyStyle}>
              {genItems(columns)}
              {index !== newList.length - 1 && separator}
            </ProGroup>
          );
        })}
      </div>
    );
  }

  return true;
};

export default Group;
