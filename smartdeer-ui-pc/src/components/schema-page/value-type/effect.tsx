import React from 'react';

import ProEffect from '../effect';
import type { SchemaRenderValueTypeFunction } from '../typing';

const Effect: SchemaRenderValueTypeFunction = (item, { genItems, index }) => {
  if (item.type === 'effect') {
    if (!item.children || !Array.isArray(item.children)) return null;

    return (
      <React.Fragment key={index}>
        <ProEffect {...item} genItems={genItems} />
      </React.Fragment>
    );
  }

  return true;
};

export default Effect;
