
import React from 'react';
import type { PageColumnsType } from '../typing';
import FieldComponent from '../field-components'
import { isString, isArray, isObject } from 'lodash';
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'

const field = (
  item: PageColumnsType,
  helpers: Record<string, any>,
) => {
  const { index, genItems } = helpers

  // let childNode: React.ReactNode = null;

  if (item.effect && item.effect.show) {

    const show = evaluateLogicalExpression({}, item.effect.show)

    if (!show) return <React.Fragment key={[item.field, index || 0].join('_')} />
  }

  let children: React.ReactNode

  if (isArray(item.children)) {
    children = genItems(item.children)
  } else if (isObject(item.children)) {
    children = genItems([item.children])
  } else if (isString(item.children)) {
    children = item.children
  }

  const getField = () => {
    return (
      <FieldComponent
        key={[item.field, index || 0].join('_')}
        valueType={item.type}
        fieldProps={item.props}
        renderChildren={children as React.ReactNode}
        effect={item.effect}
      >
      </FieldComponent>
    );
  }

  return getField();
};

export default field
