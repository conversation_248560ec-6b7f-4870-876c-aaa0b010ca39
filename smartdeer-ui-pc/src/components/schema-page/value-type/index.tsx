
import React from 'react';
import type { PageColumnsType, SchemaRenderValueTypeFunction } from '../typing';
import FieldComponent from '../field-components'
import { isString, isArray, isObject } from 'lodash';
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'
import field from './field'
import group from './group'
import effect from './effect'

const tasks: SchemaRenderValueTypeFunction[] = [
  group,
  effect
];

const renderValueType = (
  item: PageColumnsType,
  helpers: Record<string, any>,
) => {

  for (let cur = 0; cur < tasks.length; cur++) {
    const task = tasks[cur];
    const dom = task(item, helpers);

    // False 不再遍历
    // if (dom === false) {
    //   return false;
    if (dom === true) {
      // True 继续下一次
      continue;
    } else {
      // Other Is Dom
      return dom;
    }
  }

  return field(item, helpers);
};

export default renderValueType
