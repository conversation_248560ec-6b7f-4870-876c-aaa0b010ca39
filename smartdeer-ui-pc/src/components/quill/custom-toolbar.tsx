import React from 'react';
import { Tooltip } from 'antd';
import { ConfigContext } from '../config-provider'

export interface CustomToolbarProps {
  id: string
}

const CustomToolbar: React.FC<CustomToolbarProps> = (props) => {
  const { id } = props

  const { language } = React.useContext(ConfigContext)

  return (
    <div id={id} >
      <Tooltip title={language.quill.toolbar.size} placement='top'>
        <select className='ql-size' defaultValue={'selected'}>
          <option value='small'>small</option>
          <option value='selected'>normal</option>
          <option value='large'>large</option>
          <option value='huge'>huge</option>
        </select>
      </Tooltip>

      <Tooltip title={language.quill.toolbar.h1} placement='top'>
        <button type='button' className='ql-header' value='1' >
          <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path d="M183.36 192H63.936v640h119.424V555.2h308.48V832h119.424V192H491.84v260.992h-308.48V192z m776.576 0h-89.216l-2.24 2.24c-20.8 21.312-47.04 41.024-78.72 60.096a375.68 375.68 0 0 1-89.216 35.968l-5.632 1.408v116.992l9.536-2.688c53.76-15.168 98.944-37.568 136.064-67.328V832H960V192z" fill="#3c3c3c"></path></svg>
        </button>
      </Tooltip>

      <Tooltip title={language.quill.toolbar.h2} placement='top'>
        <button type='button' className='ql-header' value='2' />
      </Tooltip>


      <Tooltip title={language.quill.toolbar.h3} placement='top'>
        <button type='button' className='ql-header' value='3' >
          <svg viewBox="0 0 18 18">
            <path fill="#3c3c3c" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z" />
          </svg>
        </button>
      </Tooltip>

      <Tooltip title={language.quill.toolbar.h4} placement='top'>
        <button type='button' className='ql-header' value='4' >
          <svg viewBox="0 0 18 18">
            <path fill="#3c3c3c" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z" />
          </svg>
        </button>
      </Tooltip>

      <Tooltip title={language.quill.toolbar.h5} placement='top'>
        <button type='button' className='ql-header' value='5' >
          <svg viewBox="0 0 18 18">
            <path fill="#3c3c3c" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z" />
          </svg>
        </button>
      </Tooltip>

      <Tooltip title={language.quill.toolbar.align} placement='top'>
        <select className='ql-align' defaultValue={''}>
          <option></option>
          <option value='center'></option>
          <option value='right'></option>
          <option value='justify'></option>
        </select>
      </Tooltip>

      <Tooltip title={language.quill.toolbar.direction} placement='top'>
        <button type='button' className='ql-direction' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.bold} placement='top'>
        <button type='button' className='ql-bold' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.italic} placement='top'>
        <button type='button' className='ql-italic' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.underline} placement='top'>
        <button type='button' className='ql-underline' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.strike} placement='top'>
        <button type='button' className='ql-strike' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.color} placement='top'>
        <select className='ql-color' defaultValue={'#000000'}>
          <option value='#000000'></option>
          <option value="#e60000"></option>
          <option value="#ff9900"></option>
          <option value="#ffff00"></option>
          <option value="#008a00"></option>
          <option value="#0066cc"></option>
          <option value="#9933ff"></option>
          <option value="#ffffff"></option>
          <option value="#facccc"></option>
          <option value="#ffebcc"></option>
          <option value="#ffffcc"></option>
          <option value="#cce8cc"></option>
          <option value="#cce0f5"></option>
          <option value="#ebd6ff"></option>
          <option value="#bbbbbb"></option>
          <option value="#f06666"></option>
          <option value="#ffc266"></option>
          <option value="#ffff66"></option>
          <option value="#66b966"></option>
          <option value="#66a3e0"></option>
          <option value="#c285ff"></option>
          <option value="#888888"></option>
          <option value="#a10000"></option>
          <option value="#b26b00"></option>
          <option value="#b2b200"></option>
          <option value="#006100"></option>
          <option value="#0047b2"></option>
          <option value="#6b24b2"></option>
          <option value="#444444"></option>
          <option value="#5c0000"></option>
          <option value="#663d00"></option>
          <option value="#666600"></option>
          <option value="#003700"></option>
          <option value="#002966"></option>
          <option value="#3d1466"></option>
        </select>
      </Tooltip>

      <Tooltip title={language.quill.toolbar.background} placement='top'>
        <select className='ql-background' defaultValue={'#000000'}>
          <option value='#000000'></option>
          <option value="#e60000"></option>
          <option value="#ff9900"></option>
          <option value="#ffff00"></option>
          <option value="#008a00"></option>
          <option value="#0066cc"></option>
          <option value="#9933ff"></option>
          <option value="#ffffff"></option>
          <option value="#facccc"></option>
          <option value="#ffebcc"></option>
          <option value="#ffffcc"></option>
          <option value="#cce8cc"></option>
          <option value="#cce0f5"></option>
          <option value="#ebd6ff"></option>
          <option value="#bbbbbb"></option>
          <option value="#f06666"></option>
          <option value="#ffc266"></option>
          <option value="#ffff66"></option>
          <option value="#66b966"></option>
          <option value="#66a3e0"></option>
          <option value="#c285ff"></option>
          <option value="#888888"></option>
          <option value="#a10000"></option>
          <option value="#b26b00"></option>
          <option value="#b2b200"></option>
          <option value="#006100"></option>
          <option value="#0047b2"></option>
          <option value="#6b24b2"></option>
          <option value="#444444"></option>
          <option value="#5c0000"></option>
          <option value="#663d00"></option>
          <option value="#666600"></option>
          <option value="#003700"></option>
          <option value="#002966"></option>
          <option value="#3d1466"></option>
        </select>
      </Tooltip>

      <Tooltip title={language.quill.toolbar.sub} placement='top'>
        <button type='button' className='ql-script' value='sub' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.super} placement='top'>
        <button type='button' className='ql-script' value='super' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.blockquote} placement='top'>
        <button type='button' className='ql-blockquote' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.formula} placement='top'>
        <button type='button' className='ql-formula' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.codeBlock} placement='top'>
        <button type='button' className='ql-code-block' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.ordered} placement='top'>
        <button type='button' className='ql-list' value='ordered' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.bullet} placement='top'>
        <button type='button' className='ql-list' value='bullet' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.increaseIndent} placement='top'>
        <button type='button' className='ql-indent' value='-1' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.decreaseIndent} placement='top'>
        <button type='button' className='ql-indent' value='+1' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.table} placement='top'>
        <button type='button' className='ql-table' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.link} placement='top'>
        <button type='button' className='ql-link' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.image} placement='top'>
        <button type='button' className='ql-image' />
      </Tooltip>

      <Tooltip title={language.quill.toolbar.clean} placement='top'>
        <button type='button' className='ql-clean' />
      </Tooltip>
    </div>
  );
};

export default CustomToolbar
