---
toc: content
group: 
  title: 数据录入
  order: 10
---

# Quill 富文本编辑器

富文本编辑器	

## 示例

<code src="./demos/index.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| id | 唯一的,同一个页面多次使用`Quill`时候使用 | `string` | `--` | |
| uploadImgApi | 上传图片的接口地址 | `string` | `--` | |
| value | 输入值 | `string` | `--` | |
| onChange | 输入框内容变化时的回调 | `(value: string) => void` | `--` |  |
| fileExpire | 文件过期时间 (此功能需要配合图片token使用) | `boolean` | `false` |  |
