import React from 'react';
import { Upload, Button, message } from 'antd';
import type { UploadProps } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import S from '../../utils/storage'
import { ConfigContext } from '../config-provider'

// 兼容代码-----------
import 'antd/lib/upload/style';
//------------

const imageFileTypes = [
  'image/apng',
  'image/gif',
  'image/jpeg',
  'image/png',
];

export interface UploadImgRef {
  click: () => void
}

export type UploadStatusType = 'error' | 'done' | 'uploading' | 'removed'

interface UploadImgProps {
  api?: string;
  onSuccess?: (file: string) => void;
  onChangeStatus?: (status: UploadStatusType) => void;
}

const UploadImg = React.forwardRef<UploadImgRef, UploadImgProps>((props, ref) => {

  const { api: actionUrl, onSuccess, onChangeStatus } = props

  const { language, isFileToken } = React.useContext(ConfigContext)


  const uploadRef = React.useRef<any>(null);

  const token = S.getAuthToken()

  const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
    const check = imageFileTypes.includes(file.type)

    if (!check) {
      message.error(`${file.name} ${language.upload.defaultValidateMessages.isNotSupported}`);
    }

    return check
  }

  const handleChange: UploadProps['onChange'] = ({ file }) => {
    if (file.status) {
      onChangeStatus?.(file.status)
    }

    if (file.status === 'done') {

      let fileUrl = ''

      if (isFileToken) {
        fileUrl = file.response.data

      } else {
        fileUrl = file.response.data.absoluteFileUrl
      }

      onSuccess?.(fileUrl)
    }
  }

  React.useImperativeHandle(ref, () => ({
    click: () => {
      if (!uploadRef.current) {
        return
      }

      uploadRef.current.click()
    }
  }))

  return (
    <Upload
      accept='.jpg,.jpeg,.png'
      action={actionUrl}
      headers={{ 'Authorization': token }}
      beforeUpload={handleBeforeUpload}
      onChange={handleChange}
    >
      <Button ref={uploadRef} icon={<UploadOutlined />}>点击上传</Button>
    </Upload>
  );
});

export default UploadImg
