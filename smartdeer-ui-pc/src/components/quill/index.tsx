import React from 'react';
import classNames from 'classnames';
import Delta from 'quill-delta';
import Quill from 'quill';
import type { Range } from 'quill';
import { Spin, message, Select, Space } from 'antd';
import * as QuillTableUI from 'quill-table-ui'
import CustomToolbar from './custom-toolbar'
import UploadImg from './upload-img'
import { mergeProps } from '../../utils/withDefaultProps'
import type { UploadImgRef, UploadStatusType } from './upload-img'
import { ConfigContext } from '../config-provider'
// import hljs from 'highlight.js';
import S from '../../utils/storage'
import { STORE } from '../../consts'
import { swrUploadFile } from '../../utils/swrFetcher'
import { useFileAccessToken, useParamsDict } from '../../utils/hooks'
import { hasImageTag, formatQueryString } from '../../utils'
import { getSearchParams } from '../../utils/getSearchParams'
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate'
import { isObject } from 'lodash';


import 'quill-table-ui/dist/index.css'
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
// import 'highlight.js/styles/atom-one-dark.css'

import './index.less'
import queryString from 'query-string';

Quill.register({
  'modules/tableUI': QuillTableUI.default,
}, true)

const classPrefix = `deer-quill`

const TOOLBAR_ID = 'deer-quill-toolbar'

export interface QuillProps {
  className?: string;
  style?: React.CSSProperties;

  id?: string;
  value?: string;
  placeholder?: string;
  readonly?: boolean;
  disabled?: boolean;
  onChange?: (value: string) => void;

  fileExpire?: boolean;
  uploadImgApi?: string;
}

export interface QuillEditorRef {
  getEditor?: () => Quill;
}

const defaultProps = {
  id: 'id',
  value: '',
  placeholder: '',
  readonly: false,
  disabled: false,
  fileExpire: false
}

// https://quilljs.com/playground/react
// https://codepen.io/volser/pen/QWWpOpr
// https://github.com/volser/quill-table-ui
// https://github.com/zenoamaro/react-quill

const QuillEditor = React.forwardRef<QuillEditorRef, QuillProps>((p, ref) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,

    id,
    value,
    placeholder,
    readonly,
    disabled,
    onChange,

    fileExpire,
    uploadImgApi,
  } = props

  const { language, isFileToken, appApiBaseUrl, appDefaultEntityId, appFileTokenUploadApi, appFileTokenCommonUploadApi } = React.useContext(ConfigContext)
  const paramsDict = useParamsDict()

  const { getTheCompleteFileUrl } = useFileAccessToken()

  const [fileExpireTime, setFileExpireTime] = React.useState<number>()

  const [loading, setLoading] = React.useState<boolean>(false);
  const [hasImage, setHasImage] = React.useState(false)
  const [currentFileToken, setCurrentFileToken] = React.useState('')

  const containerRef = React.useRef<any>(null);
  const quillRef = React.useRef<any>(null);
  const uploadRef = React.useRef<UploadImgRef>(null);
  const isInitializedRef = React.useRef(false);

  const toolbarId = TOOLBAR_ID + '_' + id

  const newUploadImgApi = React.useMemo(() => {
    let apiUrl = ''

    if (uploadImgApi) {
      apiUrl = uploadImgApi.includes('http') ? uploadImgApi : `${appApiBaseUrl}${uploadImgApi}`

    } else if (isFileToken) {
      if (paramsDict.entityUUID) {
        apiUrl = `${appApiBaseUrl}${appFileTokenUploadApi}`
        apiUrl = replaceVariablesInTemplate(paramsDict, apiUrl)
      } else if (paramsDict.entityId !== appDefaultEntityId) {
        apiUrl = `${appApiBaseUrl}${appFileTokenUploadApi}`
        apiUrl = replaceVariablesInTemplate(paramsDict, apiUrl)
      } else {
        apiUrl = `${appApiBaseUrl}${appFileTokenCommonUploadApi}`
      }

    } else {
      apiUrl = `${appApiBaseUrl}/v1/common/file/uploadImage`
    }

    return apiUrl
  }, [uploadImgApi])


  React.useImperativeHandle(ref, () => {
    return {
      getEditor: () => {
        return quillRef.current;
      },
    };
  })

  const handlerUploader = async (range: Range, files: File[]) => {
    if (!quillRef.current) {
      return
    }

    if (files.length > 3) {
      message.error(language.upload.defaultValidateMessages.cannotExceed3)
      return
    }

    setLoading(true)

    try {
      const promises = files.map(async (item) => {
        const res = await swrUploadFile(newUploadImgApi, item)

        let fileUrl = ''

        if (isFileToken) {
          fileUrl = await getTheCompleteFileUrl(res.data)

          fileUrl = queryString.stringifyUrl({ url: fileUrl, query: { fileKey: res.data } })

        } else {
          fileUrl = res.data.absoluteFileUrl
        }

        return fileUrl
      })

      const images = await Promise.all(promises)

      const update = images.reduce((delta: Delta, fileUrl) => {
        return delta.insert({ image: fileUrl });
      }, new Delta().retain(range.index).delete(range.length)) as Delta;

      quillRef.current.updateContents(update, 'user');
      quillRef.current.setSelection(
        range.index + images.length,
        'silent',
      );

    } catch (err: any) {
      message.error(language.upload.defaultValidateMessages.imageUploadFailed)
    }

    setLoading(false)
  }

  const bindings = {
    // This will overwrite the default binding also named 'tab'
    keydown: {
      key: 'Enter',
      handler: function (range: any, context: any) {
        quillRef.current.setSelection(
          range.index + 1,
          'silent',
        );

        quillRef.current.setSelection(2); //光标位置加1
      }
    },
  };


  React.useEffect(() => {
    if (!containerRef.current) return

    const container = containerRef.current;

    const editorContainer = containerRef.current.appendChild(
      container.ownerDocument.createElement('div'),
    );

    const quill = new Quill(editorContainer, {
      theme: 'snow',
      debug: false,
      modules: {
        toolbar: '#' + toolbarId,
        table: true,
        tableUI: true,
        // uploader: {
        //   handler: handlerUploader
        // },
        // syntax: {
        //   hljs,
        //   useBR: false,
        // },
        // keyboard: {
        //   bindings: bindings
        // }
      },

      placeholder: placeholder,
    });

    quillRef.current = quill

    const table: any = quill.getModule('table');

    const toolbar: any = quill.getModule('toolbar');

    toolbar.addHandler('link', () => {
      const range = quill.getSelection();
      if (range === null || range.length === 0) return;
      let preview = quill.getText(range);
      if (
        /^\S+@\S+\.\S+$/.test(preview) &&
        preview.indexOf('mailto:') !== 0
      ) {
        preview = `mailto:${preview}`;
      }

      const theme: any = quill.theme

      theme.tooltip.edit('link', preview);
    });

    toolbar.addHandler('table', () => {
      table.insertTable(3, 3);
    });

    toolbar.addHandler('image', () => {
      if (!uploadRef.current) {
        return
      }

      uploadRef.current.click()
    });

    quill.on(Quill.events.TEXT_CHANGE, (...args) => {
      const html = quill.root.innerHTML

      const hasImageTagResult = hasImageTag(html);

      // setHasImage(hasImageTagResult)

      isInitializedRef.current = true
      onChange?.(html)
    });

    return () => {
      container.innerHTML = '';
      containerRef.current = null
      quillRef.current = null
    };

  }, []);

  React.useEffect(() => {
    if (!quillRef.current) return

    quillRef.current.uploader.options = {
      ...quillRef.current.uploader.options,
      handler: handlerUploader
    }


    // quillRef.current.keyboard.addBinding({ key: 'Enter' }, {
    //   // empty: true,    // implies collapsed: true and offset: 0
    //   // format: ['list']
    // }, function (range, context) {
    //   // this.quill.format('list', false);
    // });

  }, [quillRef])

  const initQuillValue = async () => {
    if (!quillRef.current || !value) return

    let htmlContent = value;

    const isForceUpdate = htmlContent.includes('data-update="force"')
    const isInitialized = isInitializedRef.current;

    if (!isForceUpdate && isInitialized) {
      return; // 提前返回，避免不必要的处理
    }

    const delta = quillRef.current.clipboard.convert({ html: htmlContent as string })

    let ops = delta.ops;

    if (isFileToken) {

      const promiseOps = delta.ops.map(async (op: any) => {
        if (isObject(op.insert) && op.insert['image']) {
          const src = op.insert['image']

          const paramsObj = getSearchParams(src)

          const fileKey = paramsObj.fileKey

          if (fileKey) {
            let fileUrl = await getTheCompleteFileUrl(fileKey)

            fileUrl = queryString.stringifyUrl({ url: fileUrl, query: { fileKey: fileKey } })

            op.insert['image'] = fileUrl
          }
        }

        return op;
      });

      ops = await Promise.all(promiseOps)
    }

    quillRef.current.setContents({ ops });
    isInitializedRef.current = true

    setLoading(false)
  }

  React.useEffect(() => {
    initQuillValue()
  }, [isInitializedRef.current, value, quillRef]);

  React.useEffect(() => {
    if (!quillRef.current) return

    quillRef.current?.enable(!(readonly || disabled));

  }, [quillRef, readonly, disabled]);


  const handleUploadImgCallback = React.useCallback(async (url: string) => {
    if (!quillRef.current) {
      return
    }

    const quill = quillRef.current; //编辑器本身
    const cursorPosition = quill?.selection?.savedRange?.index || 0; //获取当前光标位置;

    let fileUrl = url

    if (isFileToken) {
      fileUrl = await getTheCompleteFileUrl(url)
      fileUrl = queryString.stringifyUrl({ url: fileUrl, query: { fileKey: url } })
    }

    quill.insertEmbed(cursorPosition, 'image', fileUrl, 'api'); //插入图片
    quill.setSelection(cursorPosition + 1); //光标位置加1

  }, [quillRef])

  const handleChangeUploadStatus = (value: UploadStatusType) => {
    if (value === 'uploading') {
      setLoading(true)
    } else {
      setLoading(false)
    }
  }

  const handleChangeFileExpireTime = async (value: number | undefined) => {
    setLoading(true)

    // try {
    //   const { data } = await swrFetcher(appFileTokenApi as string, 'GET', {
    //     expireTime: value || Number(appFileTokenExpireTime),
    //   });

    //   const fileToken = formatQueryString(data);

    //   const htmlContent = updateImageLinks(props.value, fileToken)

    //   quillRef.current.setContents(quillRef.current.clipboard.convert({ html: htmlContent as string }));

    //   setCurrentFileToken(fileToken)

    //   setFileExpireTime(value)
    // } catch (err: any) {

    // }

    setLoading(false)
  }

  // const isShowFileExpire = React.useMemo(() => {
  //   return isFileToken && fileExpire && hasImage
  // }, [fileExpire, isFileToken, hasImage])

  return (
    <Spin
      spinning={loading}
      tip={language.upload.imageUploading}
    >
      <div className={classNames(classPrefix, className)} style={{ ...style }}>
        {/* <div className={`${classPrefix}-tools`}>
          {isShowFileExpire && (
            <Select
              allowClear
              value={fileExpireTime}
              size='small'
              style={{ width: 180 }}
              placeholder={language.quill.fileExpire.placeholder}
              onChange={handleChangeFileExpireTime}
              options={[
                { value: 864000000, label: language.quill.fileExpire.day10 },
                { value: 2592000000, label: language.quill.fileExpire.day30 },
                { value: 5184000000, label: language.quill.fileExpire.day60 },
                { value: 7776000000, label: language.quill.fileExpire.day90 },
                { value: 15552000000, label: language.quill.fileExpire.day180 },
                { value: 31104000000, label: language.quill.fileExpire.day360 },
              ]}
            />
          )}
        </div> */}

        {/* {(isShowFileExpire && !fileExpireTime) && (
          <div className={`${classPrefix}-expire-tip`}>
            <p>{language.quill.fileExpire.tip}</p>
          </div>
        )} */}

        <CustomToolbar id={toolbarId} />

        <div ref={containerRef} className={`${classPrefix}-content`} />

        <div style={{ display: 'none' }}>
          <UploadImg
            ref={uploadRef}
            api={newUploadImgApi}
            onSuccess={handleUploadImgCallback}
            onChangeStatus={handleChangeUploadStatus}
          />
        </div>
      </div>
    </Spin>
  );
});

export default QuillEditor;
