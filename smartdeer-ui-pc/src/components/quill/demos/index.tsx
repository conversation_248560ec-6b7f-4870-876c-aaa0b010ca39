import React from 'react';
import { Quill } from '@smartdeer-ui/pc'

export default () => {

  const [value, setValue] = React.useState('<table><tbody><tr><td data-row="row-4z3w">312<br></td><td data-row="row-4z3w"><br></td><td data-row="row-4z3w"><br></td></tr><tr><td data-row="row-91r2"><br></td><td data-row="row-91r2"><br></td><td data-row="row-91r2"><br></td></tr><tr><td data-row="row-sn3n"><br></td><td data-row="row-sn3n"><br></td><td data-row="row-sn3n"><br></td></tr></tbody></table>');

  const handleChange = (value: string) => {

    console.log('handleChange', value)

    setValue(value)
  }

  return (

    <Quill
      value={value}
      onChange={handleChange}
    />
  )
}
