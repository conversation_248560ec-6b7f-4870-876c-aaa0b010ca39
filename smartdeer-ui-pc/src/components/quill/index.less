.deer-quill {
  display: flex;
  flex-direction: column;
  background: white;
  position: relative;

  &-tools {
    position: absolute;
    right: 0;
    top: -24px;

    &-label {
      font-size: 12px;
    }
  }

  &-expire-tip {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    z-index: 10;
    background: rgb(255 255 255 / 50%);
    color: red;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-content{
    flex:  1 1;

    .ql-container {
      height: 100%;
    }

    .ql-editor {
      height: 100%;
      min-height: 500px;
      max-height: 1200px;
      overflow-y: auto;

      h1,h2,h3,h4,h5,h6 {
        margin-top: 10px;
      }

      img {
        display: inline-block;
        width: 100%;
        max-width: 800px !important;
        min-width: 250px !important;
      }

      a {
        color: var(--icb-color-t1);
        transition: all 0.3s;
        cursor: pointer;
        text-decoration: none;

        &:hover {
          opacity: 0.7;
        }
      }

      td {
        border: 1px solid #d0d7de;
        padding: 16px;
      }

      p {
        span {
          padding: 1px 5px 3px 5px;
          border-radius: 10px;

          &:first-child {
            padding-left: 0;
          }
        }
      }

      .ql-ui {
        padding: 0;
      }

      table {
        margin-top: 20px;
        margin-bottom: 20px;
        line-height: 20px;
        font-size: 1em;
      }
    }

    .ql-snow .ql-editor pre.ql-syntax {
      background-color: #f0f0f0;
      color: #000;
    }
  }

  .ql-toolbar.ql-snow {
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    border: 1px solid #BFBFBF !important;
  }

  .ql-container.ql-snow {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    border: 1px solid #BFBFBF !important;
  }

  &-full {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
