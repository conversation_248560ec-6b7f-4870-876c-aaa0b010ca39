import type { LanguageType } from '.';

const languageValues: LanguageType = {
  locale: 'en-US',

  common: {
    day: '日',
  },

  message: {
    success: '操作が成功しました',
    error: '操作に失敗しました',
  },

  form: {
    submitText: '提出する',
    resetText: 'リセット',
    deleteText: '削除＃サクジョ＃',
    addText: '追加',

    popconfirm: {
      title: '本当に削除しますか？',
      okText: '削除＃サクジョ＃',
      cancelText: 'キャンセル',
    },

    defaultItemLabel: {
      startTime: '始まる時間',
      endTime: '終了時間',
    },

    defaultValidateMessages: {
      upload: {
        default: 'ファイルをアップロードしてください。',
      },
      hashkeyGsLeave: {
        blockLeave: '強制休暇期間には最低5営業日の連続休暇が必要です。',
      },
    },
  },

  approvalTimeline: {
    action: {
      approving: '承認する',
      sending: '承認の開始',
      approved: '承認された',
      rejected: '拒否されました',
      abolished: '廃止',
      approvalFailed: '承認に失敗しました',
    },
  },

  auditTimeline: {
    process: {
      submit: 'コミット',
      approve: '承認',
      cc: 'CC',
      end: '終了',
      cancel: 'キャンセル済み',
    },
    status: {
      submit: 'コミット済み',
      pending: '承認待ち',
      approved: '通過しました',
      rejected: '拒否されました',
      cancelled: 'キャンセル済み',
      ended: '終了しました',
      notStarted: '開始していません',
    },
    title: {
      system: 'システム',
    },
    description: {
      autoCC: '自動CC {names} {num}人',
    },
    reasonForRejection: '却下理由：',
    comments: 'コメント',
    commentsAttachments: 'コメント添付ファイル',
    paymentCycle: '支給サイクル（今月の給与と一緒に支給されます）',
    approvedVouchers: '核定証憑',
    paymentDate: '支払日',
    paymentAttachment: '支払い添付ファイル',
    remarks: '備考',
    remarksAttachments: '備考添付ファイル',
  },

  approvalProcess: {
    cc: 'cc',
    people: '人々',
  },

  modal: {
    okText: '確認する',
    cancelText: 'キャンセル',
  },

  jsonEditor: {
    fullScreen: '画面上',
    import: '輸入',
    export: '輸出',
    clear: 'クリア',
    retract: '撤回',
  },

  codeEditor: {
    programmingLanguage: 'プログラミング言語',
  },

  dynamicForm: {
    messageCreateSuccess: '成功を生み出した',
    messageUpdateSuccess: '更新成功',
  },

  tableFilter: {
    filterCriteria: 'フィルタ基準:',
    addCriteria: '基準を追加 +',
    actions: {
      eq: 'に等しい',
      like: '含む',
      gt: 'より大きい',
      lt: '未満',
    },
  },

  TableSearch: {
    actions: {
      clear: 'はっきりした',
      search: '検索けんさく',
      downloadExcel: 'Excelのダウンロード',
      expand: 'てんかい',
      collapse: 'やめる',

      download: {
        executing: 'ファイル生成中... ページを閉じませんでください',
        downloading: 'ダウンロード中... ページを閉じませんでください',
        downloadingTip:
          'ページを早めに閉じるとダウンロードに失敗する場合があります',
        gotIt: '了解です',
        successfully: 'ダウンロード成功',
        failed: 'ダウンロード失敗',
        generated: 'ファイルが生成されました。ダウンロードをお願いいたします！',
        downloadNow: '今すぐダウンロード',
      },
    },
  },

  dynamicTable: {
    newButtonText: '追加',
  },

  button: {
    defaultValidateMessages: {
      fileTypeError: 'ファイルタイプエラー',
      jsonIsNotDefined: 'JSONが定義されていません',
      jsonTypeError: 'JSONタイプエラー',
    },
    exportJson: {
      fileName: 'ファイル名',
    },
    sendCode: {
      sendCode: '認証コードを送信',
      sending: '送信中',
      seconds: '秒',
    },
  },

  commentEditor: {
    submit: '提出する',
  },

  upload: {
    defaultValidateMessages: {
      fileTypeError: 'ファイルタイプエラー',
      imageUploadFailed: '画像のアップロードに失敗しました',
      cannotExceed3: '一度にアップロードできる画像の数は 3 つまでです',
      isNotSupported: 'このファイルタイプはサポートされていません！',
      fileSizeError: 'ファイルサイズは {size}MB 以下である必要があります。',
    },
    imageUploading: '画像のアップロード',
    onClickText: 'クリックしてアップロード',
  },

  quill: {
    toolbar: {
      size: 'ワードサイズ',
      h1: 'タイトルH1',
      h2: 'タイトルH2',
      h3: 'タイトルH3',
      h4: 'タイトルH4',
      h5: 'タイトルH5',
      align: '整列',
      direction: '方向',
      bold: '大胆な',
      italic: 'イタリック',
      underline: '下線',
      strike: 'ストライク',
      color: '色',
      background: '背景',
      sub: 'サブ',
      super: '素晴らしい',
      blockquote: 'ブロック引用',
      formula: '式',
      codeBlock: 'コードブロック',
      ordered: '順序付きリスト',
      bullet: '順序なしリスト',
      increaseIndent: 'インデントを増やします',
      decreaseIndent: 'インデントを減らす',
      table: 'table',
      link: 'リンクを追加',
      image: '画像の挿入',
      clean: 'クリーン',
    },

    fileExpire: {
      tip: 'セキュリティとプライバシーの保護のために、すべての画像とファイルに有効期限を設定する必要があります。',
      placeholder: 'ファイルの有効期限',
      day10: '10日間',
      day30: '30日間',
      day60: '60日間',
      day90: '90日間',
      day180: '180日間',
      day360: '360日間',
    },
  },

  table: {
    details: '詳しく',
  },

  popDownload: {
    successfully: 'ダウンロード成功',
    selectLanguage: '給与明細の言語を選択してください。デフォルトは英語です。',
    setPasswordType: '給与明細のパスワードタイプを設定する',
    systemGenerated: 'システム生成',
    custom: 'カスタム',
    setPassword: 'パスワードを設定する',
    enterPassword: 'パスワードを入力してください',
    passwordRules:
      'パスワードには大文字、小文字、数字、特殊文字（例：!@#$%^&*）を含める必要があります',
    systemGeneratedPassword: 'システム生成パスワード',
    saveTip:
      '「ダウンロード」ボタンをクリックした後、パスワードは消えますので、ダウンロード前に必ず保存してください。パスワードは：',
    pleaseInput: 'パスワードを入力してください',
    atLeast: '6文字以上を入力してください',
  },

  fileView: {
    download: 'ダウンロード',
  },

  f2a: {
    securityVerification: '安全验证',
    verifyTip: '为了保护信息隐私和安全，我们需要验证您的身份。',
    emailVerification: '通过电子邮件验证',
    mobileVerification: '通过手机验证',
    sendVerificationCode: '立即发送验证码',
    enterVerificationCode: '输入验证码',
    codeTip: '安全校验码已发送到{userEmail}，验证码5分钟内有效',
    incorrectCode: '验证码错误，请重试',
    enterCode: '请输入验证码',
    sendCode: '发送验证码',
    seconds: '秒',
    previousStep: '上一步',
    verificationSuccessful: '安全验证成功！',
    timeLimitTip: '您已获得 {timeStr}的免验证访问权限，请在时限内完成您的操作',
    gotIt: '我知道了',
    confirm: '确认',
    mobile: '手机',
    email: '电子邮件',
    minutes: '分钟',
    sending: '发送中',
  },

  businessComponent: {
    securityCheckModal: {
      securityVerification: '安全验证',
      verifyTip: '为了保护信息隐私和安全，我们需要验证您的身份。',
      emailVerification: '通过电子邮件验证',
      mobileVerification: '通过手机验证',
      sendVerificationCode: '立即发送验证码',
      enterVerificationCode: '输入验证码',
      codeTip: '安全校验码已发送到{userEmail}，验证码5分钟内有效',
      incorrectCode: '验证码错误，请重试',
      enterCode: '请输入验证码',
      sendCode: '发送验证码',
      seconds: '秒',
      previousStep: '上一步',
      verificationSuccessful: '安全验证成功！',
      timeLimitTip:
        '您已获得 {timeStr}的免验证访问权限，请在时限内完成您的操作',
      gotIt: '我知道了',
      confirm: '确认',
      mobile: '手机',
      email: '电子邮件',
      minutes: '分钟',
      sending: '发送中',
    },
  },

  error: {
    title: 'ページを更新してください',
    description:
      '申し訳ありません、データの読み込み中にエラーが発生しました。ネットワークの問題やシステムの異常が原因である可能性があります。ページを更新して再度お試しください。問題が解決しない場合は、カスタマーサポートまでご連絡ください。できるだけ早く対応いたします！',
    button: 'ページを更新',
  },
};

export default languageValues;
