import type { ValidateMessages } from 'rc-field-form/lib/interface';

import EnUS from './en-US';
import Ja<PERSON> from './ja-JP';
import ZhCh from './zh-CN';
import ZhTW from './zh-TW';

export const ANT_MARK = 'internalMark';

type ValidateMessagesExtra = {
  upload: {
    default: string;
  };
  hashkeyGsLeave: {
    blockLeave: string;
  };
};

export type LanguageType = {
  locale: string;

  common: {
    day: string;
  };

  message: {
    success: string;
    error: string;
  };

  form: {
    submitText: string;
    resetText: string;
    deleteText: string;
    addText: string;

    popconfirm: {
      title: string;
      okText: string;
      cancelText: string;
    };

    defaultItemLabel: Record<string, string>;
    defaultValidateMessages: ValidateMessages & ValidateMessagesExtra;
  };

  approvalTimeline: {
    action: {
      sending: string;
      approving: string;
      approved: string;
      rejected: string;
      abolished: string;
      approvalFailed: string;
    };
  };

  approvalProcess: {
    cc: string;
    people: string;
  };

  auditTimeline: {
    process: {
      submit: string;
      approve: string;
      cc: string;
      end: string;
      cancel: string;
    };
    status: {
      submit: string;
      pending: string;
      approved: string;
      rejected: string;
      cancelled: string;
      ended: string;
      notStarted: string;
    };
    title: {
      system: string;
    };
    description: {
      autoCC: string;
    };
    reasonForRejection: string;
    comments: string;
    commentsAttachments: string;
    paymentCycle: string;
    approvedVouchers: string;
    paymentDate: string;
    paymentAttachment: string;
    remarks: string;
    remarksAttachments: string;
  };

  modal: {
    okText: string;
    cancelText: string;
  };

  jsonEditor: {
    fullScreen: string;
    import: string;
    export: string;
    clear: string;
    retract: string;
  };

  codeEditor: {
    programmingLanguage: string;
  };

  dynamicForm: {
    messageCreateSuccess: string;
    messageUpdateSuccess: string;
  };

  tableFilter: {
    filterCriteria: string;
    addCriteria: string;

    actions: {
      eq: string;
      like: string;
      gt: string;
      lt: string;
    };
  };

  TableSearch: {
    actions: {
      clear: string;
      search: string;
      downloadExcel: string;
      expand: string;
      collapse: string;
      download: {
        executing: string;
        downloading: string;
        downloadingTip: string;
        gotIt: string;
        successfully: string;
        failed: string;
        generated: string;
        downloadNow: string;
      };
    };
  };

  dynamicTable: {
    newButtonText: string;
  };

  button: {
    defaultValidateMessages: {
      fileTypeError: string;
      jsonIsNotDefined: string;
      jsonTypeError: string;
    };
    exportJson: {
      fileName: string;
    };
    sendCode: {
      sendCode: string;
      sending: string;
      seconds: string;
    };
  };

  commentEditor: {
    submit: string;
  };

  upload: {
    defaultValidateMessages: {
      fileTypeError: string;
      imageUploadFailed: string;
      cannotExceed3: string;
      isNotSupported: string;
      fileSizeError: string;
    };
    imageUploading: string;
    onClickText: string;
  };

  quill: {
    toolbar: {
      size: string;
      h1: string;
      h2: string;
      h3: string;
      h4: string;
      h5: string;
      align: string;
      direction: string;
      bold: string;
      italic: string;
      underline: string;
      strike: string;
      color: string;
      background: string;
      sub: string;
      super: string;
      blockquote: string;
      formula: string;
      codeBlock: string;
      ordered: string;
      bullet: string;
      increaseIndent: string;
      decreaseIndent: string;
      table: string;
      link: string;
      image: string;
      clean: string;
    };

    fileExpire: {
      tip: string;
      placeholder: string;
      day10: string;
      day30: string;
      day60: string;
      day90: string;
      day180: string;
      day360: string;
    };
  };

  table: {
    details: string;
  };

  popDownload: {
    successfully: string;
    selectLanguage: string;
    setPasswordType: string;
    systemGenerated: string;
    custom: string;
    setPassword: string;
    enterPassword: string;
    passwordRules: string;
    systemGeneratedPassword: string;
    saveTip: string;
    pleaseInput: string;
    atLeast: string;
  };

  fileView: {
    download: string;
  };

  f2a: {
    securityVerification: string;
    verifyTip: string;
    emailVerification: string;
    mobileVerification: string;
    sendVerificationCode: string;
    enterVerificationCode: string;
    codeTip: string;
    incorrectCode: string;
    enterCode: string;
    sendCode: string;
    seconds: string;
    previousStep: string;
    verificationSuccessful: string;
    timeLimitTip: string;
    gotIt: string;
    confirm: string;
    mobile: string;
    email: string;
    minutes: string;
    sending: string;
  };

  businessComponent: {
    securityCheckModal: {
      securityVerification: string;
      verifyTip: string;
      emailVerification: string;
      mobileVerification: string;
      sendVerificationCode: string;
      enterVerificationCode: string;
      codeTip: string;
      incorrectCode: string;
      enterCode: string;
      sendCode: string;
      seconds: string;
      previousStep: string;
      verificationSuccessful: string;
      timeLimitTip: string;
      gotIt: string;
      confirm: string;
      mobile: string;
      email: string;
      minutes: string;
      sending: string;
    };
  };

  error: {
    title: string;
    description: string;
    button: string;
  };
};

export default {
  'en-US': EnUS,
  'zh-CN': ZhCh,
  'zh-TW': ZhTW,
  'ja-JP': JaJP,
};
