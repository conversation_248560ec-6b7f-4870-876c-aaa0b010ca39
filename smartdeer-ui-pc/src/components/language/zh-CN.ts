import type { LanguageType } from '.';

const languageValues: LanguageType = {
  locale: 'zh-CN',

  common: {
    day: '天',
  },

  message: {
    success: '操作成功',
    error: '操作失败',
  },

  form: {
    submitText: '提交',
    resetText: '重置',
    deleteText: '删除',
    addText: '添加',

    popconfirm: {
      title: '确定要删除吗？',
      okText: '删除',
      cancelText: '取消',
    },

    defaultItemLabel: {
      startTime: '开始时间',
      endTime: '结束时间',
    },

    defaultValidateMessages: {
      upload: {
        default: '请上传文件！',
      },
      hashkeyGsLeave: {
        blockLeave: '强制性休假期需要最少5个工作天的连续休假。',
      },
    },
  },

  approvalTimeline: {
    action: {
      sending: '提交',
      approving: '待审批',
      approved: '已审批通过',
      rejected: '已拒绝审批',
      abolished: '已取消',
      approvalFailed: '审批失败',
    },
  },

  approvalProcess: {
    cc: '抄送',
    people: '人',
  },

  auditTimeline: {
    process: {
      submit: '提交',
      approve: '审批',
      cc: '抄送',
      end: '结束',
      cancel: '取消',
    },
    status: {
      submit: '已提交',
      pending: '待审批',
      approved: '已通过',
      rejected: '已拒绝',
      cancelled: '已取消',
      ended: '已结束',
      notStarted: '未开始',
    },
    title: {
      system: '系统',
    },
    description: {
      autoCC: '自动抄送 {names} {num}人',
    },
    reasonForRejection: '驳回原因：',
    comments: '评论',
    commentsAttachments: '评论附件',
    paymentCycle: '发放周期（会随此月薪资一起发放）',
    approvedVouchers: '核定凭证',
    paymentDate: '付款日期',
    paymentAttachment: '付款附件',
    remarks: '备注',
    remarksAttachments: '备注附件',
  },

  modal: {
    okText: '确 定',
    cancelText: '取 消',
  },

  jsonEditor: {
    fullScreen: '全屏',
    import: '导入',
    export: '导出',
    clear: '清空',
    retract: '收起',
  },

  codeEditor: {
    programmingLanguage: '编程语言',
  },

  dynamicForm: {
    messageCreateSuccess: '创建成功',
    messageUpdateSuccess: '更新成功',
  },

  tableFilter: {
    filterCriteria: '筛选条件：',
    addCriteria: '添加条件 +',
    actions: {
      eq: '等于',
      like: '包含',
      gt: '大于',
      lt: '小于',
    },
  },

  TableSearch: {
    actions: {
      clear: '清空',
      search: '搜索',
      downloadExcel: '下载Excel',
      expand: '展开',
      collapse: '收起',
      download: {
        executing: '文件生成中... 请不要关闭页面',
        downloading: '正在下载中... 请不要关闭页面',
        downloadingTip: '提前关闭页面将会造成下载失败',
        gotIt: '我知道了',
        successfully: '下载成功',
        failed: '下载失败',
        generated: '文件已生成，请下载！',
        downloadNow: '立即下载',
      },
    },
  },

  dynamicTable: {
    newButtonText: '新增',
  },

  button: {
    defaultValidateMessages: {
      fileTypeError: '文件类型错误',
      jsonIsNotDefined: 'JSON 问定义',
      jsonTypeError: 'JSON 类型错误',
    },
    exportJson: {
      fileName: '文件名',
    },
    sendCode: {
      sendCode: '发送验证码',
      sending: '发送中',
      seconds: '秒',
    },
  },

  commentEditor: {
    submit: '提交',
  },

  upload: {
    defaultValidateMessages: {
      fileTypeError: '文件类型错误',
      imageUploadFailed: '图片上传失败',
      cannotExceed3: '一次上传图片数量不能超过3个',
      isNotSupported: '不支持此文件类型！',
      fileSizeError: '文件大小必须小于 {size}MB。',
    },
    imageUploading: '图片上传中',
    onClickText: '点击上传',
  },

  quill: {
    toolbar: {
      size: '字号',
      h1: '标题 h1',
      h2: '标题 h2',
      h3: '标题 h3',
      h4: '标题 h4',
      h5: '标题 h5',
      align: '对齐方式',
      direction: '文本方向',
      bold: '加粗',
      italic: '斜体',
      underline: '下划线',
      strike: '删除线',
      color: '颜色',
      background: '背景颜色',
      sub: '上标',
      super: '下标',
      blockquote: '引用',
      formula: '公式',
      codeBlock: '代码块',
      ordered: '有序列表',
      bullet: '无序列表',
      increaseIndent: '增加缩进',
      decreaseIndent: '减少缩进',
      table: '表格',
      link: '添加链接',
      image: '插入图片',
      clean: '清除字体样式',
    },

    fileExpire: {
      tip: '为了安全和隐私保密起见，所有的图片和文件都需要设置过期时间。',
      placeholder: '文件过期时间',
      day10: '10天',
      day30: '30天',
      day60: '60天',
      day90: '90天',
      day180: '180天',
      day360: '360天',
    },
  },

  table: {
    details: '详情',
  },

  popDownload: {
    successfully: '下载成功',
    selectLanguage: '选择薪资单语言，默认为英文',
    setPasswordType: '设置薪资单密码类型',
    systemGenerated: '系统生成',
    custom: '自定义',
    setPassword: '设置密码',
    enterPassword: '请输入密码',
    passwordRules:
      '密码必须包含大写字母、小写字母、数字和特殊字符（如 !@#$%^&*）',
    systemGeneratedPassword: '系统生成密码',
    saveTip: '点击“下载”按钮后，密码将会消失，请务必在下载前保存好。密码为：',
    pleaseInput: '请输入密码',
    atLeast: '请输入至少6位字符',
  },

  fileView: {
    download: '下载',
  },

  f2a: {
    securityVerification: '安全验证',
    verifyTip: '为了保护信息隐私和安全，我们需要验证您的身份。',
    emailVerification: '通过电子邮件验证',
    mobileVerification: '通过手机验证',
    sendVerificationCode: '立即发送验证码',
    enterVerificationCode: '输入验证码',
    codeTip: '安全校验码已发送到{userEmail}，验证码5分钟内有效',
    incorrectCode: '验证码错误，请重试',
    enterCode: '请输入验证码',
    sendCode: '发送验证码',
    seconds: '秒',
    previousStep: '上一步',
    verificationSuccessful: '安全验证成功！',
    timeLimitTip: '您已获得 {timeStr}的免验证访问权限，请在时限内完成您的操作',
    gotIt: '我知道了',
    confirm: '确认',
    mobile: '手机',
    email: '电子邮件',
    minutes: '分钟',
    sending: '发送中',
  },

  businessComponent: {
    securityCheckModal: {
      securityVerification: '安全验证',
      verifyTip: '为了保护信息隐私和安全，我们需要验证您的身份。',
      emailVerification: '通过电子邮件验证',
      mobileVerification: '通过手机验证',
      sendVerificationCode: '立即发送验证码',
      enterVerificationCode: '输入验证码',
      codeTip: '安全校验码已发送到{userEmail}，验证码5分钟内有效',
      incorrectCode: '验证码错误，请重试',
      enterCode: '请输入验证码',
      sendCode: '发送验证码',
      seconds: '秒',
      previousStep: '上一步',
      verificationSuccessful: '安全验证成功！',
      timeLimitTip:
        '您已获得 {timeStr}的免验证访问权限，请在时限内完成您的操作',
      gotIt: '我知道了',
      confirm: '确认',
      mobile: '手机',
      email: '电子邮件',
      minutes: '分钟',
      sending: '发送中',
    },
  },

  error: {
    title: '系统提示：数据加载失败',
    description:
      '抱歉，数据加载出现错误，可能是网络问题或系统异常。请尝试刷新页面后重新操作。如果问题仍未解决，请联系我们的客服团队，我们将尽快为您提供帮助！',
    button: '刷新页面',
  },
};

export default languageValues;
