import type { LanguageType } from '.';

const languageValues: LanguageType = {
  locale: 'en-US',

  common: {
    day: 'day',
  },

  message: {
    success: 'Operation successful',
    error: 'Operation failed',
  },

  form: {
    submitText: 'Submit',
    resetText: 'Reset',
    deleteText: 'Delete',
    addText: 'Add',

    popconfirm: {
      title: 'Are you sure to delete?',
      okText: 'Delete',
      cancelText: 'Cancel',
    },

    defaultItemLabel: {
      startTime: 'Start time',
      endTime: 'End time',
    },

    defaultValidateMessages: {
      upload: {
        default: 'Please upload the file!',
      },
      hashkeyGsLeave: {
        blockLeave:
          'A minimum of 5 consecutive business days of Mandatory Block Leave.',
      },
    },
  },

  approvalTimeline: {
    action: {
      approving: 'Pending',
      sending: 'Submit',
      approved: 'Approved',
      rejected: 'Rejected',
      abolished: 'Abolished',
      approvalFailed: 'Approval Failed',
    },
  },

  approvalProcess: {
    cc: 'Send to',
    people: 'people',
  },

  auditTimeline: {
    process: {
      submit: 'Submit',
      approve: 'Approve',
      cc: 'CC',
      end: 'End',
      cancel: 'Cancel',
    },
    status: {
      submit: 'Submitted',
      pending: 'Pending',
      approved: 'Approved',
      rejected: 'Rejected',
      cancelled: 'Cancelled',
      ended: 'End',
      notStarted: 'Not Started',
    },
    title: {
      system: 'System',
    },
    description: {
      autoCC: "Auto CC'd {num} member, including {names}",
    },

    reasonForRejection: 'Reason for rejection:',
    comments: 'Comments',
    commentsAttachments: 'Comment Attachments',
    paymentCycle:
      'Payment cycle (will be paid together with this monthly salary)',
    approvedVouchers: 'Approved vouchers',
    paymentDate: 'Payment date',
    paymentAttachment: 'Payment attachment',
    remarks: 'Remarks',
    remarksAttachments: 'Remarks Attachments',
  },

  modal: {
    okText: 'Confirm',
    cancelText: 'Cancel',
  },

  jsonEditor: {
    fullScreen: 'Sull Screen',
    import: 'Import',
    export: 'Export',
    clear: 'Clear',
    retract: 'Retract',
  },

  codeEditor: {
    programmingLanguage: 'Programming Language',
  },

  dynamicForm: {
    messageCreateSuccess: 'Created success',
    messageUpdateSuccess: 'Update success',
  },

  tableFilter: {
    filterCriteria: 'Filter Criteria:',
    addCriteria: 'Add Criteria +',
    actions: {
      eq: 'equal to',
      like: 'contain',
      gt: 'greater than',
      lt: 'less than',
    },
  },

  TableSearch: {
    actions: {
      clear: 'Clear',
      search: 'Search',
      downloadExcel: 'Download Excel',
      expand: 'Expand',
      collapse: 'Collapse',
      download: {
        executing: "File Generating... Please Don't Close the Page",
        downloading: "Downloading... Please Don't Close the Page",
        downloadingTip: 'Pre-Closing Page Will Cause the Download to Fail',
        gotIt: 'Got It',
        successfully: 'Download Successfully',
        failed: 'Download Failed',
        generated: 'The file has been generated. Please download it.',
        downloadNow: 'Download Now',
      },
    },
  },

  dynamicTable: {
    newButtonText: 'Add',
  },

  button: {
    defaultValidateMessages: {
      fileTypeError: 'File type error',
      jsonIsNotDefined: 'JSON is not defined',
      jsonTypeError: 'JSON type error',
    },
    exportJson: {
      fileName: 'File name',
    },

    sendCode: {
      sendCode: 'Send Verification Code',
      sending: 'Sending',
      seconds: 'seconds',
    },
  },

  commentEditor: {
    submit: 'Submit',
  },

  upload: {
    defaultValidateMessages: {
      fileTypeError: 'File type error',
      imageUploadFailed: 'Image upload failed',
      cannotExceed3: 'The number of uploaded images at a time cannot exceed 3',
      isNotSupported: 'This file type is not supported!',
      fileSizeError: 'The file size must be smaller than {size}MB.',
    },
    imageUploading: 'Image uploading',
    onClickText: 'Click to Upload',
  },

  quill: {
    toolbar: {
      size: 'Word Size',
      h1: 'Title H1',
      h2: 'Title H2',
      h3: 'Title H3',
      h4: 'Title H4',
      h5: 'Title H5',
      align: 'Align',
      direction: 'Direction',
      bold: 'Bold',
      italic: 'Italic',
      underline: 'Underline',
      strike: 'Strike',
      color: 'Color',
      background: 'Background',
      sub: 'Sub',
      super: 'Super',
      blockquote: 'Blockquote',
      formula: 'Formula',
      codeBlock: 'Code Block',
      ordered: 'Ordered List',
      bullet: 'Unordered List',
      increaseIndent: 'Increase Indent',
      decreaseIndent: 'Decrease Indent',
      table: 'table',
      link: 'Add Link',
      image: 'Insert Picture',
      clean: 'Clean',
    },

    fileExpire: {
      tip: 'For security and privacy reasons, all images and files need to have an expiration date set.',
      placeholder: 'File expiration time',
      day10: '10 days',
      day30: '30 days',
      day60: '60 days',
      day90: '90 days',
      day180: '180 days',
      day360: '360 days',
    },
  },

  table: {
    details: 'Details',
  },

  popDownload: {
    successfully: 'Download Successfully',
    selectLanguage: 'Payslip Language',
    setPasswordType: 'Payslip Password Type',
    systemGenerated: 'System Generated',
    custom: 'Custom',
    setPassword: 'Set Password',
    enterPassword: 'Enter Password',
    passwordRules:
      'The password must contain uppercase letters, lowercase letters, numbers, and special characters (e.g., !@#$%^&*)',
    systemGeneratedPassword: 'System-generated Password',
    saveTip:
      "After clicking the 'Download' button, the password will disappear. Please make sure to save it before downloading. The password is:",
    pleaseInput: 'Please enter your password.',
    atLeast: 'Please enter at least 6 characters.',
  },

  fileView: {
    download: 'Download',
  },

  f2a: {
    securityVerification: 'Security Verification',
    verifyTip:
      'To protect information privacy and security, we need to verify your identity.',
    emailVerification: 'Email Verification',
    mobileVerification: 'Mobile Verification',
    sendVerificationCode: 'Send Verification Code',
    enterVerificationCode: 'Enter Verification Code',
    codeTip:
      'The security verification code has been sent to the {userEmail}, and the code is valid for 5 minutes. ',
    incorrectCode: 'Incorrect verification code, please try again.',
    enterCode: 'Enter Verification Code',
    sendCode: 'Send Verification Code',
    seconds: 'seconds',
    previousStep: 'Previous Step',
    verificationSuccessful: 'Security verification successful! ',
    timeLimitTip:
      'You have got {timeStr} of verification-free access. Please complete your operations within the time limit.',
    gotIt: 'Got It',
    confirm: 'Confirm',
    mobile: 'Mobile',
    email: 'Email',
    minutes: 'minutes',
    sending: 'Sending',
  },

  businessComponent: {
    securityCheckModal: {
      securityVerification: 'Security Verification',
      verifyTip:
        'To protect information privacy and security, we need to verify your identity.',
      emailVerification: 'Email Verification',
      mobileVerification: 'Mobile Verification',
      sendVerificationCode: 'Send Verification Code',
      enterVerificationCode: 'Enter Verification Code',
      codeTip:
        'The security verification code has been sent to the {userEmail}, and the code is valid for 5 minutes. ',
      incorrectCode: 'Incorrect verification code, please try again.',
      enterCode: 'Enter Verification Code',
      sendCode: 'Send Verification Code',
      seconds: 'seconds',
      previousStep: 'Previous Step',
      verificationSuccessful: 'Security verification successful! ',
      timeLimitTip:
        'You have got {timeStr} of verification-free access. Please complete your operations within the time limit.',
      gotIt: 'Got It',
      confirm: 'Confirm',
      mobile: 'Mobile',
      email: 'Email',
      minutes: 'minutes',
      sending: 'Sending',
    },
  },

  error: {
    title: 'System Alert: Data Load Failed',
    description:
      'Sorry, there was an error loading the data. This may be due to a network issue or system error. Please refresh the page and try again. If the problem persists, feel free to contact our support team, and we’ll assist you as soon as possible!',
    button: 'Refresh',
  },
};

export default languageValues;
