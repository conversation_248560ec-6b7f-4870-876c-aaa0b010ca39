import React from 'react'
import classNames from 'classnames'
import { ConfigContext } from '../config-provider'
import { mergeProps } from '../../utils/withDefaultProps'
import { useFileAccessToken } from '../../utils/hooks'
// import * as XLSX from 'xlsx';
import * as XLSX from 'xlsx';
import { message } from 'antd'
import Empty from '../empty'
import Loading from '../loading'


import './xlsx-view.less'

export const classPrefix = `deer-xlsx-view`;

export interface XlsxViewProps {
  className?: string;
  style?: React.CSSProperties;
  cssString?: string;
  url: string;
}

const defaultProps = {
  preview: false,
  type: 'default'
}

const XlsxView: React.FC<XlsxViewProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    preview,
    url,
    cssString,
    ...restProps
  } = props

  const { isFileToken } = React.useContext(ConfigContext)

  const [__html, setHTML] = React.useState('');

  const [isLoading, setIsLoading] = React.useState(!!url);

  const { getTheCompleteFileUrl } = useFileAccessToken()

  const styleElementRef = React.useRef<HTMLStyleElement | null>(null);

  React.useEffect(() => {
    if (!cssString) return;

    const styleElement = document.createElement('style');
    styleElement.type = 'text/css';
    styleElement.appendChild(document.createTextNode(cssString));

    styleElementRef.current = styleElement;

    document.head.appendChild(styleElement);

    return () => {
      if (styleElementRef.current && styleElementRef.current.parentNode) {
        styleElementRef.current.parentNode.removeChild(styleElementRef.current);
        styleElementRef.current = null;
      }
    };
  }, [cssString]);

  const initFileUrl = async (url: string) => {
    if (!url) return

    if (isFileToken || url.includes('http')) {
      return await getTheCompleteFileUrl(url)
    }

    return url
  }

  React.useEffect(() => {
    if (!url) return;

    (async () => {

      setIsLoading(true)

      try {
        const newUrl = await initFileUrl(url)

        if (!newUrl) return;

        const file = await (await fetch(newUrl)).arrayBuffer()

        const workbook = XLSX.read(file, {
          type: 'string',
          // raw: true,
          // sheetRows: -1
        });

        const worksheet = workbook.Sheets[workbook.SheetNames[0]];

        const table = XLSX.utils.sheet_to_html(worksheet, {
          // header: true,
          // editable: true
        });

        setHTML(table);

        // const raw_data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        // console.log(raw_data);

      } catch (err: any) {
        message.error(err?.message || '加载失败')
      }

      setIsLoading(false)

    })();
  }, [url]);

  if (!url) {
    return <Empty description={`url does not exist`} />
  }

  if (isLoading) {
    return <Loading />
  }

  return (
    <div className={classNames(classPrefix, className)}>
      <div dangerouslySetInnerHTML={{ __html }}></div>
    </div>
  );
}

export default XlsxView
