.deer-xlsx-view {
  width: 100%;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  table {
    min-width: 100%;
    text-align: center;
  }

  tr {
    td {
      border-bottom: 1px solid #f0f0f0;
      border-right: 1px solid #f0f0f0;
      padding: 10px;
      &:last-child {
      border-right: 0 none;
      }
    }

    &:last-child {
      td {
        border-bottom: 0 none;
      }
    }
  }
 }
