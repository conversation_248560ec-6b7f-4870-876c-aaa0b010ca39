// 引入所需的图标组件（假设这些图标来自某个UI库，例如 Ant Design）
import { SmileOutlined, CoffeeOutlined, HeartOutlined, MessageOutlined, UserOutlined } from 'some-icon-library';

// 定义图标数组
const promptIcons = [SmileOutlined, CoffeeOutlined, HeartOutlined, MessageOutlined, UserOutlined];

/**
 * 随机返回一个图标组件
 * @returns {React.ComponentType} 随机选择的图标组件
 */
function getRandomIcon() {
  // 生成一个随机索引，范围为 [0, promptIcons.length - 1]
  const randomIndex = Math.floor(Math.random() * promptIcons.length);
  
  // 返回对应的图标组件
  return promptIcons[randomIndex];
}

export default getRandomIcon;