import { Button, Input } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import InnerHtml from '@smartdeer-ui/pc/components/inner-html';
import { functionRuntime } from '../../../services/run';
import { message } from 'antd';
import { CartItem } from './CartList';
import { swrDownloadFile } from '../../../../../utils/swrFetcher';
import { request } from '@umijs/max';


// 第一次签名的时候file也是带签名的，等于signed
// 第二次签名的时候file是也带签名的，等于unsigned

interface StepThreeProps {
  currentStep: number;
  initData: any;
  subEntity: any;
  cartItems: CartItem[];
  form: any;
  onSuccess: () => void;
  onPrevStep?: () => void;
  i18n: any;
}

const StepThree: React.FC<StepThreeProps> = ({
  currentStep,
  subEntity,
  initData,
  cartItems,
  form,
  onSuccess,
  onPrevStep,
  i18n
}) => {
  const [isSuccess, setIsSuccess] = useState(false);
  const [contractId, setContractId] = useState('');
  const [content, setContent] = useState('');
  const [signature, setSignature] = useState(initData?.userName);
  const [file, setFile] = useState({});
  const [downloadFile, setDownloadFile] = useState({});
  const [loading, setLoading] = useState(false);
  const [isDownLoading, setIsDownLoading] = useState(false);

  // 获取是否已存在合同
  const fetchHasActiveContract = async () => {
    // debugger
    try {
      const result = await functionRuntime(initData?.entityUName, 'x_river_bfm_get_active_contract_id_by_entity', {
        entityId: initData?.entityId,
        subEntityId: initData?.subEntityId ? initData?.subEntityOptions[0]?.id : subEntity?.id,
      });
      console.log('fetchHasActiveContract result:', result);
      if (result?.rs) {
        setContractId(result.rs);
        return result.rs;
      }
      return null;
    } catch (error) {
      console.error('获取活跃合同失败:', error);
      message.error('获取活跃合同失败');
      return null;
    }
  };

  // 生效就签补充协议
  const fetchSignChangeContract = async (file: any) => {
    setLoading(true);
    try {
      const formValues = form.getFieldsValue();
      const result = await functionRuntime(initData?.entityUName, 'x_river_bfm_sign_change_contract', {
        id: contractId,
        entityId: initData?.entityId,
        subEntityId: initData?.subEntityId ? initData?.subEntityOptions[0]?.id : subEntity?.id,
        currency: formValues.currency,
        canPayAbroad: formValues.canPayAbroad,
        dealIds: cartItems.map(item => item.key).join(','),
        files: JSON.stringify([file]),
      });
      console.log('fetchSignChangeContract result:', result);
      if (result?.rs) {
        setIsSuccess(true);
      }
      // return result;
    } catch (error) {
      console.error('签署补充协议失败:', error);
      message.error(`${error?.message}`);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 获取合同`html预览`
  const fetchContractPreview = async (contractId: string) => {
    try {
      setLoading(true);
      const formValues = form.getFieldsValue();
      const dealIds = cartItems.map(item => item.key).join(',');
      let params = {
        entityId: initData?.entityId,
        subEntityId: initData?.subEntityId ? initData?.subEntityOptions[0]?.id : subEntity?.id,
        currency: formValues.currency,
        canPayAbroad: formValues.canPayAbroad,
        dealIds,
      };
      if (contractId) {
        params.id = contractId;
      }
      const result = await functionRuntime(initData?.entityUName, 'x_river_bfm_preview_contract', params);
      console.log('fetchContract result:', result);
      if (result?.rs) {
        setContent(result?.rs);
      } else {
        message.warning('未获取到合同预览内容');
      }
    } catch (error) {
      console.error('获取合同预览失败:', error);
      message.error('获取合同预览失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取合同`pdf文件`
  const fetchContractFile = async (contractId: string, signature: string = '') => {
    setLoading(true);
    let params = {
      entityId: initData?.entityId,
      subEntityId: initData?.subEntityId ? initData?.subEntityOptions[0]?.id : subEntity?.id,
      currency: form?.getFieldValue('currency'),
      canPayAbroad: form?.getFieldValue('canPayAbroad'),
      dealIds: cartItems.map(item => item.key).join(',')
    }
    if (contractId) {
      params.id = contractId
    }
    if (signature) {
      params.signatureName = signature
    }
    try {
      const result = await functionRuntime(initData?.entityUName, 'x_river_bfm_create_signed_contract_file', {
        ...params
      });
      console.log(`result: `, result);
      if (result?.pdfFile && result?.fileName) {
        console.log(`success`);
        // debugger;
        let file = {
          fileName: result?.fileName,
          fileKey: result?.pdfFile,
          size: 0,
          fileType: 0,
          orderNo: 1
        };
        setFile(file);
        if (!downloadFile?.fileKey) {
          setDownloadFile(file);
        }
        return file;
      }
    } catch (error) {
      console.log(error);
      message.error(`${error?.message}`);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 签署
  const handleSign = async (file: any) => {
    setLoading(true);
    try {
      const result = await functionRuntime(initData?.entityUName, 'x_river_bfm_sign_contract', {
        entityId: initData?.entityId,
        subEntityId: initData?.subEntityId ? initData?.subEntityOptions[0]?.id : subEntity?.id,
        currency: form?.getFieldValue('currency'),
        canPayAbroad: form?.getFieldValue('canPayAbroad'),
        dealIds: cartItems.map(item => item.key).join(','),
        files: JSON.stringify([file])
      });
      // debugger;
      if (result?.rs) {
        setIsSuccess(true);
      }
    } catch (error) {
      message.error(`${error?.message}`);
    } finally {
      setLoading(false);
    }
  }

  // 串行执行获取合同ID和合同预览
  const fetchContractData = async () => {
    const contractId = await fetchHasActiveContract();
    await fetchContractPreview(contractId);
    // await fetchContractFile(contractId);
  };

  // useEffect触发数据获取
  useEffect(() => {
    if (currentStep === 3) {
      fetchContractData();
    }
  }, [initData?.entityId, initData?.subEntityId, form?.getFieldValue('currency'), cartItems, currentStep]);

  const handleDownloadPDF = async (file: any = downloadFile) => {
    try {
      setIsDownLoading(true);
      const uri = process.env.APP_FILE_TOKEN_API.replace('/entity/u/*{entityUUID}', '');
      const res = await request(uri, {
        method: 'GET',
        params: {
          fileKey: file?.fileKey,
        },
      });
      console.log(`res: `, res);
      const result = await swrDownloadFile(`${res}`, file?.fileKey);
      console.log(`result: `, result);
    } catch (e) {
      console.log(`e: `, e);
      message.error('下载失败', e);
    } finally {
      setIsDownLoading(false);
    }
  };

  return (
    <>
      <div className={`min-h-screen w-full flex flex-col items-center justify-center ${!isSuccess ? '' : '!hidden'}`}>
        <div className="w-full max-w-4xl">
          <div className="flex items-center mb-4">
            <LeftOutlined className={`text-lg cursor-pointer hover:text-[#FF812A] hover:fill-[#FF812A]`} onClick={onPrevStep} />
            <span className="ml-2 text-lg font-medium">
              {i18n?.['bfm_contract_contract_preview_title']}
            </span>
          </div>
          <div className="mb-[220px] w-full rounded-8 bg-white p-6 shadow-lg relative">
            {!content ? (
              <div className="flex justify-center items-center h-[400px]">
                {i18n?.['bfm_contract_loading']}
              </div>
            ) : (
              <InnerHtml content={content} />
            )}

            {/* 底部操作区域 */}
            <div className="max-w-[850px] fixed bottom-0  w-[850px] bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="rounded-md p-6 bg-white">
                <div className="">
                  <input
                    style={{ fontFamily: 'bailutongtongshouxieti' }}
                    placeholder=""
                    value={signature}
                    onChange={(e) => setSignature(e.target.value)}
                    className="focus:outline-none !border-none focus:shadow-none !text-[40px]"
                  />
                  <div className="mt-4 flex  border-t border-gray-200 pt-4">
                    {i18n?.['bfm_contract_client_signature']}
                  </div>
                </div>

                <div className="w-full mt-10">
                  <div className="flex justify-between my-4 gap-4">
                    <Button
                      loading={isDownLoading || loading}
                      style={{ width: '100%' }}
                      type="primary"
                      onClick={async() => {
                        if (contractId) {
                          let file = await fetchContractFile(contractId, signature);
                          // debugger
                          fetchSignChangeContract(file);
                        } else {
                          let file = await fetchContractFile(contractId, signature);
                          handleSign(file);
                        }
                      }}
                      disabled={!signature}
                    >
                      {i18n?.['bfm_contract_agree_and_sign']}
                    </Button>
                    <Button
                      loading={isDownLoading || loading}
                      className="flex-1"
                      type="default"
                      onClick={async () => {
                        let file = await fetchContractFile(contractId);
                        handleDownloadPDF(file);
                      }}
                    >
                      {i18n?.['bfm_contract_download_pdf']}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={`${isSuccess ? '' : '!hidden'}`}>
        <div className="min-h-screen w-full flex flex-col items-center  bg-white p-6">
          <div className="w-full max-w-4xl flex justify-center items-center mt-[100px]">
            <div className=" w-full rounded-8  p-6 ">
              <div className="flex justify-center items-center text-[20px]">
                {i18n?.['bfm_contract_signed_success_title']}
              </div>
              <div className="flex justify-center items-center mt-[66px]">
                <img className='w-[128px]' src="/images/sign-success.png" alt="success" />
              </div>
            </div>
          </div>

          <Button type={'primary'} className='w-[200px] mt-[66px]' onClick={onSuccess}>
            {i18n?.['bfm_contract_start_btn']}
          </Button>
        </div>
      </div>
    </>
  );
};

export default StepThree;