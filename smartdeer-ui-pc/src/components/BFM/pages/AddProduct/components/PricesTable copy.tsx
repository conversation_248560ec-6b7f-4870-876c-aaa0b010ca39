import { Table } from 'antd';
import React from 'react';
import { 
  setupFeeComputeTypes,
  terminationFeeTypes,
  platformFeeTypes,
  platformFeeComputeTypes,
  depositTypes,
  vatGstCtTypes 
} from '../constants';

interface PricesTableProps {
  data: Record<string, any>;
  customerType?: number;
}

const PricesTable: React.FC<PricesTableProps> = ({ data }) => {
  const json = data?.json ?? {};

  const numberStyle = {
    color: 'rgb(254, 145, 17)',
    fontWeight: 600
  }
  const currencyStyle = {
    color: 'rgb(254, 145, 17)',
    fontWeight: 400,
    fontStyle: 'italic'
  }
  const typeStyle = {
    color: 'rgb(254, 145, 17)',
    fontWeight: 400
  }

  const renderSetupFee = (prices: any) => {
    if (!prices) return null;
    return <div>
      咨询工作说明书期限少于 <span style={numberStyle}>{prices.setupFeeLimitMonths}</span> 个月，
      {
        (!prices.setupFeeType || prices.setupFeeType === 1) && (
          <>
            需支付一次性不可退设置费 <span style={numberStyle}>{prices.setupFeeAmount}</span> <span style={currencyStyle}>{prices.setupFeeCurrency}</span>
          </>
        )
      }
      {
        (prices.setupFeeType === 2) && (
          <>
            以 <span style={typeStyle}>{setupFeeComputeTypes[prices.setupFeeComputeType]}</span> 为基数，
            月费为 <span style={numberStyle}>{`${prices.setupFeeFeePercentage}%`}</span>，
            最低 <span style={numberStyle}>{prices.setupFeeMinAmount}</span> <span style={currencyStyle}>{prices.setupFeeCurrency}</span>
          </>
        )
      }
    </div>
  }

  const renderPlatformFee = (prices: any) => {
    if (!prices) return null;
    const platformFeeType = prices.platformFeeType?.toString();
    return (
      <>
        <div>
          计费方式：<span style={typeStyle}>{platformFeeTypes[platformFeeType]}</span>
        </div>
        {
          platformFeeType === '1' && (
            <div>
              收费标准：<span style={numberStyle}>{prices.platformFeeAmount}</span> <span style={currencyStyle}>{prices.platformFeeCurrency}</span> 人 / 月
            </div>
          )
        }
        {
          (platformFeeType === '2') && (
            <div>
              以 <span style={typeStyle}>{platformFeeComputeTypes[prices.platformFeeComputeType]}</span> 为基数，
              月费为 <span style={numberStyle}>{`${prices.platformFeeFeePercentage}%`}</span>，
              最低 <span style={numberStyle}>{prices.platformFeeCapFeePerPersonMonth}</span> <span style={currencyStyle}>{prices.platformFeeCurrency}</span>
            </div>
          )
        }
      </>
    )
  }

  const renderDeposit = (prices: any) => {
    if (!prices) return null;
    return (
      <div>
        每个顾问收取 <span style={numberStyle}>{prices.depositMonths}</span> 个月的 <span style={typeStyle}>{depositTypes[prices.depositType]}</span>
      </div>
    )
  }

  const renderOthers = (prices: any) => {
    if (!prices) return null;
    return (
      <>
        <div>
          非周期性支付服务费：<span style={numberStyle}>{prices.nonRecurringAmount}</span> <span style={currencyStyle}>{prices.nonRecurringCurrency}</span> 人/次
        </div>
        <div>
          报销管理服务费：
          {prices?.useReimbursementSystem === true && "使用报销系统，"}
          {prices?.useReimbursementSystem === false && "不使用报销系统，"}
          报销金额 * <span style={numberStyle}>{`${prices?.reimbursementPercentage}%`}</span>
        </div>
      </>
    )
  }

  const renderTermination = (prices: any) => {
    if (!prices) return null;
    if (!prices.terminationFeeType || prices.terminationFeeType === 1) {
      return (
        <div>
          一次性支付 <span style={numberStyle}>{prices.terminationFeeMonthlyPlatformFee}</span> <span style={currencyStyle}>{prices.terminationFeeCurrency}</span> 人/次
        </div>
      )
    } else if (prices.terminationFeeType === 2) {
      return (
        <div>
          以 <span style={typeStyle}>{terminationFeeTypes[prices.terminationFeeComputeType]}</span> 为基数，
          月费为 <span style={numberStyle}>{`${prices.terminationFeeFeePercentage}%`}</span>，
          最低 <span style={numberStyle}>{prices.terminationFeeMinAmount}</span> <span style={currencyStyle}>{prices.terminationFeeCurrency}</span>
        </div>
      )
    }
  }

  const renderTransaction = (prices: any) => {
    if (!prices) return null;
    return (
      <div>
        每笔支付 <span style={numberStyle}>{prices.transactionFeePerTransactionAmount}</span> <span style={currencyStyle}>{prices.transactionFeeCurrency}</span> + 支付金额的 <span style={numberStyle}>{`${prices.transactionFeePercentageOfPayment}%`}</span>
      </div>
    )
  }

  const renderVatGstCt = (prices: any) => {
    if (!prices) return null;
    return prices.vatGstCtIsEnabled ? (
      <div>
        <span style={typeStyle}>{vatGstCtTypes[prices.vatGstCtType?.toString()]}</span> VAT/GST/CT <span style={numberStyle}>{`${prices.vatGstCtPercentage}%`}</span>
      </div>
    ) : '无'
  }

  const renderFinalCompensation = (prices: any) => {
    if (!prices) return null;
    return prices.finalCompensationAccumulationIsEnabled ?
      (
        <>
          是，规则：
          <span style={{ color: 'rgb(254, 145, 17)' }}>{prices?.collectionRule}</span>
        </>
      )
      : '无'
  }

  const columns1 = [
    {
      title: "设置费",
      dataIndex: "json",
      key: "setup",
      render: renderSetupFee
    },
    {
      title: "平台费",
      dataIndex: "json",
      key: "platform",
      render: renderPlatformFee
    },
    {
      title: "保证金",
      dataIndex: "json",
      key: "deposit",
      render: renderDeposit
    },
    {
      title: "其他服务费",
      dataIndex: "json",
      key: "others",
      render: renderOthers
    },
  ];

  const columns2 = [
    {
      title: "离职服务费",
      dataIndex: "json",
      key: "termination",
      render: renderTermination
    },
    {
      title: "手续费",
      dataIndex: "json",
      key: "transaction",
      render: renderTransaction
    },
    {
      title: "增值税VAT/GST/CT",
      dataIndex: "json",
      key: "vatGstCt",
      render: renderVatGstCt
    },
    {
      title: "服务终止补偿累积",
      dataIndex: "json",
      key: "finalCompensation",
      render: renderFinalCompensation
    }
  ];

  const dataSource = [{
    key: '1',
    json: json?.listPrice || {}
  }];

  return (
    <div className="space-y-4">
      <Table
        bordered
        size="small"
        pagination={false}
        dataSource={dataSource}
        columns={columns1}
      />
      <Table
        bordered
        size="small"
        pagination={false}
        dataSource={dataSource}
        columns={columns2}
      />
    </div>
  )
}

export default PricesTable; 