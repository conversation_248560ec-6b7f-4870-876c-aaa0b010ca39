import React, { useEffect, useRef } from 'react';

interface ParabolicMotionProps {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  onEnd: () => void;
}

const ParabolicMotion: React.FC<ParabolicMotionProps> = ({
  startX,
  startY,
  endX,
  endY,
  onEnd,
}) => {
  const ballRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ball = ballRef.current;
    if (!ball) return;

    ball.style.left = `${startX}px`;
    ball.style.top = `${startY}px`;

    const controlX = (startX + endX) / 2;
    const controlY = Math.min(startY, endY) - 100;

    const keyframes = [
      {
        left: `${startX}px`,
        top: `${startY}px`,
        offset: 0,
      },
      {
        left: `${controlX}px`,
        top: `${controlY}px`,
        offset: 0.5,
      },
      {
        left: `${endX}px`,
        top: `${endY}px`,
        offset: 1,
      },
    ];

    const animation = ball.animate(keyframes, {
      duration: 800,
      easing: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
      fill: 'forwards',
    });

    animation.onfinish = () => {
      onEnd();
    };
  }, [startX, startY, endX, endY]);

  return (
    <div
      ref={ballRef}
      style={{
        position: 'fixed',
        width: '20px',
        height: '20px',
        borderRadius: '50%',
        backgroundColor: '#FF812A',
        zIndex: 9999,
        pointerEvents: 'none',
      }}
    />
  );
};

export default ParabolicMotion; 