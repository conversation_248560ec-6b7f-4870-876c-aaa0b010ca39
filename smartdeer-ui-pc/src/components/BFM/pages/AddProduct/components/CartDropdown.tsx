import { Dropdown, Button, Tag } from 'antd';
import React, { forwardRef } from 'react';
import { ShoppingCartOutlined } from '@ant-design/icons';
import { CartItem } from './CartList';

interface CartDropdownProps {
  cartItems: CartItem[];
  onRemoveFromCart: (key: string) => void;
  currentStep: number;
  i18n: any;
}

const CartDropdown = forwardRef<HTMLButtonElement, CartDropdownProps>(({
  cartItems,
  onRemoveFromCart,
  currentStep,
  i18n
}, ref) => {
  console.log(`cartItems: `, cartItems)

  return (
    <Dropdown
      menu={{
        items: cartItems?.map(item => ({
          key: item.key,
          label: (
            <Tag 
              key={item.key} 
              closable 
              onClose={() => onRemoveFromCart(item.key)}
              className="flex items-center gap-2 py-1 px-2"
            >
              <span className="text-[#FF812A]">
                {item?.countryName} {item?.employmentType} &nbsp;
                  {String(item.visaType) === '1' ? i18n?.['bfm_contract_visa_type_white_collar'] :
                  String(item.visaType) === '2' ? i18n?.['bfm_contract_visa_type_blue_collar'] :
                    String(item.visaType) === '3' ? i18n?.['bfm_contract_visa_type_senior_title'] : ''}
              </span>
            </Tag>
          )
        }))
      }}
      placement="bottom"
      arrow
      open={cartItems.length > 0 && currentStep === 1}
    >
      <Button
        ref={ref}
        icon={<ShoppingCartOutlined style={{ color: '#FF812A' }} />}
        className="text-[#FF812A]"
      >
        <span className="text-[#FF812A]">
          {/* 已添加产品 ({cartItems.length}) */}
          {i18n?.['bfm_contract_products_added']} ({cartItems.length})
        </span>
      </Button>
    </Dropdown>
  );
});

export default CartDropdown; 