import React from 'react';
import { List } from 'antd';

export interface CartItem {
  key: string;
  name: string;
  countryCode: string;
  countryName: string;
  employmentType: string;
  visaType?: string;
  data: any;
}

interface CartListProps {
  items: CartItem[];
  onRemove: (key: string) => void;
}

const CartList: React.FC<CartListProps> = ({ items, onRemove }) => {
  console.log(`items: `, items);
  debugger;
  return (
    <List
      dataSource={items}
      renderItem={item => (
        <List.Item
          key={item.key}
          actions={[
            <a key="remove" onClick={() => onRemove(item.key)}>
              删除
            </a>
          ]}
        >
          <List.Item.Meta
            title={item.name}
            description={
              <div>
                <div>雇佣国家/地区：{item.countryName}</div>
                <div>雇佣类型：{item.employmentType}</div>
                {item.visaType && (
                  <div>员工类型：
                    {item.visaType === '1' ? '白领' :
                      item.visaType === '2' ? '蓝领' :
                        item.visaType === '3' ? '高级职称' : ''}
                  </div>
                )}
              </div>
            }
          />
        </List.Item>
      )}
    />
  );
};

export default CartList; 