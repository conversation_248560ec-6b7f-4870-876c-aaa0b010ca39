import { Table } from 'antd';
import React from 'react';
import { 
  setupFeeComputeTypes,
  terminationFeeTypes,
  platformFeeTypes,
  platformFeeComputeTypes,
  depositTypes,
  vatGstCtTypes 
} from '../constants';

interface PricesTableProps {
  data: Record<string, any>;
  customerType?: number;
  i18n: any;
}

const PricesTable: React.FC<PricesTableProps> = ({ data, i18n }) => {
  const json = data?.json ?? {};

  const numberStyle = {
    color: 'rgb(254, 145, 17)',
    fontWeight: 600
  }
  const currencyStyle = {
    color: 'rgb(254, 145, 17)',
    fontWeight: 400,
    fontStyle: 'italic'
  }
  const typeStyle = {
    color: 'rgb(254, 145, 17)',
    fontWeight: 400
  }

  const renderSetupFee = (prices: any) => {
    if (!prices) return null;
    return <div>
      {i18n?.['bfm_contract_setup_fee_description']} <span style={numberStyle}>{prices.setupFeeLimitMonths}</span> {i18n?.['bfm_contract_setup_fee_description_1']}，
      {
        (!prices.setupFeeType || prices.setupFeeType === 1) && (
          <>
            {i18n?.['bfm_contract_setup_fee_description_2']} <span style={numberStyle}>{prices.setupFeeAmount}</span> <span style={currencyStyle}>{prices.setupFeeCurrency}</span>
          </>
        )
      }
      {
        (prices.setupFeeType === 2) && (
          <>
            {i18n?.['bfm_contract_prices_table_base']} <span style={typeStyle}>{i18n?.[setupFeeComputeTypes[prices.setupFeeComputeType]]}</span> {i18n?.['bfm_contract_prices_table_as_base']}
            {i18n?.['bfm_contract_prices_table_mounth_as']} <span style={numberStyle}>{`${prices.setupFeeFeePercentage}%`}</span>, 
            {i18n?.['bfm_contract_prices_table_as_lowest']} <span style={numberStyle}>{prices.setupFeeMinAmount}</span> <span style={currencyStyle}>{prices.setupFeeCurrency}</span>
          </>
        )
      }
    </div>
  }

  const renderPlatformFee = (prices: any) => {
    if (!prices) return null;
    const platformFeeType = prices.platformFeeType?.toString();
    return (
      <>
        <div>
          {i18n?.['bfm_contract_get_fee_rules']}: <span style={typeStyle}>{i18n?.[platformFeeTypes[platformFeeType]]}</span>
        </div>
        {
          platformFeeType === '1' && (
            <div>
              {i18n?.['bfm_contract_get_fee_base']}: <span style={numberStyle}>{prices.platformFeeAmount}</span> <span style={currencyStyle}>{prices.platformFeeCurrency}</span> {i18n?.['bfm_contract_fee_standard']}
            </div>
          )
        }
        {
          (platformFeeType === '2') && (
            <div>
              {i18n?.['bfm_contract_prices_table_base']} <span style={typeStyle}>{i18n?.[platformFeeComputeTypes?.[prices.platformFeeComputeType]]}</span> {i18n?.['bfm_contract_prices_table_as_base']}，
              {i18n?.['bfm_contract_prices_table_mounth_as']} <span style={numberStyle}>{`${prices.platformFeeFeePercentage}%`}</span>，
              {i18n?.['bfm_contract_prices_table_as_lowest']} <span style={numberStyle}>{prices.platformFeeCapFeePerPersonMonth}</span> <span style={currencyStyle}>{prices.platformFeeCurrency}</span>
            </div>
          )
        }
      </>
    )
  }

  const renderDeposit = (prices: any) => {
    if (!prices) return null;
    return (
      <div>
        {i18n?.['bfm_contract_prices_table_per_consultation_get']} <span style={numberStyle}>{prices.depositMonths}</span> {i18n?.['bfm_contract_prices_table_per_mounth']} <span style={typeStyle}>{i18n?.[depositTypes[prices?.depositType]]}</span>
      </div>
    )
  }

  const renderOthers = (prices: any) => {
    if (!prices) return null;
    return (
      <>
        <div>
        {i18n?.['bfm_contract_prices_table_non_periodic_payment_of_service_fees']}
          <span style={numberStyle}>{prices.nonRecurringAmount}</span> <span style={currencyStyle}>{prices.nonRecurringCurrency}</span> {i18n?.['bfm_contract_one_time_payment']}
        </div>
        <div>
          {i18n?.['bfm_contract_prices_table_reimbursement_management_service_fee']}:&nbsp;
          {prices?.useReimbursementSystem === true && i18n?.['bfm_contract_reimbursement_management_fee_use_sys']}
          {prices?.useReimbursementSystem === false && i18n?.['bfm_contract_reimbursement_management_fee_use_sys_no']}
          {i18n?.['bfm_contract_reimbursement_management_fee']} * <span style={numberStyle}>{`${prices?.reimbursementPercentage}%`}</span>
        </div>
      </>
    )
  }

  const renderTermination = (prices: any) => {
    if (!prices) return null;
    if (!prices.terminationFeeType || prices.terminationFeeType === 1) {
      return (
        <div>
          {
            i18n?.['bfm_contract_once_pay']
          }
          <span style={numberStyle}>{prices.terminationFeeMonthlyPlatformFee}</span> <span style={currencyStyle}>{prices.terminationFeeCurrency}</span> 
          {
            i18n?.['bfm_contract_one_time_payment']
          }
        </div>
      )
    } else if (prices.terminationFeeType === 2) {
      return (
        <div>
          {
            i18n?.['bfm_contract_prices_table_base']
          } <span style={typeStyle}>{terminationFeeTypes[prices.terminationFeeComputeType]}</span> {i18n?.['bfm_contract_prices_table_base']}, 
          {
            i18n?.['bfm_contract_prices_table_mounth_as']
          } <span style={numberStyle}>{`${prices.terminationFeeFeePercentage}%`}</span>, 
          {
            i18n?.['bfm_contract_prices_table_as_lowest']
          } <span style={numberStyle}>{prices.terminationFeeMinAmount}</span> <span style={currencyStyle}>{prices.terminationFeeCurrency}</span>
        </div>
      )
    }
  }

  const renderTransaction = (prices: any) => {
    if (!prices) return null;
    return (
      <div>
        {i18n?.['bfm_contract_each_pay']} <span style={numberStyle}>{prices.transactionFeePerTransactionAmount}</span> <span style={currencyStyle}>{prices.transactionFeeCurrency}</span> + {' ' + i18n?.['bfm_contract_eyery_each_payment']} <span style={numberStyle}>{`${prices.transactionFeePercentageOfPayment}%`}</span>
      </div>
    )
  }

  const renderVatGstCt = (prices: any) => {
    if (!prices) return null;
    return prices.vatGstCtIsEnabled ? (
      <div>
        <span style={typeStyle}>{i18n?.[vatGstCtTypes[prices.vatGstCtType?.toString()]]}</span> VAT/GST/CT <span style={numberStyle}>{`${prices.vatGstCtPercentage}%`}</span>
      </div>
    ) : i18n?.['bfm_contract_none']
  }

  const renderFinalCompensation = (prices: any) => {
    if (!prices) return null;
    return prices.finalCompensationAccumulationIsEnabled ?
      (
        <>
          {i18n?.['bfm_contract_yes_rules_is']}:&nbsp;
          <span style={{ color: 'rgb(254, 145, 17)' }}>{prices?.collectionRule}</span>
        </>
      )
      : i18n?.['bfm_contract_none']
  }


  const columns1 = [
    {
      title: i18n?.['bfm_contract_prices_table_title_1'],
      dataIndex: "json",
      key: "setup",
      render: renderSetupFee
    },
    {
      title: i18n?.['bfm_contract_prices_table_title_2'],
      dataIndex: "json",
      key: "platform",
      render: renderPlatformFee
    },
    {
      title: i18n?.['bfm_contract_prices_table_title_3'],
      dataIndex: "json",
      key: "deposit",
      render: renderDeposit
    },
    {
      title: i18n?.['bfm_contract_prices_table_title_4'],
      dataIndex: "json",
      key: "others",
      render: renderOthers
    },
  ];

  const columns2 = [
    {
      title: i18n?.['bfm_contract_prices_table_title_5'],
      dataIndex: "json",
      key: "termination",
      render: renderTermination
    },
    {
      title: i18n?.['bfm_contract_prices_table_title_6'],
      dataIndex: "json",
      key: "transaction",
      render: renderTransaction
    },
    {
      title: i18n?.['bfm_contract_prices_table_title_7'],
      dataIndex: "json",
      key: "vatGstCt",
      render: renderVatGstCt
    },
    {
      title: i18n?.['bfm_contract_prices_table_title_8'],
      dataIndex: "json",
      key: "finalCompensation",
      render: renderFinalCompensation
    }
  ];

  const dataSource = [{
    key: '1',
    json: json?.listPrice || {}
  }];

  return (
    <div className="space-y-4">
      <Table
        bordered
        size="small"
        pagination={false}
        dataSource={dataSource}
        columns={columns1}
      />
      <Table
        bordered
        size="small"
        pagination={false}
        dataSource={dataSource}
        columns={columns2}
      />
    </div>
  )
}

export default PricesTable; 