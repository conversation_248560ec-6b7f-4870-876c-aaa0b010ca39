import { Card, Typography, Button, Form, Select, Radio, Row, Col } from 'antd';
import React, { useEffect } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { paymentCurrency } from '../constants';
import { CartItem } from './CartList';
import {LeftOutlined} from '@ant-design/icons';

interface StepTwoProps {
  form: any;
  cartItems: CartItem[];
  initData: any;
  subEntity: any;
  currentStep: number;
  onRemoveFromCart: (key: string) => void;
  onPrevStep: () => void;
  onNextStep: () => void;
  i18n: any;
}

const StepTwo: React.FC<StepTwoProps> = ({
  form,
  cartItems,
  initData,
  subEntity,
  currentStep,
  onRemoveFromCart,
  onPrevStep,
  onNextStep,
  i18n
}) => {
  // const intl = useIntl(); // 已不再需要

  // 根据国籍过滤可选币种
  const filteredCurrencyOptions = React.useMemo(() => {
    const nationality = initData?.subEntityId ? JSON.parse(initData?.subEntityOptions?.[0]?.json)?.country : JSON.parse(subEntity?.json || '{}')?.country;
    // debugger;
    if (nationality === 'CN') {
      return paymentCurrency.filter(item => item.value === 'CNY');
    } else {
      return paymentCurrency.filter(item => ['USD', 'USDC'].includes(item.value));
    }
  }, [currentStep, initData?.entity?.json?.nationality]);

  // 设置默认值（更健壮，保证币种选中）
  useEffect(() => {
    const currencyOptions = filteredCurrencyOptions;
    const currentCurrency = form.getFieldValue('currency');
    // 当前币种不在 options 里，或者没选，自动选第一项
    if (!currentCurrency || !currencyOptions.find(opt => opt.value === currentCurrency)) {
      if (currencyOptions.length > 0) {
        form.setFieldsValue({ currency: currencyOptions[0].value });
      } else {
        form.setFieldsValue({ currency: undefined });
      }
    }
  }, [filteredCurrencyOptions, form, form.getFieldValue('currency')]);

  const currency = Form.useWatch('currency', form);

  return (
    <>
      <div className="flex justify-between w-full mb-5">
        <Typography.Title level={4} className="mb-6">
          
        <LeftOutlined className={`text-lg cursor-pointer hover:text-[#FF812A] hover:fill-[#FF812A]`} onClick={onPrevStep} />
        &nbsp;&nbsp;
          {
            initData?.subEntityId ? initData?.entityName : subEntity?.defaultName
          }
        </Typography.Title>
      </div>
      <Card>
        <Typography.Title level={5} className="mb-6">
          {i18n?.['bfm_contract_selected_products_title']} :
        </Typography.Title>
        <div className="gap-4 mb-6">
          {cartItems?.map((item) => (
            <Card
              key={item.key}
              size="small"
              className="relative mb-4"
            >
              <div className="flex w-full justify-between">
                <div className="flex flex-col gap-2">
                  <div className="text-lg font-medium text-[#FF812A]">{item.name}</div>
                  <div className="text-gray-500 text-sm">
                    <div>
                      {i18n?.['bfm_contract_country_region']}:&nbsp;
                      {item?.countryName}
                    </div>
                    <div>
                      {i18n?.['bfm_contract_employment_type_title']}:&nbsp;
                      {item.employmentType}
                    </div>
                    <div className={item.visaType ? '' : 'hidden'}>
                      {i18n?.['bfm_contract_visa_type_options_title']}:&nbsp;
                        {String(item.visaType) === '1' ? i18n?.['bfm_contract_visa_type_white_collar'] :
                          String(item.visaType) === '2' ? i18n?.['bfm_contract_visa_type_blue_collar'] :
                            String(item.visaType) === '3' ? i18n?.['bfm_contract_visa_type_senior_title'] : ''}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-start">
                  <Button
                    type="default"
                    onClick={() => onRemoveFromCart(item.key)}
                    icon={<CloseOutlined />}
                  >
                    {i18n?.['bfm_contract_delete']}
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        <div className="mt-8">
          <Form
            form={form}
            layout="vertical"
          >
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label={i18n?.['bfm_contract_payment_currency_title']}
                  name="currency"
                  rules={[{ required: true, message: i18n?.['bfm_contract_select_payment_currency'] }]}
                >
                  <Select placeholder={i18n?.['bfm_contract_select_currency']}>
                    {filteredCurrencyOptions?.map(item => (
                      <Select.Option key={item?.value} value={item?.value}>{item?.label}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              {currency === 'CNY' && (
                <Col span={8}>
                  <Form.Item
                    label={i18n?.['bfm_contract_cny_overseas_payment_title']}
                    name="canPayAbroad"
                    rules={[{ required: true, message: i18n?.['bfm_contract_select_can_pay_abroad'] }]}
                  >
                    <Radio.Group
                      buttonStyle="solid"
                      className="w-full"
                    >
                      <Radio.Button value={false} className="w-1/2 text-center">
                        {i18n?.['bfm_contract_btn_no']}
                      </Radio.Button>
                      <Radio.Button value={true} className="w-1/2 text-center">
                        {i18n?.['bfm_contract_btn_yes']}
                      </Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              )}
            </Row>
          </Form>
        </div>

        <div className="text-right mt-6">
          <Button onClick={onPrevStep} className="mr-4">
            {i18n?.['bfm_contract_previous']}
          </Button>
          <Button type="primary" onClick={onNextStep}>
            {i18n?.['bfm_contract_next']}
          </Button>
        </div>
      </Card>
    </>
  );
};

export default StepTwo; 