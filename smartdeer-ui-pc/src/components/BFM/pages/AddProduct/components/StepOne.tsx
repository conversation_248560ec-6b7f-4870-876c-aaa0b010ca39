import { <PERSON>, Typo<PERSON>, Button, Form, Select, Radio, Row, Col } from 'antd';
import React, { useEffect } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { CountryStateSelect } from '../../../components/app';
import { visaTypeOptions } from '../constants';
import PricesTable from './PricesTable';
import CartDropdown from './CartDropdown';
import ParabolicMotion from './ParabolicMotion';

interface StepOneProps {
  currentStep: number;
  form: any;
  i18n: any;
  loading: boolean;
  companyList: any[];
  dealList: any;
  cartItems: any[];
  subEntity: {
    id: number;
    name: string;
    json: string;
  };
  initData: any;
  onAddToCart: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onRemoveFromCart: (key: string) => void;
  onNextStep: () => void;
  showAnimation: boolean;
  animationConfig: {
    startX: number;
    startY: number;
    endX: number;
    endY: number;
  };
  onAnimationEnd: () => void;
  cartButtonRef: React.RefObject<HTMLButtonElement>;
}

const StepOne: React.FC<StepOneProps> = ({
  form,
  loading,
  companyList,
  dealList,
  cartItems,
  initData,
  subEntity,
  onAddToCart,
  onRemoveFromCart,
  onNextStep,
  showAnimation,
  animationConfig,
  onAnimationEnd,
  cartButtonRef,
  currentStep,
  i18n
}) => {
  const visaType = Form.useWatch(['visaType'], form);
  const countryAndState = Form.useWatch(['countryAndState'], form);
  const foreignEmployment = Form.useWatch(['foreignEmployment'], form);

  // 清除visaType逻辑
  useEffect(() => {
    const needVisaType = foreignEmployment === true && countryAndState?.countryCode === 'SA';
    if (!needVisaType && form.getFieldValue('visaType')) {
      form.setFieldValue('visaType', undefined);
    }
  }, [countryAndState, foreignEmployment]);

  console.log('dealList', dealList, initData);
  // debugger

  // 计算是否需要强制选visaType
  const needVisaType = foreignEmployment === true && countryAndState?.countryCode === 'SA';

  return (
    <>
      {showAnimation && (
        <ParabolicMotion
          {...animationConfig}
          onEnd={onAnimationEnd}
        />
      )}
      <div className="flex justify-between w-full mb-5">
        <Typography.Title level={4} className="mb-6">
          {
            initData?.subEntityId ? initData?.subEntityOptions?.[0]?.defaultName : subEntity?.defaultName
          }
        </Typography.Title>
        <CartDropdown
          i18n={i18n}
          currentStep={currentStep}
          cartItems={cartItems}
          onRemoveFromCart={onRemoveFromCart}
          ref={cartButtonRef}
        />
      </div>
      <Card>
        <Typography.Title level={5} className="mb-6">
          {i18n?.['bfm_contract_title']}
        </Typography.Title>
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={24}>
            {/* subEntityId 传就是默认选中，禁用签约实体 Select */}
            {/* subEntityId 不传，在组件内自己选 签约实体 */}
            <Col span={24}>
              <Form.Item
                label={i18n?.['bfm_contract_contracting_entity']}
                name="ourSubjectId"
                rules={[{ required: true, message: i18n?.['bfm_contract_select_our_subject'] }]}
              >
                <Select
                  disabled={!!initData?.subEntityId}
                  placeholder={i18n?.['bfm_contract_select_our_subject']}
                  loading={loading}
                  defaultValue={initData?.subEntityId ? initData?.subEntityOptions?.[0]?.defaultName : companyList?.[0]}
                  options={(companyList || []).map((company: any) => ({
                    value: company?.uuid,
                    label: company?.defaultName
                  }))}
                  filterOption={(input, option) =>
                    (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label={i18n?.['bfm_contract_country_region']}
                name="countryAndState"
                rules={[{ required: true, message: i18n?.['bfm_contract_select_country'] }]}
              >
                {initData?.entityId && (
                  <CountryStateSelect
                    i18n={i18n}
                    entityId={initData?.entityId}
                    entityUName={initData?.entityUName}
                    changeOnSelect onlySelectCountry
                    onChange={() => form.setFieldValue('visaType', undefined)}
                  />
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={i18n?.['bfm_contract_employment_type_title']}
                name="foreignEmployment"
                rules={[{ required: true, message: i18n?.['bfm_contract_select_employment_type'] }]}
              >
                <Radio.Group buttonStyle="solid" className="w-full" onChange={() => form.setFieldValue('visaType', undefined)}>
                  <Radio.Button value={true} className="w-1/2 text-center">
                    {i18n?.['bfm_contract_expat_employment']}
                  </Radio.Button>
                  <Radio.Button value={false} className="w-1/2 text-center">
                    {i18n?.['bfm_contract_local_employment']}
                  </Radio.Button>
                </Radio.Group>
              </Form.Item>

            </Col>
            <Col span={8}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, nextValues) => (
                  prevValues['countryAndState'] !== nextValues['countryAndState'] ||
                  prevValues['foreignEmployment'] !== nextValues['foreignEmployment']
                )}
              >
                {({ getFieldValue }) => {
                  const foreignEmployment = getFieldValue('foreignEmployment')
                  const needWisaType = foreignEmployment === true && getFieldValue('countryAndState')?.countryCode === 'SA'

                  console.log(dealList);
                  // debugger;
                  if (needWisaType) {
                    return (
                      <Form.Item
                        label={i18n?.['bfm_contract_visa_type_options_title']}
                        name="visaType"
                        rules={[{ required: true }]}
                      >
                        <Radio.Group buttonStyle="solid">
                          {visaTypeOptions?.map(item => (
                            <Radio.Button key={item.value} value={item.value}>
                              {i18n?.[item?.labelKey]}
                            </Radio.Button>
                          ))}
                        </Radio.Group>
                      </Form.Item>
                    )
                  }
                }}
              </Form.Item>

              {/* <Form.Item
                noStyle
                shouldUpdate={(prevValues, nextValues) => (
                  prevValues['countryAndState'] !== nextValues['countryAndState'] ||
                  prevValues['foreignEmployment'] !== nextValues['foreignEmployment']
                )}
              >
                {({ getFieldValue }) => {
                  const isLocalEmployment = getFieldValue('foreignEmployment') === false
                  const needWisaType = isLocalEmployment && getFieldValue('countryAndState')?.countryCode === 'CN' && (initData?.entity?.json?.nationality === 'CN')
                  if (needWisaType) {
                    return (
                      <Form.Item
                        label={'客户主体所在地'}
                        name="localSignatory"
                        rules={[{ required: true }]}
                      >
                        <Select
                          disabled
                          suffixIcon={null}
                          options={[
                            { label: '中国境内主体', value: true },
                            { label: '中国境外主体', value: false }
                          ]}
                        />
                      </Form.Item>
                    )
                  }
                }}
              </Form.Item> */}
            </Col>
          </Row>

          {dealList && (
            <div className="mt-8 w-full">
              <div className="w-full flex justify-between">
                <div className="flex flex-col">
                  <Typography.Title level={5} className="mb-4">
                    {i18n?.['bfm_contract_suitable_products_title']} :
                  </Typography.Title>
                  <div className="mb-4">
                    {form.getFieldValue('countryAndState')?.countryName}
                    {/* {form.getFieldValue('foreignEmployment') ? ' 海外雇佣' : ' 本地雇佣'} */}
                    {form.getFieldValue('foreignEmployment') ? ' ' + i18n?.['bfm_contract_expat_employment'] : i18n?.['bfm_contract_local_employment']} &nbsp;
                    {visaTypeOptions?.find(x => x.value === visaType)?.label ? ` ${visaTypeOptions?.find(x => x.value === visaType)?.label}` : ''}
                  </div>
                </div>

                <div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={onAddToCart}
                    disabled={cartItems.some(item => item.key === dealList.id) || (needVisaType && !visaType)}
                  >
                    {i18n?.['bfm_contract_add_product']}
                  </Button>
                </div>
              </div>

              <PricesTable data={dealList} i18n={i18n} />
            </div>
          )}

          <div className="text-right mt-6">
            <Button
              type="primary"
              onClick={onNextStep}
              disabled={cartItems.length === 0 || (needVisaType && !visaType)}
            >
              {i18n?.['bfm_contract_next']}
            </Button>
          </div>
        </Form>
      </Card>
    </>
  );
};

export default StepOne; 