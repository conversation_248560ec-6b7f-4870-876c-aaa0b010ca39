import { Form } from 'antd';
import React, { useState, useRef, useEffect } from 'react';
import { functionRuntime } from '../../services/run';
import { CartItem } from './components/CartList';
import StepOne from './components/StepOne';
import StepTwo from './components/StepTwo';
import StepThree from './components/StepThree';
import enUs from '../../locales/en-US';
import jaJP from '../../locales/ja-JP';
import zhCN from '../../locales/zh-CN';
import zhTW from '../../locales/zh-TW';
import { getLocale } from '@umijs/max';

const locales = {
  'en': enUs,
  'zh-CN': zhCN,
  'en-US': enUs,
  'zh-TW': zhTW,
  'ja-JP': jaJP
}

interface ChatProps {
  onClose: () => void;
  onSuccess: () => void;
  isChatModalVisible: boolean;
  positionName?: string;
  setUpdateLoading: (loading: boolean) => void;
  initData: {
    entity: any;

    entityId: number | string;
    entityUName: string;
    entityName: string;

    subEntityId: number | string;
    subEntityOptions: any[];
    subEntityUName: string;
    userName?: string;
  };
}

const App: React.FC<ChatProps> = (props) => {
  const { initData, onSuccess, onClose } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [companyList, setCompanyList] = useState([]);
  const [subEntity, setSubEntity] = useState({});
  const [currentStep, setCurrentStep] = useState(1);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [dealList, setDealList] = useState<any>(null);
  const [showAnimation, setShowAnimation] = useState(false);
  const [animationConfig, setAnimationConfig] = useState({ startX: 0, startY: 0, endX: 0, endY: 0 });
  const cartButtonRef = useRef<HTMLButtonElement>(null);

  const ourSubjectId = Form.useWatch(['ourSubjectId'], form);
  const countryAndState = Form.useWatch(['countryAndState'], form);
  const foreignEmployment = Form.useWatch(['foreignEmployment'], form);
  const visaType = Form.useWatch(['visaType'], form);
  const i18n = locales[getLocale() || 'zh'];

  console.log(`i18n: `, getLocale(), i18n);


  const fetchCompanyList = async () => {
    setLoading(true);
    try {
      const response = await functionRuntime(initData?.entityUName, 'x_river_b_sub_entity', {
        current: 1,
        limit: 1000,
        size: 1000,
        searchInfo: {
          searchItems: [],
          orders: []
        }
      });
      // debugger;
      setCompanyList(response?.dataInfo || []);
    } catch (error) {
      console.error('获取签约主体列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDealList = async (localSignatory?: boolean) => {
    const searchItems: Record<string, any>[] = [
      {
        key: "countryCode",
        action: "eq",
        value: countryAndState.countryCode
      },
      {
        key: "dealType",
        action: "eq",
        value: "1"
      },
      {
        key: "json.foreignEmployment",
        action: "eq",
        value: String(foreignEmployment)
      },
    ];

    if (visaType) {
      searchItems.push({
        key: "json.visaType",
        action: "eq",
        value: String(visaType)
      });
    }

    if (localSignatory !== undefined) {
      searchItems.push({
        key: 'json.localSignatory',
        action: "eq",
        value: String(localSignatory)
      });
    }

    try {
      const result = await functionRuntime(initData?.entityUName, 'search_river_eor_deal_list', {
        current: 1,
        limit: 500,
        size: 500,
        searchInfo: {
          searchItems: searchItems
        }
      });

      if (result?.dataInfo) {
        setDealList(result?.dataInfo?.[0]);
      }
    } catch (error) {
      console.error('获取产品列表失败:', error);
    }
  };

  const handleAddToCart = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!dealList) return;

    const buttonRect = e.currentTarget.getBoundingClientRect();
    const cartButtonRect = cartButtonRef.current?.getBoundingClientRect();

    if (cartButtonRect) {
      setAnimationConfig({
        startX: buttonRect.left + buttonRect.width / 2,
        startY: buttonRect.top + buttonRect.height / 2,
        endX: cartButtonRect.left + cartButtonRect.width / 2,
        endY: cartButtonRect.top + cartButtonRect.height / 2
      });
      setShowAnimation(true);
    }

    const values = form.getFieldsValue();
    const newItem: CartItem = {
      key: dealList.id,
      name: dealList.name || '未命名产品',
      countryCode: values.countryAndState?.countryCode,
      countryName: values.countryAndState?.countryName,
      employmentType: values.foreignEmployment ? i18n?.['bfm_contract_expat_employment'] : i18n?.['bfm_contract_local_employment'],
      visaType: values.visaType,
      data: dealList
    };

    setTimeout(() => {
      setCartItems(prev => [...prev, newItem]);
    }, 800);
  };

  const handleRemoveFromCart = (key: string) => {
    setCartItems(prev => {
      const newItems = prev.filter(item => item.key !== key);
      if (newItems.length === 0) {
        setCurrentStep(1);
      }
      return newItems;
    });
  };

  const handleAnimationEnd = () => {
    setShowAnimation(false);
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      // 切换到第二步时初始化表单值
      form.setFieldsValue({
        currency: '',
        canPayAbroad: false
      });
    }
    setCurrentStep(prev => prev + 1);
  };

  const handlePrevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  useEffect(() => {
    if (countryAndState?.countryCode && foreignEmployment !== undefined) {
      let localSignatory = undefined;
      // if (countryAndState.countryCode === 'CN' && !foreignEmployment) {
      //   // 中国大陆+本地雇佣，需要判断客户客户主体所在地（境内/境外）
      //   const isCustomerInCN = ourSubjectId.countryCode === 'CN';
      //   localSignatory = isCustomerInCN;
      //   form.setFieldValue('localSignatory', localSignatory);
      // }
      fetchDealList(localSignatory);
    }
  }, [countryAndState, foreignEmployment, visaType]);

  // visaType清理兜底（多一层保险）
  useEffect(() => {
    const needVisaType = foreignEmployment === true && countryAndState?.countryCode === 'SA';
    if (!needVisaType && form.getFieldValue('visaType')) {
      form.setFieldValue('visaType', undefined);
    }
  }, [countryAndState, foreignEmployment]);

  // useEffect(() => {
  //   // console.log(`ourSubjectId: `, initData);
  //   // debugger;
  //   if (ourSubjectId) {
  //     // fetchSubEntity();
  //   }
  // }, [ourSubjectId]);

  useEffect(() => {
    // 不然就取 init?.subEntityOptions
    if (!initData?.subEntityId) {
      console.log(`initData`, initData)
      fetchCompanyList();
    }
  }, [initData?.entityId]);

  useEffect(() => {
    if (ourSubjectId && initData?.subEntityOptions) {
      const selected = initData.subEntityOptions.find(opt => opt?.uuid === ourSubjectId);
      console.log('selected', selected);
      // debugger
      setSubEntity(selected || {});
    }
  }, [ourSubjectId, initData?.subEntityOptions]);

  return (
    <>
      <div className={`p-6 w-full ${currentStep === 1 ? '' : '!hidden'}`}>
        <StepOne
          subEntity={subEntity as { id: number; name: string; json: string }}
          setSubEntity={setSubEntity}
          form={form}
          loading={loading}
          companyList={companyList}
          dealList={dealList}
          cartItems={cartItems}
          initData={initData}
          onAddToCart={handleAddToCart}
          onRemoveFromCart={handleRemoveFromCart}
          onNextStep={handleNextStep}
          showAnimation={showAnimation}
          animationConfig={animationConfig}
          onAnimationEnd={handleAnimationEnd}
          cartButtonRef={cartButtonRef}
          currentStep={currentStep}
          ourSubjectId={ourSubjectId}
          i18n={i18n}
        />
      </div>
      <div className={`p-6 w-full ${currentStep === 2 ? '' : '!hidden'}`}>
        <StepTwo
          currentStep={currentStep}
          form={form}
          cartItems={cartItems}
          initData={initData}
          subEntity={subEntity}
          onRemoveFromCart={handleRemoveFromCart}
          onPrevStep={handlePrevStep}
          onNextStep={handleNextStep}
          i18n={i18n}
        />
      </div>
      <div className={` ${currentStep === 3 ? '' : '!hidden'}`}>
        <StepThree
          subEntity={subEntity}
          onPrevStep={handlePrevStep}
          onSuccess={onSuccess}
          currentStep={currentStep}
          initData={initData}
          cartItems={cartItems}
          form={form}
          i18n={i18n}
        />
      </div>
      <div className="" style={{ fontFamily: 'bailutongtongshouxieti' }}></div>
    </>
  );
};

export default App;