import React from 'react';
import { <PERSON><PERSON>, <PERSON>, message, Modal, Table, TablePaginationConfig, Typography, Card, Popconfirm, Space, Tag, Drawer } from 'antd';
import { functionRuntime } from '@/services/run';
import Column from 'antd/es/table/Column';
import CurdTableColumnContent from '@smartdeer-ui/pc/components/crud-table/table-column-content'
import { PlusOutlined, EyeOutlined, AuditOutlined, EditOutlined, HistoryOutlined } from '@ant-design/icons';
import { history, useLocation } from '@umijs/max';
import { TableSearch } from '@smartdeer-ui/pc/';
import EditForm from '../comps/edit-form';
import CustomerDetailView from '../comps/detail';
import { AuditDetail } from '../../app/components/app';
import S from '@/utils/storage';
import { isArray } from 'lodash';

const { Title } = Typography;

interface CustomerData {
  id: number;
  code: string;
  groupId: number;
  creatorId: number;
  updateTime: number;
  source: number;
  sourceChannelName: string;
  type: number;
  bdId: number;
  isDeleted: number;
  createTime: number;
  deleteTime: number;
  countryCode: string;
  name: string;
  json: string;
  auditStatus: number;
  registerCode: string;
  status: number;
  operatorName: string;
  bdName: string;
}

const tableColumns = [
  {
    title: "客户ID",
    key: "id",
    width: 80,
    fixed: "left",
  },
  {
    title: "客户名称",
    key: "name",
    width: 120,
    fixed: "left",
  },
  {
    title: "客户编码",
    key: "code",
    width: 120,
  },
  {
    title: "注册号/证件号",
    key: "registerCode", 
    width: 130
  },
  {
    title: "所属客户组",
    key: "groupName",
    width: 120
  },
  {
    title: "客户性质",
    key: "subjectTypeStr",
    width: 120
  },
  {
    title: '国家/地区',
    key: 'country',
    width: 150,
    type: 'template',
    template: "<div>*{countryNameZh}</div>"
  },
  {
    title: "对接BD",
    key: "bdName",
    width: 120,
  },
  {
    title: "客户类型",
    key: "typeStr",
    width: 120,
  },
  {
    title: "客户来源",
    key: "sourceStr",
    width: 120,
  },
  // {
  //   title: "注册码",
  //   key: "registerCode", 
  //   width: 150
  // },
  // {
  //   title: "国家代码",
  //   key: "countryCode",
  //   width: 120,
  //   render: (countryCode: string) => (
  //     <span className="flex items-center">
  //       <img 
  //         src={`https://flagcdn.com/16x12/${countryCode.toLowerCase()}.png`}
  //         alt={countryCode}
  //         style={{ marginRight: 8, verticalAlign: 'middle' }}
  //       />
  //       {countryCode}
  //     </span>
  //   )
  // },
  // {
  //   title: "BD",
  //   key: "bdName",
  //   width: 120
  // },
  {
    title: "审核状态",
    key: "auditStatus",
    width: 155,
    type: 'status',
    template: [
      {
        vif: '*{auditStatus}===0',
        name: '待审核',
        color: 'warning',
      },
      {
        vif: '*{auditStatus}===1',
        name: '审核中',
        color: 'processing',
      },
      {
        vif: '*{auditStatus}===2',
        name: '审核通过',
        color: 'success',
      }
    ]
  },
  {
    title: "状态",
    key: "status",
    width: 120,
    type: 'status',
    template: [
      {
        vif: '*{status}===0',
        name: '待生效',
        color: 'gray',
      },
      {
        vif: '*{status}===1',
        name: '已生效',
        color: 'green',
      }
    ]
  },
  {
    title: "操作人",
    key: "operatorName",
    width: 120
  },
  {
    title: "创建时间",
    key: "createTime",
    width: 180,
    type: "template",
    template: "<div>*{createTime | formatDate(YYYY-MM-DD HH:mm)}</div>"
  },
  {
    title: "操作",
    key: "action",
    type: "action", 
    fixed: "right",
    width: 200,
    template: [
      {
        "name": "查看",
        "type": "modal",
      }
    ]
  }
];

const filterColumns = [
  {
    title: "ID",
    type: 'input',
    field: 'id',
    action: { value: 'eq' },
    props: {
      placeholder: '请输入ID',
      allowClear: true
    }
  },
  {
    title: "名称",
    type: 'input',
    field: 'name',
    props: {
      placeholder: '请输入名称',
      allowClear: true
    }
  },
  {
    title: "客户类型",
    type: 'select',
    field: 'type',
    action: {
      value: 'eq'
    },
    props: {
      placeholder: '请选择类型',
      allowClear: true,
      options: [
        {
          label: "直客",
          value: "0"
        },
        {
          label: "分销商",
          value: "1"
        }
      ]
    }
  },
  {
    title: "客户来源",
    type: 'select',
    field: 'source',
    action: {
      value: 'eq'
    },
    props: {
      placeholder: '请选择客户来源',
      allowClear: true,
      options: [
        {
          label: "自拓客户",
          value: "0"
        },
        {
          label: "公司资源",
          value: "1"
        },
        {
          label: "公司举办会议资源",
          value: "2"
        },
        {
          label: "渠道资源",
          value: "3"
        },
        {
          label: "GS自助平台",
          value: "4"
        }
      ]
    }
  },
  {
    title: "状态",
    type: 'select',
    field: 'status',
    action: {
      value: 'eq'
    },
    props: {
      placeholder: '请选择状态',
      allowClear: true,
      options: [
        {
          label: "待生效",
          value: "0"
        },
        {
          label: "已生效",
          value: "1"
        }
      ]
    }
  },
  {
    title: "审核状态",
    type: 'select',
    field: 'auditStatus',
    action: {
      value: 'eq'
    },
    props: {
      placeholder: '请选择审核状态',
      allowClear: true,
      options: [
        {
          label: "待发起审批",
          value: "0"
        },
        {
          label: "审核中",
          value: "1"
        },
        {
          label: "审核通过",
          value: "2"
        },
        {
          label: "审核驳回",
          value: "3"
        }
      ]
    }
  },
  {
    "type": "select",
    "field": "creatorId",
    "title": "创建人",
    "action": {
      "value": 'eq'
    },
    "props": {
      "placeholder": '请选择创建人',
      "showSearch": "true",
      "fieldNames": {
        "value": "accountId",
        "label": "userName",
      },
      "supplementingTheString": true,
    },
    "labelProps": {
    },
    "effect": {
      "fetch": {
        "type": "function",
        "functionKey": "search_sage_account",
        "defaultParams": {
          "entityId": "0",
          "current": "1",
          "size": "1000",
          "limit": "1000"
        },
        "data": "dataInfo"
      }
    }
  },
  {
    "type": "select",
    "field": "json.am.amId",
    "title": "对接AM",
    "action": {
      "value": 'eq'
    },
    "props": {
      "placeholder": '请选择对接AM',
      "showSearch": "true",
      "fieldNames": {
        "value": "accountId",
        "label": "userName",
      },
      "supplementingTheString": true,
    },
    "labelProps": {
    },
    "effect": {
      "fetch": {
        "type": "function",
        "functionKey": "search_sage_account",
        "defaultParams": {
          "entityId": "0",
          "current": "1",
          "size": "1000",
          "limit": "1000"
        },
        "data": "dataInfo"
      }
    }
  },
  {
    "type": "select",
    "field": "bdId",
    "title": "对接BD",
    "action": {
      "value": 'eq'
    },
    "props": {
      "placeholder": '请选择对接BD',
      "showSearch": "true",
      "fieldNames": {
        "value": "accountId",
        "label": "userName",
      },
      "supplementingTheString": true
    },
    "labelProps": {
    },
    "effect": {
      "fetch": {
        "type": "function",
        "functionKey": "search_sage_account",
        "defaultParams": {
          "entityId": "0",
          "current": "1",
          "size": "1000",
          "limit": "1000"
        },
        "data": "dataInfo"
      }
    }
  },{
    "type": "select",
    "field": "countryCode",
    "title": "国家",
    "action": {
      "value": 'eq'
    },
    "props": {
      "placeholder": '请选择国家',
      "showSearch": "true",
      "fieldNames": {
        "value": "countryCode",
        "label": "countryNameZh",
      },
      "supplementingTheString": true,
    },
    "labelProps": {
    },
    "effect": {
      "fetch": {
        "type": "function",
        "functionKey": "x_bfm_area_country_list",
        "defaultParams": {},
        "data": "rs"
      }
    }
  },
  {
    title: "客户编码",
    type: 'input',
    field: 'code',
    props: {
      placeholder: '请输入编码',
      allowClear: true
    }
  },
  
];

const Page: React.FC = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const groupId = queryParams.get('groupId');
  const initialSearchInfo: {[key: string]: any} = {}
  if (groupId) {
    // initialSearchInfo['groupId'] = groupId;
    initialSearchInfo['searchItems'] = [{action: 'eq', key: 'groupId', value: groupId}]
  }

  const [data, setData] = React.useState<CustomerData[]>();
  const [loading, setLoading] = React.useState(false);
  const [isBtnShow, setBtnShow] = React.useState(false);
  const [tableParams, setTableParams] = React.useState<any>({
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`
    },
    searchInfo: initialSearchInfo
  });

  const [detailDrawerData, setDetailDrawerData] = React.useState<{
    open: boolean,
    id?: number,
    record?: any,
    status?: 'view' | 'edit',
    editButtonLoading?: boolean,
  }>({
    open: false,
  });
  const [auditDetailStatus, setAuditDetailStatus] = React.useState<any>({
    open: false,
    record: null,
    canAuditCancel: true,
  })

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await functionRuntime(0, "search_bfm_customer_list", {
        "current": tableParams.pagination.current,
        "limit": tableParams.pagination.pageSize,
        "size": tableParams.pagination.pageSize,
        "searchInfo": {
          ...tableParams.searchInfo, 
          "orders": [{
            key: 'id',
            asc: 'desc',
          }]
        }
      });
      const dataInfo = res.dataInfo;
      setData(dataInfo)
      setTableParams({
        ...tableParams,
        pagination: {...tableParams.pagination, total: res.total}
      })
    } catch (err: any) {
      message.error(err)
    }
    setLoading(false)
  };

  React.useEffect(() => {
    fetchData()
  }, [
    tableParams.pagination?.current,
    tableParams.pagination?.pageSize,
    tableParams.searchInfo
  ]);

  React.useEffect(() => {
    const customerId = localStorage.getItem('customerId');
    if (customerId) {
      localStorage.removeItem('customerId');
      setDetailDrawerData({
        open: true,
        id: Number(customerId),
        status: 'view',
      })
    }

    let roles = S.get('bfm-role', true);
    let isBtnShow = roles.some((role: any) => role.startsWith("bfm.customer.edit"));
    setBtnShow(isBtnShow);
  }, [])

  const handleChangeTable = (pagination: TablePaginationConfig) => {
    setTableParams({
      ...tableParams,
      pagination
    });
  };

  const handleAddCustomer = () => {
    history.push("/client-manage/new")
  }

  const onShowDrawer = (column: any, record: any) => {
    setDetailDrawerData({open: true, id: record?.id})
  }

  const handleClickSearch = (values: Record<string, any>) => {
    let searchItems = values?.searchItems || [];
    if (isArray(searchItems)) {
      searchItems = searchItems.map((item) => {
        if (item.key === 'name') {
          return {...item, caseInsensitive: true}
        }
        return item;
      })

    }
    setTableParams({
      ...tableParams,
      pagination: {...tableParams.pagination, current: 1},
      searchInfo: {...values, searchItems}
    })
  }

  const handleEditClick = async (record: any) => {
    console.log(record)
    const id = record.id;
    // if (record.status !== 0 || [1, 2].includes(record.auditStatus)) {
    //   message.error('已生效或审核中的客户，不可编辑')
    //   return;
    // }

    setDetailDrawerData({...detailDrawerData, editButtonLoading: true})
    // 判断是否在审批中
    let isInApprove = record.auditStatus === 1;

    if (isInApprove) {
      Modal.warning({
        title: '提示',
        content: '您有一个正在审批中的任务，编辑后将撤回审核中的信息，本次修改信息需要重新进行审核，确认编辑吗',
        okText: '撤销审批，并编辑',
        onOk: async () => {
          try {
            await functionRuntime(0, 'x_bfm_customer_cancel_approve', {id})
            setDetailDrawerData({...detailDrawerData, record: {...record, auditStatus: 0}, status: 'edit', editButtonLoading: false})
            fetchData();
          } catch (err: any) {
            setDetailDrawerData({...detailDrawerData, editButtonLoading: false})
          }
        },
        onCancel: () => {
          setDetailDrawerData({...detailDrawerData, editButtonLoading: false})
        },
        closable: true
      })
    } else {
      setDetailDrawerData({...detailDrawerData, status: 'edit', editButtonLoading: false})
    }
  }

  const handleEditFinish = async (values: any) => {
    console.log('handleEditFinish', values)

    const record = detailDrawerData.record;
    if (record?.status === 1) {
      try {
        const response = await functionRuntime(0, 'x_bfm_customer_non_critical_info_update', {id: record?.id, json: values.json});
        if (response?.rs) {
          message.success('更新成功');
          setDetailDrawerData({open: false})
          fetchData();
        } else {
          message.error('更新失败：' + response?.message);
        }
      } catch (error) {
        console.error(error);
        message.error('更新失败，请稍后重试');
      }
    } else {
      try {
        const response = await functionRuntime(0, 'x_bfm_customer_update', {...values, id: detailDrawerData.record?.id});
        if (response?.rs) {
          message.success('更新成功');
          setDetailDrawerData({open: false})
          fetchData();
        } else {
          message.error('更新失败：' + response?.message);
        }
      } catch (error) {
        console.error(error);
        message.error('更新失败，请稍后重试');
      }
    }

  }

  const handleStartAuditClick = async (record: any) => {
    try {
      let res = await functionRuntime(0, 'x_bfm_customer_start_approve', {id: record.id})
      if (res?.rs) {
        message.success(`${record.name} 成功发起审批`);
        fetchData();
      } else {
        message.error(res?.message);
      }
    } catch (err: any) {
      message.error(err?.message);
    }
  }

  const handleAuditProgressClick = (data: any, canAuditCancel: boolean) => {
    setDetailDrawerData({open: false})
    setAuditDetailStatus({open: true, record: data, canAuditCancel})
  }

  return (
    <Card className="m-4 shadow-lg rounded-lg">
      <div className="flex justify-between items-center mb-8">
        <Title level={4} className="!mb-0">客户管理</Title>
        {
          isBtnShow && (
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAddCustomer}
              size="large"
            >
              新建客户
            </Button>
          )
        }
      </div>

      <Card className="mb-6">
        <TableSearch
          type='searchItems'
          downloadExcelRender={false}
          showSearchButton
          onSearch={handleClickSearch}
          columns={filterColumns}
        />
      </Card>

      <Table
        rowKey={(record) => record.id}
        dataSource={data}
        pagination={tableParams.pagination}
        loading={loading}
        onChange={handleChangeTable}
        className="shadow-sm"
        scroll={{ x: 1200 }}
      >
        {
          tableColumns.map((column: any) => {
            return (
              <Column
                title={column.title}
                dataIndex={column.dataIndex || column.key}
                key={column.key}
                width={column.width}
                fixed={column.fixed}
                render={(value: any, record: any, index: number) => {
                  if (column.key === 'country') {
                    // const json = JSON.parse(record.json)
                    return [record.countryNameZh, record.json?.region==='all'?'':record.json?.region].filter((x) => !!x).join(' / ')
                  }
                  if (column.key === 'action') {
                    return (
                      <Space size={0}>
                        <Button 
                          type="link" 
                          size="middle"
                          icon={<EyeOutlined />}
                          onClick={() => onShowDrawer(column, record)}
                        >
                          查看
                        </Button>
                        {
                          (record.status === 0 && record.auditStatus !== 1) && (
                            <Popconfirm
                              title="确认发起审批?"
                              onConfirm={() => handleStartAuditClick(record)}
                              okText="确认"
                              cancelText="取消"
                            >
                              <Button 
                                type="link" 
                                size="middle"
                                icon={<AuditOutlined />}
                              >
                                发起审批
                              </Button>
                            </Popconfirm>
                          )
                        }
                      </Space>
                    )
                  }
                  if (column.key === 'auditStatus') {
                    return (
                      <>
                        {
                          record.auditStatus === 0 && (
                            <Tag color="default">待发起审批</Tag>
                          )
                        }
                        {
                          record.auditStatus === 1 && (
                            <Space size={0}>
                              <Tag color="processing">审核中</Tag>
                              <Button 
                                type="link" 
                                size="small"
                                onClick={() => handleAuditProgressClick(record, true)}
                              >
                                查看进度
                              </Button>
                            </Space>
                          )
                        }
                        {
                          record.auditStatus === 2 && (
                            <Tag color="success">审核通过</Tag>
                          )
                        }
                        {
                          record.auditStatus === 3 && (
                            <Space size={0}>
                              <div>
                                <Tag color="error">审核驳回</Tag>
                              </div>
                              <Button 
                                type="link" 
                                size="small"
                                onClick={() => handleAuditProgressClick(record, false)}
                              >
                                查看原因
                              </Button>
                            </Space>
                          )
                        }
                      </>
                    )
                  }
                  return (
                    <CurdTableColumnContent
                      column={column}
                      text={value}
                      index={index}
                      record={record}
                      onShowModal={onShowDrawer}
                      onShowDrawer={() => {}}
                    />
                  )
                }}
              />
            )
          })
        }
      </Table>

      <Drawer
        open={detailDrawerData.open}
        title={
          <div className="flex items-center justify-between">
            <span>客户详情</span>
            <Button 
              type="link"
              icon={<HistoryOutlined />}
              onClick={() => {
                localStorage.setItem('customerId', detailDrawerData.id ? `${detailDrawerData.id}` : '')
                history.push(`/operation-log/customer/${detailDrawerData.id}/list`)
              }}
              className="text-orange-500 hover:text-orange-600"
            >
              查看日志
            </Button>
          </div>
        }
        onClose={() => setDetailDrawerData({open: false})}
        extra={
          (detailDrawerData.record?.auditStatus !== 1 ) && (
            detailDrawerData.status === 'edit' ? (
              <Button 
                type="primary"
                onClick={() => setDetailDrawerData({...detailDrawerData, status: 'view'})}
              >
                取消编辑
              </Button>
            ) : (
              <Button 
                type="primary"
                icon={<EditOutlined />}
                onClick={() => handleEditClick(detailDrawerData.record)}
                loading={detailDrawerData.editButtonLoading}
              >
                编辑
              </Button>
            )
          )
        }
        width={900}
      >
        {
          detailDrawerData.status === 'edit' ? (
            <EditForm
              data={detailDrawerData.record}
              onFinish={handleEditFinish}
              isEdit
            />
          ) : (
            <div className="bg-white p-6 rounded-lg w-full overflow-auto">
              <CustomerDetailView 
                id={detailDrawerData.id}
                onAuditProgressClick={handleAuditProgressClick}
                onDataFetched={(data) => setDetailDrawerData({...detailDrawerData, record: data})}
              />
            </div>
          )
        }
      </Drawer>

      <Drawer
        title="审核进度"
        placement="right"
        width={900}
        onClose={() => setAuditDetailStatus({open: false})}
        open={auditDetailStatus.open}
      >
        <AuditDetail
          open={auditDetailStatus.open}
          getFunctionKey='x_bfm_customer_get_approve_info'
          cancelFunctionKey='x_bfm_customer_cancel_approve'
          onClose={() => setAuditDetailStatus({open: false})}
          id={auditDetailStatus.record?.id}
          onAuditCanceled={() => {setAuditDetailStatus({open: false}); fetchData();}}
          canCancelAudit={auditDetailStatus.canAuditCancel}
          detail={(
            <CustomerDetailView 
              id={detailDrawerData.record?.id}
              onAuditProgressClick={handleAuditProgressClick}
            />
          )}
        />
      </Drawer>
    </Card>
  );
};

export default Page;
