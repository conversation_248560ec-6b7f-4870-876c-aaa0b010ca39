import React from "react";
import CustomerDetailView from "../comps/detail";
import { ConfigContext } from "@smartdeer-ui/pc/components/config-provider";
import { Button, Typography } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { useNavigate } from '@umijs/max';

const Page = () => {
  const navigate = useNavigate();

  const context = React.useContext(ConfigContext);
  const params = context.useUrlParams();
  const id = params['id'];

  return (
    <>
      <div className="flex items-center gap-4 mb-6 justify-between">
        <Typography.Title level={4} style={{ margin: 0 }}>
          客户详情
        </Typography.Title>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(-1)}
        >
          返回
        </Button>
      </div>
      <CustomerDetailView
        id={id}
        onAuditProgressClick={() => {}}
      />
    </>
  );
};

export default Page;