
import React from 'react';
import { DynamicTable } from '@smartdeer-ui/pc';
import { Button, message, Modal, Table, Typography } from 'antd';
import { functionRuntime } from '@/services/run';
import Column from 'antd/es/table/Column';
import CurdTableColumnContent from '@smartdeer-ui/pc/components/crud-table/table-column-content'
import { ConfigContext } from '@smartdeer-ui/pc/components/config-provider';
import OperatoinLogsTable from '@/components/app/opration-logs-table';
const { Title } = Typography;

const Page: React.FC = () => {
  const context = React.useContext(ConfigContext);
  const params = context.useUrlParams();
  const id = params['id'] || '';

  return (
    <>
      <OperatoinLogsTable
        type='customer'
        id={id}
        diffColums={[
          // {
          //   key: 'name',
          //   label: '客户组名称'
          // },
          // {
          //   key: 'industry',
          //   label: '所属行业'
          // },
          // {
          //   key: 'description',
          //   label: '客户组简介'
          // }
        ]}
      />
    </>
  );
};

export default Page;