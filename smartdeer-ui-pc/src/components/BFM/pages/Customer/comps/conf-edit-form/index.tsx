import React, { Children } from 'react';
import { Form, Input, Select, Radio, Space, Divider, Typography, Button, Modal, Popconfirm } from 'antd';
import { MinusCircleOutlined, PlusOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { request, useNavigate } from '@umijs/max';
import { debounce } from 'lodash';
import { CountryStateSelect } from '@/components/app';


const { Title } = Typography;
const APP_CUSTOMER = process.env.APP_CUSTOMER;

  
const hostName = '/v1/sage/entity/33/function/runtime';

type Props = {
  data: any,
  onFinish: (values: any) => Promise<void>
}

const Main: React.FC<Props> = (props) => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [customers, setCustomers] = React.useState<any[]>([]);

  React.useEffect(() => {
    // 获取客户组列表
    request(hostName, {
      method: 'POST',
      data: {
        functionKey: 'search_bfm_customer_list',
        params: {
          current: 1,
          limit: 500,
          size: 500,
          searchInfo: {}
        },
      },
    }).then(res => {
      if (res?.dataInfo) {
        let c = res?.dataInfo.filter((item: any) => item.auditStatus === 2)
        // debugger
        setCustomers(c || []);
      }
    });

  }, []);

  React.useEffect(() => {
    if (!props.data) {
      return;
    }

    const jsonObject = JSON.parse(props.data.json)
    const formData = {
      ...props.data,
      ...jsonObject,
      countryAndState: {countryCode: props.data.countryCode, state: jsonObject.region}
    }
    form.setFieldsValue(formData)
  }, [props.data])

  const onFinish = async (values: any) => {
    try {

      console.log("values", values);
      debugger;

      const data = {
        ...values
      };
      await props.onFinish(data);
    } catch (error) {
      console.error(error);
    }
  };


  const confirmDelete = (remove: (index: number | number[]) => void, index: number) => {
    remove(index);
  };


  return (
    <div className='max-w-[800px] pb-8'>
      
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
      >
        <div className="bg-white p-6 rounded-lg">

          <Form.Item 
            noStyle
          >
              <Form.Item 
                label="客户名称" 
                name="customerId"
                required
                style={{ marginBottom: 24 }}
                rules={[{ required: true, message: '请选择客户名称' }]}
              >
                <Select placeholder="请选择客户名称">
                  {customers.map(item => (
                    <Select.Option key={item.id} value={item.id}>
                      {item.name}
                    </Select.Option>
                  ))}
              </Select>
            </Form.Item>
          </Form.Item>

          <Form.Item
            label="客户配置key"
            name="confKey"
            style={{ marginBottom: 24 }}
            rules={[{ required: true, message: '请输入客户配置key' }]}
          >
            <Input placeholder="请输入客户配置key" />
          </Form.Item>

          <Form.Item
            label="客户配置Value"
            name="confValue"
            required
            style={{ marginBottom: 24 }}
            rules={[{ required: true, message: '请输入客户confValue' }]}
          >
            <Input.TextArea placeholder="请输入客户confValue" autoSize={{ minRows: 3, maxRows: 6 }} />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" size="large" block>
              确认提交
            </Button>
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default Main;