import React, { Children } from 'react';
import { Form, Input, Select, Radio, Space, Divider, Typography, Button, Modal, Popconfirm, message, Row, Col, Card } from 'antd';
import { MinusCircleOutlined, PlusOutlined, ArrowLeftOutlined, CloseOutlined } from '@ant-design/icons';
import { request, useNavigate } from '@umijs/max';
import { debounce } from 'lodash';
import { CountryStateSelect } from '../../../../components/app';
import AMCountrySelect from './am-country-select';
import { functionRuntime } from '@/services/run';
import InvoiceCNItem from './invoice-cn-item';
import InvoiceKRItem from './invoice-kr-item';
import { dealTypes } from '../../../../consts';

const hostName = '/v1/sage/entity/33/function/runtime';

type Props = {
  data: any,
  onFinish: (values: any) => Promise<void>
  isEdit?: boolean
}

const Main: React.FC<Props> = (props) => {
  const { data, onFinish, isEdit } = props;

  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [groupList, setGroupList] = React.useState<any[]>([]);
  const [bdList, setBdList] = React.useState<any[]>([]);
  const [allCountries, setAllCountries] = React.useState<any[]>([]);
  const [submitting, setSubmitting] = React.useState(false);

  const isActive = data?.status === 1;

  // 获取初始化数据
  const fetchInitialData = async () => {
    try {
      // 获取客户组列表
      const groupRes = await request(hostName, {
        method: 'POST',
        data: {
          functionKey: 'search_bfm_customer_group_list',
          params: {
            current: 1,
            limit: 500,
            size: 500,
            searchInfo: {
              searchItems: [
                {
                  key: 'status',
                  action: 'eq', 
                  value: '1'
                }
              ],
              orders: [{ key: "id", asc: "desc" }]
            }
          },
        },
      });

      if (groupRes?.dataInfo) {
        setGroupList(groupRes.dataInfo);
      } else {
        message.error('获取客户组列表失败');
      }

      // 获取BD列表
      const bdRes = await request(hostName?.replace('33', '0'), {
        method: 'POST',
        data: {
          functionKey: 'search_sage_account',
          params: {
            entityId: '0',
            current: 1,
            size: 3000,
            limit: 3000
          }
        }
      });

      if (bdRes?.dataInfo) {
        setBdList(bdRes.dataInfo);
      } else {
        message.error('获取BD列表失败');
      }

      // 获取国家列表
      const countryRes = await functionRuntime(0, 'x_bfm_area_country_list', {});
      if (countryRes?.rs) {
        setAllCountries(countryRes.rs);
      } else {
        message.error('获取国家列表失败');
      }

      // 设置默认值
      form.setFieldsValue({
        subjectType: 1,
        type: 0,
        source: 0
      });

    } catch (error) {
      console.error('初始化数据获取失败:', error);
      message.error('初始化数据获取失败');
    }
  };

  React.useEffect(() => {
    fetchInitialData();
  }, []);

  React.useEffect(() => {
    if (!props.data) {
      return;
    }

    try {
      const jsonObject = JSON.parse(props?.data?.json);
      const formData = {
        ...props?.data,
        countryAndState: { countryCode: props?.data?.countryCode, state: jsonObject?.region },
        contacts: jsonObject?.contacts,
        bd: jsonObject?.bdId,
        customerDetailAddress: jsonObject?.customerDetailAddress,
        customerAccount: jsonObject?.customerAccount,
        remark: jsonObject?.remark,
        am: jsonObject?.am?.map((item: any) => ({
          dealType: item.dealType,
          amId: item.amId,
          countries: item.countryCodes?.map((countryCode: any, index: number) => ({
            countryCode: countryCode,
            countryName: item.countryName?.length > index ? item.countryName[index] : '',
            countryNameZh: item.countryZh?.length > index ? item.countryZh[index] : '',
          }))
        })),
        invoice: jsonObject.invoice,
      };
      form.setFieldsValue(formData);
    } catch (error) {
      console.error('表单数据设置失败:', error);
      message.error('表单数据设置失败');
    }
  }, [props.data]);

  const onSubmmit = async (values: any) => {
    if (submitting) return;

    try {
      setSubmitting(true);

      // 检查必填字段
      const requiredFields = {
        subjectType: '请选择客户性质',
        ...(values.subjectType === '1' ? { groupId: '请选择客户组' } : {}),
        name: '请输入客户名称',
        countryAndState: '请选择客户所在国家/地区',
        registerCode: '请输入客户注册号',
        bd: '请选择对接BD',
        type: '请选择客户类型',
        source: '请选择客户来源'
      };

      const missingFields = Object.entries(requiredFields).filter(
        ([field]) => !(values[field] || values[field] === 0)
      );

      if (missingFields.length > 0) {
        missingFields.forEach(([field, msg]) => {
          form.setFields([{
            name: field,
            errors: [msg]
          }]);
        });
        return;
      }

      if (!checkAmList(values.am)) {
        message.error('同一产品类型, 同一国家，只能添加一个对接 AM');
        return;
      }

      const data = {
        name: values?.name,
        groupId: values?.groupId,
        json: JSON.stringify({
          contacts: values?.contacts,
          region: values?.countryAndState.state,
          bd: values?.bd,
          customerDetailAddress: values?.customerDetailAddress,
          customerAccount: values?.customerAccount,
          bdId: values?.bd,
          remark: values?.remark,
          am: values?.am?.map((item: any) => ({
            amId: item.amId,
            amName: bdList.find((x: any) => x.accountId?.toString() === item.amId)?.userName,
            dealType: item.dealType,
            dealName: dealTypes.find((x) => x.value.toString() === item.dealType)?.label,
            countryCodes: item.countries.map((item: any) => item.countryCode),
            countryZh: item.countries.map((item: any) => item.countryNameZh),
            countryEn: item.countries.map((item: any) => item.countryName),
          })),
          invoice: values?.invoice
        }),
        countryCode: values?.countryAndState.countryCode,
        type: values?.type,
        source: values?.source,
        registerCode: values?.registerCode,
        bdId: values?.bd,
        subjectType: values?.subjectType
      };

      await props.onFinish(data);
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      setSubmitting(false);
    }
  };

  const customerNameValidator = async (name: string) => {
    if (name === props.data?.name) {
      return Promise.resolve();
    }
    try {
      const response = await request(hostName, {
        method: 'POST',
        data: {
          functionKey: 'x_bfm_customer_name_exist',
          params: { name }
        }
      });
      if (response?.rs) {
        return Promise.reject('该客户名称已存在')
      }
    } catch (err: any) {
      console.error('客户名称验证失败:', err);
    }
    return Promise.resolve();
  }

  const amListValidator = (value: any) => {
    if (!value || value.length === 0) {
      return Promise.reject(new Error('至少需要添加一个 AM'));
    }
    return Promise.resolve();
  }

  const checkAmList = (amList?: any[]): boolean => {
    if (!amList) {
      return true;
    }
    const group = amList.reduce((acc: any, cur: any) => {
      if (acc[cur.dealType]) {
        acc[cur.dealType].push(cur);
      } else {
        acc[cur.dealType] = [cur];
      }
      return acc;
    }, {});

    // 检查每个组中国家不能重复
    let result = true;
    Object.keys(group).forEach((key) => {
      const list = group[key];
      const countryCodeList = list.reduce((acc: any[], cur: any) => {
        acc = acc.concat(cur.countries?.map((item: any) => item.countryCode))
        return acc;
      }, [])
      if (countryCodeList.includes('GLOBAL') && countryCodeList.length > 1) {
        result = false;
      }
      const countrySet = new Set(countryCodeList);
      if (countryCodeList.length !== countrySet.size) {
        result = false;
      }
    });

    return result;
  }

  const confirmDelete = (remove: (index: number | number[]) => void, index: number) => {
    remove(index);
  };

  return (
    <div className='max-w-[800px] pb-8'>
      <Form
        form={form}
        layout="vertical"
        onFinish={onSubmmit}
      >
        <div className="bg-white p-6 rounded-lg">
          <Form.Item
            label="客户性质"
            name="subjectType"
            required
            style={{ marginBottom: 24 }}
            rules={[{ required: true, message: '请选择客户性质' }]}
          >
            <Radio.Group
              disabled={isActive}
            >
              <Radio value={1}>公司</Radio>
              <Radio value={0}>个人</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.subjectType !== currentValues.subjectType}
          >
            {({ getFieldValue }) =>
              getFieldValue('subjectType') === 1 && (
                <Form.Item
                  label="客户组"
                  name="groupId"
                  required
                  style={{ marginBottom: 24 }}
                  rules={[{ required: true, message: '请选择客户组' }]}
                >
                  <Select showSearch placeholder="请选择客户组"
                    disabled={isActive}
                    filterOption={(input, option) => (option?.label as unknown as string)?.toLowerCase().includes(input.toLowerCase())}>
                    {groupList.map(group => (
                      <Select.Option key={group.id} value={group.id} label={group.name}>
                        {group.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              )
            }
          </Form.Item>

          <Form.Item
            label="客户主体名称"
            name="name"
            required
            style={{ marginBottom: 24 }}
            rules={[
              { required: true, message: '请输入客户名称' },
              {
                validator: async (_, value) => {
                  if (value) {
                    return customerNameValidator(value);
                  }
                }
              }
            ]}
          >
            <Input
              disabled={isActive}
              placeholder="请输入客户名称"
            />
          </Form.Item>

          <Form.Item
            label='客户所在国家/地区'
            name={'countryAndState'}
            rules={[
              { required: true, message: '请选择国家/地区' }
            ]}
          >
            <CountryStateSelect changeOnSelect disabled={isActive}
              onChange={() => form.resetFields(['invoice'])}
            />
          </Form.Item>

          <Form.Item
            label="客户详细地址"
            name="customerDetailAddress"
            style={{ marginBottom: 24 }}
          >
            <Input placeholder="请输入客户详细地址" />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.subjectType !== currentValues.subjectType}
          >
            {({ getFieldValue }) => {
              const subjectType = getFieldValue('subjectType');
              if (subjectType === 0) {
                return (
                  <Form.Item
                    label="证件号"
                    name="registerCode"
                    required
                    style={{ marginBottom: 24 }}
                    rules={[{ required: true, message: '请输入客户证件号' }]}
                  >
                    <Input placeholder="请输入客户的证件号" disabled={isActive} />
                  </Form.Item>
                );
              } else if (subjectType === 1) {
                return (
                  <Form.Item
                    label="客户注册号"
                    name="registerCode"
                    required
                    style={{ marginBottom: 24 }}
                    rules={[{ required: true, message: '请输入客户注册号' }]}
                  >
                    <Input placeholder="请输入合同中客户的注册号" disabled={isActive} />
                  </Form.Item>
                );
              }
            }}
          </Form.Item>

          <Form.List name="contacts" initialValue={[{}]}>
            {(fields, { add, remove }) => (
              <>
                <Form.Item label="客户联系人" required>
                  {fields.map(({ key, name, ...restField }) => (
                    <div key={key} style={{ display: 'flex', gap: 8, alignItems: 'baseline' }}>
                      <Form.Item
                        {...restField}
                        name={[name, 'title']}
                        rules={[{ required: true, message: '请选择职位' }]}
                      >
                        <Select style={{ width: 100 }} placeholder="职位">
                          <Select.Option value="hr">HR</Select.Option>
                          <Select.Option value="business">业务</Select.Option>
                          <Select.Option value="purchase">采购</Select.Option>
                          <Select.Option value="finance">财务</Select.Option>
                        </Select>
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'name']}
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input style={{ width: 160 }} placeholder="姓名" />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'phone']}
                        rules={[{ required: true, message: '请输入手机号' }]}
                      >
                        <Input style={{ width: 160 }} placeholder="手机号" />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'email']}
                        rules={[{ required: true, message: '请输入邮箱' }]}
                      >
                        <Input style={{ width: 160 }} placeholder="联系邮箱" />
                      </Form.Item>
                      {fields.length > 1 && (
                        <Popconfirm
                          title="确定要删除该联系人吗?"
                          onConfirm={() => confirmDelete(remove, name)}
                          okText="确定"
                          cancelText="取消"
                        >
                          <MinusCircleOutlined
                            style={{ color: '#ff4d4f', cursor: 'pointer' }}
                            title="删除联系人"
                          />
                        </Popconfirm>
                      )}
                    </div>
                  ))}
                </Form.Item>
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                    style={{ marginBottom: 24 }}
                  >
                    添加联系人
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>

          <Form.Item
            label="客户银行账户"
            name="customerAccount"
            style={{ marginBottom: 24 }}
          >
            <Input.TextArea
              placeholder="请输入客户账户"
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.countryAndState !== currentValues.countryAndState}
          >
            {({ getFieldValue }) => {
              const countryAndState = getFieldValue('countryAndState');
              const countryCode = countryAndState?.countryCode;
              if (['CN', 'KR'].includes(countryCode)) {
                return (
                  <Form.Item label="客户开票信息">
                    <Form.List
                      name='invoice'
                      initialValue={[]}
                    >
                      {(fields, { add, remove }) => (
                        <div style={{ display: 'flex', rowGap: 16, flexDirection: 'column' }}>
                          {fields.map((field) => (
                            <Card
                              key={field?.key}
                              title={`开票信息${field.name + 1}`}
                              extra={<CloseOutlined onClick={() => remove(field.name)} />}
                            >
                              {countryCode === 'CN' && (
                                <InvoiceCNItem key={field.key} field={field} />
                              )}
                              {countryCode === 'KR' && (
                                <InvoiceKRItem key={field.key} field={field} />
                              )}
                            </Card>
                          ))}

                          <Button
                            type="dashed"
                            onClick={() => add()}
                            block
                            icon={<PlusOutlined />}
                          >
                            添加开票信息
                          </Button>
                        </div>
                      )}
                    </Form.List>

                  </Form.Item>
                )
              }
              return <></>
            }}


          </Form.Item>

          <Divider orientation="left">其他信息</Divider>

          <div className='flex flex-col'>
            <Form.Item
              label="对接BD"
              name="bd"
              required
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', width: '300px' }}
              rules={[{ required: true, message: '请选择对接BD' }]}
            >
              <Select
                disabled={isActive}
                style={{ minWidth: 750 }}
                placeholder="请选择对接BD"
                showSearch
                filterOption={(input, option) =>
                  (option?.userName as unknown as string)?.toLowerCase().includes(input.toLowerCase()) ||
                  (option?.alias as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
                options={bdList.map(bd => {
                  return {
                    ...bd,
                    key: bd.accountId,
                    value: bd.accountId,
                    label: (
                      <>
                        <span style={{ fontWeight: 600 }}>{bd.userName} </span><span>{`[${bd.alias}]`}</span>
                      </>
                    )
                  }
                })}
              >
              </Select>
            </Form.Item>

            {isEdit && (
              <Form.List name="am" initialValue={[{}]}
              >
                {(fields, { add, remove }) => (
                  <>
                    <Form.Item label="对接 AM">
                      {fields.map(({ key, name, ...restField }) => (
                        <Row key={key} className='w-full' gutter={20}>
                          <Col span={5}>
                            <Form.Item
                              {...restField}
                              name={[name, 'dealType']}
                              rules={[{ required: true, message: '请选择产品类型' }]}
                            >
                              <Select
                                placeholder="请选择产品类型"
                                options={dealTypes?.map((item) => ({ ...item, value: item.value.toString() }))}
                              >
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, 'countries']}
                              rules={[{ required: true, message: '请选择国家' }]}
                            >
                              <AMCountrySelect countryList={allCountries} />
                            </Form.Item>
                          </Col>
                          <Col span={7}>
                            <Form.Item
                              {...restField}
                              name={[name, 'amId']}
                              rules={[{ required: true, message: '请选择对接AM' }]}
                            >
                              <Select
                                placeholder="请选择对接AM"
                                showSearch
                                filterOption={(input, option) =>
                                  (option?.userName as unknown as string)?.toLowerCase().includes(input.toLowerCase()) ||
                                  (option?.alias as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                                }
                                options={bdList.map(bd => {
                                  return {
                                    ...bd,
                                    key: bd.accountId?.toString(),
                                    value: bd.accountId?.toString(),
                                    label: (
                                      <>
                                        <span style={{ fontWeight: 600 }}>{bd.userName} </span><span>{`[${bd.alias}]`}</span>
                                      </>
                                    )
                                  }
                                })}
                              >
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            {fields.length > 0 && (
                              <Popconfirm
                                title="确定要删除该 AM 吗?"
                                onConfirm={() => confirmDelete(remove, name)}
                                okText="确定"
                                cancelText="取消"
                              >
                                <MinusCircleOutlined
                                  style={{ color: '#ff4d4f', cursor: 'pointer', marginTop: '8px' }}
                                  title="删除AM"
                                />
                              </Popconfirm>
                            )}
                          </Col>
                        </Row>
                      ))}
                      <Button
                        type="dashed"
                        onClick={() => add()}
                        block
                        icon={<PlusOutlined />}
                        style={{ marginBottom: 24 }}
                      >
                        添加 AM
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            )}

            <Form.Item
              label="客户类型"
              name="type"
              required
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
              rules={[{ required: true, message: '请选择客户类型' }]}
            >
              <Radio.Group
                disabled={isActive}
              >
                <Radio value={0}>直客</Radio>
                <Radio value={1}>分销商</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              label="客户来源"
              name="source"
              required
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
              rules={[{ required: true, message: '请选择客户来源' }]}
            >
              <Radio.Group
                disabled={isActive}
              >
                <Radio value={0}>自拓客户</Radio>
                <Radio value={1}>公司资源</Radio>
                <Radio value={2}>公司举办会议资源</Radio>
                <Radio value={3}>渠道资源</Radio>
                <Radio value={4}>GS自助平台</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              label="备注"
              name="remark"
              style={{ marginBottom: 24 }}
            >
              <Input.TextArea style={{ width: '100%' }} placeholder="备注"
                disabled={isActive}
              />
            </Form.Item>

          </div>

          <Form.Item name="_submitError" style={{ marginBottom: 24 }}>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" size="large" block loading={submitting}>
              保存
            </Button>
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default Main;