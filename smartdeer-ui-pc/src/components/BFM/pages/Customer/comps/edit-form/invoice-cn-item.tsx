import { Card, Col, Form, FormListFieldData, Input, Row, Select } from "antd";


type InvoiceCNItemProps = {
  field: FormListFieldData
}

const InvoiceCNItem: React.FC<InvoiceCNItemProps> = (props) => {
  const { field } = props;

  return (
    <Row gutter={24}>
      <Col span={12}>
        <Form.Item label={'发票类型'} 
          name={[field.name, 'invoiceType']} 
          rules={[{ required: true, message: '请选择发票类型' }]}
        >
          <Select
            placeholder="请选择发票类型"
            options={[
              { value: '增值税专用发票', label: '增值税专用发票' },
              { value: '增值税普通发票', label: '增值税普通发票' },
              { value: '增值税差额发票', label: '增值税差额发票' },
            ]}
          />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="抬头名称" 
          name={[field.name, 'titleName']} 
          rules={[{ required: true, message: '请输入抬头名称' }]}
        >
          <Input placeholder="请输入抬头名称" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label="纳税人识别号" 
          name={[field.name, 'taxpayerIdentificationNumber']} 
          rules={[{ required: true, message: '请输入纳税人识别号' }]}
        >
          <Input placeholder="请输入纳税人识别号" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label={'项目名称'} name={[field.name, 'projectName']}
          rules={[{ required: true, message: '请输入项目名称' }]}
        >
          <Select
            placeholder="请选择项目名称"
            options={[
              { value: '服务费', label: '服务费' },
              { value: '咨询费', label: '咨询费' },
            ]}
          />
        </Form.Item>
      </Col>
    </Row>
  );
}
export default InvoiceCNItem;