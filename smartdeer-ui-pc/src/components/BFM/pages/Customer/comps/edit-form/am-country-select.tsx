import { CountryStateSelect } from "@/components/app";
import { usePropsValue } from "@smartdeer-ui/pc/utils/hooks";
import { Checkbox, Select } from "antd";
import { useEffect, useState } from "react";

const globalCountry = {
  countryCode: 'GLOBAL',
  countryName: 'Global',
  countryNameZh: '全球'
}

type Country = {
  countryCode: string,
  countryName: string,
  countryNameZh: string,
}

type AMCountrySelectProps = {
  value?: Country[],
  onChange?: (val: Country[]) => void,
  countryList?: Country[],
  selsectedCountries?: Country[],
}

const AMCountrySelect: React.FC<AMCountrySelectProps> = (props) => {
  const { countryList = [], selsectedCountries = [] } = props;
  const p = {...props, defaultValue: []}
  const [value, setValue] = usePropsValue<Country[]>(p)

  const [globalChecked, setGlobalChecked] = useState<boolean>(false);

  useEffect(() => {
    const hasGlobal = !!(value?.find(item => item.countryCode === 'GLOBAL'))
    setGlobalChecked(hasGlobal)
    if (hasGlobal && value.length !== 1) {
      setValue([globalCountry])
    } 

  }, [value])

  return (
    <div className="flex items-center">
      <Checkbox style={{width: 80}}
        checked={globalChecked}
        onChange={(e) => {
          if (e.target.checked) {
            setValue([globalCountry])
          } else {
            setValue([])
          }
        }}
      >
        全球
      </Checkbox>

      <Select
        placeholder="请选择国家"
        disabled={globalChecked}
        mode="multiple"
        value={value.filter((item) => item.countryCode !== 'GLOBAL').map((item) => item.countryCode)}
        onChange={(value, option) => {
          setValue(option as Country[]);
        }}
        fieldNames={{value: 'countryCode', label: 'countryNameZh'}}
        options={countryList}
        filterOption={(input, option) => 
          (option?.countryNameZh as string)?.toLowerCase().includes(input.toLowerCase())
        }
      />
    </div>
  );
}
export default AMCountrySelect;