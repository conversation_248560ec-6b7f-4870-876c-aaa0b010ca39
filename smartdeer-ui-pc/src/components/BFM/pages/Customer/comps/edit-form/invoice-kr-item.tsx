import { Card, Col, Form, FormListFieldData, Grid, Input, Row, Select } from "antd";


type InvoiceKRItemProps = {
  field: FormListFieldData
}

const InvoiceKRItem: React.FC<InvoiceKRItemProps> = (props) => {
  const { field } = props;

  return (
    <Row gutter={24}>
      <Col span={12}>
        <Form.Item label={'产业登录证号码'} 
          name={[field.name, 'businessRegistrationNumber']} 
          rules={[{ required: true, message: '请输入产业登录证号码' }]}
        >
          <Input placeholder="产业登录证号码 산업등록증번호" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label={'公司全称'} 
          name={[field.name, 'companyName']} 
          rules={[{ required: true, message: '请输入公司全称' }]}
        >
          <Input placeholder="公司全称 상호(법인명명)" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label={'法人代表姓名'} 
          name={[field.name, 'legalRepresentativeName']} 
          rules={[{ required: true, message: '请输入法人代表姓名' }]}
        >
          <Input placeholder="法人代表姓名 법인(대표자)성명" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label={'公司地址'} 
          name={[field.name, 'companyAddress']} 
          rules={[{ required: true, message: '请输入公司地址' }]}
        >
          <Input placeholder="公司地址 사업장 주소" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label={'接收国税厅开票信息邮箱1'} 
          name={[field.name, 'email1']} 
          rules={[{ required: true, message: '请输入接收国税厅开票信息邮箱1' }]}
        >
          <Input placeholder="接收国税厅开票信息邮箱1 이메일1" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label={'接收国税厅开票信息邮箱2'} 
          name={[field.name, 'email2']} 
        >
          <Input placeholder="接收国税厅开票信息邮箱2 이메일2" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label={'业务类型'} 
          name={[field.name, 'businessType']} 
          rules={[{ required: true, message: '请输入业务类型' }]}
        >
          <Input placeholder="业务类型 업태" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item label={'营业内容细项'} 
          name={[field.name, 'businessDetails']} 
          rules={[{ required: true, message: '请输入营业内容细项' }]}
        >
          <Input placeholder="营业内容细项 종목" />
        </Form.Item>
      </Col>
    </Row>
  )
}
export default InvoiceKRItem;