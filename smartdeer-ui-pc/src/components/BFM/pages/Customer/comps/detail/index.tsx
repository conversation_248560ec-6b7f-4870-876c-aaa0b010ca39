import { functionRuntime } from "@/services/run";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { Button, Col, message, Row, Space, Spin, Table, Tag, Typography } from "antd";
import { ColumnsType } from "antd/es/table";
import { CSSProperties, useEffect, useState } from "react";

type CustomerDetailViewProps = {
  id?: number | string
  onAuditProgressClick: (data: any, canAuditCancel: boolean) => void
  onDataFetched?: (data: any) => void
}

const registerCodeNames: Record<string, string> = {
  '0': '客户证件号',
  '1': '客户注册号',
}

const CustomerDetailView: React.FC<CustomerDetailViewProps> = (props) => {
  const { id, onAuditProgressClick, onDataFetched } = props;

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);
  const fetchData = async () => {
    setLoading(true);
    try {
      const { rs } = await functionRuntime(0, 'x_bfm_customer_get', { id });
      setData(rs);
      onDataFetched?.(rs);
    } catch (err: any) {
      message.error('数据加载失败，' + err.message);
    }
    setLoading(false);
  }

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id])

  if (loading) {
    return (
      <div style={{ textAlign: 'center', paddingTop: 100 }}>
        <Spin></Spin>
      </div>
    )
  }

  if (!data) {
    return <></>
  }

  const getAMTable = (am?: any[]) => {
    if (!am || am.length === 0) {
      return '-'
    }
    const group = am.reduce((acc: any, cur: any) => {
      const key = cur.dealType
      if (!acc[key]) {
        acc[key] = []
      }
      acc[key].push(cur)
      return acc
    }, {})

    const dataSource: any[] = [];
    Object.entries(group).forEach(([key, value]) => {
      const list = value as any[];
      list.forEach((item: any, index) => dataSource.push({...item, rowSpan: index === 0 ? list.length : 0}))
    })

    const tableColumns: ColumnsType = [
      { 
        title: '产品类型', 
        dataIndex: 'dealName',
        render: (value, record) => ({
          children: value,
          props: {
            rowSpan: record.rowSpan
          }
        })
      },
      {
        title: '国家',
        dataIndex: 'countryZh',
        render: (value, record) => value?.join('，')
      },
      { 
        title: 'AM', 
        dataIndex: 'amName',
      },
    ]

    return (
      <Table
        pagination={false}
        bordered
        size="small"
        columns={tableColumns}
        dataSource={dataSource}
      />
    )
  }

  const getInvoiceTable = (invoice?: any[], countryCode?: string) => {
    if (!invoice || invoice.length === 0) {
      return '-';
    }
    let tableColumns: ColumnsType = [];
    if (countryCode === 'CN') {
      tableColumns = [
        { 
          title: '发票类型', 
          dataIndex: 'invoiceType',
        },
        { 
          title: '抬头名称', 
          dataIndex: 'titleName',
        },
        { 
          title: '纳税人识别号', 
          dataIndex: 'taxpayerIdentificationNumber',
        },
        { 
          title: '项目名称', 
          dataIndex: 'projectName',
        }
      ]
    } else if (countryCode === 'KR') {
      tableColumns = [
        { 
          title: '产业登录证号码', 
          dataIndex: 'businessRegistrationNumber',
        },
        { 
          title: '公司全称', 
          dataIndex: 'companyName',
        },
        { 
          title: '法人代表姓名', 
          dataIndex: 'legalRepresentativeName',
        },
        { 
          title: '公司地址', 
          dataIndex: 'companyAddress',
        },
        { 
          title: '接收国税厅开票信息邮箱1', 
          dataIndex: 'email1',
        },
        { 
          title: '接收国税厅开票信息邮箱2', 
          dataIndex: 'email2',
          render: (value, record) => value || '-',
        },
        { 
          title: '业务类型', 
          dataIndex: 'businessType',
        },
        { 
          title: '营业内容细项', 
          dataIndex: 'businessDetails',
        }
      ]
    }

    return (
      <div style={{ overflowX: 'auto', width: '100%' }}>
        <Table
          pagination={false}
          bordered
          size="small"
          columns={tableColumns}
          dataSource={invoice}
          scroll={{ x: 'max-content' }}
        />

      </div>
    ) 
  }

  const jsonObject = JSON.parse(data.json)

  const dataInfo: {name: string, content: React.ReactNode}[] = [
    {
      name: '客户状态',
      content: (
        data.status === 0 
        ? <Tag color="default" bordered={false} style={{color: 'gray'}}>未生效</Tag> 
        : <Tag color="green"  bordered={false}>已生效</Tag>
      ),
    },
    {
      name: '审核状态',
      content: (
        <Space>
          {
            data.auditStatus === 0 && (
              <Tag color="default" bordered={false} style={{color: 'gray'}}>待发起审批</Tag>
            )
          }
          {
            data.auditStatus === 1 && (
              <>
                <Tag color="processing"  bordered={false}>审核中</Tag>
                <Button
                  type="link" 
                  size="small"
                  onClick={() => onAuditProgressClick(data, true)}
                >
                  查看进度
                </Button>
              </>
            )
          }
          {
            data.auditStatus === 2 && <Tag color="success"  bordered={false}>审核通过</Tag>
          }
          {
            data.auditStatus === 3 && (
              <>
                <Tag color="error"  bordered={false}>审核驳回</Tag>
                <Button 
                  type="link" 
                  size="small"
                  onClick={() => onAuditProgressClick(data, false)}
                >
                  查看原因
                </Button>
              </>
            )
          }
        </Space>
      ),
    },
    {
      name: '客户主体名称',
      content: data.name,
    },
    {
      name: '客户编码',
      content: data.code,
    },
    {
      name: '所在客户组',
      content: data?.groupName,
    },
    {
      name: '客户性质',
      content: data?.subjectTypeStr,
    },
    {
      name: '客户所在国家/地区',
      content:  [data?.countryNameZh, jsonObject?.region==='all'?'':jsonObject?.region].filter((x) => !!x).join(' / ')
    },
    {
      name: '客户详细地址',
      content: jsonObject?.customerDetailAddress,
    },
    {
      name: registerCodeNames[data?.subjectType] ,
      content: data?.registerCode,
    },
    {
      name: '客户联系人',
      content: (
        <Table
          bordered
          columns={[
            {key: 'title', title: '职位', dataIndex: "title"},
            {key: 'name', title: '姓名', dataIndex: "name"},
            {key: 'phone', title: '电话', dataIndex: "phone"},
            {key: 'email', title: '邮箱', dataIndex: "email"}
          ]}
          dataSource={jsonObject?.contacts}
          size='small'
          pagination={false}
        >
        </Table>
      ),
    },
    {
      name: '客户银行账户',
      content: jsonObject?.customerAccount,
    },
    ...(['CN', 'KR'].includes(data?.countryCode) ? [{
      name: '开票信息',
      content: getInvoiceTable(jsonObject?.invoice, data?.countryCode),
    }] : []),
    {
      name: '对接BD',
      content: data?.bdName,
    },
    {
      name: '对接AM',
      content: getAMTable(jsonObject?.am)
    },
    {
      name: '客户类型',
      content: data?.typeStr,
    },
    {
      name: '客户来源',
      content: data?.sourceStr,
    },
    {
      name: '备注',
      content: jsonObject?.remark,
    },
  ]

  const tdStyle: CSSProperties = {backgroundColor: 'white', border: '1px solid rgb(240, 240, 240)', padding: '12px'}

  return (
    <>
      <table  className="overflow-x-auto w-full" 
        style={{tableLayout: 'fixed'}}
      >
        {
          dataInfo.map((item: any, index: number) => {
            return (
              <tr key={index} style={{width: '100%'}}>
                <td style={{...tdStyle, color: 'gray', width: '20%'}}>
                  {item.name}
                </td>
                <td style={{...tdStyle, overflowX: 'auto'}}>
                  {item.content}
                </td>

              </tr>
            )
          })
        }

      </table>
    </>
    
  )
}
export default CustomerDetailView;