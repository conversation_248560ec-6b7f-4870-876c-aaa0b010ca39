import { Button, Col, Row, Space, Table, Tag } from "antd";
import { CSSProperties } from "react";

type CustomerDetailViewProps = {
  data: any,
  onAuditProgressClick: (data: any, canAuditCancel: boolean) => void
}

const CustomerDetailView: React.FC<CustomerDetailViewProps> = (props) => {
  const { data, onAuditProgressClick } = props;

  if (!data) {
    return <></>
  }

  const dataInfo: {name: string, content: React.ReactNode}[] = [
    {
      name: '客户主体名称',
      content: data.name,
    },
    {
      name: '所在客户组',
      content: data.groupName,
    },
    {
      name: '客户性质',
      content: data.subjectTypeStr,
    },
    {
      name: '对接BD',
      content: data.bdName,
    },
    {
      name: '客户类型',
      content: data.typeStr,
    },
    {
      name: '客户来源',
      content: data.sourceStr,
    },
  ]

  const tdStyle: CSSProperties = {backgroundColor: 'white', border: '1px solid rgb(240, 240, 240)', padding: '12px'}

  return (
    <table width="100%">
      {
        dataInfo.map((item: any, index: number) => {
          return (
            <tr key={index}>
              <td style={{...tdStyle, color: 'gray', width: '20%'}}>
                {item.name}
              </td>
              <td style={{...tdStyle,}}>
                {item.content}
              </td>

            </tr>
          )
        })
      }

    </table>
  )
}
export default CustomerDetailView;