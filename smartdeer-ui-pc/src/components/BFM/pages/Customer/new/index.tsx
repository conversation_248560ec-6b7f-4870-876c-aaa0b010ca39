import React from 'react';
import { Form, Input, Select, Radio, Space, Divider, Typography, Button, Modal, Popconfirm, message } from 'antd';
import { MinusCircleOutlined, PlusOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { request, useNavigate } from '@umijs/max';
import { debounce } from 'lodash';
import EditForm from '../comps/edit-form';
import {functionRuntime} from '@/services/run';


const { Title } = Typography;
const APP_CUSTOMER = process.env.APP_CUSTOMER;

  
const hostName = 'https://sage.smartdeer.com/bfm/v1/sage/entity/33/function/runtime';

const Page: React.FC = ({initData, onSuccess}) => {
  const navigate = useNavigate();

  const onFinish = async (values: any) => {
    try {
      const response = await request(hostName, {
        method: 'POST',
        data: {
          functionKey: 'x_bfm_customer_add',
          params:  {
            ...values,
            ...initData
          }
        }
      });

      if (response?.rs) {
        navigate('/client-manage/all/list');
      } else {
        message.error('创建失败：' + response?.message)
      }
    } catch (error: any) {
      message.error('创建失败：' + error?.message)
    }
  };

  return (
    <div className='max-w-[800px] pb-8'>
      <div className="flex items-center gap-4 mb-6 justify-between">
        <Title level={4} style={{ margin: 0 }}>
          新建客户
        </Title>
        <Button 
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate(-1)}
        >
          返回
        </Button>
      </div>
      <EditForm
        onFinish={onFinish}
        data={undefined}
      />
    </div>
  );
};

export default Page;