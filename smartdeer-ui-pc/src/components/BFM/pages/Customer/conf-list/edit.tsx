import React from "react";
import { Form, Input, Select, Radio, Space, Typography, message } from 'antd';
import { request } from '@umijs/max';

const { Title } = Typography;
const hostName = '/v1/sage/entity/33/function/runtime';

interface CustomerData {
  id: string;
  name: string;
  registerCode: string;
  countryCode: string;
  groupId: string;
  type: number;
  source: number;
  json: string;
}

interface EditFormProps {
  customerData: CustomerData;
  onSuccess: () => void;
}

const EditForm: React.FC<EditFormProps> = ({ customerData, onSuccess }) => {
  const [form] = Form.useForm();
  const [editingField, setEditingField] = React.useState<string | null>(null);

  React.useEffect(() => {
    form.setFieldsValue({
      name: customerData.name,
      groupId: customerData.groupId,
      countryCode: customerData.countryCode,
      type: customerData.type === 1 ? 'direct' : 'channel',
      source: customerData.source === 1 ? 'self' : 'other',
      registerCode: customerData.registerCode
    });
  }, [customerData, form]);

  const handleFieldUpdate = async (fieldName: string, value: any) => {
    try {
      const values = form.getFieldsValue();
      
      const response = await request(hostName, {
        method: 'POST',
        data: {
          functionKey: 'x_bfm_customer_update',
          params: {
            id: customerData.id,
            name: values.name || customerData.name,
            groupId: values.groupId || customerData.groupId,
            json: customerData.json,
            countryCode: values.countryCode || customerData.countryCode,
            type: values.type ? (values.type === 'direct' ? 1 : 2) : customerData.type,
            source: values.source ? (values.source === 'self' ? 1 : 2) : customerData.source,
            registerCode: values.registerCode || customerData.registerCode
          }
        }
      });

      if (response?.rs) {
        message.success('更新成功');
        setEditingField(null);
        onSuccess();
      } else {
        message.error('更新失败：' + response?.message);
      }
    } catch (error) {
      console.error(error);
      message.error('更新失败，请稍后重试');
    }
  };

  const renderField = (fieldName: string, value: any, inputComponent: React.ReactNode) => {
    return editingField === fieldName ? (
      <Form.Item name={fieldName} noStyle>
        {React.cloneElement(inputComponent as React.ReactElement, {
          onBlur: () => handleFieldUpdate(fieldName, form.getFieldValue(fieldName))
        })}
      </Form.Item>
    ) : (
      <div onClick={() => setEditingField(fieldName)} style={{minHeight: '32px', padding: '4px 11px', cursor: 'pointer'}}>
        {value || '点击编辑'}
      </div>
    );
  };

  return (
    <div className='p-6'>
      <Title level={4} style={{ marginBottom: 24 }}>基本信息</Title>
      
      <Form
        form={form}
        layout="vertical"
      >
        <div>
          <Form.Item
            label="客户名称"
            required
            style={{ marginBottom: 24 }}
          >
            {renderField('name', customerData.name,
              <Select placeholder="请选择客户名称">
                <Select.Option value="bd1">BD1</Select.Option>
                <Select.Option value="pm1">PM1</Select.Option>
                <Select.Option value="hr1">HR1</Select.Option>
                <Select.Option value="finance1">财务1</Select.Option>
              </Select>
            )}
          </Form.Item>

          <Form.Item 
            label="客户组" 
            required
            style={{ marginBottom: 24 }}
          >
            {renderField('groupId', customerData.groupId,
              <Input placeholder="请输入客户组ID" />
            )}
          </Form.Item>

          <Form.Item
            label="国家代码"
            required
            style={{ marginBottom: 24 }}
          >
            {renderField('countryCode', customerData.countryCode,
              <Input placeholder="请输入国家代码" />
            )}
          </Form.Item>

          <Form.Item
            label="客户类型" 
            required
            style={{ marginBottom: 24 }}
          >
            {renderField('type', customerData.type === 1 ? '直客' : '分销商',
              <Radio.Group>
                <Radio value="direct">直客</Radio>
                <Radio value="channel">分销商</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          <Form.Item
            label="客户来源"
            required
            style={{ marginBottom: 24 }}
          >
            {renderField('source', customerData.source === 1 ? '自拓客户' : '其他',
              <Radio.Group>
                <Radio value="self">自拓客户</Radio>
                <Radio value="other">其他</Radio>
              </Radio.Group>
            )}
          </Form.Item>

          <Form.Item
            label="注册码"
            required
            style={{ marginBottom: 24 }}
          >
            {renderField('registerCode', customerData.registerCode,
              <Input placeholder="请输入注册码" />
            )}
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default EditForm;
