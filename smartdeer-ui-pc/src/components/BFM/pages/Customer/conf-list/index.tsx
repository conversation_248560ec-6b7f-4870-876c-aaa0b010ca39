import React, { useEffect } from 'react';
import { Button, Col, message, Modal, Row, Table, TablePaginationConfig, Typography, Card, Popconfirm, Space, Tag, Drawer } from 'antd';
import { functionRuntime } from '@/services/run';
import Column from 'antd/es/table/Column';
import CurdTableColumnContent from '@smartdeer-ui/pc/components/crud-table/table-column-content'
import { PlusOutlined, SearchOutlined, EyeOutlined, AuditOutlined, EditOutlined, HistoryOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { CrudTable, CurdTableColumnType, TableSearch } from '@smartdeer-ui/pc';
import { ColumnsType } from '@smartdeer-ui/pc/components/table-search/typing';
import EditForm from '../comps/conf-edit-form';
import CustomerDetailView from '../comps/conf-detail';
import { AuditDetail } from '@/components/app';

const { Title } = Typography;

interface CustomerData {
  id: number;
  code: string;
  groupId: number;
  creatorId: number;
  updateTime: number;
  source: number;
  sourceChannelName: string;
  type: number;
  bdId: number;
  isDeleted: number;
  createTime: number;
  deleteTime: number;
  countryCode: string;
  name: string;
  json: string;
  auditStatus: number;
  registerCode: string;
  status: number;
  operatorName: string;
  bdName: string;
  confKey: string;
  confValue: string;
  customerId: number;
  uniqueKey: string;
}

const tableColumns: CurdTableColumnType[] = [
  {
    title: "配置键",
    key: "confKey",
    width: 150,
    fixed: "left",
  },
  {
    title: "配置值",
    key: "confValue",
    width: 200,
    ellipsis: true,
  },
  {
    title: "客户ID",
    key: "customerId",
    width: 100,
  },
  {
    title: "唯一标识",
    key: "uniqueKey",
    width: 150,
  },
  {
    title: "创建时间",
    key: "createTime",
    width: 180,
    type: "template",
    template: "<div>*{createTime | formatDate(YYYY-MM-DD HH:mm:ss)}</div>"
  },
  {
    title: "更新时间",
    key: "updateTime",
    width: 180,
    type: "template",
    template: "<div>*{updateTime | formatDate(YYYY-MM-DD HH:mm:ss)}</div>"
  },
  {
    title: "状态",
    key: "isDeleted",
    width: 100,
    type: 'status',
    template: [
      {
        vif: '*{isDeleted}===0',
        name: '正常',
        color: 'success',
      },
      {
        vif: '*{isDeleted}===1',
        name: '已删除',
        color: 'error',
      }
    ]
  },
  {
    title: "操作",
    key: "action",
    type: "action",
    fixed: "right",
    width: 150,
    template: [
      {
        "name": "查看",
        "type": "modal",
      },
      {
        "name": "编辑",
        "type": "modal",
      }
    ]
  }
];

const filterColumns: ColumnsType[] = [
  {
    title: "ID",
    type: 'input',
    field: 'id',
    action: { value: 'eq' },
    props: {
      placeholder: '请输入ID',
      allowClear: true
    }
  },
  {
    title: "名称",
    type: 'input',
    field: 'name',
    props: {
      placeholder: '请输入名称',
      allowClear: true
    }
  },
  {
    title: "操作人",
    type: 'input',
    field: 'operatorName',
    props: {
      placeholder: '请输入操作人',
      allowClear: true
    }
  }
];

const Page: React.FC = () => {
  const [data, setData] = React.useState<CustomerData[]>();
  const [loading, setLoading] = React.useState(false);
  const [tableParams, setTableParams] = React.useState<any>({
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`
    },
    searchInfo: {}
  });

  const [detailDrawerData, setDetailDrawerData] = React.useState<any>({
    open: false,
    record: null,
    status: 'view', // or 'edit'
    editButtonLoading: false,
  });
  const [auditDetailStatus, setAuditDetailStatus] = React.useState<any>({
    open: false,
    record: null,
    canAuditCancel: true,
  })

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await functionRuntime(0, "search_bfm_customer_conf_list", {
        "current": tableParams.pagination.current,
        "limit": tableParams.pagination.pageSize,
        "size": tableParams.pagination.pageSize,
        "searchInfo": {
          ...tableParams.searchInfo, 
          "orders": [{
            key: 'id',
            asc: 'desc',
          }],
          "key": "customerId",
          "action": "eq",
          "value": "1"
        }
      });
      const dataInfo = res.dataInfo;
      setData(dataInfo)
      setTableParams({
        ...tableParams,
        pagination: {...tableParams.pagination, total: res.total}
      })
    } catch (err: any) {
      message.error(err)
    }
    setLoading(false)
  };

  React.useEffect(() => {
    fetchData()
  }, [
    tableParams.pagination?.current,
    tableParams.pagination?.pageSize,
    tableParams.searchInfo
  ]);

  const handleChangeTable = (pagination: TablePaginationConfig) => {
    setTableParams({
      ...tableParams,
      pagination
    });
  };

  const handleAddCustomer = () => {
    history.push("/client-manage/conf-new")
  }

  const onShowDrawer = (column: any, record: any) => {
    debugger
    setDetailDrawerData({open: true, record})
  }

  const handleClickSearch = (values: Record<string, any>) => {
    setTableParams({
      ...tableParams,
      searchInfo: values
    })
  }

  const handleEditClick = async (record: any) => {
    console.log(record)
    const id = record.id;
    if (record.status !== 0 || [1, 2].includes(record.auditStatus)) {
      message.error('已生效或审核中的客户，不可编辑')
      return;
    }

    setDetailDrawerData({...detailDrawerData, editButtonLoading: true})
    // 判断是否在审批中
    let isInApprove = record.auditStatus === 1;

    if (isInApprove) {
      Modal.warning({
        title: '提示',
        content: '您有一个正在审批中的任务，编辑后将撤回审核中的信息，本次修改信息需要重新进行审核，确认编辑吗',
        okText: '撤销审批，并编辑',
        onOk: async () => {
          try {
            await functionRuntime(0, 'x_bfm_customer_cancel_approve', {id})
            setDetailDrawerData({...detailDrawerData, record: {...record, auditStatus: 0}, status: 'edit', editButtonLoading: false})
          } catch (err: any) {
            message.error('xxxxxxxxxx')
            setDetailDrawerData({...detailDrawerData, editButtonLoading: false})
          }
        },
        onCancel: () => {
          setDetailDrawerData({...detailDrawerData, editButtonLoading: false})
        },
        closable: true
      })
    } else {
      setDetailDrawerData({...detailDrawerData, status: 'edit', editButtonLoading: false})
    }
  }

  const handleEditFinish = async (values: any) => {
    console.log('handleEditFinish', values)
    try {
      const response = await functionRuntime(0, 'x_bfm_customer_update', {...values, id: detailDrawerData.record?.id});
      if (response?.rs) {
        message.success('更新成功');
        setDetailDrawerData({open: false})
        fetchData();
      } else {
        message.error('更新失败：' + response?.message);
      }
    } catch (error) {
      console.error(error);
      message.error('更新失败，请稍后重试');
    }
  }

  const handleStartAuditClick = async (record: any) => {
    try {
      await functionRuntime(0, 'x_bfm_customer_start_approve', {id: record.id})
      message.success(`${record.name} 成功发起审批`);
      fetchData();
    } catch (err: any) {
      message.error('发起审批失败');
    }
  }

  const handleAuditProgressClick = (data: any, canAuditCancel: boolean) => {
    setDetailDrawerData({open: false})
    setAuditDetailStatus({open: true, record: data, canAuditCancel})
  }

  useEffect(() => {
    console.log('detailDrawerData', detailDrawerData)
  }, [detailDrawerData])

  return (
    <Card className="m-4 shadow-lg rounded-lg">
      <div className="flex justify-between items-center mb-8">
        <Title level={4} className="!mb-0">客户配置</Title>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={handleAddCustomer}
          size="large"
        >
          新建客户配置
        </Button>
      </div>

      <Card className="mb-6">
        <TableSearch
          type='searchItems'
          downloadExcelRender={false}
          showSearchButton
          onSearch={handleClickSearch}
          columns={filterColumns}
        />
      </Card>

      <Table
        rowKey={(record) => record.id}
        dataSource={data}
        pagination={tableParams.pagination}
        loading={loading}
        onChange={handleChangeTable}
        className="shadow-sm"
        scroll={{ x: 1200 }}
      >
        {
          tableColumns.map((column: any) => {
            return (
              <Column
                title={column.title}
                dataIndex={column.dataIndex || column.key}
                key={column.key}
                width={column.width}
                fixed={column.fixed}
                render={(value: any, record: any, index: number) => {
                  if (column.key === 'action') {
                    return (
                      <Space size={0}>
                        <Button 
                          type="link" 
                          size="middle"
                          icon={<EyeOutlined />}
                          onClick={() => onShowDrawer(column, record)}
                        >
                          查看
                        </Button>
                      </Space>
                    )
                  }
                  return (
                    <CurdTableColumnContent
                      column={column}
                      text={value}
                      index={index}
                      record={record}
                      onShowModal={onShowDrawer}
                      onShowDrawer={() => {}}
                    />
                  )
                }}
              />
            )
          })
        }
      </Table>

      <Drawer
        open={detailDrawerData.open}
        title={
          <div className="flex items-center justify-between">
            <span>客户详情</span>
            {/* <Button 
              type="link"
              icon={<HistoryOutlined />}
              onClick={() => history.push(`/client-manage/${detailDrawerData.record?.id}/logs`)}
              className="text-orange-500 hover:text-orange-600"
            >
              查看日志
            </Button> */}
          </div>
        }
        onClose={() => setDetailDrawerData({open: false})}
        extra={
          detailDrawerData.status === 'edit' ? (
            <Button 
              type="primary"
              onClick={() => setDetailDrawerData({...detailDrawerData, status: 'view'})}
            >
              取消编辑
            </Button>
          ) : (
            <Button 
              type="primary"
              icon={<EditOutlined />}
              // onClick={() => setDetailDrawerData({...detailDrawerData, status: 'edit'})}
              onClick={() => handleEditClick(detailDrawerData.record)}
              loading={detailDrawerData.editButtonLoading}
            >
              编辑
            </Button>
          )
        }
        width={900}
      >
        {
          detailDrawerData.status === 'edit' ? (
            <EditForm
              data={detailDrawerData.record}
              onFinish={handleEditFinish}
            />
          ) : (
            <div className="bg-white p-6 rounded-lg">
              <CustomerDetailView 
                data={detailDrawerData.record}
                onAuditProgressClick={handleAuditProgressClick}
              />
            </div>
          )
        }
      </Drawer>

      <Drawer
        title="审核进度"
        placement="right"
        width={900}
        onClose={() => setAuditDetailStatus({open: false})}
        open={auditDetailStatus.open}
      >
        <AuditDetail
          open={auditDetailStatus.open}
          getFunctionKey='x_bfm_customer_get_approve_info'
          cancelFunctionKey='x_bfm_customer_cancel_approve'
          onClose={() => setAuditDetailStatus({open: false})}
          id={auditDetailStatus.record?.id}
          onAuditCanceled={() => {setAuditDetailStatus({open: false}); fetchData();}}
          canCancelAudit={auditDetailStatus.canAuditCancel}
          detail={(
            <CustomerDetailView 
              data={auditDetailStatus.record}
              onAuditProgressClick={handleAuditProgressClick}
            />
          )}
        />
      </Drawer>
    </Card>
  );
};

export default Page;
