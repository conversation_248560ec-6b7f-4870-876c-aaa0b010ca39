import { TableSearch } from '@smartdeer-ui/pc';
import {
  Button,
  Card,
  Table,
  TablePaginationConfig,
  Typography,
  message,
} from 'antd';
import { merge } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { PermissionButton } from '..';

import { PlusOutlined } from '@ant-design/icons';
import { tableController } from './table-controller';

export { tableController };

const { Title } = Typography;

// 搜索项类型定义
interface SearchItem {
  key: string;
  action: string;
  value: any;
}

// 排序项类型定义
interface OrderItem {
  key: string;
  asc: 'asc' | 'desc';
}

// 搜索信息类型定义
interface SearchInfoType {
  searchItems: Array<SearchItem>;
  orders?: Array<OrderItem>;
  [key: string]: any;
}

// 表格参数类型定义
interface TableParamsType {
  pagination: TablePaginationConfig;
  searchInfo: SearchInfoType;
}

interface BfmTableListProps {
  /** 表格唯一ID，用于外部调用刷新方法 */
  tableId: string;
  /** 页面标题 */
  pageTitle?: string;
  /** 新建按钮属性 */
  newButtonProps?: {
    title?: string;
    children?: React.ReactNode;
    onClick?: () => void;
    [key: string]: any;
  };
  /** 表格属性 */
  tableProps?: {
    columns?: any[];
    rowKey?: string;
    pagination?: any;
    onChange?: (pagination: any) => void;
    [key: string]: any;
  };
  /** 搜索组件属性 */
  searchProps?: any;
  /** 获取列表数据的方法 */
  onFetchList: (params: {
    pagination: TablePaginationConfig;
    searchInfo: {
      searchItems: SearchItem[];
      orders?: OrderItem[];
      [key: string]: any;
    };
  }) => Promise<{ total: number; dataInfo: any[] }>;
  /** 默认搜索条件 */
  defaultSearchInfo?: {
    searchItems: SearchItem[];
    [key: string]: any;
  };
  /** 组件准备好后的回调，返回refetch方法 */
  onReady?: (refetch: () => void) => void;
}

const defaultNewButtonProps = {
  title: '新建',
  children: '新建',
  type: 'primary',
  icon: <PlusOutlined />,
  onClick: () => {
    console.log('[BfmTableList] 请传入必要参数');
  },
};

const defaultTableProps = {
  columns: [],
  rowKey: 'id',
};

/**
 * BFM表格列表组件
 *
 * 使用方法：
 * 1. 导入组件和控制器: import BfmTableList, { tableController } from '路径/bfm-table-list';
 * 2. 定义唯一表格ID: const tableId = 'my-unique-table-id';
 * 3. 使用组件: <BfmTableList tableId={tableId} ... />
 * 4. 在任何地方刷新表格: tableController.refetch(tableId);
 */
const BfmTableList: React.FC<BfmTableListProps> = ({
  tableId,
  pageTitle,
  newButtonProps,
  tableProps,
  searchProps,
  onFetchList,
  defaultSearchInfo = {
    searchItems: [],
    orders: [],
  },
}) => {
  const mergedNewButtonProps = {
    ...defaultNewButtonProps,
    ...newButtonProps,
  };
  const mergedTableProps = merge({}, defaultTableProps, tableProps);

  const [loading, setLoading] = useState(false);
  const [tableParams, setTableParams] = useState<TableParamsType>({
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
    },
    searchInfo: defaultSearchInfo as SearchInfoType,
  });
  const [tableData, setTableData] = useState<any[]>([]);

  const fetchData = async (
    onFetchList: (params: {
      pagination: TablePaginationConfig;
      searchInfo: {
        searchItems: SearchItem[];
        orders?: OrderItem[];
      };
    }) => Promise<{ total: number; dataInfo: any[] }>,
  ) => {
    setLoading(true);
    try {
      const { total, dataInfo } = await onFetchList(tableParams);
      setTableData(dataInfo);
      setTableParams({
        ...tableParams,
        pagination: { ...tableParams.pagination, total },
      });
    } catch (err: any) {
      message.error(err.message || '获取失败');
      setTableData([]);
    } finally {
      setLoading(false);
    }
  };

  const isFetchListExist = useMemo(() => {
    return !!onFetchList;
  }, [onFetchList]);

  // 注册到控制器
  useEffect(() => {
    if (!onFetchList || !tableId) {
      return;
    }
    const unregister = tableController.registerRefetch(tableId, () =>
      fetchData(onFetchList),
    );
    return () => unregister(); // 清理函数
  }, [tableId, isFetchListExist]);

  const handleClickSearch = (values: Record<string, any>) => {
    setTableParams({
      pagination: { ...tableParams.pagination, current: 1 },
      searchInfo: {
        ...tableParams.searchInfo,
        searchItems: [
          ...(defaultSearchInfo.searchItems || []),
          ...values.searchItems,
        ],
      },
    });
  };

  const handleChangeTable = (pagination: TablePaginationConfig) => {
    setTableParams({
      ...tableParams,
      pagination,
    });
    mergedTableProps?.onChange?.(pagination);
  };

  useEffect(() => {
    if (!onFetchList) {
      return;
    }

    fetchData(onFetchList);
  }, [
    isFetchListExist,
    tableParams.pagination?.current,
    tableParams.pagination?.pageSize,
    tableParams.searchInfo,
  ]);

  const renderNewButton = () => {
    if (!newButtonProps) {
      return null;
    }
    if (newButtonProps.rule) {
      return (
        <PermissionButton {...newButtonProps}>
          {newButtonProps.title}
        </PermissionButton>
      );
    }
    return <Button {...mergedNewButtonProps}>{newButtonProps.title}</Button>;
  };

  return (
    <Card className="m-4 shadow-lg rounded-lg">
      <div className="flex justify-between items-center mb-8">
        <Title level={4} className="!mb-0">
          {pageTitle}
        </Title>
        {renderNewButton()}
      </div>
      {searchProps && (
        <Card className="mb-6">
          <TableSearch
            type="searchItems"
            downloadExcelRender={false}
            showSearchButton
            onSearch={handleClickSearch}
            {...searchProps}
          />
        </Card>
      )}
      <div className="mt-4">
        <Table
          loading={loading}
          pagination={tableParams.pagination}
          {...mergedTableProps}
          onChange={handleChangeTable}
          dataSource={tableData}
        />
      </div>
    </Card>
  );
};

export default BfmTableList;
