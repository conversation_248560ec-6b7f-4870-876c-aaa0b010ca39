// 表格控制器 - 用于跨组件通信
class TableController {
  private static instance: TableController;
  private listeners: Map<string, Set<() => void>> = new Map();

  private constructor() {}

  // 单例模式
  public static getInstance(): TableController {
    if (!TableController.instance) {
      TableController.instance = new TableController();
    }
    return TableController.instance;
  }

  // 注册刷新监听器
  public registerRefetch(tableId: string, callback: () => void): () => void {
    if (!this.listeners.has(tableId)) {
      this.listeners.set(tableId, new Set());
    }

    this.listeners.get(tableId)!.add(callback);

    // 返回取消注册的函数
    return () => {
      const callbacks = this.listeners.get(tableId);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.listeners.delete(tableId);
        }
      }
    };
  }

  // 触发刷新
  public refetch(tableId: string): void {
    const callbacks = this.listeners.get(tableId);
    if (callbacks) {
      callbacks.forEach((callback) => callback());
    }
  }
}

// 导出控制器单例
export const tableController = TableController.getInstance();
