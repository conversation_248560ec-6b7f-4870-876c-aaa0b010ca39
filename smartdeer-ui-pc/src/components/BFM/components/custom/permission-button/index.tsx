// import S from '@/utils/storage';
import { Button, ButtonProps } from 'antd';
import React from 'react';
import { useEffect, useState } from 'react';

interface PermissionButtonProps extends ButtonProps {
  role?: string;
}

const getLocalStorage = (key: string) => {
  const encryptKey = base64EncoderDecoder.encrypt(key);

    let item = this.memory.getItem(encryptKey);

    if (!item || item == 'null' || item == 'undefined') {
      return null;
    }

    item = base64EncoderDecoder.decrypt(item);

    //先将拿到的试着进行json转为对象的形式
    try {
      item = JSON.parse(item);
    } catch (error) {
      //如果不行就不是json的字符串，就直接返回
      item = item;
    }
}

const Page = ({ role: roleKey, ...restButtonProps }: PermissionButtonProps) => {
  const [buttonShow, setButtonShow] = useState(false);

  useEffect(() => {
    // let roles = S.get('bfm-role', true);
    let roles: string[] = window.localStorage.getItem('bfm-role') || []
    let isBtnShow = roles.some((role: any) => role.startsWith(roleKey));
    setButtonShow(isBtnShow);
  }, []);
  return buttonShow ? <Button {...restButtonProps} /> : null;
};

export default Page;
