import { ConfigContext, JsonRender } from '@smartdeer-ui/pc';
import { message } from 'antd';
import { isArray } from 'lodash';
import React, { useEffect, useState } from 'react';
import { ConfType } from '../../../../components/json-render/typing';
import {
  processedEvents,
  resolveRefConfig,
} from '../../../../components/json-render/utils';
import { useJsonFetcher } from '../../../../components/json-render/version2/hooks/useFetcher';
import { FetchType } from '../../../../typing';
import { useParamsDict } from '../../../../utils/hooks';
import { replaceVariablesInTemplate } from '../../../../utils/replaceVariablesInTemplate';
import { cachedSwrFetcher } from '../../../../utils/swrFetcher';
import * as customComponents from '../custom';

interface ConfRefObjectType {
  conf: {
    components: any[];
    events: any[];
    [key: string]: any;
  };
  confMap: Record<string, any>;
}

interface JsonRenderResponse {
  json: string;
  innerScript: string;
}

interface BfmJsonRenderProps {
  /** 页面配置标识 */
  confKey?: string;
  /** 产品编码 */
  productCode?: string;
  /** 组件props */
  props?: Record<string, any>;
  /** 获取配置的接口 */
  confEffect?: FetchType;
  /** 配置对象 */
  confObject?: ConfType;
  /** 配置含有引用的对象，会对引用进行替换 */
  confRefObject?: ConfRefObjectType;
  /** 本地组件 */
  components?: Record<string, React.ComponentType>;
  /** 内置组件：builtInComponents 不会通过json render渲染 */
  builtInComponents?: Record<string, (props: any) => React.ReactNode>;
  /** 常量 */
  consts?: Record<string, any>;
}

// 缓存内置组件
let buildIns: Record<string, (props: any) => React.ReactNode> = {};

/**
 * BfmJsonRender - 业财 Json Render Wrapper
 *
 * 功能说明：
 * 1. 支持远程配置和本地mock配置的智能页面渲染
 * 2. 支持传入配置对象渲染
 * 3. 统一注入业务自定义组件
 * 4. 处理页面配置的获取、解析和状态管理
 *
 * @param confKey - 页面配置标识
 * @param productCode - 产品编码
 * @param props - 组件props
 * @param confEffect - 获取配置的接口
 * @param confObject - 配置对象
 * @param confRefObject - 配置含有引用的对象，会对引用进行替换
 */
const BfmJsonRender = ({
  confKey,
  productCode,
  props,
  confEffect,
  confObject,
  confRefObject,
  components = {},
  consts = {},
  builtInComponents = {},
}: BfmJsonRenderProps) => {
  const { jsonFetcher } = useJsonFetcher();
  const [json, setJson] = useState<string>('');
  const [events, setEvents] = useState<any[]>([]);
  const [initialState, setInitialState] = useState<any>({});

  buildIns = {
    ...buildIns,
    ...builtInComponents,
  };

  const bfmComponents = confKey ? buildIns[confKey] : undefined;

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict({});
  let appFunctionApi = configContext.appFunctionApi;
  appFunctionApi = replaceVariablesInTemplate(paramsDict, appFunctionApi!);

  const fetchProductConf = async () => {
    try {
      const { data } = await cachedSwrFetcher(appFunctionApi, 'POST', {
        functionKey: 'x_sage_get_bfm_product_smart_page',
        params: {
          productCode,
          pageKey: confKey,
        },
      });

      const { json, innerScript } = data?.rs || {};
      const script = JSON.parse(innerScript);
      setJson(json);
      setEvents(script);
    } catch (error) {
      message.error(`获取配置失败: ${confKey}`);
      setJson('');
      setEvents([]);
    }
  };

  const fetchCommonConf = async () => {
    try {
      const { data } = await cachedSwrFetcher(appFunctionApi, 'POST', {
        functionKey: 'x_sage_get_smd_smart_page_by_key',
        params: {
          pageKey: confKey,
        },
      });

      const { json, innerScript } = data?.rs || {};
      const script = JSON.parse(innerScript);
      setJson(json);
      setEvents(script);
    } catch (error) {
      message.error(`获取配置失败: ${confKey}`);
      setJson('');
      setEvents([]);
    }
  };

  const fetchConfEffect = async () => {
    try {
      const { rs } = (await jsonFetcher({
        ...confEffect,
      })) as { rs: JsonRenderResponse };
      const { json, innerScript } = rs;
      const script = JSON.parse(innerScript);
      setJson(json);
      setEvents(script);
    } catch (error) {
      message.error(`获取配置失败: ${confEffect?.functionKey}`);
      setJson('');
      setEvents([]);
    }
  };

  useEffect(() => {
    // 业财自定义组件优先使用
    if (!!bfmComponents) {
      return;
    }
    // 使用配置对象(可用于本地调试)
    if (confObject) {
      if (!confObject.components) {
        message.error('配置对象中缺少components');
        return;
      }
      if (confObject.events && !isArray(confObject.events)) {
        message.error('配置对象中events必须是数组格式');
        return;
      }
      setJson(JSON.stringify({ components: confObject.components }));
      setEvents(processedEvents(confObject.events));
      setInitialState(confObject.initialState || {});
      return;
    }
    // 如果存在confRefObject，解析confRefObject，可用于本地调试，使用场景是复杂的含有ref引用的配置
    if (confRefObject) {
      const { conf, confMap } = confRefObject;
      const refConfig = resolveRefConfig(conf, confMap);
      setJson(refConfig.json);
      setEvents(refConfig.events);
      // 下方打印结果可以直接粘贴到智能页面 mock 中生成远程配置
      console.log({
        events: refConfig.events,
        components: JSON.parse(refConfig.json).components,
      });
      return;
    }

    // 如果存在productCode，获取产品智能页面配置
    if (productCode && confKey) {
      fetchProductConf();
      return;
    }
    // 如果存在confKey，获取通用智能页面配置
    if (confKey) {
      fetchCommonConf();
      return;
    }
    // 如果存在confEffect，根据confEffect获取配置
    if (confEffect) {
      fetchConfEffect();
      return;
    }
  }, [
    confKey,
    productCode,
    confObject,
    confEffect,
    confRefObject,
    !!bfmComponents,
  ]);

  // 如果存在业财自定义组件，优先使用，不会再使用json render渲染
  if (bfmComponents) {
    return bfmComponents(props);
  }

  if (!json || json === '{}') {
    return null;
  }

  return (
    <JsonRender
      version="2"
      json={json}
      events={events || []}
      customComponents={{
        // 组件本身
        BfmJsonRender: BfmJsonRender,
        // 一些公共的业务组件和自定义组件
        ...customComponents,
        // 通过参数传入的组件
        ...components,
      }}
      consts={consts}
      props={props}
      initialState={initialState}
    />
  );
};

export default BfmJsonRender;
