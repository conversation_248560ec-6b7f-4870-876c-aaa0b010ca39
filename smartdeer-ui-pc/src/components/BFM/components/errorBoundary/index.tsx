import React, { Component } from 'react';
import { Mo<PERSON>, Result as AntdResult, Button } from 'antd';
import { getLocale, history } from '@umijs/max';

interface ErrorBoundaryState {
  error?: Error | null;
  errorInfo?: React.ErrorInfo | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

const locales: Record<string, any> = {
  'zh-CN': {
    updateNotificationTitle: '版本更新通知：全新功能与优化！',
    updateNotificationDescription: '亲爱的用户，我们为您带来了全新的版本更新！本次更新包含了一系列全新的功能和优化，旨在提升您的使用体验。我们修复了一些已知问题，并增加了更多的稳定性和性能改进。请尽快更新以享受最新的功能和改进。感谢您的支持和反馈，我们将继续努力为您提供更好的服务！',
    update: '更新',
    resultTitle: '出了点问题。',
    backHome: '返回首页',
  },
  'en-US': {
    updateNotificationTitle: 'Version update notification: New features and optimizations!',
    updateNotificationDescription: 'Dear user, we have brought you a brand new version update! This update includes a series of new features and optimizations aimed at enhancing your user experience. We have fixed some known issues and added more stability and performance improvements. Please update as soon as possible to enjoy the latest features and improvements. Thank you for your support and feedback. We will continue to work hard to provide you with better service!',
    update: 'Update',
    resultTitle: 'Something went wrong.',
    backHome: 'Back Home',
  },
  'zh-TW': {
    updateNotificationTitle: '版本更新通知：全新功能與優化！',
    updateNotificationDescription: '親愛的用戶，我們為您帶來了全新的版本更新！本次更新包含了一系列全新的功能和優化，旨在提升您的使用體驗。我們修復了一些已知問題，並增加了更多的穩定性和性能改進。請盡快更新以享受最新的功能和改進。感謝您的支持和反饋，我們將繼續努力為您提供更好的服務！',
    update: '更新',
    resultTitle: '出了點問題。',
    backHome: '返回首頁',
  }
}

const UpdateNotification = () => {
  const { updateNotificationTitle, updateNotificationDescription, update } = locales[getLocale() || 'zh-CN']

  return (
    <Modal
      title={updateNotificationTitle}
      centered
      open={true}
      footer={
        <Button type="primary" onClick={() => window.location.reload()}>
          {update}
        </Button>
      }
    >
      {updateNotificationDescription}
    </Modal>
  );
}

const Result = ({ error }: { error: Error }) => {
  const { resultTitle, backHome } = locales[getLocale() || 'zh-CN']

  return (
    <AntdResult
      status='error'
      title={resultTitle}
      subTitle={error.toString()}
      extra={
        <Button type="primary" onClick={() => history.replace('/home')}>
          {backHome}
        </Button>
      }
    ></AntdResult >
  );
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      error: null,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 你同样可以在这里将错误上报到你的日志服务  
    console.log('ErrorBoundary caught an error:', error.toString());
    console.log('ErrorBoundary caught an errorInfo:', errorInfo);

    // 更新状态以触发重新渲染  
    this.setState({
      error,
      errorInfo,
    });
  }

  render() {
    if (this.state.error && this.state.error.toString().includes('ChunkLoadError')) {
      return (
        <UpdateNotification />
      );
    } else if (this.state.error) {
      return (
        <Result error={this.state.error} />
      );
    }

    // 正常情况下，渲染子组件  
    return this.props.children;
  }
}

export default ErrorBoundary;