import React, { useState } from 'react';
import { Steps, StepProps } from 'antd';
import { formatDate } from '@/utils/time';
import { LoadingOutlined } from '@ant-design/icons';

const timeLineTypeStrs: {[key: string]: string} = {
  'START': '发起审批',
  "PASS": "通过",
  "REJECT": "拒绝",
  "AUTO_PASS": "自动通过",
  "AUTO_REJECT": "自动拒绝",
  "REMOVE_REPEAT": "去重",
  "TRANSFER": "转交",
  "ADD_APPROVER_BEFORE": "前加签",
  "ADD_APPROVER": "并加签",
  "ADD_APPROVER_AFTER": "后加签",
  "DELETE_APPROVER": "减签",
  "ROLLBACK_SELECTED": "指定回退",
  "ROLLBACK": "全部回退",
  "CANCEL": "撤回",
  "DELETE": "删除",
  "CC": "抄送"
}

const taskStatusStrs: {[key: string]: string} = {
  "PENDING": "审批中",
  "APPROVED": "通过",
  "REJECTED": "拒绝",
  "TRANSFERRED": "已转交",
  "DONE": "完成",
}

const taskTypeStrs: {[key: string]: string} = {
  "AND": "以下人员全部同意后审批通过",
  "OR": "以下任意一人同意后审批通过",
  "AUTO_PASS": "自动通过",
  "AUTO_REJECT": "自动拒绝",
  "SEQUENTIAL": "按顺序",
}

export type AuditDetailProps = {
  data: any,
  endLabel?: React.ReactNode,
}

const Main: React.FC<AuditDetailProps> = (props) => {
  const {data, endLabel} = props;
  const [setpItems, setStepItems] = useState<any[]>([])

  React.useEffect(() => {
    setStepItems(getStepItems(data))
  }, [data])

  const getStepItems = (aproveInfo: any) => {
    if (!aproveInfo) {
      return []
    }

    const timeline = aproveInfo?.timeline;  // 审批动态
    const taskList = aproveInfo?.task_list; // 审批节点/任务

    // 将审批中的任务拿出来
    const pendingTasks = taskList?.filter((task: any) => {
      return task.status === 'PENDING'
    })
    // 合并同一个审批节点的任务
    const pendingTaskGroups = pendingTasks.reduce((acc: any, task: any) => {
      const existingGroup = acc.find((group: any) => group.find((x: any) => x.node_id === task.node_id));
      if (existingGroup) {
        existingGroup.push(task);
      } else {
        acc.push([task]);
      }
      return acc;
    }, [] as any[])


    const stepsItems: StepProps[] = [];
    
    timeline?.forEach((item: any) => {
      let title;
      if (item.type === 'CC') {
        title = `${timeLineTypeStrs[item.type]}: ${item.user_name_list?.join(', ')}`
      } else {
        title = `${item.user_name || item.user_id}: ${timeLineTypeStrs[item.type]}`
      }

      stepsItems.push({
        title: title,
        subTitle: `${formatDate(item.create_time, 'YYYY-MM-DD HH:mm')}`,
        description: item.comment,
        status: (item.type === 'REJECT') ? 'error' : 'finish'
      })
    });

    pendingTaskGroups?.forEach((tasks: any[]) => {
      const type = tasks[0].type;
      const typeStr = taskTypeStrs[type];
      stepsItems.push({
        title: (
          <div>
            <div style={{color: 'gray'}}>
              {typeStr}
            </div>
            {
              tasks.map((task) => {
                return <div key={task.id}>
                  {`${task.user_name}: ${taskStatusStrs[task.status]}`}
                </div>
              })
            }
          </div>
        ),
        status: 'process',
        icon: <LoadingOutlined/>
      })
    });

    if (aproveInfo.end_time != 0) {
      stepsItems.push({
        title: data.status === 'APPROVED' ? (endLabel || '结束') : '结束',
        subTitle: `${formatDate(aproveInfo.end_time, 'YYYY-MM-DD HH:mm')}`,
        status: 'finish'
      })
    }

    return stepsItems
  }

  return (
    <>
      <div>
        <Steps
          direction='vertical'
          size='small'
          items={setpItems}
        >
        </Steps>
      </div>
    
    </>
  )
};

export default Main;