import React, { useState } from 'react';
import { But<PERSON>, Spin, message, Steps, Col, StepProps, Popconfirm } from 'antd';
import { functionRuntime } from '@/services/run';
import { formatDate } from '@/utils/time';
import { LoadingOutlined } from '@ant-design/icons';
// import { isArray, isObject } from 'lodash';
import { AuditProcess } from '..';

// const { Title } = Typography;

const timeLineTypeStrs: {[key: string]: string} = {
  'START': '发起审批',
  "PASS": "通过",
  "REJECT": "拒绝",
  "AUTO_PASS": "自动通过",
  "AUTO_REJECT": "自动拒绝",
  "REMOVE_REPEAT": "去重",
  "TRANSFER": "转交",
  "ADD_APPROVER_BEFORE": "前加签",
  "ADD_APPROVER": "并加签",
  "ADD_APPROVER_AFTER": "后加签",
  "DELETE_APPROVER": "减签",
  "ROLLBACK_SELECTED": "指定回退",
  "ROLLBACK": "全部回退",
  "CANCEL": "撤回",
  "DELETE": "删除",
  "CC": "抄送"
}

const taskStatusStrs: {[key: string]: string} = {
  "PENDING": "审批中",
  "APPROVED": "通过",
  "REJECTED": "拒绝",
  "TRANSFERRED": "已转交",
  "DONE": "完成",
}

export type AuditDetailProps = {
  open?: boolean
  onClose: () => void
  id?: number | string
  onAuditCanceled: () => void
  canCancelAudit?: boolean
  getFunctionKey: string
  cancelFunctionKey: string
  functionParams?: any,
  detail?: React.ReactNode,
  cancelDisabled?: boolean
}

const Main: React.FC<AuditDetailProps> = (props) => {
  const {open, onClose, id, getFunctionKey: functionKey, functionParams, cancelDisabled} = props;

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<any>();
  const [setpItems, setStepItems] = useState<any[]>([])
  const [approveData, setApproveData] = useState<any>();

  const fetchApproveInfo = async () => {
    setLoading(true);
    try {
      const {rs: data} = await functionRuntime(0, functionKey, functionParams || {id})
      const form = data?.form;
      if (form) {
        const info = JSON.parse(form)
        setFormData(info)
      }
      // setAuditSteps(getStepsData(data))
      setStepItems(getStepItems(data))
      setApproveData(data)
      
    } catch (err: any) {
      console.error(err)
      message.error(err.message)
    }
    setLoading(false);
  }

  React.useEffect(() => {
    if (!props.id) {
      return;
    }
    fetchApproveInfo()
  }, [props.id])

  const getStepItems = (aproveInfo: any) => {
    const timeline = aproveInfo?.timeline;  // 审批动态
    const taskList = aproveInfo?.task_list; // 审批节点/任务

    // // 将 taskList 按照 node_id 分组，每个组是一个审批节点
    // const taskGroups = taskList.reduce((acc: any, task: any) => {
    //   const existingGroup = acc.find((group: any) => group.find((x: any) => x.node_id === task.node_id));
    //   if (existingGroup) {
    //     existingGroup.push(task);
    //   } else {
    //     acc.push([task]);
    //   }
    //   return acc;
    // }, [] as any[])

    // // 将timeline和tasks合并成一个审批流
    // const filterTaskGroups = taskGroups;
    // timeline.forEach((node: any) => {
    //   const taskId = node.task_id;
    //   if (taskId) {
    //     const taskGroup = filterTaskGroups.find((group: any[]) => group.find((task: any) => task.id === taskId));
    //     if (taskGroup) {
    //       const taskTypeIsAnd = taskGroup[0].type === 'AND'; // 是否是会签，会签需要全部通过
          
    //     }
    //   }
    // });

    // // 最后加上结束节点（如果已结束）

    

    // 将审批中的任务拿出来
    const pendingTasks = taskList?.filter((task: any) => {
      return task.status === 'PENDING'
    })
    // 合并同一个审批节点的任务
    const pendingTaskGroups = pendingTasks.reduce((acc: any, task: any) => {
      const existingGroup = acc.find((group: any) => group.find((x: any) => x.node_id === task.node_id));
      if (existingGroup) {
        existingGroup.push(task);
      } else {
        acc.push([task]);
      }
      return acc;
    }, [] as any[])


    const stepsItems: StepProps[] = [];
    
    timeline?.forEach((item: any) => {
      let title;
      if (item.type === 'CC') {
        title = `${timeLineTypeStrs[item.type]}: ${item.user_name_list?.join(', ')}`
      } else {
        title = `${item.user_name}: ${timeLineTypeStrs[item.type]}`
      }

      stepsItems.push({
        title: title,
        subTitle: `${formatDate(item.create_time, 'YYYY-MM-DD HH:mm')}`,
        description: item.comment,
        status: (item.type === 'REJECT') ? 'error' : 'finish'
      })
    });

    pendingTaskGroups?.forEach((tasks: any[]) => {
      stepsItems.push({
        title: tasks.map((task) => {
          return <div key={task.id}>
            {`${task.user_name}: ${taskStatusStrs[task.status]}`}
          </div>
        }),
        status: 'process',
        icon: <LoadingOutlined/>
      })
    });
    return stepsItems
  }

  const handleCancel = () => {
    onClose();
    setFormData(null);
  }
  
  const handleCancelAuditClick = async () => {
    try {
      let response = await functionRuntime(0, props.cancelFunctionKey, {id})
      // debugger;
      if (response?.rs) {
        message.success('成功撤销审批')
        handleCancel();
        props.onAuditCanceled();
      } else {
        message.error(response?.msg)
      }
    } catch (err: any) {
      message.error(err?.message)
    }       
  }

  if (loading) {
    return (
      <div className='h-full w-full flex items-center justify-center'>
        <Spin/>
      </div>
    )
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const getFormValueRender = (item: any) => {
    const type = item?.type;
    if (type === 'fieldList') {
      return item?.value?.map((list: any[]) => {
        return list?.map((i: any) => {
          return (
            <>
              <Col span={8} className="font-medium text-gray-600">{i.name}</Col>
              <Col span={16}>{getFormValueRender(i)}</Col>
            </>
          )
        })
      })
    }

    return item?.value
  }

  return (
    <>
      <div className='w-full'>

        <AuditProcess
          data={approveData}
        />
      
        {
          (!cancelDisabled && approveData?.end_time === '0') && (
            <div className='mb-6 mt-3 w-full'>
              <Popconfirm
                title="确认撤销审批吗"
                onConfirm={handleCancelAuditClick}
                okText="撤销审批"
                cancelText="取消"
              >
                <Button
                  className='w-full'
                  type='primary'
                >
                  撤销审批
                </Button>
              </Popconfirm>
            </div>
          )
        }
        {
          props.detail && (
            props.detail
          )
        }
      </div>
    
    </>
  )
};

export default Main;