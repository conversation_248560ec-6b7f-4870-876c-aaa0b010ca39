import React, { useEffect, useState } from 'react';
import type { MenuProps } from 'antd';
import { Dropdown } from 'antd';
import { setLocale, getLocale, useParams } from '@umijs/max';
import api from '@/services'
import S from '@/utils/storage';
import { STORE } from '@/consts';

import langImage from '@/assets/image/icon/lang.svg';

type LangType = {
  key: ICB.Lang;
  label: string;
  mapping: string
}

interface SelectLangProps {
  isRefreshPage?: boolean;
  langList?: LangType[]
}

const defaultLangList = [
  {
    key: 'en-US', //  en
    label: 'English',
    mapping: 'en'
  },
  {
    key: 'zh-CN', // zh
    label: '简体中文',
    mapping: 'zh'
  },
  {
    key: 'zh-TW', // zht
    label: '繁体中文',
    mapping: 'zht'
  }
]

const SelectLang: React.FC<SelectLangProps> = (props) => {
  const { isRefreshPage = true, langList } = props

  const params = useParams()
  const [lang, setLang] = useState(getLocale())
  const [items, setItems] = useState<LangType[]>(langList || defaultLangList)

  const getLocaleLabel = (key: string) => {
    const item: any = items.find(item => item?.key === key)

    return item?.label
  }

  const fetch = async () => {
  }

  useEffect(() => {
    fetch()
  }, [])

  const onClick: MenuProps['onClick'] = ({ key }) => {
    if (lang === key) return

    console.log(key)

    setLocale(key, isRefreshPage)

    S.set(STORE.GS_LOCALE, key, true)

    if (!isRefreshPage) {
      setLang(key)
    }
  };

  return (
    <Dropdown menu={{ items, onClick }} placement="bottom">
      <div className={`text-[#474747] cursor-pointer flex items-center whitespace-nowrap`}>
        <img src={langImage} className={`w-[18px] h-[18px]`} />
        <span className={`ml-[4px] text-[14px]`}>
          {getLocaleLabel(lang)}
        </span>
      </div>
    </Dropdown>
  );
};

export default React.memo(SelectLang);
