import { useEffect, memo, useState } from 'react';
import { useParams, history, useIntl, setLocale, useModel } from '@umijs/max';
import api from '@/services';
import { Dropdown, Avatar } from 'antd';
import type { MenuProps } from 'antd';
import { LogoutOutlined, SettingOutlined, SendOutlined, EditOutlined } from '@ant-design/icons';
import S from '@/utils/storage';
import { setUserAccountId } from '@smartdeer-ui/pc';
import { setSageEntityAuthTokens, setSecurityToken, setUserAccountInfo } from '@smartdeer-ui/pc/components/config-provider/utils';

interface TProps {
  entityId?: ICB.EntityId
}

const AccountInfo: React.FC<TProps> = memo((props) => {
  const { user } = useModel('useUser')

  const intl = useIntl()
  const params = useParams()

  const items: MenuProps['items'] = [
    {
      key: 'editPassword',
      icon: <EditOutlined />,
      label: intl.formatMessage({ id: 'common.xiugaimima' })
    },
    // {
    //   key: 'ssc',
    //   icon: <SendOutlined />,
    //   label: '发起工单'
    // },
    {
      key: 'setting',
      icon: <SettingOutlined />,
      label: intl.formatMessage({ id: 'common.setting' })
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: intl.formatMessage({ id: 'common.dengchu' })
    },
  ];

  const entityId = params.entityId || props.entityId

  const [info, setInfo] = useState({
    name: '',
    formalPhoto: ''
  })

  const fetch = async () => {
    // if (!entityId) return

    const res = await api.account.getAccountInfo()

    if (res.accountId) {
      setUserAccountId(res.accountId.toString());
      setUserAccountInfo(res);
    }

    // setInfo({
    //   name: res.realName,
    //   formalPhoto: res.formalPhoto
    // })
  }

  useEffect(() => {
    fetch()
  }, [])

  const handleLogout = async () => {
    try {
      await api.account.logout()
    } catch (error) { } finally {
    }
  }

  const onClick: MenuProps['onClick'] = async({ key }) => {
    switch (key) {
      // 修改密码
      case 'ssc':
        history.push('/ssc/new');
        break;

      // 修改密码
      case 'editPassword':
        history.push('/forgot-password?source=my');
        break;

      // 退出登录
      case 'logout':
        await handleLogout();
        S.removeAuthToken();
        setSageEntityAuthTokens({});
        setSecurityToken('');

        history.push('/login');
        break;
      case 'setting':
        history.replace('/account/setting');
        break;

      default:
        break;
    }
  };

  let color = '#ffaa3b'
  if (user.avatar) {
    color = 'white'
  }

  return (
    <Dropdown menu={{ items, onClick }}>
      <div className={`text-[#474747] cursor-pointer whitespace-nowrap flex items-center`}>
        <Avatar src={user.avatar} size={26} style={{ backgroundColor: color, verticalAlign: 'middle' }}>
          <div className={`text-[12px]`}>{user.userName}</div>
        </Avatar>
        <div className={`hidden lg:block ml-[6px] text-[14px] overflow-hidden text-ellipsis`}>
          {user.userName}
        </div>
      </div>
    </Dropdown>
  );
});

export default AccountInfo;
