import { memo } from 'react';
import { Space } from 'antd';
import AccountInfo from './account-info'
import SelectLang from './select-lang'
import SelectTimezone from './select-timezone'

import styles from './index.less';

interface TProps {
  entityId?: ICB.EntityId
}

const HeaderActions: React.FC<TProps> = memo((props) => {

  return (
    <div className={`${styles.actions} px-[16px] hidden large:block`}>
      <Space size={20}>
        <SelectTimezone />

        <SelectLang />

        {/*<div className={`cursor-pointer bg-red-600 w-6 h-6 rounded-full flex items-center justify-center text-white text-10`}>*/}
        {/*  99*/}
        {/*</div>*/}

        <AccountInfo entityId={props.entityId} />
      </Space>
    </div>
  );
});

export default HeaderActions;
