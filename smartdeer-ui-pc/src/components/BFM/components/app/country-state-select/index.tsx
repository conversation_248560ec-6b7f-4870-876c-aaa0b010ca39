import { functionRuntime } from "../../../services/run";
import { Cascader, CascaderProps, Select } from "antd";
import { DefaultOptionType } from "antd/es/cascader";
import { isArray } from "lodash";
import { useEffect, useState } from "react";

interface Option {
  value?: string | number | null;
  label: React.ReactNode;
  children?: Option[];
  isLeaf?: boolean;
}

interface StateValue {
  countryCode?: string | string[],
  state?: string,
  countryName?: string | string[],
}

type Props = {
  i18n: any;
  entityId: string,
  entityUName: string,
  value?: StateValue,
  onChange?: (value: StateValue) => void
  changeOnSelect?: boolean,
  onlySelectCountry?: boolean,
  multipleCountry?: boolean,
  disabled?: boolean,
}

const MAX_RETRY_TIME = 3 * 60 * 1000; // 3分钟
const RETRY_INTERVAL = 10 * 1000; // 10秒

// const lang = getLocale() || 'zh'

const Main: React.FC<Props> = (props) => {
  const {i18n} = props;
  const [options, setOptions] = useState<Option[]>([]);
  const [value, setValue] = useState<any[]>();
  // // debugger;
  useEffect(() => {
    if (props.multipleCountry) {
      setValue(Array.isArray(props.value?.countryCode) ? props.value.countryCode : [])
      return
    }

    const newValue = []
    if (props.value?.countryCode) {
      newValue.push(props.value.countryCode)
    }
    if (props.value?.state && props.value.state !== 'all' && !props.onlySelectCountry) {
      newValue.push(props.value.state)
    }
    setValue(newValue)
  }, [props.value])

  const fetchStates = async (countryCode: string) => {
    try {
      const response = await functionRuntime(props.entityUName, 'search_river_eor_select_info_list', {
        countryCode,
        current: 1,
        limit: 500, 
        searchInfo: {}
      });
      const stateList = response?.dataInfo;
      if (!isArray(stateList)) {
        return [];
      }
      return stateList;
    } catch (err: any) {
      return [];
    }
  }

  const fetch = async () => {
    try {
      const response = await functionRuntime(props.entityUName, 'search_river_eor_select_info_list', {
        current: 1,
        limit: 500, 
        searchInfo: {}
      });
      let countryList = response?.dataInfo;
      if (!isArray(countryList)) {
        return;
      }
      // 去重逻辑
      let newDataInfo: any[] = [];
      countryList.forEach((item: any) => {
        if (!newDataInfo.some((country: any) => country.countryCode === item.countryCode)) {
          newDataInfo.push(item);
        }
      });
      countryList = newDataInfo;
      const options: any[] = []
      for (let i = 0; i < countryList.length; i++) {
        const country = countryList[i];
        let children;
        if (!props.onlySelectCountry && ['CN', 'US'].includes(country.countryCode)) {
          const stateList = await fetchStates(country.countryCode)
          children = stateList?.map((state: any) => {
            return {
              label: state.stateNameZh,
              value: state.stateNameZh
            }
          })
        }
        options.push({
          label: country.countryNameZh,
          value: country.countryCode,
          countryName: country.countryNameZh,
          children: children
        })
      }
      setOptions(options)
    } catch (err: any) {
      // ignore
    }
    return;
  }

  const onChange: CascaderProps<DefaultOptionType>['onChange'] = (value, selectedOptions) => {
    if (props.multipleCountry) {
      const countryNames = selectedOptions.map(option => option.countryName)
      props.onChange?.({
        countryCode: value as string[],
        countryName: countryNames
      })
      return
    }

    let countryCode = value.length > 0 ? value[0] : undefined;
    let countryName = selectedOptions.length > 0 ? selectedOptions[0].countryName : undefined
    let state = value.length > 1 ? value[1] : 'all';
    props.onChange?.({countryCode: countryCode as string, state: state as string, countryName: countryName})
  };

  useEffect(() => {
    fetch()
  }, [])

  if (props.multipleCountry) {
    return (
      <Select
        mode="multiple"
        disabled={props.disabled}
        showSearch
        options={options}
        value={value}
        onChange={(value, options) => {
          const selectedOptions = Array.isArray(options) ? options : [options];
          onChange(value, selectedOptions);
        }}
        placeholder={i18n?.['bfm_contract_please_select_country']}
        allowClear={false}
        style={{ width: '100%' }}
        filterOption={(input, option) => 
          (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
        }
      />
    )
  }
  console.log('options', options)
  // debugger

  return <Cascader 
    options={options} 
    onChange={onChange} 
    expandTrigger="hover"
    value={value}
    showSearch
    // placeholder="请选择国家/地区"
    placeholder={i18n?.['bfm_contract_please_select_country']}
    allowClear={false}
    dropdownMatchSelectWidth={true}
    changeOnSelect={props.changeOnSelect}
    disabled={props.disabled}
  />;
}

export default Main;