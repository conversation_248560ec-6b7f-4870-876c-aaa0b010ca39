import { Button, Space, Tag } from "antd";


type AuditStatusInTableProps = {
  status: number
  onAuditProgressClick?: (auditStatus: number) => void
}

const AuditStatusInTable: React.FC<AuditStatusInTableProps> = (props) => {
  const { status, onAuditProgressClick } = props
  const statusStr = `${status}`;

  if (status === 0) {
    return <Tag color="default">待发起审批</Tag>
  }

  if (status === 1) {
    return (
      <Space size={0}>
        <Tag color="processing">审核中</Tag>
        <Button
          type="link" 
          size="small"
          onClick={() => onAuditProgressClick && onAuditProgressClick(status)}
        >
          查看进度
        </Button>
      </Space>
    )
  }

  if (status === 2) {
    return <Tag color="success">审核通过</Tag>
  }

  if (status === 3) {
    return (
      <Space size={0}>
        <Tag color="error">审核驳回</Tag>
        <Button 
          type="link" 
          size="small"
          onClick={() => onAuditProgressClick && onAuditProgressClick(status)}
        >
          查看原因
        </Button>
      </Space>
    )
  }

  if (status === 4) {
    return (
      <Tag color="default">审批取消</Tag>
    )
  }

  return (
    <></>
  );
}
export default AuditStatusInTable;