import React, { useState, ReactNode } from 'react';
import { Button } from 'antd';

export interface NavigationProps {
  navigateTo: (content: ReactNode, title?: string) => void;
  goBack: () => void;
}

type PageContent = 
  | ReactNode 
  | ((props: NavigationProps) => ReactNode);

interface Page {
  content: PageContent;
  title?: string;
  isRoot: boolean;
}

interface DynamicPageContainerProps {
  content: PageContent;
  renderBackButton?: (onBack: () => void) => ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

function isNavigableElement(
  element: React.ReactNode
): element is React.ReactElement<Partial<NavigationProps>> {
  return React.isValidElement(element);
}

const DynamicPageContainer: React.FC<DynamicPageContainerProps> = ({
  content: initialContent,
  // renderBackButton = (onBack) => <Button onClick={onBack}>返回</Button>,
  renderBackButton = (onBack) => (
    <div onClick={onBack} className='flex items-center'>
      <svg xmlns="http://www.w3.org/2000/svg" width="15" height="12" viewBox="0 0 15 12" fill="none">
        <path d="M2.77824 5.35855L6.56921 1.56793C6.81956 1.3176 6.81957 0.911721 6.56923 0.661381C6.3189 0.411051 5.91304 0.41105 5.66271 0.661378L0.583012 5.741C0.520454 5.80356 0.489162 5.88554 0.48915 5.96748L0.489136 6.03193C0.489102 6.114 0.520394 6.19608 0.583012 6.2587L5.65698 11.3349C5.91048 11.5885 6.32327 11.5843 6.57224 11.3262C6.81494 11.0747 6.81189 10.6745 6.56472 10.4273L2.77792 6.6406L13.4101 6.64086C13.7641 6.64087 14.0511 6.35387 14.0511 5.99984C14.0511 5.64581 13.7642 5.35882 13.4101 5.35881L2.77824 5.35855Z" fill="#474747"/>
      </svg>
      <span className='ml-2' style={{color: '#474747', fontSize: '14px', fontWeight: 500}}>返回</span>
    </div>
  ),
  className,
  style
}) => {
  const [pageStack, setPageStack] = useState<Page[]>([
    { 
      content: initialContent,
      isRoot: true 
    }
  ]);

  const currentPage = pageStack[pageStack.length - 1];

  const navigateTo = (content: ReactNode, title?: string): void => {
    setPageStack(prev => [
      ...prev, 
      { 
        content,
        title,
        isRoot: false
      }
    ]);
  };

  const goBack = (): void => {
    if (pageStack.length > 1) {
      setPageStack(prev => prev.slice(0, -1));
    }
  };

  const renderPageContent = (page: Page): ReactNode => {
    if (typeof page.content === 'function') {
      return page.content({ navigateTo, goBack });
    }
    
    if (isNavigableElement(page.content)) {
      return React.cloneElement(page.content, { navigateTo, goBack });
    }
    
    return page.content;
  };

  return (
    <div 
      className={className}
      style={{ 
        position: 'relative', 
        height: '100%', 
        overflow: 'hidden',
        ...style 
      }}
    >
      {pageStack.map((page, index) => (
        <div
          key={index}
          style={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            transform: `translateX(${index === pageStack.length - 1 ? 0 : -100}%)`,
            transition: 'transform 0.3s ease',
            zIndex: index + 1,
            background: '#fff',
            padding: '0 16px 0 16px',
            overflowY: 'auto'
          }}
        >
          {!page.isRoot && (
            <div style={{ marginBottom: 16 }}>
              {renderBackButton(goBack)}
            </div>
          )}
          <div className='flex-1 overflow-y-auto'>
            {renderPageContent(page)}
          </div>
        </div>
      ))}
    </div>
  );
};

export default DynamicPageContainer;