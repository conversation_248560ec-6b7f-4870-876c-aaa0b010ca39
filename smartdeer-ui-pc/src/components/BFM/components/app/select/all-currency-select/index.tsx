import { useEffect, useState } from "react";
import { Select, Form } from "antd";
import useFetchAllCurrencyOptions from "../../../../hooks/select-hooks/useFetchAllCurrencyOptions";
import { functionRuntime } from "../../../../services/run";

type Props = {
  isShowItem?: boolean;
  label?: string;
  message?: string;
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
};

const Main: React.FC<Props> = (props) => {
  const { isShowItem = true, label, message, value, onChange, className } = props;
  const { options, loading } = useFetchAllCurrencyOptions(functionRuntime);

  const handleChange = (value: string) => {
    onChange?.(value);
  };

  return isShowItem ? (
    <Form.Item
      name="incomeCurrency"
      label={label || "收款币种"}
      rules={[{ required: true, message: message || "请选择币种" }]}
    >
      <Select
        options={options}
        onChange={handleChange}
        value={value}
        key={value}
        showSearch
        loading={loading}
        placeholder={message || "请选择币种"}
        allowClear={false}
      />
    </Form.Item>
  ) : (
    <Select
      options={options}
      key={value}
      onChange={handleChange}
      value={value}
      showSearch
      loading={loading}
      placeholder={message || "请选择币种"}
      allowClear={false}
      className={className}
    />
  );
};

export default Main;