// import { useEffect, useState } from "react";
import { Select, Form } from "antd";
import useFetchInvoiceCurrencyOptions from "@/hooks/select-hooks/useFetchInvoiceCurrencyOptions";
import { functionRuntime } from "@/services/run";

type Props = {
  isShowItem?: boolean;
  label?: string;
  message?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
};

const Main: React.FC<Props> = (props) => {
  const { isShowItem = true, label, message, value, onChange, disabled } = props;
  const { options, loading } = useFetchInvoiceCurrencyOptions(functionRuntime);

  const handleChange = (value: string) => {
    onChange?.(value);
  };

  return isShowItem ? (
    <Form.Item
      name="incomeCurrency"
      label={label || "收款币种"}
      rules={[{ required: true, message: message || "请选择收款币种" }]}
    >
      <Select
        options={options}
        onChange={handleChange}
        value={value}
        key={value}
        showSearch
        loading={loading}
        placeholder={message || "请选择收款币种"}
        allowClear={false}
      />
    </Form.Item>
  ) : (
    <Select
      options={options}
      key={value}
      onChange={handleChange}
      value={value}
      showSearch
      loading={loading}
      placeholder={message || "请选择收款币种"}
      allowClear={false}
      disabled={disabled}
    />
  );
};

export default Main;