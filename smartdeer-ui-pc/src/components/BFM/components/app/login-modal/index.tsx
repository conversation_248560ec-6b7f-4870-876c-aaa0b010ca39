import { ConfigContext } from '@smartdeer-ui/pc/components/config-provider';
import { setLoginModalOpen } from '@smartdeer-ui/pc/components/config-provider/utils';
import { message, Modal } from 'antd';
import { useContext, useEffect } from 'react';
import LoginForm from '../login-form';
import styles from './index.less';

const LoginModal: React.FC = () => {
  const { loginModalOpen } = useContext(ConfigContext);

  const onCancel = () => {
    setLoginModalOpen(false);
  };

  const handleCloseModal = () => {
    onCancel();
    const form = document.querySelector('#root form');
    if (!form) {
      window.location.reload();
    }
  };

  useEffect(() => {
    message.destroy();
  }, []);

  return (
    <Modal
      mask={false}
      open={loginModalOpen}
      footer={null}
      width={'100vw'}
      onCancel={onCancel}
      maskClosable={false}
      closable={false}
      style={{
        top: 0,
        maxWidth: '100vw',
      }}
      styles={{
        content: {
          borderRadius: '0px',
        },
      }}
      wrapClassName={styles['login-verify-modal']}
      maskTransitionName=""
      transitionName=""
      destroyOnClose
    >
      <LoginForm onSuccess={handleCloseModal} />
    </Modal>
  );
};

export default LoginModal;
