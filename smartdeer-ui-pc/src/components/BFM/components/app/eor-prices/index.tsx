import { Collapse, Tag, Button } from "antd"
import { useState } from 'react'

import styles from './index.module.scss'
import { isArray } from "lodash"

const currencyTypes: Record<string, string> = {
  "1": "法币",
  "2": "crypto",
}

const setupFeeComputeTypes: Record<string, string> = {
  "1": "月薪",
  "2": "雇主成本"
}

const terminationFeeTypes: Record<string, string> = {
  "1": "月薪",
  "2": "雇主成本"
}

const platformFeeTypes: Record<string, string> = {
  "1": "按人算",
  "2": "月费率最低",
  "3": "月费率最高",
}
const platformFeeComputeTypes: Record<string, string> = {
  "1": "月薪",
  "2": "雇主成本",
}
const depositTypes: Record<string, string> = {
  "1": "咨询费和平台费总和",
  "2": "咨询费",
  "3": " 顾问的月薪"
}
const vatGstCtTypes: Record<string, string> = {
  "1": "全单",
  "2": "服务费"
}
const visaTypes: Record<string, string> = {
  "1": "蓝领",
  "2": "白领",
  "3": "高级岗位"
}

const numberStyle = {
  color: 'rgb(254, 145, 17)',
  fontWeight: 600
}
const currencyStyle = {
  color: 'rgb(254, 145, 17)',
  fontWeight: 400,
  fontStyle: 'italic'
}
const typeStyle = {
  color: 'rgb(254, 145, 17)',
  fontWeight: 400
}

export const getPricesRender = (priceInfo: any) => {
  if (!priceInfo) {
    return <>没有报价信息</>
  }
  const platformFeeType = priceInfo?.platformFeeType?.toString();
  const values: any[] = [
    {
      label: '设置费',
      children: (
        <div>
          咨询工作说明书期限少于 <span style={numberStyle}>{priceInfo.setupFeeLimitMonths}</span> 个月，
          {
            (!priceInfo.setupFeeType || priceInfo.setupFeeType === 1) && (
              <>
                需支付一次性不可退设置费 <span style={numberStyle}>{priceInfo.setupFeeAmount}</span> <span style={currencyStyle}>{priceInfo.setupFeeCurrency}</span>
              </>
            )
          }
          {
            (priceInfo.setupFeeType === 2) && (
              <>
                以 <span style={typeStyle}>{setupFeeComputeTypes[priceInfo.setupFeeComputeType]}</span> 为基数，
                月费为 <span style={numberStyle}>{`${priceInfo.setupFeeFeePercentage}%`}</span>，
                最低 <span style={numberStyle}>{priceInfo.setupFeeMinAmount}</span> <span style={currencyStyle}>{priceInfo.setupFeeCurrency}</span>
              </>
            )
          }
        </div>
      )
    },
    {
      label: '平台费',
      children: (
        <div>
          计费方式：<span style={typeStyle}>{platformFeeTypes[platformFeeType]}</span>
          {
            platformFeeType === '1' && (
              <>
                收费标准：<span style={numberStyle}>{priceInfo?.platformFeeAmount}</span> <span style={currencyStyle}>{priceInfo?.platformFeeCurrency}</span> 人 / 月
              </>
            )
          }
          {
            (platformFeeType === '2') && (
              <>
                以 <span style={typeStyle}>{platformFeeComputeTypes[priceInfo?.platformFeeComputeType]}</span> 为基数，
                月费为 <span style={numberStyle}>{`${priceInfo?.platformFeeFeePercentage}%`}</span>，
                最低 <span style={numberStyle}>{priceInfo?.platformFeeCapFeePerPersonMonth}</span> <span style={currencyStyle}>{priceInfo?.platformFeeCurrency}</span>
              </>
            )
          }
        </div>
      )
    },
    {
      label: '保证金',
      children: (
        <div>
          每个顾问收取 <span style={numberStyle}>{priceInfo?.depositMonths}</span> 个月的 <span style={typeStyle}>{depositTypes[priceInfo?.depositType]}</span>
        </div>
      )
    },
    {
      label: '其他服务费',
      children: (
        <>
          <div>
            非周期性支付服务费：<span style={numberStyle}>{priceInfo?.nonRecurringAmount}</span> <span style={currencyStyle}>{priceInfo?.nonRecurringCurrency}</span> 人/次
          </div>
          <div>
            报销管理服务费：
            {/* useReimbursementSystem 为空是老数据，老数据没有这个字段 */}
            {priceInfo?.useReimbursementSystem === true && "使用报销系统，"}
            {priceInfo?.useReimbursementSystem === false && "不使用报销系统，"}
            报销金额 * <span style={numberStyle}>{`${priceInfo?.reimbursementPercentage}%`}</span>
          </div>
        </>
      )
    },
    {
      label: '离职服务费',
      children: (
        <>
          {(!priceInfo.terminationFeeType || priceInfo.terminationFeeType === 1) && (
            <div>
              一次性支付 <span style={numberStyle}>{priceInfo.terminationFeeMonthlyPlatformFee}</span> <span style={currencyStyle}>{priceInfo.terminationFeeCurrency}</span> 人/次
            </div>
          )}
          {(priceInfo.terminationFeeType === 2) && (
            <div>
              以 <span style={typeStyle}>{terminationFeeTypes[priceInfo.terminationFeeComputeType]}</span> 为基数，
              月费为 <span style={numberStyle}>{`${priceInfo.terminationFeeFeePercentage}%`}</span>，
              最低 <span style={numberStyle}>{priceInfo.terminationFeeMinAmount}</span> <span style={currencyStyle}>{priceInfo.terminationFeeCurrency}</span> 
            </div>
          )}
        </>
      )
    },
    {
      label: '手续费',
      children: (
        <div>
          每笔支付 <span style={numberStyle}>{priceInfo?.transactionFeePerTransactionAmount}</span> <span style={currencyStyle}>{priceInfo?.transactionFeeCurrency}</span> + 支付金额的 <span style={numberStyle}>{`${priceInfo?.transactionFeePercentageOfPayment}%`}</span>
        </div>
      )
    },
    {
      label: '增值税VAT/GST/CT',
      children: priceInfo.vatGstCtIsEnabled ? (
        <div>
          <span style={typeStyle}>{vatGstCtTypes[priceInfo?.vatGstCtType?.toString()]}</span> VAT/GST/CT的 <span style={numberStyle}>{`${priceInfo?.vatGstCtPercentage}%`}</span>
        </div>
      ) : (
        '无'
      )
    },
    {
      label: '服务终止补偿累积',
      children: priceInfo.finalCompensationAccumulationIsEnabled ?
        <>
          是，规则：
          <span style={{ color: 'rgb(254, 145, 17)' }}>{priceInfo?.collectionRule}</span>
        </>
        : '无'
    },
  ]

  return (
    <table width="100%">
      {
        values.map((item, index) => {
          return (
            <tr key={index}>
              <td style={{ backgroundColor: 'white', border: '1px solid rgb(240, 240, 240)', padding: '12px' }}>
                {`${item.label}:`}
              </td>
              <td style={{ backgroundColor: 'white', border: '1px solid rgb(240, 240, 240)', padding: '12px' }}>
                {item.children}
              </td >
            </tr>
          )
        })
      }
    </table>
  )
}

const EorPrices: React.FC<{
  onQuotationIdChange?: (id: string | number) => void,
  eorList?: any[],
  stateDisabled?: boolean,
  isSelectDisabled?: boolean,
  isDisabledBtn?: boolean,
  isSelectId?: string | number,
  tags?: Record<string, React.ReactNode>,
  extras?: Record<string, React.ReactNode>,
}> = ({ onQuotationIdChange, eorList, stateDisabled, isSelectDisabled, isDisabledBtn, isSelectId, tags, extras }) => {
  const [selectedId, setSelectedId] = useState<string | number | null>(isSelectId || null)
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])

  if (!isArray(eorList)) {
    return <></>
  }

  const handleSelect = (id: string | number) => {
    const newSelectedId = selectedId === id ? null : id
    setSelectedId(newSelectedId)
    if (newSelectedId !== null) {
      onQuotationIdChange?.(newSelectedId)
    }
  }

  // 按国家地区进行分组
  const states: Record<string, any[]> = {};
  eorList.forEach((item: any) => {
    const key = stateDisabled ? `${item.countryCode}` : `${item.countryCode}_${item.state}`;
    const pre = states[key];
    if (pre) {
      states[key] = [...pre, item]
    } else {
      states[key] = [item]
    }
  })

  // 将分组的报价按照本地、外籍进行排序
  Object.keys(states).forEach((key: string) => {
    const list = states[key];
    list.sort((a: any, b: any) => (a.foreignEmployment ? 1 : 0) - (b.foreignEmployment ? 1 : 0))
  })

  const getQuatationTitle = (quoData: any) => {
    const localSignatory = quoData.localSignatory;
    let localSignatoryStr;
    if (localSignatory !== null && localSignatory !== undefined) {
      localSignatoryStr = localSignatory ? '中国境内主体' : "中国境外主体"
    }

    let visaTypeStr;
    if (quoData.visaType) {
      visaTypeStr = visaTypes[quoData.visaType];
    }
    const foreignEmploymentStr = quoData.foreignEmployment ? '外籍雇佣' : '本地雇佣';

    const title = [foreignEmploymentStr, localSignatoryStr, visaTypeStr].filter(x => !!x).join(' - ')
    const statusStr = quoData.status === 1 ? '已废弃' : undefined;
    return (
      <div className="flex">
        <>
          <span>{title}</span>
          {
            !!statusStr && (
              <Tag style={{ marginLeft: 20 }} bordered={false} color="#bbbbbb">{statusStr}</Tag>
            )
          }
          {
            tags && (
              <div style={{ marginLeft: 8 }}>
                {tags[quoData.id]}
              </div>
            )
          }
        </>
        <div className={`ml-auto ${!isSelectDisabled ? 'hidden' : ''}`} onClick={(e) => e.stopPropagation()}>
          {
              selectedId === quoData.id ? <span className="text-[#fe9111]">已选择此报价</span> : (
                <Button
                  disabled={isDisabledBtn}
                  type={selectedId === quoData.id ? 'primary' : 'default'}
                  size="small"
                  onClick={() => handleSelect(quoData.id)}
                >
                  
                  选择此报价
                </Button> 
              )
            }
          {/* <Button
            disabled={isDisabledBtn}
            type={selectedId === quoData.id ? 'primary' : 'default'}
            size="small"
            onClick={() => handleSelect(quoData.id)}
          >
            
            选择此报价
          </Button> */}
        </div>
      </div>
    )
  }

  return (
    <div>
      {
        Object.values(states).map((eorQuoListOfState: any[], index: number) => {
          const first = eorQuoListOfState[0];
          return (
            <div key={index} style={{ marginBottom: 12 }}>
              <div className={isSelectDisabled ? 'hidden' : 'font-bold text-16px ml-18px mb-8px'}>
                {
                  [
                    first.countryName || first.countryNameZh || first.countryCode,
                    stateDisabled ? undefined : (first.state === 'all' ? '' : first.state)
                  ].filter((x) => !!x).join(' / ')
                }
              </div>
              <Collapse
                activeKey={expandedKeys}
                onChange={(keys) => setExpandedKeys(keys as string[])}
              >
                {
                  eorQuoListOfState?.map((item: any) => {
                    return (
                      <Collapse.Panel
                        key={`${item.id}`}
                        header={getQuatationTitle(item)}
                        extra={extras?.[item.id]}
                        className={styles['collapseContentOverride']}
                      >
                        {getPricesRender(item?.priceInfo)}
                      </Collapse.Panel>
                    )
                  })
                }
              </Collapse>
            </div>
          )
        })
      }
    </div>
  )
}

export default EorPrices;
