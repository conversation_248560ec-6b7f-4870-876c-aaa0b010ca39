import logoImage from '@/assets/image/main-logo.svg';
import api from '@/services';
import S from '@/utils/storage';
import { setAuthorization } from '@smartdeer-ui/pc';
import { useModel, history } from '@umijs/max';
import { Button, Form, FormInstance, Input, message } from 'antd';
import { useEffect, useState } from 'react';
import { Sendcode } from '..';
import styles from './index.less';
import { setSecurityToken } from '@smartdeer-ui/pc/components/config-provider/utils';
import { PRODUCT_LINE, STORE } from '@/consts'

const APP_TOKEN_EXPIRED = process.env.APP_TOKEN_EXPIRED;

const emailSuffix = '@ideal-careerbridge.com';

interface LoginFormProps {
  onSuccess: () => void;
}

const RenderAlias = () => {
  return (
    <Form.Item
      name="alias"
      rules={[
        {
          required: true,
          message: '请输入邮箱',
          whitespace: true,
        },
      ]}
    >
      <Input placeholder={'邮箱'} addonAfter={emailSuffix} />
    </Form.Item>
  );
};

const RenderCodeAndPassword = ({
  form,
  checkConfig,
}: {
  form: FormInstance<any>;
  checkConfig: Record<string, any>;
}) => {
  // 获取验证码
  const handleClickSendCode = async (): Promise<boolean> => {
    const { alias } = form.getFieldsValue();

    if (!alias) {
      message.info('请输入有效的邮箱地址');
      return false;
    }

    if (checkConfig.checkType !== '2') {
      message.info('仅支持邮箱验证方式');
      return false;
    }

    try {
      await api.account.get2FaPermissionCode({
        alias: alias + emailSuffix,
      });

      return true;
    } catch (err: any) {
      message.error(err.message);
      return false;
    }
  };

  return (
    <Form.Item>
      <Form.Item
        name="password"
        rules={[{ required: true, message: '请输入密码', whitespace: true }]}
      >
        <div className="w-full flex justify-between gap-[8px]">
          <Input.Password placeholder={'密码'} />
        </div>
      </Form.Item>
      <Form.Item
        name="code"
        rules={[{ required: true, message: '请输入验证码', whitespace: true }]}
      >
        <div className="w-full flex justify-between gap-[8px]">
          <Input.Password placeholder={'验证码'} style={{ width: '100%' }} />
          <Sendcode onClick={handleClickSendCode} />
        </div>
      </Form.Item>

      <div className={`mt-[20px] text-center text-gray-400`}>
        没有账号请联系 OPS 系统管理员
      </div>
      <div className={`mt-[10px] text-center text-gray-400`}>
        <EMAIL> / <EMAIL>
      </div>
    </Form.Item>
  );
};

const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const { setUser } = useModel('useUser');
  const [checkConfig, setCheckConfig] = useState({});

  const [status, setStatus] = useState({
    btnLoading: false,
  });

  const [form] = Form.useForm();

  const fetch2FaConfig = async () => {
    const data = await api.account.fetch2FaLoginConfig();
    setCheckConfig(data);
  };

  useEffect(() => {
    fetch2FaConfig();
  }, []);

  // 登录
  const onSubmit = async (values: any) => {
    setStatus({
      ...status,
      btnLoading: true,
    });

    try {

      const alias = values.alias?.trim() + emailSuffix

      const { status: accountStatus } = await api.account.getAccountTouch({
        phoneCode: values.phoneCode,
        alias: alias,
        aliasType: '2',
        productLine: PRODUCT_LINE
      });

      // 未注册 
      if (accountStatus === '0') {
        message.error('账号或密码错误');

        setStatus({
          ...status,
          btnLoading: false,
        });
        return;
      }


      const { token, coverToken } = await api.account.loginByPasswordAndCode({
        code: values.code?.trim(),
        password: values.password?.trim(),
        alias: alias,
        isHash: accountStatus === '4'
      });

      S.set(STORE.LOGIN_ALIAS, alias, true)

      S.setAuthToken(token);
      setAuthorization(token, Number(APP_TOKEN_EXPIRED));
      setSecurityToken(coverToken); // 2fa token

      const profile = await api.account.getAccountInfo();

      setUser({
        ...profile,
        id: profile.accountId,
      });

      onSuccess();
    } catch (err: any) {
      message.error(err.message);
    }

    setStatus({
      ...status,
      btnLoading: false,
    });
  };

  const handleClickForgotPassword = () => {
    history.push(`/forgot-password?source=login`);
  }

  return (
    <div className={styles.page}>
      <div className={styles['body']}>
        <div className={styles['body-container']}>
          <div className={styles.logo}>
            <img src={logoImage} />
          </div>

          <div className={`${styles['body-form']} mt-6`}>
            <Form
              form={form}
              onFinish={onSubmit}
              autoComplete="off"
              size={'large'}
              initialValues={{ phoneCode: 1 }}
            >
              <RenderAlias />

              <RenderCodeAndPassword form={form} checkConfig={checkConfig} />

              <Form.Item>
                <Button
                  loading={status.btnLoading}
                  block
                  type="primary"
                  htmlType="submit"
                >
                  登录
                </Button>
              </Form.Item>
            </Form>
          </div>

          {/* <div className={`mt-[20px] text-center`}>
            <span onClick={handleClickForgotPassword} className={`text-[#87878d] cursor-pointer underline hover:opacity-80`}>
              忘记密码
            </span>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
