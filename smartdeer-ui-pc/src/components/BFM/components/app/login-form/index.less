.page {
  min-height: 100vh;
  padding-top: 10%;
}

.body {
  max-width: 450px;
  min-width: 200px;
  box-shadow: 0 0 20px #eee;
  border-radius: 6px;
  margin: 10px auto;
  padding: 20px;

  &-container {
    .logo {
      width: 200px;
      margin: 0 auto;
    }

    .tabs {
      font-size: 14px;
      font-weight: 500;
      color: #87878D;

      .tab {
        cursor: pointer;
        transition: all 0.2;
        padding-bottom: 7px;
        border-bottom: 2px solid transparent;

        &:hover {
          color: #fe9111;
        }
      }

      .active {
        color: #fe9111;
        border-bottom: 2px solid #fe9111;
      }
    }
  }

  &-form {
    width: 100%;
  }

  &-tip {
    text-align: center;
    margin-top: 10px;
  }
}

.affix {
  position: fixed;
  right: 40px;
  top: 40px;
  padding: 10px;
  background: #fff;
  width: 130px;
  // height: 100px;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 28%);
  z-index: 5;

  img {
    width: 100%;
  }
}
