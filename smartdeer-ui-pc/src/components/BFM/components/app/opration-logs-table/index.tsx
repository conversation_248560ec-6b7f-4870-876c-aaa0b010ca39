import React from 'react';
import { DynamicTable } from '@smartdeer-ui/pc';
import { Button, message, Modal, Table, Typography, Drawer } from 'antd';
import { functionRuntime } from '@/services/run';
import Column from 'antd/es/table/Column';
import CurdTableColumnContent from '@smartdeer-ui/pc/components/crud-table/table-column-content'
import { ConfigContext } from '@smartdeer-ui/pc/components/config-provider';
const { Title } = Typography;


export type OperatoinLogsTableProps = {
  type: string,
  id: number | string
  diffColums: {
    key: string,
    label: string,
    valueRender?: (value: any, record: any, index: number) => React.ReactNode,
  }[]
}


const OperatoinLogsTable: React.FC<OperatoinLogsTableProps> = (props) => {
  const {type, id, diffColums: diffValues} = props

  const diffRenders: {[key: string]: any} = {}
  diffValues.forEach((item: any) => {
    diffRenders[item.key] = item
  })

  const diffColums = [
    {
      key: "path", 
      title: "修改项", 
      dataIndex: 'path', 
      render: (path: string) => diffRenders[path]?.label || path
    },
    {
      key: "oldVal", 
      title: "原值", 
      dataIndex: 'oldVal', 
      render: (value: any, record: any, index: number) => (diffRenders[record.path]?.valueRender && diffRenders[record.path].valueRender(value, record, index)) || value || '--'
    },
    {
      key: "newVal", 
      title: "修改为", 
      dataIndex: 'newVal', 
      render: (value: any, record: any, index: number) => (diffRenders[record.path]?.valueRender && diffRenders[record.path].valueRender(value, record, index)) || value || '--'
    },
  ]

  // const context = React.useContext(ConfigContext);
  // const params = context.useUrlParams();
  // const type = params['type'];
  // const id = params['id'];

  const [data, setData] = React.useState<any[]>();
  const [loading, setLoading] = React.useState(false);
  const [tableParams, setTableParams] = React.useState<any>({
    pagination: {
      current: 1,
      pageSize: 10,
    },
  });

  const [drawerStatus, setDrawerStatus] = React.useState<any>({
    open: false,
  })


  const columns = [
    {
      title: "ID",
      key: "id"
    },
    {
      title: "操作账号",
      key: "operatorName"
    },
    {
      title: "操作时间",
      key: "updateTime",
      type: "template",
      template: "<div>*{updateTime | formatDate(YYYY-MM-DD HH:mm)}</div>"
    },
    {
      title: "操作项",
      key: "operationDesc"
    },
  ]

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await functionRuntime(0, "search_bfm_operation_log_list", {
        "current": tableParams.pagination.current,
        "limit": tableParams.pagination.pageSize,
        "size": tableParams.pagination.pageSize,
        "searchInfo": {
          "searchItems": [
            {
                "key": "object_type",
                "action": "eq",
                "value": type
            },
            {
                "key": "object_id",
                "action": "eq",
                "value": id
            }
          ]
        }
      });
      const dataInfo = res.dataInfo;
      setData(dataInfo)
      setTableParams({
        ...tableParams.pagination,
        total: res.total
      })
    } catch (err: any) {
      message.error(err)
    }
    setLoading(false)
  };

  React.useEffect(() => {
    fetchData()
  }, [
    tableParams.pagination?.current,
    tableParams.pagination?.pageSize,
    tableParams?.sortOrder,
    tableParams?.sortField,
    JSON.stringify(tableParams.filters),
  ]);


  return (
    <>
      <Table
        title={() => (<Title level={4} style={{ marginBottom: 24 }}>操作日志记录</Title>)}
        // columns={columns}
        rowKey={(record) => record.id}
        dataSource={data}
        pagination={tableParams.pagination}
        loading={loading}
      >

        {
          columns.map((column: any) => {
            return (
              <Column
                title={column.title}
                dataIndex={column.dataIndex || column.key}
                key={column.key}
                render={(value: any, record: any, index: number) => (
                  <CurdTableColumnContent
                    column={column}
                    text={value}
                    index={index}
                    record={record}
                    onShowModal={() => {}}
                    onShowDrawer={() => {}}
                  />
                )}
              />
            )
          })
        }
        <Column
          title="修改内容"
          dataIndex="diffValue"
          key="diffValue"
          render={(diffValue: string) => (
            <Button
              type='text'
              color='primary'
              onClick={() => setDrawerStatus({open: true, data: JSON.parse(diffValue)})}
            >
              查看详情
            </Button>
          )}
        />

      </Table>

      <Drawer
        title="修改内容"
        open={drawerStatus.open}
        onClose={() => setDrawerStatus({open: false})}
        width={600}
      >
        <Table
          columns={diffColums}
          dataSource={drawerStatus.data}
          pagination={false}
        >
        </Table>
      </Drawer>
    </>
  );
};

export default OperatoinLogsTable;