import React, { useEffect } from 'react';
import { useModel } from 'umi'; // 假设你使用的是 umi 的 useModel

// accountId: 14138,
// avatar: "https://global-image.smartdeer.work/test/images/0xc576951311bd480cb7f54f3700b71824.png",
// feishu: "d9d4922d",
// id: 14138,
// totp: false,
// userName: "lizi.zeng",

const CozeChat = ({ user }: { user: any }) => {
  // const { setUser } = useModel('useUser');

  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.1.0-beta.0/libs/cn/index.js";
    script.async = true;
    script.onload = () => {
      // debugger
      const cozeWebSDK = new CozeWebSDK.WebChatClient({
        config: {
          botId: '7467768716261523508',
          isIframe: false,
        },
        auth: {
          // type: 'token',
          // token: 'pat_zxzSAzxawer234zASNElEglZxcmWJ5ouCcq12gsAAsqJGALlq7hcOqMcPFV3wEVDiqjrg****',
          // onRefreshToken: () => 'pat_zxzSAzxawer234zASNElEglZxcmWJ5ouCcq12gsAAsqJGALlq7hcOqMcPFV3wEVDiqjrg****',
        },
        userInfo: {
          id: user?.id,
          url: user?.avatar,
          nickname: user?.userName,
        },
        ui: {
          base: {
            icon: 'https://cdn-icons-png.flaticon.com/512/4084/4084044.png',
            layout: window.innerWidth < 640 ? 'mobile' : 'pc',
            zIndex: 1003,
          },
          header: {
            isShow: true,
            isNeedClose: true,
          },
          asstBtn: {
            isNeed: true,
          },
          footer: {
            isShow: false,
          },
          chatBot: {
            title: "SmartDeer Bot",
            uploadable: false,
            width: 400,
            el: undefined,
            onHide: () => {
              // todo...
            },
            onShow: () => {
              // todo...
            },
          },
        },

      });
      window.cozeWebSDK = cozeWebSDK;
    };
    document.body.appendChild(script);

    // 清理函数，组件卸载时移除脚本
    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div id="coze-container"></div>
  );
};

export default CozeChat;