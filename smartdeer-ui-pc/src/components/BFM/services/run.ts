import { request } from '@umijs/max';
const APP_FUNCTION_API = process.env.APP_FUNCTION_API as string;
const APP_PROCESS_API = process.env.APP_PROCESS_API as string;
const APP_RIVER_FUNCTION_API = process.env.APP_RIVER_FUNCTION_API as string;
const APP_RIVER_ENTITY_FUNCTION_API = process.env.APP_RIVER_ENTITY_FUNCTION_API as string;

export const PROCESS_DICT = {
  checkLeave: 'gs_check_leave', // 检查是否可以请假
  startLeave: 'gs_start_leave', // 请假
  abolishLeave: 'gs_abolish_leave',
  getLeave: 'gs_get_leave',
  checkCancelLeave: 'gs_check_cancel_leave', // 检查销假
  startCancelLeave: 'gs_start_cancel_leave', // 提交销假
  abolishCancelLeave: 'gs_abolish_cancel_leave', // 撤销销假
  getCancelLeave: 'gs_get_cancel_leave', // 查询销假详情
  startDiscardCancelLeave: 'gs_start_discard_cancel_leave', // 废弃销假
  getDiscardCancelLeave: 'gs_get_discard_cancel_leave', // 获取废弃销假详情
  abolishDiscardCancelLeave: 'gs_abolish_discard_cancel_leave', // 取消废弃销假
  eorStartLeave: 'gs_start_eor_leave', // EOR请假
  eorGetLeave: 'gs_get_eor_leave',
  eorStartCancelLeave: 'gs_start_eor_cancel_leave_pro', // EOR提交销假
  eorGetCancelLeave: 'gs_get_pro_eor_cancel_leave', // 查询销假详情
  createReceiptData: 'gs_create_receipt_data', // 新建报销
  createReceiptFlow: 'gs_create_receipt', // 新建审批
  getReceipt: 'gs_get_receipt', // 获取报销详情
  abolishReceipt: 'gs_abolish_receipt', // 取消报销
  getReceiptByFlow: 'gs_get_receipt_by_flow', // 获取报销审批详情
  updateReceiptDate: 'gs_update_receipt_date', // 修改报销详情
  gsSearchEmployeeInfo: 'gs_search_employee_info', // 获取员工基本信息
  gsSearchSalaryMonth: 'gs_search_salary_month', // 获取员工薪资月份
  gsSearchSalaryDetail: 'gs_search_salary_detail', // 获取员工信息单信息
  gsSearchEmployeeFile: 'gs_search_employee_file', // 文件列表
};

/**
 * 过程
 * */
export async function processRuntime(
  entityUUID: ICB.EntityId,
  processKey: string,
  params = {},
) {
  const uri = APP_PROCESS_API.replace('*{entityUUID}', entityUUID);
  return request(uri, {
    method: 'post',
    data: {
      processKey,
      params,
    },
  });
}

export const FUNCTION_DICT = {
  eorCheckLeave: 'x_gs_eor_check_leave', // 检查是否可以请假
  searchMyLeave: 'search_my_leave', // 查询休假记录
  searchMyEorLeave: 'search_gs_eor_leave_pro', // 查询休假记录
  searchCancelLeave: 'search_cancel_leave', // 查询我的销假列表
  getCancelLeave: 'x_get_cancel_leave', // 查询销假详情
  getGlobalElement: 'x_get_global_element', // 获取系统组件
  getPrivateFileTmpCode: 'get_private_file_tmp_code', // 下载我的文件所需
  searchReceipt: 'search_receipt', // 查询报销审批列表记录
  searchReceiptList: 'search_receipt_list', // 查询报销记录
  xGsGetLeaveCalendar: 'x_gs_get_leave_calendar', // 获取日历里面有请假的日期
  searchSegaCustomerReportFollowup: 'search_sage_customer_report_followup', // 查询工单followup
  searchAllEntityConf: 'search_all_entity_conf',
  getEntityConfByKey: 'get_entity_conf_by_key',
  xGetCorehrOrgStructureRoot: 'x_get_corehr_org_structure_root', // 获取组织架构根节点
  xGetCorehrOrgStructureChild: 'x_get_corehr_org_structure_child', // 获取组织架构子节点
  xGetCorehrOrgPeople: 'x_get_corehr_org_people', // 获取组织架构员工列表
  xGetOrgStructure: 'x_get_org_structure', // 获取组织架构
  xGetAllCoreStructure: 'x_gs_corehr_get_all_core_structure', // 获取组织架构
  xAddOrgStructure: 'x_add_org_structure', // 新增组织架构
  xEditOrgStructure: 'x_edit_org_structure', // 修改组织架构
  xRemoveOrgStructure: 'x_remove_org_structure', // 删除组织架构
  xGetReportEntity: 'x_get_report_entity', // 获取entity包含的汇报线实体列表
  searchCorePeople: 'search_core_people', // corehr people搜索列表
  xGetCorehrOrgStructureChildAll: 'x_get_corehr_org_structure_child_all', // 获取组织架构所有子节点（包含员工）
  xAddCorePeople: 'x_add_core_people', // 新增在职员工
  xGetCorePeopleDiff: 'x_get_core_people_diff', // 获取修改在职员工diff
  xEditCorePeople: 'x_edit_core_people', // 修改人员信息
  xGSGetCorePeople: 'x_gs_get_core_people', // 获取人员信息
  searchCoreOnBoardingPeople: 'search_gs_core_on_boarding_people', // 预入职列表
  xAddOnboardingPeople: 'x_add_onboarding_people', // 新增预入职
  xEditOnboardingPeople: 'x_edit_onboarding_people', // 更新预入职
  xGetCoreOnBoardingPeople: 'x_gs_corehr_get_core_on_boarding_people', // 获取预入职详情
  searchCorehrPeopleOperate: 'gs_corehr_search_people_operate', // 查询员工操作记录
  searchPayrollMonth: 'gs_search_payroll_month', // 获取薪资月份列表
  searchFile: 'gs_search_file', // 获取文件列表
  xGetPayrollToken: 'x_gs_get_payroll_token', // 下载薪资单获取token
  searchEmployee: 'search_gs_core_people', // 获取在职人员列表
};

/**
 * 函数
 * */
export async function functionRuntime(
  entityUUID: ICB.EntityId,
  functionKey: string,
  params = {},
) {
  const uri = APP_RIVER_ENTITY_FUNCTION_API.replace('*{entityUUID}', entityUUID);
  return request(uri, {
    method: 'post',
    data: {
      functionKey,
      params,
    },
  });
}
