import { dealTypes } from "./deal-types";
import { billBatches } from "./bill-batches";

type RecordType = Record<string, any>;

/**
 * store key
 *
 * @property { string } TOKEN_IDENTIFIER // token 缓存名称
 * @property { string } USER_INFO // user 信息缓存名称
 * @property { string } menu // 菜单信息
 * */
export const STORE = {
  TOKEN_IDENTIFIER: 'auth_token',
  USER_INFO: 'user_info',
  MENU: 'menu',
  ICB_AUTH: 'icb_auth',
  ROUTES: 'auth_routes',
  ROUTE_MAP: 'auth_route_map',
  GS_LOCALE: 'gs_locale',
  GS_TIMEZONE: 'gs_timezone',
  PEOPLE_DIFF_INFO: 'people_diff_info',
  LOGIN_ALIAS: 'login_alias',
} as const;

export type STORE_KEY = keyof typeof STORE;
export type STORE_VALUE = (typeof STORE)[STORE_KEY];

export const SESSION_STORE = {
  COREHR_MENU: 'corehr_menu',
} as const;

export type SESSION_STORE_KEY = keyof typeof SESSION_STORE;
export type SESSION_STORE_VALUE = (typeof SESSION_STORE)[SESSION_STORE_KEY];

export const DICT = {
  contractType: 'contractType', //合同类型
  countryName: 'countryName', //EOR 支持国家
  native: 'native', //是否是本国人
  emailLang: 'emailLang', //邮件语言
  employeePermission: 'employeePermission', //员工授权
  nationality: 'nationality', //国籍
  employingState: 'employingState', //雇佣国家
  employeeIdType: 'employeeIdType', //证件类型
  dialCode: 'dialCode', //国家电话区号
  jobLevel: 'jobLevel', //职位等级
  jobType: 'jobType', //职位类型
  salaryType: 'salaryType', //薪资类型
  paidVacation: 'paidVacation', //薪资类型
  workPermit: 'workPermit', //是否已取得工作签证
  gender: 'gender', //性别
  marriage: 'marriage', //婚姻状况
  bankListHK: 'bankListHK', //香港支持银行列表
} as const;

export const PRODUCT_LINE = 'SMART_DEER_APP';

export const ACCEPT_LANGUAGE: RecordType = {
  'zh-CN': 'zh',
  'en-US': 'en',
  'zh-TW': 'zht',
} as const;

export const BOARDING_FLOW_SYSTEM_STATUS: RecordType = {
  '0': '草稿',
  '1': '待邀请',
  '2': '信息审核中',
  '3': '信息已审核',
  '4': '待开始',
  '5': '生效中',
  '6': '已取消',
  '7': '已邀请',
} as const;

export {
  dealTypes, 
  billBatches
}
