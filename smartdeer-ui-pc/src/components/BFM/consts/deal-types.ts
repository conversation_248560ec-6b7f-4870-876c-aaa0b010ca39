

interface BillType {
  value: number;
  label: string;
  needBillBatch?: boolean;
  billDetailDisabled?: boolean
}

interface DealType {
  value: number;
  label: string;
  billTypes: BillType[];
}

export const dealTypes: DealType[] = [
  { 
    value: 1, 
    label: 'EOR',
    billTypes: [
      { 
        value: 1, 
        label: '应收账单-保证金账单',
      },
      { 
        value: 2, 
        label: '应收账单-月度账单' ,
        needBillBatch: true,
      }
    ],
  },
  { 
    value: 2, 
    label: 'Payroll',
    billTypes: [
      { 
        value: 3, 
        label: '应收账单-月度账单-代发薪',
        needBillBatch: true,
      },
      { 
        value: 4, 
        label: '应收账单-月度账单-非代发薪',
        billDetailDisabled: true,
        needBillBatch: true,
      }
    ]
  },
  { 
    value: 3, 
    label: '工作签证',
    billTypes: [
      { 
        value: 2, 
        label: '应收账单-月度账单',
        billDetailDisabled: true
      }
    ]
  },
  { 
    value: 5, 
    label: 'AOR',
    billTypes: [
      { 
        value: 1,
        label: '应收账单-保证金账单',
        needBillBatch: false, 
      },
      { 
        value: 2, 
        label: '应收账单-月度账单',
        needBillBatch: true, 
      }
    ]
  },
  { 
    value: 6, 
    label: 'IC',
    billTypes: [
      { 
        value: 2, 
        label: '应收账单-月度账单',
        needBillBatch: true, 
      }
    ]
  },
  { 
    value: 7,
    label: '财税服务',
    billTypes: [
      { 
        value: 2, 
        label: '应收账单-月度账单',
        billDetailDisabled: true
      }
    ]
  },
  { 
    value: 8, 
    label: '公司设立', 
    billTypes: [
      { 
        value: 2, 
        label: '应收账单-月度账单',
        billDetailDisabled: true
      }
    ]
  },
  {
    value: 4, 
    label: '其他',
    billTypes: [
      { 
        value: 2, 
        label: '应收账单-月度账单',
        billDetailDisabled: true
      }
    ]
  },
]