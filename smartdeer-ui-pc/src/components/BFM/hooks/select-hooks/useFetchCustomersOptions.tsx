import { useState, useEffect, useRef } from 'react';

const useFetchCustomersOptions = (functionRuntime: any) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const retryCount = useRef<number>(0); // 重试次数
  const retryTimer = useRef<NodeJS.Timeout | null>(null); // 重试定时器

  // 清除重试定时器
  const clearRetryTimer = () => {
    if (retryTimer.current) {
      clearTimeout(retryTimer.current);
      retryTimer.current = null;
    }
  };

  // 获取用户选项
  const fetchCustomersOptions = async () => {
    // 如果已经有数据，直接返回
    if (options.length > 0) {
      return;
    }

    setLoading(true);
    setError(null);
   
    try {
      const { dataInfo } = await functionRuntime(0, 'search_bfm_customer_list', {
        current: 1,
        limit: 500,
        searchInfo: { 
          searchItems: [
            {
              key: 'status',
              action: 'eq',
              value: '1'
            }
          ],
          orders: [{ key: "id", asc: "desc" }] 
        },
        size: 500
      });

      if (dataInfo) {
        setOptions(dataInfo);
        clearRetryTimer(); // 获取到数据后取消重试
      } else {
        throw new Error('No data found');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch currency options');

      // 前 3 分钟内每 10 秒重试一次
      if (retryCount.current < 18) { // 3 分钟 = 180 秒，180 / 10 = 18 次
        retryCount.current += 1;
        retryTimer.current = setTimeout(fetchCustomersOptions, 10000); // 10 秒后重试
      }
    } finally {
      setLoading(false);
    }
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    fetchCustomersOptions();
    return () => {
      clearRetryTimer();
    };
  }, []);

  return { options, loading, error, fetchCustomersOptions };
};

export default useFetchCustomersOptions;