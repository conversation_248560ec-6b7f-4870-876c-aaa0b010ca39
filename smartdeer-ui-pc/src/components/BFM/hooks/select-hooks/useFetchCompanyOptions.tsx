import { useState, useEffect, useRef } from 'react';

const useFetchCompanyOptions = (functionRuntime: any) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const retryCount = useRef<number>(0); // 重试次数
  const retryTimer = useRef<NodeJS.Timeout | null>(null); // 重试定时器

  // 清除重试定时器
  const clearRetryTimer = () => {
    if (retryTimer.current) {
      clearTimeout(retryTimer.current);
      retryTimer.current = null;
    }
  };

  // 获取用户选项
  const fetchOurCompanyOptions = async () => {
    // 如果已经有数据，直接返回
    if (options.length > 0) {
      return;
    }

    setLoading(true);
    setError(null);
   
    try {
      const { dataInfo } = await functionRuntime(0, 'search_bfm_company_list', {
        current: 1,
        limit: 100,
        size: 100,
        searchInfo: {
          searchItems: [
            // { action: "like", key: "companyStatus", value: 0 }
          ],
          orders: [{ key: "id", asc: "desc" }]
        }
      });

      if (dataInfo) {
        setOptions(dataInfo);
        clearRetryTimer(); // 获取到数据后取消重试
      } else {
        throw new Error('No data found');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch currency options');

      // 前 3 分钟内每 10 秒重试一次
      if (retryCount.current < 18) { // 3 分钟 = 180 秒，180 / 10 = 18 次
        retryCount.current += 1;
        retryTimer.current = setTimeout(fetchOurCompanyOptions, 10000); // 10 秒后重试
      }
    } finally {
      setLoading(false);
    }
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    fetchOurCompanyOptions();
    return () => {
      clearRetryTimer();
    };
  }, []);

  return { options, loading, error, fetchOurCompanyOptions };
};

export default useFetchCompanyOptions;