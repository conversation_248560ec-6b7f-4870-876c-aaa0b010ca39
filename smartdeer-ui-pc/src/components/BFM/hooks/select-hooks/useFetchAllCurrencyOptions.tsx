import { useState, useEffect, useRef } from 'react';

const useFetchCurrencyOptions = (functionRuntime: any) => {
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const retryCount = useRef<number>(0); // 重试次数
  const retryTimer = useRef<NodeJS.Timeout | null>(null); // 重试定时器

  // 清除重试定时器
  const clearRetryTimer = () => {
    if (retryTimer.current) {
      clearTimeout(retryTimer.current);
      retryTimer.current = null;
    }
  };

  // 获取货币选项
  const fetchCurrencyOptions = async () => {
    // 如果已经有数据，直接返回
    if (options.length > 0) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { rs } = await functionRuntime(33, 'x_bfm_common_conf_get_by_key', {
        confKey: 'system.allCurrency',
      });

      if (rs?.confValue) {
        const parsedOptions = JSON.parse(rs.confValue);
        setOptions(parsedOptions);
        clearRetryTimer(); // 获取到数据后取消重试
      } else {
        throw new Error('No data found');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch currency options');

      // 前 3 分钟内每 10 秒重试一次
      if (retryCount.current < 18) { // 3 分钟 = 180 秒，180 / 10 = 18 次
        retryCount.current += 1;
        retryTimer.current = setTimeout(fetchCurrencyOptions, 10000); // 10 秒后重试
      }
    } finally {
      setLoading(false);
    }
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    fetchCurrencyOptions();
    return () => {
      clearRetryTimer();
    };
  }, []);

  return { options, loading, error, fetchCurrencyOptions };
};

export default useFetchCurrencyOptions;