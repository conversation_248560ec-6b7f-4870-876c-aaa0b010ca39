import api from '@/services';
import { message } from 'antd';
import { useEffect, useState } from 'react';

interface retFetch {
  info: ICB.ConfTable;
  isLoad: boolean;
  retry: () => void;
  errorMsg: any;
}

const columnsFilter = (columns: any[]) => {
  let search = false;

  const list = columns.map((column) => {
    const item = { ...column };

    if (column.search === 'true') {
      search = true;
    } else {
      item.search = false;
    }

    return {
      ...item,
    };
  });

  return {
    search,
    list,
  };
};

/**
 * fetch
 * */
export default function useFetchTable(conf: ICB.Conf): retFetch {
  const [load, setLoad] = useState(true);

  const [info, setInfo] = useState({
    columns: [],
    search: false,
  });

  const [errorMsg, setErrorMsg] = useState('');

  async function doFetch() {
    setLoad(true);

    try {
      const { columns, extra } = await api.conf.getTableColumns(conf);

      const { list, search }: ICB.Object = columnsFilter(columns);

      console.log('extra', extra.action);

      // 添加操作
      if (extra.action) {
        list.push(extra.action);
      }

      setInfo({
        columns: list,
        search,
      });
    } catch (err: any) {
      message.error(err.message);
      setErrorMsg(err.message);
    }

    setLoad(false);
  }

  useEffect(() => {
    doFetch();
  }, []);

  return {
    info,
    isLoad: load,
    retry: doFetch,
    errorMsg,
  };
}
