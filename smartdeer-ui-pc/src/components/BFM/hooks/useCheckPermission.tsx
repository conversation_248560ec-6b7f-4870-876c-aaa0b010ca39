import { Participant } from '@/pages/customer/detail/typing';
import { useModel } from '@umijs/max';
import { useState } from 'react';

export default function useCheckPermission() {
  const { user } = useModel('useUser');
  const [hasPermission, setHasPermission] = useState(false);

  const checkPermission = (ps: Participant[]) => {
    if (!user || !user.email) {
      setHasPermission(false);
    }
    const email = user.email;
    let canVisit: boolean;
    canVisit = ps.some(
      (person) => person.email === email && person.owner === '1',
    );
    setHasPermission(canVisit);
  };
  return {
    hasPermission,
    setHasPermission,
    checkPermission,
  };
}
