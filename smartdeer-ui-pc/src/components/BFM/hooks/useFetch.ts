import { compose } from '@/utils';
import { useParams } from '@umijs/max';
import { useEffect, useState } from 'react';

interface retFetch {
  response: { [propName: string]: any };
  isLoad: boolean;
  error: any;
  retry: () => void;
}

interface TExtra {
  middleware?: Function[];
  pageSize?: number;
  isFetch?: boolean;
}

/**
 * fetch
 * */
export default function useFetch(
  fetch: (...args: any) => Promise<ICB.Object>,
  params: ICB.Object = {},
  extra: TExtra = {},
): retFetch {
  const routeParams = useParams();

  const { middleware = [], pageSize = 20, isFetch } = extra;

  const [response, setResponse] = useState<ICB.Object>({});
  const [load, setLoad] = useState(true);
  const [error, setError] = useState(null as any);

  async function doFetch() {
    setLoad(true);

    try {
      // 执行 fetch 函数
      const res = await fetch(routeParams.entityId, params);

      if (middleware?.length) {
        const data = compose(...middleware)(res);

        setResponse(() => data);
      } else {
        setResponse(() => (Array.isArray(res) ? { list: res } : res));
      }
    } catch (e) {
      setError(e);
    }

    setLoad(false);
  }

  useEffect(() => {
    doFetch();
  }, []);

  return {
    response,
    isLoad: load,
    error,
    retry: doFetch,
  };
}
