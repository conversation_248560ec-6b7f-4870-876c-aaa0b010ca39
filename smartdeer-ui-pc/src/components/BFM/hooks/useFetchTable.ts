import api from '@/services';
import React from 'react';

interface retFetch {
  info: {
    columns: any[];
    tableActions: any[];
    dataSource: any[];
  };
  data: ICB.Object;
  isPageLoad: boolean;
  isLoad: boolean;
  pagination: ICB.Pagination;
  onChange: (pagination: any) => void;
  onRetry: () => void;
  error: any;
}

type SearchInfoType = {
  searchItems?: ICB.Object[];
  orders?: ICB.Object[];
};

type ExtraType = {};

/**
 * fetch
 * */
const useFetchTable = (
  functionKey: string,
  params: ICB.Object,
  // searchInfo: SearchInfoType,
  extra?: ExtraType,
): retFetch => {
  const [isPageLoad, setPageLoad] = React.useState(true);
  const [isLoad, setLoad] = React.useState(false);

  const [pagination, setPagination] = React.useState<ICB.Pagination>({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    hideOnSinglePage: true,
  });

  const [info, setInfo] = React.useState({
    columns: [] as any[],
    tableActions: [] as any[],
    dataSource: [] as any[],
  });

  const [data, setData] = React.useState({});

  const [error, setError] = React.useState({});

  const doFetch = async () => {
    setLoad(true);
    try {
      const { total, dataInfo } = await api.dynamic.getFunctionRuntimeTest({
        functionKey,
        params: {
          ...params,
          current: pagination.current,
          size: pagination.pageSize,
          start: 0,
          limit: pagination.pageSize,
        },
      });

      setData({
        list: dataInfo,
      });

      setPagination((values) => {
        return { ...values, total };
      });
    } catch (err: any) {
      setError(err);
    }

    setLoad(false);

    setPageLoad(false);
  };

  React.useEffect(() => {
    doFetch();
  }, [pagination.current, params]);

  // 分页事件
  const onChange = async (pagination: ICB.Pagination) => {
    setPagination({
      ...pagination,
    });
  };

  return {
    info,
    data,
    isPageLoad,
    isLoad,
    pagination,
    onChange,
    onRetry: doFetch,
    error,
  };
};

export default useFetchTable;
