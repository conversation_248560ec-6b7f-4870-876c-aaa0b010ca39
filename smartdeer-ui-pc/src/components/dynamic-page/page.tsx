import React from 'react'
import classNames from 'classnames'
import swrFetcher from '../../utils/swrFetcher'
import { ConfigContext } from '../config-provider'
import { devError } from '../../utils/log'
import Loading from '../loading'
import { useParamsDict } from '../../utils/hooks'
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate';
import SchemaPage from '../schema-page'
import type { SchemaPageProps } from '../schema-page'
import { replaceReferences } from '../../utils/replaceReferences'

import './page.less';

const classPrefix = `deer-dynamic-page`

export interface DynamicPageProps extends SchemaPageProps {
  className?: string;
  style?: React.CSSProperties;

  serviceAbility?: boolean,
  conf?: {
    confKey: string;
    version?: string;
    language?: string;

    areaCode?: string;
    productCode?: string;
    confType?: '1' | '2' | '3' | '4' | '5';  //1:负责人 2:模版 3:规则 4:常量 5:事件
  };
  confObject?: SchemaPageProps['columns']
}

const DynamicPage: React.FC<DynamicPageProps> = (props) => {
  const {
    className,
    style,

    serviceAbility,
    conf,
    confObject,

    ...restProps
  } = props

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict()

  if (!conf && !confObject) {
    devError('DynamicPage', '缺少必要参数 conf ！')
  }

  const [isLoading, setIsLoading] = React.useState(true)
  const [columns, setColumns] = React.useState<SchemaPageProps['columns']>([])

  const getConf = async () => {
    if (confObject) {
      setColumns(confObject)
      setIsLoading(false)
      return
    }

    if (!conf) {
      setIsLoading(false)
      return
    }

    try {
      const api = replaceVariablesInTemplate(paramsDict, configContext.appFunctionApi!)

      let functionKey = configContext.formatterUiByKey
      let params: Record<string, any> = {
        confKey: conf?.confKey,
        language: conf?.language || 'gl',
        version: conf?.version || '1'
      }

      if (serviceAbility) {
        functionKey = configContext.serviceAbilityKey
        params = {
          ...conf
        }
      }

      const { data } = await swrFetcher(api!, 'POST', {
        functionKey,
        params
      })

      const { page, ...rest } = JSON.parse(data.rs)

      const newPage = replaceReferences(page, rest)

      setColumns(newPage as SchemaPageProps['columns'])

    } catch (err) {
      console.error(err)

      throw new Error(`[smartdeer-ui: DynamicPage] Conf 配置错误！`)
    }

    setIsLoading(false)
  }

  React.useEffect(() => {
    getConf()
  }, [])

  if (isLoading) {
    return <Loading />
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <SchemaPage
        {...restProps}
        columns={columns}
      />
    </div>
  );
};

export default DynamicPage;
