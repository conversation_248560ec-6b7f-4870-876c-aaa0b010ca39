import { Empty, Pagination, PaginationProps } from 'antd';
import classNames from 'classnames';
import React from 'react';

import dayjs from 'dayjs';
import { sanitizeInput } from '../../utils/sanitizeInput';
import QuillView from '../quill-view';
import FieldListItemFiles from '../schema-page/field-components/list-item-files';
import Avatar from './avatar';
import './index.less';

const classPrefix = `deer-follow-up`;

interface FollowUpItem {
  avatar: string;
  creatorName: string;
  creatorId: string;
  updateTime: string;
  notifyName: string;
  json: {
    files: Array<{ name: string; url: string }>;
    text: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface FollowUpProps {
  className?: string;
  style?: React.CSSProperties;
  dataSource?: Array<FollowUpItem>;
  pagination?: PaginationProps;
  onPageChange?: (current: number, pageSize: number) => void;
}

const FollowUp: React.FC<FollowUpProps> = (props) => {
  const {
    dataSource = [],
    className,
    style,
    pagination: paginationConf,
    onPageChange,
  } = props;

  const [pagination, setPagination] = React.useState<PaginationProps>({
    ...paginationConf,
  });

  const handleChangePage = (current: number, pageSize: number) => {
    onPageChange?.(current, pageSize);
    setPagination({
      ...pagination,
      current,
      pageSize,
    });
  };

  if (dataSource?.length === 0) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {dataSource.map((item, index: number) => {
        return (
          <div key={index} className={`${classPrefix}-item`}>
            <div className={`${classPrefix}-avatar`}>
              <Avatar
                src={item.creatorJson.avatar}
                name={item.isOutsideSubmit === 1 ? item.customerCreatorName : item.creatorName}
                id={item.creatorId}
              />
            </div>
            <div className={`${classPrefix}-content`}>
              <div className={`${classPrefix}-info`}>
                <span className={`${classPrefix}-name`}>
                  {item.isOutsideSubmit === 1 ? item.customerCreatorName : item.creatorName}
                </span>
                <span className={`${classPrefix}-time`}>
                  {dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
              <div className={`${classPrefix}-text`}>
                <QuillView
                  content={sanitizeInput(item.json.text)}
                  style={{ lineHeight: '26px' }}
                />
              </div>
              {item?.json?.files && (
                <div className={`${classPrefix}-files`}>
                  <FieldListItemFiles
                    description={JSON.stringify(item?.json?.files)}
                    type="text"
                  />
                </div>
              )}
              {item.notifyName && (
                <div className={`${classPrefix}-notify`}>
                  <span>通知：</span>
                  {item.notifyName}
                </div>
              )}
            </div>
          </div>
        );
      })}
      {!!paginationConf && (
        <div className={`${classPrefix}-page`}>
          <Pagination {...pagination} onChange={handleChangePage} />
        </div>
      )}
    </div>
  );
};

export default FollowUp;
