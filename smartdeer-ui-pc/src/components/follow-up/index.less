.deer-follow-up {
  &-item {
    width: 100%;
    display: flex;
    font-size: 14px;
    margin-bottom: 40px;
  }

  &-content {
    margin-left: 10px;
    flex: 1;
    width: 400px;
  }

  &-avatar {
    flex: 34px 0 0;
  }

  &-info {
    display: flex;
    justify-content: space-between;
  }

  &-name {
    font-weight: 500;
    color: #474747;
  }

  &-time {
    color: #B0B0B0;
  }

  &-text {
    margin-top: 8px;
  }

  &-files {
    background: #F8FAFC;
    padding: 1px 10px 10px;
    margin-top: 5px;
  }

  &-notify {
    font-size: 12px;
    color: #B0B0B0;
    margin-top: 5px;
  }

  &-page {
    display: flex;
    justify-content: flex-end;
  }
}
