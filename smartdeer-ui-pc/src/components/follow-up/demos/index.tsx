import { FollowUp } from '@smartdeer-ui/pc';
import React from 'react';

const dataInfo = [
  {
      "isDeleted": 0,
      "createTime": 1729073710202,
      "deleteTime": 0,
      "isNotify": 0,
      "creatorId": 13175,
      "json": "{\"text\":\"跟进中\",\"files\":[{\"url\":\"https://global-image.smartdeer.work/files/test/sage/file-c161de76-3c56-474d-994c-3e7dcbb03117.jpg\",\"name\":\"file-b2f22e16-00bd-4f11-8a50-006f3488ef7d.jpg\"}]}",
      "html": "",
      "updateTime": 1729073710202,
      "id": 3,
      "ticketId": 7,
      "notify": "",
      "status": 1,
      "creatorInfo": "13175:10",
      "creatorJson": {
          "userName": "刘志旭",
          "email": "<EMAIL>",
          "avatar": "https://global-image.smartdeer.work/test/images/0x9670dc90cfde4f99b02bdb926b08c43b.jpg"
      },
      "creatorName": "刘志旭"
  },
  {
      "isDeleted": 0,
      "createTime": 1729071925420,
      "deleteTime": 0,
      "isNotify": 0,
      "creatorId": 13175,
      "json": "{\"text\":\"跟进中\",\"files\":[{\"url\":\"https://global-image.smartdeer.work/files/test/sage/file-c161de76-3c56-474d-994c-3e7dcbb03117.jpg\",\"name\":\"file-b2f22e16-00bd-4f11-8a50-006f3488ef7d.jpg\"}]}",
      "html": "",
      "updateTime": 1729071925420,
      "id": 2,
      "ticketId": 7,
      "notify": "",
      "status": 1,
      "creatorInfo": "13175:10",
      "creatorJson": {
          "userName": "刘志旭",
          "email": "<EMAIL>",
          "avatar": "https://global-image.smartdeer.work/test/images/0x9670dc90cfde4f99b02bdb926b08c43b.jpg"
      },
      "creatorName": "刘志旭"
  }
]

export default () => {
  return <FollowUp dataInfo={JSON.stringify(dataInfo)} />;
};
