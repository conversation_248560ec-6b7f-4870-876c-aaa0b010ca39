---
toc: content
group: 
  title: 数据展示
  order: 9
---

# DynamicTable 动态表格

可以配置 table 表单

## 示例

<code src="./demos/index.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| title | 标题 | `ReactNode` | `--` | |
| filterType | 搜索类型 | `string` | `--` | |
| filterConfKey | filterConfKey | `string` | `--` | |
| tableConfKey | tableConfKey | `string` | `--` | |
| pagination | 分页器 | `object` \| `false` | `--` | |
