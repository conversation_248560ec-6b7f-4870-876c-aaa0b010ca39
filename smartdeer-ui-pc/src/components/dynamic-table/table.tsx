import { useDeepCompareEffect } from 'ahooks';
import type { TablePaginationConfig } from 'antd';
import { Button, Space, Spin } from 'antd';
import classNames from 'classnames';
import { isEmpty, isString } from 'lodash';
import queryString from 'query-string';
import React from 'react';
import type { FetchType, SwrFetcherType } from '../../typing';
import { deepMergeImmutable } from '../../utils/deepMerge';
import { getEffectFetchConfig } from '../../utils/getEffectFetchConfig';
import { useParamsDict } from '../../utils/hooks';
import { devError } from '../../utils/log';
import { replaceReferences } from '../../utils/replaceReferences';
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate';
import swrFetcher from '../../utils/swrFetcher';
import { transformDataBasedOnRules } from '../../utils/transformDataBasedOnRules';
import { ConfigContext } from '../config-provider';
import type { CrudTableProps } from '../crud-table';
import CrudTable from '../crud-table';
import type { CurdTableColumnType } from '../crud-table/typing';
import Loading from '../loading';
import PageHeader from '../page-header';
import type { TableFilterProps } from '../table-filter';
import TableFilter from '../table-filter';
import type { TableSearchProps } from '../table-search';
import TableSearch from '../table-search';

import { ArrowLeftOutlined } from '@ant-design/icons';
import './table.less';

const classPrefix = `deer-dynamic-table`;

type FetchProType = FetchType & {
  defaultSearchItems?: object[];
};

export type DynamicTableTableConfObjectType = {
  onGet: FetchProType;
  tableProps: CrudTableProps;
  columns: CurdTableColumnType[];
};

export interface DynamicTableProps {
  className?: string;
  style?: React.CSSProperties;

  title?: string;
  type?: 'filter' | 'search';

  searchType?: TableSearchProps['type'];
  searchConfKey?: string;
  searchConf?: {
    version?: string;
    confKey?: string;
  };
  searchConfObject?: TableSearchProps;

  filterType?: TableFilterProps['type'];
  filterConfKey?: string;
  filterConf?: {
    version?: string;
    confKey?: string;
  };
  filterConfObject?: TableFilterProps;

  serviceAbility?: boolean;
  tableConfKey?: string;
  tableConf?: {
    version?: string;
    confKey: string;
    language?: string;

    areaCode?: string;
    productCode?: string;
    confType?: '1' | '2' | '3' | '4' | '5'; //1:负责人 2:模版 3:规则 4:常量 5:事件
  };
  tableConfObject?: DynamicTableTableConfObjectType;

  suffix?: React.ReactNode;
  newButton?: string;
  scroll?: {
    scrollToFirstRowOnChange?: boolean;
    x?: string | number | true;
    y?: string | number;
  };

  pagination?: TablePaginationConfig;

  back?: boolean;
  showPageHeader?: boolean;
}

const DynamicTable: React.FC<DynamicTableProps> = (props) => {
  const {
    className,
    style,

    title,
    back,
    type = 'default',

    searchType,
    searchConfObject,

    filterType,
    filterConfKey,
    filterConfObject,

    tableConfKey,
    tableConfObject,

    suffix,
    newButton,
    scroll,
    serviceAbility,
    tableConf,

    showPageHeader = true,

    ...extra
  } = props;

  if (!tableConfObject && !tableConf) {
    devError('DynamicTable', '缺少必要参数 tableConf ！');
  }

  const configContext = React.useContext(ConfigContext);
  const { router, language } = configContext;
  const paramsDict = useParamsDict();

  const [isLoading, setIsLoading] = React.useState(true);
  const [isTableLoading, setIsTableLoading] = React.useState(true);
  const [pagination, setPagination] = React.useState<TablePaginationConfig>(
    extra.pagination || {
      current: 1,
      pageSize: 20,
      total: 0,
      showSizeChanger: true,
      hideOnSinglePage: true,
    },
  );

  const tableGetRef = React.useRef<FetchProType>();

  const tableFetchConfigRef = React.useRef<SwrFetcherType>();

  const [tableSearchProps, setTableSearchProps] =
    React.useState<TableSearchProps>({ type: searchType });

  const [tableFilterProps, setTableFilterProps] =
    React.useState<TableFilterProps>({ type: filterType });

  const [tableParams, setTableParams] = React.useState<Record<string, any>>({});
  const [tableProps, setTableProps] = React.useState<CrudTableProps>({});
  const [tableColumns, setTableColumns] = React.useState<CurdTableColumnType[]>(
    [],
  );
  const [dataSource, setDataSource] = React.useState<any[]>([]);

  // 获取 table filter 配置
  const fetchTableSearchConf = async () => {
    if (searchConfObject) {
      return searchConfObject;
    }

    return {} as any;
  };

  // 获取 table filter 配置
  const fetchTableFilterConf = async () => {
    if (filterConfObject) {
      return filterConfObject;
    }

    if (!filterConfKey) {
      return {};
    }

    try {
      const { data } = await swrFetcher(
        '/v1/common/dict/conf/formatted',
        'POST',
        {
          key: filterConfKey,
          language: 'gl',
          confType: '0',
          version: '1',
        },
      );

      return JSON.parse(data.json);
    } catch (err) {
      throw new Error(
        `[smartdeer-ui: DynamicTable] Table Filter Conf 配置错误！`,
      );
    }
  };

  // 获取 table 配置
  const fetchTableConf = async () => {
    if (tableConfObject) {
      return tableConfObject;
    }

    try {
      if (tableConf) {
        const api = replaceVariablesInTemplate(
          paramsDict,
          configContext.appFunctionApi!,
        );

        let functionKey = configContext.formatterUiByKey;
        let params: Record<string, any> = {
          confKey: tableConf?.confKey as string,
          language: tableConf?.language || 'gl',
          version: tableConf?.version || '1',
        };

        if (serviceAbility) {
          functionKey = configContext.serviceAbilityKey;
          params = {
            ...tableConf,
          };
        }

        const { data } = await swrFetcher(api!, 'POST', {
          functionKey,
          params,
        });

        const { page, ...rest } = JSON.parse(data.rs);

        const newPage = replaceReferences(page, rest);

        return newPage;
      } else {
        const { data } = await swrFetcher(
          '/v1/common/dict/conf/formatted',
          'POST',
          {
            key: tableConfKey,
            language: 'gl',
            confType: '0',
            version: '1',
          },
        );

        return JSON.parse(data.json);
      }
    } catch (err) {
      throw new Error(`[smartdeer-ui: DynamicTable] Table Conf 配置错误！`);
    }
  };

  const fetch = async () => {
    setIsLoading(true);

    const [tableFilterConf, tableSearchConf, tableConf] = await Promise.all([
      fetchTableFilterConf(),
      fetchTableSearchConf(),
      fetchTableConf(),
    ]);

    tableGetRef.current = tableConf.onGet!;

    setTableFilterProps({
      ...tableFilterConf,
      columns: tableFilterConf.columns || tableFilterConf.form || [],
      ...tableConf.tableFilterProps,
    });

    setTableSearchProps({
      ...tableSearchConf,
      columns: tableSearchConf.columns || tableSearchConf.form || [],
      ...tableConf.tableSearchProps,
    });

    setTableProps(tableConf.tableProps || {});
    setTableColumns(tableConf.columns || []);

    if (!tableConf.onGet) {
      throw new Error(
        `[smartdeer-ui: DynamicTable] Table Conf onGet 配置错误！`,
      );
    }

    tableFetchConfigRef.current = getEffectFetchConfig(
      tableConf.onGet,
      configContext,
      paramsDict,
    );

    setIsLoading(false);
  };

  React.useEffect(() => {
    fetch();
  }, []); 
  
  React.useEffect(() => {
    fetch();
  }, [tableConfObject]);

  const getTableList = async () => {
    if (!tableFetchConfigRef.current) return;

    const {
      api,
      method,
      params,
      type,
      dataIndex = 'dataInfo',
    } = tableFetchConfigRef.current;
    const {
      defaultSearchItems = [],
      sortKey,
      sortOrder = 'descend',
      transform,
    } = tableGetRef.current || {};

    const paginationInfo = {
      current: pagination.current,
      // start: 0,
      size: pagination.pageSize,
      limit: pagination.pageSize,
    };

    let fetchUrl = '';
    let fetchMethod = method;
    let fetchParams: Record<string, any> = JSON.parse(JSON.stringify(params));

    setIsTableLoading(true);

    const isNormalApi = type === 'api';

    if (isNormalApi) {
      fetchUrl = queryString.stringifyUrl({
        url: api,
        query: paginationInfo,
      });

      const searchInfo = deepMergeImmutable(params.params.searchInfo ?? {}, {
        searchItems: defaultSearchItems,
        orders: [
          {
            asc: 'desc',
            key: 'updateTime',
            order: 0,
          },
          {
            asc: 'asc',
            key: 'createTime',
            order: 1,
          },
        ],
      });

      const newParams = deepMergeImmutable(params.params, tableParams);

      fetchParams = {
        ...fetchParams,
        params: { ...newParams, searchInfo }
      }

    } else {
      fetchUrl = api;

      const isSearchItems =
        tableSearchProps.type === 'searchItems' ||
        tableFilterProps.type === 'searchItems';
      const isExpressions =
        tableSearchProps.type === 'expressions' ||
        tableFilterProps.type === 'expressions';
      const isParams = tableSearchProps.type === 'params';

      if (isSearchItems) {
        fetchParams.params = deepMergeImmutable(fetchParams.params, {
          searchInfo: { searchItems: defaultSearchItems },
        });

        if (!isEmpty(tableParams)) {
          fetchParams.params = deepMergeImmutable(fetchParams.params, {
            searchInfo: tableParams,
          });
        }
      }

      if (isExpressions || isParams) {
        fetchParams.params = deepMergeImmutable(
          fetchParams.params,
          tableParams,
        );
      }

      fetchParams.params = deepMergeImmutable(
        fetchParams.params,
        paginationInfo,
      );
    }

    try {
      const { data } = await swrFetcher(fetchUrl, fetchMethod, fetchParams);

      const list = dataIndex && data ? data[dataIndex] : data;

      let newList = list.map((item: any) => {
        const jsonObject = isString(item.json) &&  item.json.length > 0 ? JSON.parse(item.json) : {};

        return {
          ...item,
          jsonObject,
        };
      });

      if (sortKey && Array.isArray(newList)) {
        const isDescend = sortOrder === 'descend';
        newList.sort((a: Record<string, any>, b: Record<string, any>) => {
          return isDescend ? b[sortKey] - a[sortKey] : a[sortKey] - b[sortKey];
        });
      }

      if (transform) {
        newList = transformDataBasedOnRules(newList, transform);
      }

      setDataSource(newList);

      setPagination((values) => {
        return { ...values, total: data.total };
      });
    } catch (err: any) {
      console.error(err.message);
    }

    setIsTableLoading(false);
  };

  useDeepCompareEffect(() => {
    getTableList();
  }, [
    tableFetchConfigRef.current,
    pagination.current,
    pagination.pageSize,
    tableParams,
  ]);

  const handleRefresh = () => {
    getTableList();
  };

  const handleClickSearch = (values: Record<string, any>) => {
    setPagination((values) => {
      return { ...values, current: 1 };
    });
    setTableParams(values);
  };

  const handleChangeTable = (pagination: TablePaginationConfig) => {
    setPagination({
      ...pagination,
    });
  };

  const handleClickToLink = (url?: string) => {
    if (router && url) {
      router.push(url);
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  const showTableFilter =
    (tableFilterProps?.columns && tableFilterProps?.columns.length > 0) ||
    (tableSearchProps?.columns && tableSearchProps?.columns.length > 0);

  const newButtonText = language?.dynamicTable?.newButtonText;

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      { showPageHeader &&
        <PageHeader
          title={
            <Space>
              {back && (
                <ArrowLeftOutlined
                  onClick={() => {
                    router?.back();
                  }}
                  className={`${classPrefix}-back`}
                />
              )}
              <span className={`${classPrefix}-title-text`}>{title}</span>
            </Space>
          }
          suffix={
            suffix ||
            (newButton ? (
              <Button onClick={() => handleClickToLink(newButton)} type="primary">
                {newButtonText}
              </Button>
            ) : null)
          }
        />
      }

      {showTableFilter && (
        <React.Fragment>
          {type === 'search' ? (
            <TableSearch
              {...tableSearchProps}
              className={`${classPrefix}-filter`}
              onSearch={handleClickSearch}
            />
          ) : (
            <TableFilter
              {...tableFilterProps}
              className={`${classPrefix}-filter`}
              onSearch={handleClickSearch}
            />
          )}
        </React.Fragment>
      )}

      <Spin spinning={isTableLoading}>
        <CrudTable
          className={`${classPrefix}-table`}
          scroll={scroll}
          columns={tableColumns}
          dataSource={dataSource}
          pagination={pagination}
          onChange={handleChangeTable}
          onRefresh={handleRefresh}
          {...tableProps}
        />
      </Spin>
    </div>
  );
};

export default DynamicTable;
