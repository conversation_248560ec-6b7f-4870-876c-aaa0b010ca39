import React from 'react';
import { Upload, Button, message } from 'antd';
import type { UploadProps } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import S from '../../../utils/storage'
import { STORE } from '../../../consts'
import { ConfigContext } from '../../config-provider'

// 兼容代码-----------
import 'antd/lib/upload/style';
//------------

const imageFileTypes = [
  'image/apng',
  'image/gif',
  'image/jpeg',
  'image/png',
];

export interface UploadImgRef {
  click: () => void
}

interface UploadImgProps {
  api?: string;
  onSuccess?: (file: string) => void;
}

const UploadImg = React.forwardRef<UploadImgRef, UploadImgProps>((props, ref) => {

  const { api, onSuccess } = props

  const { language, appApiBaseUrl } = React.useContext(ConfigContext)

  const uploadRef = React.useRef<any>(null);

  const token = S.getAuthToken()

  const actionUrl = React.useMemo(() => {
    let apiUrl = ''

    if (api) {
      apiUrl = api.includes('http') ? api : `${appApiBaseUrl}${api}`

    } else {
      apiUrl = `${appApiBaseUrl}/v1/common/file/uploadImage`
    }

    return apiUrl
  }, [api])

  const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
    const check = imageFileTypes.includes(file.type)

    if (!check) {
      message.error(`${file.name} ${language.upload.defaultValidateMessages.fileTypeError}`);
    }

    return check
  }

  const handleChange: UploadProps['onChange'] = ({ file }) => {

    if (file.status === 'done') {

      let fileUrl = file.response.data.absoluteFileUrl

      onSuccess?.(fileUrl)
    }
  }

  React.useImperativeHandle(ref, () => ({
    click: () => {
      if (!uploadRef.current) {
        return
      }

      uploadRef.current.click()
    }
  }))

  return (
    <Upload
      accept='.jpg,.jpeg,.png'
      action={actionUrl}
      headers={{ 'Authorization': token }}
      beforeUpload={handleBeforeUpload}
      onChange={handleChange}
    >
      <Button ref={uploadRef} icon={<UploadOutlined />}>点击上传</Button>
    </Upload>
  );
});

export default UploadImg
