import React from 'react';

export interface CountdownProps {
  time?: number;
  onSuccess: () => void;
}

const Countdown: React.FC<CountdownProps> = (props) => {
  const { onSuccess } = props;
  const time = props.time || 60;

  const [value, setValue] = React.useState(time);

  // 定时器
  const timerRef = React.useRef<NodeJS.Timeout>();

  React.useEffect(() => {
    // 监听 value 变化，value <= 0 关闭定时器，重制 value
    if (value <= 0) {
      clearInterval(timerRef.current);
      setValue(time);
      onSuccess()
    }
  }, [value]);

  React.useEffect(() => {
    // 开始执行定时器
    timerRef.current = setInterval(() => {
      setValue((value) => value - 1);
    }, 1000);

    // 不依赖任何变量，在组件卸载前执行清理操作
    return () => {
      clearInterval(timerRef.current);
    };
  }, []);

  return (
    <>
      {value}
    </>
  );
};

export default React.memo(Countdown);
