import React from 'react'
import './index.less'
import { isObject, isArray } from 'lodash'

const classPrefix = `deer-format-json`

interface FormatJsonProps {
  content: string
}

const FormatJson: React.FC<FormatJsonProps> = (props) => {
  const { content } = props

  const [text, setText] = React.useState('')

  React.useEffect(() => {
    let str = ''

    try {
      if (isObject(content) || isArray(content)) {
        str = JSON.stringify(content, null, 4)
      } else {
        str = JSON.parse(props.content)
        str = JSON.stringify(str, null, 4)
      }
    } catch (e) {
      str = props.content
    }

    setText(str)
  }, [content])

  return (
    <pre className={classPrefix}>
      {text}
    </pre>
  )
}

export default React.memo(FormatJson)
