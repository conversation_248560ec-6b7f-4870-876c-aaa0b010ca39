import { Empty, Timeline } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { isArray } from 'lodash';
import React, { FC, useMemo } from 'react';
import { mergeProps } from '../../utils/withDefaultProps';
import { ConfigContext } from '../config-provider';
import Avatar from './avatar';
import {
  cancelIcon,
  failIcon,
  pendingIcon,
  successIcon,
  systemIcon,
} from './icon';
import type { AuditTimelineProps } from './typing';

const classPrefix = `deer-audit-timeline`;

type RecordType = Record<string, any>;

const defaultProps = {
  dataInfo: '',
  rowKey: 'updateTime',
  showAvatar: true,
  showCC: false,
};

const defaultThemeColor = '#148AFF';

const AuditTimeline: FC<AuditTimelineProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const { language } = React.useContext(ConfigContext);

  const autoCCText = language.auditTimeline.description.autoCC;

  const { className, style, showAvatar, dataInfo, showCC } = props;

  let dataSource = useMemo(() => {
    if (!dataInfo) return null;
    let result: Record<string, any> | null = {};
    try {
      result = JSON.parse(dataInfo);
    } catch (e) {
      result = null;
    }
    return result;
  }, [dataInfo]);

  const processMap: Record<string, string> = {
    '1': language.auditTimeline.process.submit,
    '2': language.auditTimeline.process.approve,
    '3': language.auditTimeline.process.cc,
    '4': language.auditTimeline.process.cancel,
    '5': language.auditTimeline.process.end,
  };

  const statusMap: RecordType = {
    submit: {
      title: language.auditTimeline.status.submit,
      color: '#00B05B',
      icon: successIcon,
    },
    pending: {
      title: language.auditTimeline.status.pending,
      color: '#FF812A',
      icon: pendingIcon,
    },
    approved: {
      title: language.auditTimeline.status.approved,
      color: '#00B05B',
      icon: successIcon,
    },
    rejected: {
      title: language.auditTimeline.status.rejected,
      color: '#F91B42',
      icon: failIcon,
    },
    cancelled: {
      title: language.auditTimeline.status.cancelled,
      color: '#F91B42',
      icon: cancelIcon,
    },
    default: {
      color: '#B0B0B0',
    },
    end: {
      title: language.auditTimeline.status.ended,
      color: '#B0B0B0',
    },
  };

  const handleTransformData = (dataSource: Record<string, any>) => {
    const result: any = [];
    const { auditMap = {}, system_status } = dataSource;
    let completeTime = 0;

    Object.keys(auditMap).forEach((i: string) => {
      let color = '';
      const item = auditMap[i];
      const approvalList: RecordType[] = [];
      let currentPointTime = 0;

      // 处理提交和审批节点
      if (item.type !== '3') {
        const approvals = item.approval.split(',');
        // 审批类型: '0'-默认; '1'-当前节点有多个审批人，一个通过则全部通过
        const auditType = item.auditType;

        approvals.forEach((approvalId: string) => {
          const key = `${i}:${approvalId}`;
          let statusValue = '';
          let timestemp = '';
          let description = '';
          let textColor = '';
          let icon = '';

          if (dataSource[key]) {
            const value = dataSource[key].split(':') || [];
            statusValue = value[0] || '';
            timestemp = value[1] || '';
            completeTime = value[1]
              ? Math.max(completeTime, Number(value[1]))
              : completeTime;
            currentPointTime = value[1]
              ? Math.max(completeTime, Number(value[1]))
              : completeTime;
          }

          if (statusValue === '-1') {
            description = statusMap['submit'].title;
            color = statusMap['submit'].color;
            icon = statusMap['submit'].icon;
            textColor = statusMap['submit'].color;
          }
          if (statusValue === '1') {
            description = statusMap['approved'].title;
            color = statusMap['approved'].color;
            icon = statusMap['approved'].icon;
            textColor = statusMap['approved'].color;
          }
          if (statusValue === '2') {
            description = statusMap['rejected'].title;
            color = statusMap['rejected'].color;
            icon = statusMap['rejected'].icon;
            textColor = statusMap['rejected'].color;
          }
          if (statusValue === '3') {
            description = statusMap['cancelled'].title;
            color = statusMap['cancelled'].color;
            icon = statusMap['cancelled'].icon;
            textColor = statusMap['cancelled'].color;
          }

          // system_status 0:待审批 1:审批通过 2:驳回 3:取消
          if (!statusValue && system_status === '0') {
            description = statusMap['pending'].title;
            color = statusMap['pending'].color;
            icon = statusMap['pending'].icon;
            textColor = statusMap['pending'].color;
          }

          // 当前审批人还没审批，但流程已结束(拒绝或取消)
          if (
            !statusValue &&
            (system_status === '2' || system_status === '3')
          ) {
            description = statusMap['end'].title;
            color = statusMap['end'].color;
          }

          // 当前节点有多个审批人，审批规则为一个通过则全部通过的情况，判断当前节点是否有其他审批人已通过
          if (!statusValue && auditType === '1') {
            const hasApproved = approvals.find((approvalItem: any) => {
              const key = `${i}:${approvalItem}`;
              if (dataSource[key]) {
                const value = dataSource[key]?.split(':') || [];
                const statusValue = value[0] || '';
                return statusValue === '1';
              }
              return false;
            });

            if (hasApproved && system_status === '1') {
              description = statusMap['end'].title;
              color = statusMap['approved'].color;
              icon = statusMap['approved'].icon;
              textColor = statusMap['end'].color;
            }

            if (hasApproved && system_status !== '1') {
              description = statusMap['approved'].title;
              color = statusMap['approved'].color;
              icon = statusMap['approved'].icon;
              textColor = statusMap['approved'].color;
            }
          }

          const approvalInfo = item?.approvalInfo?.find(
            (item: any) => item.id === approvalId,
          );

          approvalList.push({
            name: approvalInfo?.employeeName,
            id: approvalInfo?.id,
            description,
            textColor,
            icon,
            avatar: item.avatar,
            time: timestemp
              ? dayjs(Number(timestemp)).format('YYYY-MM-DD HH:mm')
              : '',
          });
        });
        result.push({
          title: processMap[item.type],
          color,
          timestemp: currentPointTime,
          approver: approvalList,
        });
      }

      // 处理抄送节点
      if (showCC && item.type === '3') {
        let description = '';
        let textColor = statusMap['default'].color;
        let icon = system_status === '1' ? statusMap['approved'].icon : '';
        currentPointTime = system_status === '1' ? completeTime : 0;
        // 抄送描述
        const num = item.approval.split(',').length;
        const names = item?.approvalInfo
          ?.map((item: any) => item.employeeName)
          .join(',');
        description = autoCCText
          .replace('{num}', num)
          .replace('{names}', names);
        if (num > 1) {
          description = description.replace('member', 'members');
        }
        // 设置时间线dot的颜色，请假申请时使用蓝色点，审批成功时用绿色，其余用灰色
        color =
          system_status === undefined
            ? defaultThemeColor
            : system_status === '1'
              ? statusMap['approved'].color
              : statusMap['default'].color;
        approvalList.push({
          name: language.auditTimeline.title.system,
          description,
          textColor,
          icon,
          avatar: systemIcon,
          time: currentPointTime
            ? dayjs(Number(currentPointTime)).format('YYYY-MM-DD HH:mm')
            : '',
        });
        result.push({
          title: processMap[item.type],
          color,
          timestemp: currentPointTime,
          approver: approvalList,
        });
      }
    });

    // 按照操作时间对结果进行排序，兼容发起人取消的场景
    result.sort((a: any, b: any) => {
      const timeA = a.timestemp ? a.timestemp : Infinity;
      const timeB = b.timestemp ? b.timestemp : Infinity;
      return timeA - timeB;
    });

    // 结束
    const isAuditEnded = system_status !== undefined && system_status !== '0';
    result.push({
      title: processMap['5'],
      color:
        system_status === '1'
          ? statusMap['approved'].color
          : system_status === undefined
            ? defaultThemeColor
            : statusMap['end'].color,

      approver: [
        {
          // name: language.auditTimeline.title.system,
          avatar: systemIcon,
          description: isAuditEnded ? statusMap['end'].title : '',
          time: isAuditEnded
            ? dayjs(Number(completeTime)).format('YYYY-MM-DD HH:mm')
            : '',
          textColor: statusMap['end'].color,
          showAvatar: isAuditEnded ? true : false,
          icon: system_status === '1' ? statusMap['approved'].icon : '',
        },
      ],
    });
    return result;
  };

  const transformedData = useMemo(() => {
    if (dataSource) {
      return handleTransformData(dataSource);
    }
    return {};
  }, [dataSource]);

  if (
    !dataSource?.auditMap ||
    Object.keys(dataSource?.auditMap || {}).length === 0
  ) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  const renderTimelineChildren = (item: any) => {
    const children = (
      <div className={`${classPrefix}-item`}>
        <div className={`${classPrefix}-strong`}>{item.title}</div>

        {isArray(item.approver) && (
          <div className={`${classPrefix}-workers`}>
            {item.approver.map((row: any, rowIndex: number) => {
              return (
                <div key={rowIndex} className={`${classPrefix}-worker`}>
                  {showAvatar && row.showAvatar !== false && (
                    <div className={`${classPrefix}-worker-avatar`}>
                      <Avatar src={row.avatar} name={row.name} id={row.id} />
                      <div
                        className={`${classPrefix}-worker-avatar-status-icon`}
                      >
                        {row.icon}
                      </div>
                    </div>
                  )}
                  <div className={`${classPrefix}-worker-info`}>
                    <div className={`${classPrefix}-worker-name`}>
                      {row.name}
                    </div>
                    <div className={`${classPrefix}-worker-description`}>
                      <span style={{ color: row.textColor || item.color }}>
                        {row.description}
                      </span>
                      <span>{row.time}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );

    return {
      children,
      color: item.color || defaultThemeColor,
    };
  };

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <Timeline
        items={transformedData.map((item: any) => renderTimelineChildren(item))}
      />
    </div>
  );
};

export default AuditTimeline;
