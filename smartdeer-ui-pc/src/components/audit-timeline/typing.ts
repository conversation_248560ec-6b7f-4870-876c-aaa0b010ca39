export type RecordType = Record<string, any>;

export type StoreType = {
  label: string;
  value: string;
  type?: 'file' | 'date';
  suffix?: string;
};

type StatusMapType = {
  case: string | string[];
  title: string;
  color?: string;
  icon?: string;
  end?: boolean;
  extra?: Array<
    Omit<StatusMapType, 'case'> & {
      type: string | string[];
    }
  >;
};

export interface AuditTimelineProps {
  className?: string;
  style?: React.CSSProperties;
  showAvatar?: boolean;
  showCC?: boolean;

  steps?: string;
  config?: string;
  store?: StoreType[];

  dataInfo?: string;
  rowKey?: string;
  version?: '1.0' | '2.0' | '3.0' | 'PRO_FLOW';
  defaultThemeColor?: string;
  hideApprover?: boolean;

  processNameMap?: Record<string, string>;
  statusMaps?: Array<StatusMapType>;
  systemStatusMaps?: Array<StatusMapType>;
  title?: string;
  extra?: RecordType
}
