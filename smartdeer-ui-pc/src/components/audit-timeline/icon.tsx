import React from 'react';

export const systemIcon = (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16 32C7.1635 32 0 24.8365 0 16C0 7.1635 7.1635 0 16 0C24.8365 0 32 7.1635 32 16C32 24.8365 24.8365 32 16 32ZM16.556 11.3385V10.1615C16.7331 10.0598 16.8801 9.91297 16.9821 9.73601C17.0842 9.55906 17.1375 9.35825 17.1367 9.154C17.1367 8.5165 16.6292 8 16.0033 8C15.377 8 14.8695 8.5165 14.8695 9.15375C14.8695 9.58375 15.1005 9.95875 15.443 10.1573V11.3385H11.41C9.5275 11.3405 8.002 12.8932 8 14.8092V19.5257C8 21.4432 9.52625 22.998 11.41 23H20.59C22.4738 22.998 24 21.443 24 19.5257V14.8092C23.998 12.8932 22.4725 11.3405 20.59 11.3385H16.556ZM12.9832 15.83C13.7092 15.831 14.2975 16.43 14.2985 17.1688C14.2985 17.9083 13.7095 18.5075 12.9832 18.5075C12.2568 18.5075 11.6678 17.9083 11.6678 17.1688C11.6678 16.4295 12.2568 15.83 12.9832 15.83ZM19.0098 15.83C19.7358 15.831 20.324 16.43 20.325 17.1688C20.325 17.7103 20.0045 18.1985 19.513 18.4055C19.2726 18.5067 19.0072 18.5332 18.7515 18.4815C18.4959 18.4297 18.2617 18.3022 18.0795 18.1155C17.8951 17.9265 17.7699 17.6877 17.7193 17.4285C17.6687 17.1693 17.6948 16.901 17.7945 16.6565C17.998 16.1562 18.4778 15.83 19.0095 15.83H19.0098Z"
      fill="#3282F7"
    />
  </svg>
);

export const successIcon = (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="6" cy="6" r="5.5" fill="#00B05B" stroke="white" />
    <path
      d="M4.86493 8.49157C4.78909 8.46015 4.72019 8.4141 4.66215 8.35605L3.1355 6.70279C3.07746 6.64475 3.03141 6.57584 3 6.5C2.96859 6.42416 2.95242 6.34288 2.95242 6.26079C2.95242 6.09501 3.01827 5.93602 3.1355 5.81879C3.25273 5.70157 3.41172 5.63571 3.5775 5.63571C3.74328 5.63571 3.90228 5.70157 4.0195 5.81879L5.07715 7.00305L7.93295 4.18308C7.99099 4.12504 8.0599 4.079 8.13574 4.04758C8.21158 4.01617 8.29286 4 8.37495 4C8.45703 4 8.53832 4.01617 8.61416 4.04758C8.69 4.079 8.7589 4.12504 8.81695 4.18308C8.87499 4.24113 8.92104 4.31004 8.95245 4.38587C8.98386 4.46171 9.00003 4.543 9.00003 4.62508C9.00003 4.70717 8.98386 4.78845 8.95245 4.86429C8.92104 4.94013 8.87499 5.00904 8.81695 5.06708L5.54615 8.35605C5.48811 8.4141 5.4192 8.46015 5.34336 8.49157C5.26752 8.52299 5.18624 8.53916 5.10415 8.53916C5.02206 8.53916 4.94077 8.52299 4.86493 8.49157Z"
      fill="white"
    />
  </svg>
);

export const failIcon = (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1961_29174)">
      <circle cx="6" cy="6" r="5.5" fill="#F91B42" stroke="white" />
      <rect
        x="4.28564"
        y="8.42707"
        width="1.00813"
        height="5.85691"
        rx="0.504065"
        transform="rotate(-135 4.28564 8.42707)"
        fill="white"
        stroke="white"
        strokeWidth="0.2"
      />
      <rect
        x="3.57293"
        y="4.28564"
        width="1.00813"
        height="5.85691"
        rx="0.504065"
        transform="rotate(-45 3.57293 4.28564)"
        fill="white"
        stroke="white"
        strokeWidth="0.2"
      />
    </g>
    <defs>
      <clipPath id="clip0_1961_29174">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const cancelIcon = (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="6" cy="6" r="5.5" fill="#F91B42" stroke="white" />
    <path
      d="M4.86493 8.49157C4.78909 8.46015 4.72019 8.4141 4.66215 8.35605L3.1355 6.70279C3.07746 6.64475 3.03141 6.57584 3 6.5C2.96859 6.42416 2.95242 6.34288 2.95242 6.26079C2.95242 6.09501 3.01827 5.93602 3.1355 5.81879C3.25273 5.70157 3.41172 5.63571 3.5775 5.63571C3.74328 5.63571 3.90228 5.70157 4.0195 5.81879L5.07715 7.00305L7.93295 4.18308C7.99099 4.12504 8.0599 4.079 8.13574 4.04758C8.21158 4.01617 8.29286 4 8.37495 4C8.45703 4 8.53832 4.01617 8.61416 4.04758C8.69 4.079 8.7589 4.12504 8.81695 4.18308C8.87499 4.24113 8.92104 4.31004 8.95245 4.38587C8.98386 4.46171 9.00003 4.543 9.00003 4.62508C9.00003 4.70717 8.98386 4.78845 8.95245 4.86429C8.92104 4.94013 8.87499 5.00904 8.81695 5.06708L5.54615 8.35605C5.48811 8.4141 5.4192 8.46015 5.34336 8.49157C5.26752 8.52299 5.18624 8.53916 5.10415 8.53916C5.02206 8.53916 4.94077 8.52299 4.86493 8.49157Z"
      fill="white"
    />
  </svg>
);

export const pendingIcon = (
  <svg
    width="12"
    height="13"
    viewBox="0 0 12 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1979_30579)">
      <circle cx="6" cy="6.41406" r="5.5" fill="#FF812A" stroke="white" />
      <rect
        x="4.9"
        y="3.31406"
        width="1.2"
        height="4.2"
        rx="0.6"
        fill="white"
        stroke="white"
        strokeWidth="0.2"
      />
      <rect
        x="4.9"
        y="7.51406"
        width="1.2"
        height="4.2"
        rx="0.6"
        transform="rotate(-90 4.9 7.51406)"
        fill="white"
        stroke="white"
        strokeWidth="0.2"
      />
    </g>
    <defs>
      <clipPath id="clip0_1979_30579">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="translate(0 0.414062)"
        />
      </clipPath>
    </defs>
  </svg>
);


interface IconsProps {
  name: string;
}

const Icons: React.FC<IconsProps> = (props) => {
  const { name } = props

  return (
    <div>
      {name === "success" && successIcon}
      {name === "fail" && failIcon}
      {name === "cancel" && cancelIcon}
      {name === "pending" && pendingIcon}
    </div>
  )
}

export default Icons;
