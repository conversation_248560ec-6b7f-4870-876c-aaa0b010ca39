import React from 'react';
import { Avatar as AntdAvatar } from 'antd';

interface CurrentAvatarProps {
  src?: string;
  name?: string;
  id?: string;
}

const Avatar: React.FC<CurrentAvatarProps> = (props) => {

  const {
    src,
    name = '',
    id = '',
  } = props;

  if (src) {
    return <AntdAvatar size={34} src={src} />;
  }

  let colors = [
    '#FFA500',
    '#FF8060',
    '#F45B89',
    '#00CED1',
    '#4386EA',
    '#7C6DDB',
    '#5F9EA0',
    '#C5822F',
    '#32CD32',
    '#71B11D',
  ];
  const index = Number(id.toString().charAt(id.length - 1));

  return (
    <AntdAvatar size={32} style={{ background: colors[index] || '#FF812A' }}>
      {name.slice(0, 1)}
    </AntdAvatar>
  );
};

export default Avatar;
