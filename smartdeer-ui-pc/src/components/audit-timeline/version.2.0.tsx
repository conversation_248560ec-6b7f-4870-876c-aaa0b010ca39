import { Empty, Timeline } from 'antd';
import classNames from 'classnames';
import { isArray, isEmpty, isString } from 'lodash';
import React, { FC, useMemo } from 'react';
import { mergeProps } from '../../utils/withDefaultProps';
import { ConfigContext } from '../config-provider';
import {
  cancelIcon,
  failIcon,
  pendingIcon,
  successIcon,
  systemIcon,
} from './icon';
import dayjs from 'dayjs';
import Avatar from './avatar';
import type { AuditTimelineProps } from './typing'

const classPrefix = `deer-audit-timeline`;

type RecordType = Record<string, any>;

const defaultProps = {
  dataInfo: '',
  rowKey: 'updateTime',
  showAvatar: true,
  showCC: false,
  processNameMap: {},
  statusMaps: [],
  systemStatusMaps: [],
  defaultThemeColor: '#B0B0B0',
  hideApprover: false,
  title: ''
};

const AuditTimeline: FC<AuditTimelineProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const {
    className,
    style,
    showAvatar,
    dataInfo,
    showCC,
    processNameMap,
    statusMaps,
    systemStatusMaps,
    defaultThemeColor,
    hideApprover,
    title
  } = props;

  const { language } = React.useContext(ConfigContext);

  let dataSource = useMemo(() => {
    if (!dataInfo) return null;
    let result: Record<string, any> | null = {};
    try {
      result = JSON.parse(dataInfo);
    } catch (e) {
      result = null;
    }
    return result;
  }, [dataInfo]);

  const handleTransformData = (dataSource: RecordType) => {
    const { auditMap = {}, system_status } = dataSource;

    const result: any = [];

    let systemStatus = system_status.toString();
    let isEnd = false;
    let latestCompletionTime = 0;

    Object.keys(auditMap).forEach((index: string) => {
      const item = auditMap[index];

      const approvalList: RecordType[] = [];

      let color = '';
      let currentCompletionTime = 0;

      if (!hideApprover) {
        const approvals = item.approval.split(',');

        approvals.forEach((approvalId: string) => {
          const fullKey = `${index}:${approvalId}`;

          let statusValue = '';
          let timestamp = '';
          let reason = '';

          let description = '';
          let textColor = '';
          let icon = '';

          if (dataSource[fullKey]) {
            const [status, ts, ...re] = dataSource[fullKey].split(':') || [];

            statusValue = status || '';
            timestamp = ts || ''

            reason = !isEmpty(re) ? re.join(':') : '';

            latestCompletionTime = Math.max(latestCompletionTime, Number(timestamp) || 0);

            currentCompletionTime = Number(timestamp) || 0;
          }

          if (statusValue) {
            const currentStatus = statusMaps.find(row => {
              if (isString(row.case)) {
                return row.case === (statusValue)
              } else {
                return row.case.includes(statusValue)
              }
            })

            if (currentStatus?.extra) {
              const extraStatus = currentStatus?.extra.find(row => {
                if (isString(row.type)) {
                  return row.type === (item.type)
                } else {
                  return row.type.includes(item.type)
                }
              })

              if (extraStatus?.title) {
                currentStatus.title = extraStatus.title
              }

              if (extraStatus?.color) {
                currentStatus.color = extraStatus.color
              }

              if (extraStatus?.icon) {
                currentStatus.icon = extraStatus.icon
              }
            }

            color = currentStatus?.color || defaultThemeColor;
            description = currentStatus?.title || '';

            isEnd = currentStatus?.end || false;
          }

          if (!description && systemStatus) {

            const currentSystemStatus = systemStatusMaps.find(row => {
              if (row.case === '') {
                return true
              } else if (isString(row.case)) {
                return row.case === (systemStatus)
              } else {
                return row.case.includes(systemStatus)
              }
            })

            color = currentSystemStatus?.color || defaultThemeColor;
            description = currentSystemStatus?.title || '';
            isEnd = currentSystemStatus?.end || false;

            systemStatus = '';
          }

          if (!description) {
            if (isEnd) {
              description = language.auditTimeline.status.ended
            } else {
              description = language.auditTimeline.status.notStarted
            }
          }

          const approvalInfo = item?.approvalInfo?.find(
            (item: any) => item.id === approvalId,
          );

          approvalList.push({
            name: approvalInfo?.employeeName,
            id: approvalInfo?.id,
            description,
            textColor,
            icon,
            avatar: item.avatar,
            time: timestamp
              ? dayjs(Number(timestamp)).format('YYYY-MM-DD HH:mm')
              : '',
            reason: reason
          });
        });
      }

      result.push({
        title: processNameMap[item.type],
        color,
        timestamp: currentCompletionTime,
        approver: approvalList,
      });
    });


    // 按照操作时间对结果进行排序，兼容发起人取消的场景
    result.sort((a: any, b: any) => (a.timestamp || Infinity) - (b.timestamp || Infinity));

    return result;
  };

  const transformedData = useMemo(() => {
    if (dataSource) {
      return handleTransformData(dataSource);
    }
    return {};
  }, [dataSource]);

  if (
    !dataSource?.auditMap ||
    Object.keys(dataSource?.auditMap || {}).length === 0
  ) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  const renderTimelineChildren = (item: any) => {
    const children = (
      <div className={`${classPrefix}-item`}>
        <div className={classNames({
          [`${classPrefix}-strong`]: isArray(item.approver) && !!item.approver.length,
          [`${classPrefix}-grey`]: !(isArray(item.approver) && !!item.approver.length),
        })}>
          {item.title}
        </div>

        {
          (isArray(item.approver) && !!item.approver.length) && (
            <div className={`${classPrefix}-workers`}>
              {item.approver.map((row: any, rowIndex: number) => {
                return (

                  <React.Fragment key={rowIndex}>
                    <div key={rowIndex} className={`${classPrefix}-worker`}>
                      {showAvatar && row.showAvatar !== false && (
                        <div className={`${classPrefix}-worker-avatar`}>
                          <Avatar
                            src={row.avatar}
                            name={row.name}
                            id={row.id}
                          />
                          <div
                            className={`${classPrefix}-worker-avatar-status-icon`}
                          >
                            {row.icon}
                          </div>
                        </div>
                      )}
                      <div className={`${classPrefix}-worker-info`}>
                        <div className={`${classPrefix}-worker-name`}>
                          {row.name}
                        </div>
                        <div className={`${classPrefix}-worker-description`}>
                          <span style={{ color: row.textColor || item.color }}>
                            {row.description}
                          </span>
                          <span>{row.time}</span>
                        </div>
                      </div>
                    </div>

                    {row.reason && (
                      <div className={`${classPrefix}-worker-reason`}>
                        {language.auditTimeline.reasonForRejection} {row.reason}
                      </div>
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          )
        }
      </div >
    );

    return {
      children,
      color: item.color || defaultThemeColor,
    };
  };

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {title && (
        <h3 className={`${classPrefix}-title`}>
          {title}
        </h3>
      )}

      <Timeline
        items={transformedData.map((item: any) => renderTimelineChildren(item))}
      />
    </div>
  );
};

export default AuditTimeline;
