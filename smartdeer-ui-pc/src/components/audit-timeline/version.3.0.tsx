import { Empty, Timeline } from 'antd';
import classNames from 'classnames';
import { isArray, isEmpty, isString } from 'lodash';
import React, { FC, useMemo } from 'react';
import dayjs from 'dayjs';
import { mergeProps } from '../../utils/withDefaultProps';
import { ConfigContext } from '../config-provider';
import Icons from './icon';
import Avatar from './avatar';
import type { AuditTimelineProps, StoreType, RecordType } from './typing'
import FileView from '../file-view';

const classPrefix = `deer-audit-timeline`;


const defaultProps = {
  config: '',
  steps: '',
  rowKey: 'updateTime',
  showAvatar: true,
  defaultThemeColor: '#B0B0B0',
  title: ''
};

const renderStore = (data: RecordType, store: StoreType) => {
  let valueNode = null;

  switch (store.type) {
    case 'file':
      valueNode = (
        <FileView.Group
          list={data[store.value]}
          layout={'text'}
        />
      )
      break;

    case 'date':
      valueNode = dayjs(Number(data[store.value])).format('YYYY-MM-DD')
      break;

    default:
      valueNode = data[store.value]
      break;
  }

  return (
    <div style={{ padding: '8px 0 ' }}>
      <div style={{ fontSize: '12px', color: '#75757' }}>
        {store.label}
      </div>

      <div style={{ fontSize: '14px', color: '#474747' }}>
        {valueNode}

        {store.suffix && (
          <span style={{ marginLeft: '4px' }}>
            {store.suffix}
          </span>
        )}
      </div>
    </div>
  )
}


const AuditTimeline: FC<AuditTimelineProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const {
    className,
    style,
    showAvatar,
    steps,
    store,
    config,
    defaultThemeColor,
    title,
  } = props;

  const { language, systemName } = React.useContext(ConfigContext);

  let dataSource = useMemo(() => {
    if (!steps) return null;
    let result: Record<string, any> | null = {};
    try {
      result = JSON.parse(steps);
    } catch (e) {
      result = null;
    }
    return result;
  }, [steps]);

  const handleTransformData = (dataSource: RecordType) => {
    const conf = config ? JSON.parse(config) : {};

    if (isEmpty(conf)) {
      return dataSource.filter((item: any) => item.isShow === 1);
    }

    const confMap: Record<string, any> = {}

    Object.values(conf).forEach((item: any) => {
      confMap[item.flowKey] = item
    })

    const result = dataSource.map((item: any) => {
      const step = item.step.map((row: any) => {

        return {
          ...row,
          ...confMap[row.auditKey]
        }
      })

      return {
        ...item,
        step
      }
    }).filter((item: any) => item.isShow === 1);

    return result;
  };

  const transformedData = useMemo(() => {
    if (dataSource) {
      return handleTransformData(dataSource);
    }
    return [];
  }, [dataSource]);

  if (isEmpty(steps)) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  const renderTimelineChildren = (item: any) => {

    const children = (
      <div className={`${classPrefix}-item`}>
        <div className={classNames({
          [`${classPrefix}-strong`]: isArray(item.step) && !!item.step.length,
          [`${classPrefix}-grey`]: !(isArray(item.step) && !!item.step.length),
        })}>
          {item.stepKeyDesc}
        </div>

        {
          (isArray(item.step) && !!item.step.length) && (
            <div className={classNames(`${classPrefix}-workers`)}>
              {item.step.map((row: any, rowIndex: number) => {

                if (row.isShow !== 1) {
                  return <React.Fragment key={rowIndex} />;
                }

                const rowStore = row.store ? JSON.parse(row.store) : {};

                const storeNode = store?.filter((v: StoreType) => {
                  if (rowStore[v.value]) {
                    return true;
                  }

                  return false;
                }).map((v: StoreType) => {
                  return renderStore(rowStore, v);
                })

                return (
                  <React.Fragment key={rowIndex}>
                    <div key={rowIndex} className={`${classPrefix}-worker`}>
                      {showAvatar && row.showAvatar !== false && (
                        <div className={`${classPrefix}-worker-avatar`}>
                          <Avatar
                            src={row.avatar}
                            name={row.auditName}
                            id={row.id}
                          />
                          <div
                            className={`${classPrefix}-worker-avatar-status-icon`}
                          >
                            <Icons name={row.icon} />
                          </div>
                        </div>
                      )}
                      <div className={`${classPrefix}-worker-info`}>
                        <div className={`${classPrefix}-worker-name`}>
                          {row.auditName}
                        </div>
                        <div className={`${classPrefix}-worker-description`}>
                          <span style={{ color: row.color }}>
                            {row.auditKeyDesc}
                          </span>
                          <span>
                            {row.finishTime ? dayjs(row.finishTime).format('YYYY-MM-DD HH:mm') : ''}
                          </span>
                        </div>
                      </div>
                    </div>

                    {(isArray(storeNode) && storeNode.length > 0) && (
                      <div className={`${classPrefix}-worker-reason`}>
                        {storeNode?.map((n: any, index) => <React.Fragment key={index}>{n}</React.Fragment>)}
                      </div>
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          )
        }
      </div >
    );

    return {
      children,
      color: item.color || defaultThemeColor,
    };
  };

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {title && (
        <h3 className={`${classPrefix}-title`}>
          {title}
        </h3>
      )}

      <Timeline
        items={transformedData.map((item: any) => renderTimelineChildren(item))}
      />
    </div>
  );
};

export default AuditTimeline;
