import React from 'react';
import Version1 from './version.1.0';
import Version2 from './version.2.0';
import Version3 from './version.3.0';
import type { AuditTimelineProps } from './typing'

export type { AuditTimelineProps } from './typing';

import './index.less';

const AuditTimeline: React.FC<AuditTimelineProps> = (props) => {

  const { version = '1.0' } = props;

  const node = React.useMemo(() => {
    let node: React.ReactNode = null;

    switch (version) {
      case '1.0':

        node = <Version1 {...props} />;
        break;

      case '2.0':

        node = <Version2 {...props} />;
        break;

      case '3.0':

        node = <Version3 {...props} />;
        break;

      case 'PRO_FLOW':

        node = <Version3 {...props} />;
        break;

      default:
        node = <Version1 {...props} />;
        break;

    }

    return node;
  }, [version, props]);

  return (
    <>{node}</>
  );
};

export default AuditTimeline;
