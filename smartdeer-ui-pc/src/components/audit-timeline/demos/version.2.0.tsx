import { AuditTimeline } from '@smartdeer-ui/pc';
import React from 'react';

const dataInfo = {
  "customerId": "12",
  "receipt": {
    "id": "78",
    "uuid": "8941d413d7604884a9ecec974d18e53f",
    "entityId": "33",
    "customerId": "12",
    "systemSubject": "2",
    "accountId": "13269",
    "corePeopleId": "77",
    "json": "{\"isDuty\":\"1\",\"receiptType\":\"19\",\"costDetails\":[{\"country\":\"1\",\"type\":\"14\",\"amount\":3000,\"notes\":\"测试\",\"time\":\"*************\"}],\"customerId\":\"12\",\"system_receipt_subject\":\"2\",\"files\":[{\"url\":\"files/common/private/33/ICB_test/file-3bd93ca2-d8a0-451f-b0c1-1b86f7cabd55.png\",\"name\":\"个人信息.png\"}]}",
    "flowId": "0",
    "status": "-1",
    "createTime": "*************",
    "updateTime": "*************",
    "deleted": "0",
    "deleteTime": "0"
  },
  "auditMap": {
    "0": {
      "approvalInfo": [
        {
          "employeeName": "Tian Jia",
          "id": "13269"
        }
      ],
      "approval": "13269",
      "type": "1"
    },
    "1": {
      "approvalInfo": [
        {
          "employeeName": "客户公司审批",
          "id": "13269"
        }
      ],
      "approval": "13269",
      "type": "2"
    },
    "2": {
      "approvalInfo": [
        {
          "employeeName": "SmartDeer",
          "id": "<EMAIL>"
        }
      ],
      "approval": "<EMAIL>",
      "type": "3"
    },
    "3": {
      "approvalInfo": [
        {
          "employeeName": "SmartDeer",
          "id": "system"
        }
      ],
      "approval": "system",
      "type": "5"
    },
    "4": {
      "approvalInfo": [
        {
          "employeeName": "SmartDeer",
          "id": "system"
        }
      ],
      "approval": "system",
      "type": "6"
    }
  },
  "receiptId": "78",
  "audits": [
    "13269",
    "<EMAIL>"
  ],
  "system_status": "7",
  "system_uuid": "SMD-EOR_RECEIPT-f419dfa4051e434c",
  "system_create_time": "1727258639563",
  "system_current_index": "5",
  "currentAuditId": "<EMAIL>",
  "currentAuditType": "EMAIL",
  "currentAuditFlowId": "3648",
  "0:13269": "-1:1727258639609",
  "1:13269": "1:1727258653399",
  "2:<EMAIL>": "1:1727258680249",
  "3:system": "1:1727258690851",
  "4:system": "1:1727258715622"
}
// 1:提交 1:审批通过 2:驳回 3:撤销

// 流程状态： 0:待客户审核 3:发起人撤回 1:客户审批通过，待平台审批 2:客户已驳回 4:平台审批通过 5:平台已驳回 6:待付款 7:已付款

const processNameMap = {
  '1': '发起',
  '2': '审批',
  '3': '审批',
  '4': '取消',
  '5': '核定费用',
  '6': '支付费用',
}

const statusMaps = [
  {
    case: '-1',
    title: '已提交',
    color: '#00B05B',
  },
  {
    case: '1',
    title: '已通过',
    color: '#00B05B',
    extra: [
      {
        type: '5',
        title: '已核定',
      },
      {
        type: '6',
        title: '已支付',
      }
    ]
  },
  {
    case: '2',
    title: '审批未通过',
    color: '#F91B42',
    end: true,
  },
  {
    case: '3',
    title: '已取消',
    color: '#F91B42',
    end: true,
  }
]

const systemStatusMaps = [
  {
    case: '0',
    title: '待审核',
    color: '#FF812A',
  },
  {
    case: '1',
    title: '待审核',
    color: '#FF812A',
  },
  {
    case: '2',
    title: '已结束',
    color: '#B0B0B0',
    end: true
  },
  {
    case: '3',
    title: '已结束',
    color: '#B0B0B0',
    end: true
  },
  {
    case: '4',
    title: '待核定',
    color: '#FF812A',
  },
  {
    case: '5',
    title: '已结束',
    color: '#B0B0B0',
    end: true
  },
  {
    case: '6',
    title: '待付款',
    color: '#FF812A',
  }
]

export default () => {
  return (
    <AuditTimeline
      dataInfo={JSON.stringify(dataInfo)}
      showAvatar={true}
      version='2.0'
      processNameMap={processNameMap}
      statusMaps={statusMaps}
      systemStatusMaps={systemStatusMaps}
    />
  );
};
