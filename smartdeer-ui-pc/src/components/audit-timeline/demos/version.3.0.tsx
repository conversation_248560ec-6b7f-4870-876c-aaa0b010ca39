import { AuditTimeline } from '@smartdeer-ui/pc';
import React from 'react';

const dataInfo = {
  "version": "PRO_FLOW",//标记新流程
  "steps": [
    {
      "stepKey": "flow.start",
      "step": [
        {
          "id": 1,
          "entityId": 33,
          "accountId": "13269",
          "step": 1,
          "stepKey": "flow.start",
          "isDefault": 1,
          "isShow": 1,//是否展示
          "confGroup": "CREATE_EOR_LEAVE",
          "pattern": "{\n\t\"type\":\"DEFAULT\"\n}",
          "skip": 0,
          "json": "",
          "sort": 1,
          "auditName": "Tian Jia" //审批人
        }
      ],
      "stepKeyDesc": "提交" //节点名称
    },
    {
      "stepKey": "flow.audit",
      "step": [
        {
          "id": 2,
          "entityId": 33,
          "accountId": "13269",
          "step": 2,
          "stepKey": "flow.audit",
          "isDefault": 0,
          "isShow": 1,
          "confGroup": "CUSTOMER_AUDIT_EOR_LEAVE",
          "pattern": "{\n\t\"type\":\"ANY\"\n}",
          "skip": 0,
          "json": "{\n  \"uniqueType\":\"accountId\",\n  \"steps\": {\n      \"step\": \"1\",\n      \"isLastStep\": \"1\",\n      \"stepKey\": \"flow.eor.leave\",\n      \"stepDesc\": \"flow.eor.leave.desc\",\n      \"stepRemark\": \"\",\n      \"isRemark\": \"0\",\n      \"formKey\": \"leave_form\",\n      \"formVersion\": \"latest\",\n      \"validator\": {},\n      \"onSubmit\": [\n        {\n          \"type\":\"setVariables\",\n          \"target\":\"self\",\n          \"key\":\"system_create_time\",\n          \"value\":\"${NOW}\"\n        }\n      ],\n      \"onCreate\": [\n\n      ],\n      \"onSuccess\": [\n        {\n          \"type\": \"returnRet\"\n        }\n      ],\n      \"onGet\": [\n        {}\n      ],\n      \"afterUpdateVariables\":[\n       {\n         \"type\":\"process\",\n         \"expression\":\"pass->IF(system_status=1,TRUE=x_gs_eor_leave_audit_pass->x_gs_eor_calc_leave_detail(accountId= $x_gs_eor_leave_audit_pass.params.applyId)~x_gs_eor_leave_send_audit_pass_email_message~x_gs_eor_leave_send_audit_pass_system_message~x_gs_eor_leave_audit_pass_cc)\"\n       },\n       {\n        \"type\":\"process\",\n        \"expression\":\"pass->IF(system_status=2,TRUE=x_gs_eor_leave_audit_reject~x_gs_eor_leave_send_audit_reject_system_message)\"\n       }\n      ],\n      \"onComplete\": [\n      ],\n      \"onFailed\": {}\n    },\n   \"onCreate\": [\n    {\n      \"type\": \"setVariables\",\n      \"target\": \"self\",\n      \"value\": {\n        \"system_status\": \"0\",\n        \"system_create_time\":\"${NOW}\"\n      }\n    }\n  ],\n  \"onGet\": {},\n  \"afterCreate\":[\n  ],\n  \"beforeUpdateVariables\": [\n    {\n      \"type\": \"validator\",\n      \"expression\": \"if:system_status=0->system_status=1,system_status=2\",\n      \"errorMessage\":\"flow status change error\",\n      \"errorMessageKey\":\"flow_status_change_error\"\n    }\n  ]\n}",
          "sort": 2,
          "auditName": "Tian Jia"
        },
        {
          "id": 2,
          "entityId": 33,
          "accountId": "13980",
          "step": 2,
          "stepKey": "flow.audit",
          "isDefault": 0,
          "isShow": 1,
          "confGroup": "CUSTOMER_AUDIT_EOR_LEAVE",
          "pattern": "{\n\t\"type\":\"ANY\"\n}",
          "skip": 0,
          "json": "{\n  \"uniqueType\":\"accountId\",\n  \"steps\": {\n      \"step\": \"1\",\n      \"isLastStep\": \"1\",\n      \"stepKey\": \"flow.eor.leave\",\n      \"stepDesc\": \"flow.eor.leave.desc\",\n      \"stepRemark\": \"\",\n      \"isRemark\": \"0\",\n      \"formKey\": \"leave_form\",\n      \"formVersion\": \"latest\",\n      \"validator\": {},\n      \"onSubmit\": [\n        {\n          \"type\":\"setVariables\",\n          \"target\":\"self\",\n          \"key\":\"system_create_time\",\n          \"value\":\"${NOW}\"\n        }\n      ],\n      \"onCreate\": [\n\n      ],\n      \"onSuccess\": [\n        {\n          \"type\": \"returnRet\"\n        }\n      ],\n      \"onGet\": [\n        {}\n      ],\n      \"afterUpdateVariables\":[\n       {\n         \"type\":\"process\",\n         \"expression\":\"pass->IF(system_status=1,TRUE=x_gs_eor_leave_audit_pass->x_gs_eor_calc_leave_detail(accountId= $x_gs_eor_leave_audit_pass.params.applyId)~x_gs_eor_leave_send_audit_pass_email_message~x_gs_eor_leave_send_audit_pass_system_message~x_gs_eor_leave_audit_pass_cc)\"\n       },\n       {\n        \"type\":\"process\",\n        \"expression\":\"pass->IF(system_status=2,TRUE=x_gs_eor_leave_audit_reject~x_gs_eor_leave_send_audit_reject_system_message)\"\n       }\n      ],\n      \"onComplete\": [\n      ],\n      \"onFailed\": {}\n    },\n   \"onCreate\": [\n    {\n      \"type\": \"setVariables\",\n      \"target\": \"self\",\n      \"value\": {\n        \"system_status\": \"0\",\n        \"system_create_time\":\"${NOW}\"\n      }\n    }\n  ],\n  \"onGet\": {},\n  \"afterCreate\":[\n  ],\n  \"beforeUpdateVariables\": [\n    {\n      \"type\": \"validator\",\n      \"expression\": \"if:system_status=0->system_status=1,system_status=2\",\n      \"errorMessage\":\"flow status change error\",\n      \"errorMessageKey\":\"flow_status_change_error\"\n    }\n  ]\n}",
          "sort": 2,
          "auditName": "zhangying"
        }
      ],
      "stepKeyDesc": "审批"
    },
    {
      "stepKey": "flow.cc",
      "step": [
        {
          "id": 7,
          "entityId": 33,
          "accountId": "<EMAIL>",
          "step": 2,
          "stepKey": "flow.cc",
          "isDefault": 0,
          "isShow": 0,
          "confGroup": "CUSTOMER_AUDIT_EOR_LEAVE",
          "pattern": "{\n\t\"type\":\"CC\"\n}",
          "skip": 0,
          "sort": 3,
          "auditName": "tianjia"
        }
      ],
      "stepKeyDesc": "抄送"
    },
    {
      "stepKey": "flow.finish",
      "step": {},
      "stepKeyDesc": "完成"
    }
  ]
}



export default () => {
  return (
    <AuditTimeline
      dataInfo={JSON.stringify(dataInfo)}
      showAvatar={true}
      version='3.0'
    />
  );
};
