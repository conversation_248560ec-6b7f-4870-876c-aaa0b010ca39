import { AuditTimeline } from '@smartdeer-ui/pc';
import React from 'react';

const dataInfo = {
  cc: '13980',
  reason: 'test',
  startFrom: '*************',
  leaveConfigGroupKey: 'bereavement_Leave',
  auditMap: {
    '0': {
      approvalInfo: [
        {
          employeeName: 'tianjia',
          id: '13321',
        },
      ],
      approval: '13321',
      type: '1',
    },
    '1': {
      approvalInfo: [
        {
          employeeName: '张颖',
          id: '13980',
        },
      ],
      approval: '13980',
      type: '2',
    },
    '2': {
      approvalInfo: [
        {
          employeeName: '张颖',
          id: '13980',
        },
      ],
      approval: '13980',
      type: '3',
    },
  },
  userLeaveIds: ['2898'],
  leaveConfId: '56',
  endTo: '*************',
  ccAccountIds: ['13980'],
  files: [
    {
      url: 'files/common/private/51/hashkey/file-8c4e081c-71dd-4347-ad0a-b19debd7dc88.jpg',
      name: 'IMG_2609 (1).jpg',
    },
  ],
  userReallyWant: '3',
  audits: ['13980'],
  userLeaves: [
    {
      id: '2898',
      uuid: '6dc7d961688340f88cecbab2a5381b5a',
      traceId: '57894bffa06f479e8b3d9e28faedddbf',
      entityId: '51',
      accountId: '13321',
      leaveConfId: '56',
      parentId: '0',
      sign: '1',
      cost: '0',
      timeZone: 'Asia/Hong_Kong',
      fromTime: '*************',
      toTime: '*************',
      originFromTime: '*************',
      originToTime: '*************',
      flowId: '0',
      discardFlowId: '0',
      cancelLeaveId: '0',
      createTime: '*************',
      updateTime: '*************',
      deleted: '0',
      deleteTime: '0',
      status: '0',
    },
  ],
  system_status: '1',
  system_uuid: 'SMD-LEAVE-42fb8a3f619d4503',
  system_create_time: '*************',
  system_current_index: '2',
  currentAuditId: '13980',
  currentAuditFlowId: '2771',
  '0:13321': '-1:*************',
  '1:13980': '1:*************',
  statusStr: 'Completed',
  groupKeyStr: 'Bereavement Leave',
};

export default () => {
  return (
    <AuditTimeline dataInfo={JSON.stringify(dataInfo)} showAvatar={true} />
  );
};
