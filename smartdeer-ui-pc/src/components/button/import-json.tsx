import React, { <PERSON> } from 'react'
import { Upload, Button, message } from "antd";
import type { UploadProps } from 'antd';
import type { ButtonProps as AntdButtonProps } from 'antd'
import classNames from 'classnames'
import { ConfigContext } from '../config-provider'

import { classPrefix } from '.'

import './button.less'

interface ImportJsonProps extends AntdButtonProps {
  onCallback?: (value: string) => void
}

const ImportJson: FC<ImportJsonProps> = (props) => {

  const {
    children,
    onCallback,

    className,
    ...restProps
  } = props

  const { language } = React.useContext(ConfigContext)

  const [isButtonLoading, setIsButtonLoading] = React.useState(false)

  const handleChange: UploadProps['onChange'] = ({ file }) => {

    if (file.type !== 'application/json') {
      message.error(language.button.defaultValidateMessages.fileTypeError)
      return
    }

    setIsButtonLoading(true)

    try {
      const reader = new FileReader()

      reader.readAsText(file as any, 'UTF-8')

      reader.onload = (e: any) => {
        onCallback?.(e.target.result as string)
      }

    } catch (err: any) {
      message.error(err.message)
    }

    setIsButtonLoading(false)
  }

  return (
    <Upload
      accept='json'
      showUploadList={false}
      beforeUpload={() => false}
      onChange={handleChange}
    >
      <Button
        {...restProps}
        loading={isButtonLoading}
        className={classNames(classPrefix, `${classPrefix}-import-json`, className)}
      >
        {children}
      </Button>
    </Upload >
  )
}

export default ImportJson
