import React from 'react'
import { But<PERSON> as AntdButton } from 'antd'
import type { ButtonProps as AntdButtonProps } from 'antd'
import classNames from 'classnames'

import { classPrefix } from '.'

import './button.less'

export interface ButtonProps extends AntdButtonProps {
  borderRadius?: string;
}

const Button: React.FC<ButtonProps> = (props) => {
  const {
    children,
    className,
    ...restProps
  } = props

  return (
    <AntdButton className={classNames(classPrefix, className)} {...restProps}>
      {children}
    </AntdButton>
  )
}

export default Button
