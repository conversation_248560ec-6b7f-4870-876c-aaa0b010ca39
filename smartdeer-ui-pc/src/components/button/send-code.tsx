import React, { useCallback, useState } from 'react';
import { Button } from 'antd';
import type { ButtonProps as AntdButtonProps } from 'antd'
import Countdown from '../common/countdown';
import { ConfigContext } from '../config-provider';
import { classPrefix } from '.'
import { mergeProps } from '../../utils/withDefaultProps'

import './send-code.less'

interface SendCodeProps extends Omit<AntdButtonProps, 'onClick'> {
  time?: number;
  onClick?: () => Promise<boolean>;
  status?: number;
}

const defaultProps: SendCodeProps = {
  type: 'default',
  time: 60,
  status: 0,
}

const SendCode: React.FC<SendCodeProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const { onClick, children, type, size, time } = props;

  const [status, setStatus] = useState(props.status);

  const { language } = React.useContext(ConfigContext);

  const intl = language.button.sendCode;

  const handleClick = async () => {
    if (status !== 0) return;

    // 发送中
    setStatus(1);

    // 调用父级组件
    const res = await onClick?.();

    // 发送失败
    if (!res) {
      setStatus(0);
      return;
    }

    // 发送成功, 开始倒计时
    setStatus(2);
  };

  const handleClear = useCallback(() => {
    setStatus(0);
  }, []);

  return (
    <Button
      className={`${classPrefix} ${classPrefix}-send-code`}
      type={type}
      size={size}
      onClick={handleClick}
    >
      {status === 0 ? (
        <>
          {children ? children : intl.sendCode}
        </>
      ) : status === 1 ? (
        <span className={`${classPrefix}-send-code-sending`}>
          {intl.sending}
        </span>
      ) : (
        <span className={`${classPrefix}-send-code-seconds`}>
          <Countdown time={time} onSuccess={handleClear} />
          &nbsp;
          {intl.seconds}
        </span>
      )}
    </Button>
  );
};

export default SendCode;
