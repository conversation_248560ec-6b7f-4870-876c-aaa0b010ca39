---
toc: content
group: 
  title: 基础
  order: 1
---

# Button 按钮

按钮用于开始一个即时操作。

## 示例

<code src="./demos/index.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| block | 是否是块级元素	 | `boolean` | `false` |  |
| type | 设置按钮类型 | `'primary' \| 'secondary' \| 'tertiary' \| 'rounded'` | `'primary'` |  |
| size | 大小	 | `'large' \| 'middle' \| 'small'` | `'large'` |  |
| borderRadius | 圆角	 | `string` | `'8px'` |  |
| loading | 加载状态 | `boolean` | `false` |  |
| disabled | 是否禁用 | `boolean` | `false` |  |
| onClick | 点击事件 | `(info: {}, e: React.MouseEvent) => void` | `--` |  |
