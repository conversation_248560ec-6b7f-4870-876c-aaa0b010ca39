import React, { FC } from 'react'
import { Button, Input, Modal, message } from "antd";
import type { ButtonProps as AntdButtonProps } from 'antd'
import classNames from 'classnames'
import { ConfigContext } from '../config-provider'
import { downloadJson } from '../../utils'

import { classPrefix } from '.'

import './button.less'

interface ExportJsonProps extends AntdButtonProps {
  json?: string
}

const ExportJson: FC<ExportJsonProps> = (props) => {
  const {
    className,
    children,
    json,

    onClick,
    ...restProps
  } = props

  const { language } = React.useContext(ConfigContext)

  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const [fileName, setFileName] = React.useState('')

  const handleClickButton = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    if (!json) {
      message.error(language.button.defaultValidateMessages.jsonIsNotDefined)
      return
    }

    setIsModalOpen(true)

    onClick?.(e)
  }

  const handleClickExport = () => {
    const newFileName = fileName || `json-${Date.now()}`

    try {
      downloadJson(newFileName, JSON.parse(json as string))
    } catch (err) {
      message.error(language.button.defaultValidateMessages.jsonTypeError)
    }

    setIsModalOpen(false)
    setFileName('')
  }

  return (
    <>
      <Button
        {...restProps}
        className={classNames(classPrefix, `${classPrefix}-import-json`, className)}
        onClick={handleClickButton}
      >
        {children}
      </Button>

      <Modal
        title={language.button.exportJson.fileName}
        open={isModalOpen}
        width={350}
        onCancel={() => setIsModalOpen(false)}
        onOk={handleClickExport}
        okText={language.modal.okText}
        cancelText={language.modal.cancelText}
      >
        <Input value={fileName} onChange={(e) => setFileName(e.target.value)} />
      </Modal>
    </>
  )
}

export default ExportJson
