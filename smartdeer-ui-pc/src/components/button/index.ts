import type { ButtonProps } from './button';
import InternalButton from './button';
import ExportJson from './export-json';
import ImportJson from './import-json';
import SendCode from './send-code';

export const classPrefix = `deer-button`;

export { type ButtonProps };

type CompoundedComponent = React.FC<ButtonProps> & {
  ImportJson: typeof ImportJson;
  ExportJson: typeof ExportJson;
  SendCode: typeof SendCode;
};

const Button = InternalButton as CompoundedComponent;

Button.ImportJson = ImportJson;
Button.ExportJson = ExportJson;
Button.SendCode = SendCode;

export default Button;
