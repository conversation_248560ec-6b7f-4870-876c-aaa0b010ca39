import type { FetchType, OnSuccessType } from '../../typing';
import type { SchemaFormProps } from '../schema-form';

export type DynamicFormConfigType = {
  onGet?: FetchType;
  onBeforeCreate?: FetchType;
  onCreate?: FetchType;
  onAfterCreate?: FetchType;
  onBeforeUpdate?: FetchType;
  onUpdate?: FetchType;
  onSuccess?: OnSuccessType;
  form?: Omit<SchemaFormProps, 'supplementingTheString' | 'columns'> & {
    transformFinishValues?: { from: string; to: string; format?: string }[];
    transformInitialValues?: { from: string; to: string; format?: string }[];
    supplementingTheString?: 'false' | 'true' | boolean;
    list?: SchemaFormProps['columns'];
    columns?: SchemaFormProps['columns'];
  };
};
