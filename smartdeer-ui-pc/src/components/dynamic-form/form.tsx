import React, { useState } from 'react'
import classNames from 'classnames'
import { FormProps, message } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons'
import Loading from '../loading'
import { ConfigContext } from '../config-provider'
import swrFetcher from '../../utils/swrFetcher'
import type { SchemaFormProps, SchemaFormRef } from '../schema-form'
import SchemaForm from '../schema-form'
import type { FormColumnsType } from '../schema-form/typing'
import { getEffectFetchConfig } from '../../utils/getEffectFetchConfig'
import { useParamsDict, useSuccess } from '../../utils/hooks'
import type { FetchType, OnSuccessType } from '../../typing'
import type { DynamicFormConfigType } from './typing'
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate'
import { replaceReferences } from '../../utils/replaceReferences'
import { isArray, isBoolean } from 'lodash'
import { transformDataBasedOnRules } from '../../utils/transformDataBasedOnRules'
import { omitUndefined } from '../../utils/omitUndefined'
import { evaluateLogicalExpression } from '../../utils/evaluateLogicalExpression'

import './form.less'

export interface DynamicFormProps {
  className?: string
  style?: React.CSSProperties

  title?: string
  serviceAbility?: boolean,
  conf?: {
    language?: string;
    version?: string;
    formKey?: string;

    confKey?: string;
    areaCode?: string;
    productCode?: string;
    confType?: '1' | '2' | '3' | '4' | '5';  //1:负责人 2:模版 3:规则 4:常量 5:事件
  }
  confKey?: string
  confObject?: DynamicFormConfigType
  type?: 'add' | 'edit'
  back?: boolean
  onBack?: () => void
  onCrudTableRetry?: () => void

  middleware?: SchemaFormProps['middleware'],
  formVariables?: SchemaFormProps['variables'],

  onBeforeCreate?: (values: Record<string, any>) => Promise<Record<string, any>>
  footer?: (submitButton: React.ReactNode, restButton: React.ReactNode) => React.ReactNode;
  formProps?: FormProps
  insertNodes?: Array<{
    component: React.ReactNode
    index?: number  // 索引，用于插入到表单中的位置, 默认插入到最后
  }>
  transformSubmitData?: (data: Record<string, any>) => Record<string, any>
}

const classPrefix = `deer-dynamic-form`

const DynamicForm: React.FC<DynamicFormProps> = (props) => {
  const {
    className,
    style,

    title,
    serviceAbility,
    conf,
    confKey,
    confObject,
    middleware,
    back = true,
    onBack,
    onCrudTableRetry,
    formVariables,
    onBeforeCreate,
    footer,
    formProps: extraFormProps,
    insertNodes,
    ...restProps
  } = props

  const configContext = React.useContext(ConfigContext)
  const language = configContext.language.dynamicForm

  const formRef = React.useRef<SchemaFormRef>(null)

  const paramsDict = useParamsDict(formVariables)

  const handleReset = () => {
    formRef.current?.resetFields()
  }

  const handleRetry = () => {
    configContext.router?.go(0)
  }

  const onSuccess = useSuccess({
    onReset: handleReset,
    onRetry: handleRetry,
  })

  const [isLoading, setIsLoading] = React.useState(true)

  const [schemaFormColumns, setSchemaFormColumns] = React.useState<FormColumnsType[]>([])
  const [initialValues, setInitialValues] = React.useState({})

  const [submitLoading, setSubmitLoading] = React.useState(false)

  const [supplementingTheString, setSupplementingTheString] = React.useState(false)

  const formConfRef = React.useRef<DynamicFormConfigType>()
  const formConfGetRef = React.useRef<FetchType>()
  const formConfUpdateRef = React.useRef<FetchType>()
  const formConfBeforeCreateRef = React.useRef<FetchType | FetchType[]>()
  const formConfCreateRef = React.useRef<FetchType>()
  const formConfAfterCreateRef = React.useRef<FetchType>()
  const formConfSuccessRef = React.useRef<OnSuccessType>()

  const [formProps, setFormProps] = useState<Record<string, any>>()

  const isHandleAdd = restProps.type === 'add'

  const getFormConf = async () => {
    if (confObject) {
      return confObject
    }

    try {
      if (conf) {
        const api = replaceVariablesInTemplate(paramsDict, configContext.appFunctionApi!)

        let functionKey = configContext.formatterFormByKey
        let params: Record<string, any> = {
          formKey: conf.formKey,
          language: conf.language || 'gl',
          version: conf.version || '1',
        }

        if (serviceAbility) {
          functionKey = configContext.serviceAbilityKey
          params = {
            ...conf
          }
        }

        const { data } = await swrFetcher(api!, 'POST', {
          functionKey,
          params
        })

        const { page, ...rest } = JSON.parse(data.rs)

        const newPage = replaceReferences(page, rest)

        return newPage

      } else {
        const { data } = await swrFetcher('/v1/common/dict/conf/formatted', 'POST', {
          key: confKey,
          language: 'gl',
          confType: '0',
          version: '1',
        })

        return JSON.parse(data.json)
      }

    } catch (err) {
      message.error(`[smartdeer-ui: DynamicForm Form Conf 配置错误！`)
    }
  }

  const getFormValues = async (fetchConfig?: FetchType) => {
    if (isHandleAdd) {
      return {}
    }

    if (!fetchConfig) {
      message.error(`[smartdeer-ui: DynamicForm] onGet 解析错误！`)
      return
    }

    const { api, method, params, dataIndex } = getEffectFetchConfig(fetchConfig, configContext, paramsDict)

    const { data } = await swrFetcher(api, method, params)

    return dataIndex ? data[dataIndex] : data
  }

  /**
   * 数据处理
   * @param values 数据源
   * @param rules 数据处理集
   */
  // function transformValues(values: Record<string, any>, rules: any[]) {
  //   let tValues = { ...values }
  //   rules?.forEach((item: any) => {
  //     if (item.format) {
  //       const method = pipe.getFilter(item.format)

  //       tValues = method(tValues, item.from, item.to)
  //     } else {
  //       if (values[item.from]) {
  //         tValues[item.to] = values[item.from]
  //       }
  //     }
  //   })
  //   return tValues
  // }

  const fetch = async () => {
    setIsLoading(true)

    if (!formConfRef.current) {
      formConfRef.current = await getFormConf()
    }

    if (!formConfRef.current) {
      return
    }

    const columns = formConfRef.current?.form?.list || formConfRef.current?.form?.columns

    setSchemaFormColumns(columns || [])
    setFormProps(formConfRef.current?.form)

    const newSupplementingTheString = isBoolean(formConfRef.current?.form?.supplementingTheString) ? formConfRef.current?.form?.supplementingTheString : formConfRef.current?.form?.supplementingTheString !== 'false'

    setSupplementingTheString(newSupplementingTheString)

    formConfGetRef.current = formConfRef.current?.onGet
    formConfBeforeCreateRef.current = formConfRef.current?.onBeforeCreate
    formConfCreateRef.current = formConfRef.current?.onCreate
    formConfAfterCreateRef.current = formConfRef.current?.onAfterCreate

    formConfUpdateRef.current = formConfRef.current?.onUpdate
    formConfSuccessRef.current = formConfRef.current?.onSuccess

    const formValues = await getFormValues(formConfGetRef.current)

    const newFormValues = !isHandleAdd && formConfRef.current?.form?.transformInitialValues
      ? transformDataBasedOnRules(formValues, formConfRef.current?.form?.transformInitialValues)
      : formValues

    setInitialValues({
      ...formConfRef.current?.form?.initialValues,
      ...newFormValues,
    })

    setIsLoading(false)
  }

  React.useEffect(() => {
    fetch()
  }, [])

  const handleClickBack = () => {
    if (onBack) {
      onBack()
    } else {
      configContext.router?.back()
    }
  }

  const handleBeforeCreate = async (conf: FetchType, values: Record<string, any>) => {
    if (!conf) {
      message.error(`[smartdeer-ui: DynamicForm] formConfBeforeCreateRef 解析错误！`)
      return
    }

    if (conf.if) {
      const is = evaluateLogicalExpression({ ...paramsDict, ...values }, conf.if)

      if (!is) return
    }

    const { api, method, params, dataIndex } = getEffectFetchConfig(conf, configContext, { ...paramsDict, ...values }, {})

    const { data } = await swrFetcher(api, method, params)

    let result

    if (dataIndex) {
      result = data[dataIndex]
    } else {
      result = data
    }

    try {
      result = JSON.parse(result);
    } catch (e) {
    }

    if (conf.transform) {
      result = transformDataBasedOnRules(result, conf.transform);
    }

    return result;
  }

  const handleCreate = async (values: Record<string, any>) => {
    if (!formConfCreateRef.current) {
      message.error(`[smartdeer-ui: DynamicForm] onCreate 解析错误！`)
      return
    }

    const { api, method, params } = getEffectFetchConfig(formConfCreateRef.current, configContext, paramsDict, values)

    const res = await swrFetcher(api, method, params)

    if (formConfAfterCreateRef.current) {
      return res.data
    }

    if (formConfSuccessRef.current) {
      await onSuccess(formConfSuccessRef.current, res.data)
    } else {
      message.success(language.messageCreateSuccess)
      if (back) handleClickBack()
    }
  }

  const handleAfterCreate = async (values: Record<string, any>) => {
    if (!formConfAfterCreateRef.current) {
      return
    }

    let newValues = values

    if (formConfAfterCreateRef.current.transformParams) {
      newValues = transformDataBasedOnRules(newValues, formConfAfterCreateRef.current.transformParams)
    }

    const { api, method, params } = getEffectFetchConfig(formConfAfterCreateRef.current, configContext, paramsDict, newValues)

    const res = await swrFetcher(api, method, params)
    if (formConfSuccessRef.current) {
      await onSuccess(formConfSuccessRef.current, res.data)
    } else {
      message.success(language.messageCreateSuccess)
      if (back) handleClickBack()
    }
  }

  const handleUpdate = async (values: Record<string, any>) => {
    if (!formConfUpdateRef.current) {
      message.error(`[smartdeer-ui: DynamicForm] onUpdate 解析错误！`)
      return
    }

    const { api, method, params } = getEffectFetchConfig(formConfUpdateRef.current, configContext, paramsDict, values)

    await swrFetcher(api, method, params)

    if (formConfSuccessRef.current) {
      onSuccess(formConfSuccessRef.current)
    } else {
      message.success(language.messageUpdateSuccess)
      if (back) handleClickBack()
    }
  }

  const onFinish = async (values: Record<string, any>) => {

    // console.log('onFinish', values)

    // return

    setSubmitLoading(true)

    try {

      let newValues = omitUndefined(values)

      if (formConfBeforeCreateRef.current) {
        const formBeforeCreateList = isArray(formConfBeforeCreateRef.current) ? formConfBeforeCreateRef.current : [formConfBeforeCreateRef.current]

        const promiseList = formBeforeCreateList.map(item => handleBeforeCreate(item, newValues))

        const extraValues = await Promise.all(promiseList)

        try {
          extraValues.filter(item => { return !!item }).forEach((item) => {
            newValues = { ...item, ...newValues }
          })
        } catch (err) {
          console.error(err)
        }
      }

      if (onBeforeCreate) {
        newValues = await onBeforeCreate(newValues)
      }

      if (formConfRef.current?.form?.transformFinishValues) {
        newValues = transformDataBasedOnRules(newValues, formConfRef.current?.form?.transformFinishValues)
      }

      if (restProps?.transformSubmitData) {
        newValues = restProps.transformSubmitData(newValues);
      }

      newValues = omitUndefined(newValues)

      // console.log('newValues', newValues)

      // return

      if (isHandleAdd) {
        const res = await handleCreate(newValues as Record<string, any>)

        handleAfterCreate({ ...newValues, ...res })
      } else {
        await handleUpdate(newValues as Record<string, any>)
      }

      onCrudTableRetry?.()
    } catch (err: any) {
      message.error((err as Error).message)
    }

    setSubmitLoading(false)
  }

  if (isLoading) {
    return <Loading />
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      {!!title && (
        <h3 className={`${classPrefix}-title`}>
          {back && (
            <ArrowLeftOutlined
              onClick={handleClickBack}
              className={`${classPrefix}-back`}
            />
          )}

          <span className={`${classPrefix}-title-text`}>{title}</span>
        </h3>
      )}

      <div className={`${classPrefix}-content`}>
        <SchemaForm
          ref={formRef}
          {...formProps}
          footer={footer as any}
          formProps={extraFormProps}
          columns={schemaFormColumns}
          supplementingTheString={supplementingTheString}
          initialValues={initialValues}
          submitLoading={submitLoading}
          middleware={middleware}
          onFinish={onFinish}
          variables={formVariables}
          insertNodes={insertNodes}
        />
      </div>
    </div>
  )
}

export default DynamicForm
