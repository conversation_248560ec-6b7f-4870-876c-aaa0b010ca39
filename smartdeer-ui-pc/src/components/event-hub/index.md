---
toc: content
group: 
  title: 事件中心
  order: 0
---

# EventHub 事件中心

用于管理组件内部的事件的系统。

<!-- ## 示例

<code src="./demos/index.tsx"></code> -->

## API

### Form 事件

| 事件名称 | 说明 | 参数 |  版本 |
| --- | --- | --- | --- |
| `field:form-submit` | `Form` 提交 |  | |
| `field:form-steps-to` | `Steps` 跳转到指定步骤	 | `(index: string)`  | |
| `field:form-steps-back` | `Steps` 回退	|  | | 
| `field:form-steps-next` | `Steps` 下一步	|  | |
| `field:form-steps-submit` | `Steps` 提交	|  | |

## FAQ
### field 是什么？
field 是组件的唯一标识，在组件内部的事件中会用到。
