import React from 'react'
import { useDrag, DragPreviewImage } from 'react-dnd'
import { isFunction } from 'lodash';

type RecordType = Record<string, any>

type ChildrenType = React.ReactNode | ((collectedProps: RecordType) => React.ReactNode) | null;

interface DragProps {
  className?: string;
  style?: React.CSSProperties;
  dragPreviewImageSrc?: string;
  type: string;
  item?: RecordType;
  children: ChildrenType;
}

export const classPrefix = `deer-erag`;

const Drag: React.FC<DragProps> = (props) => {
  const {
    className,
    style,
    dragPreviewImageSrc,
    type,
    item,
    children
  } = props

  const [collectedProps, drag, dragPreview] = useDrag(() => ({
    type,
    item,
    collect: monitor => ({
      isDragging: !!monitor.isDragging(),
    }),
  }))

  return (
    <>
      {dragPreviewImageSrc && (
        <DragPreviewImage connect={dragPreview} src={dragPreviewImageSrc} />
      )}

      <div
        ref={drag}
        className={`${classPrefix} ${className}`}
        style={{
          opacity: collectedProps.isDragging ? 0.5 : 1,
          ...style
        }}
      >
        {isFunction(children) ? children(collectedProps) : children}
      </div>
    </>
  )

}

export default Drag
