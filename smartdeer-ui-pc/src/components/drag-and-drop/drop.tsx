import React from 'react'
import { useDrop } from 'react-dnd'
import { mergeProps } from '../../utils/withDefaultProps'
import { isFunction } from 'lodash';

type RecordType = Record<string, any>

type ChildrenType = React.ReactNode | ((collectedProps: RecordType) => React.ReactNode) | null;

interface DragProps {
  className?: string
  style?: React.CSSProperties;
  accept: string | string[];
  item?: RecordType;
  children: ChildrenType;
  canMove?: (...args: any) => boolean;
  onMove?: (...args: any) => void;
  onHover?: (...args: any) => void;
}

export const classPrefix = `deer-drop`;

const defaultProps = {
  canMove: () => true,
}

const Drop: React.FC<DragProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,
    accept,
    canMove,
    onMove,
    onHover,
    children
  } = props

  const [collectedProps, drop] = useDrop(() => ({
    accept,
    canDrop: (...args: any) => canMove(...args),
    drop: (...args: any) => onMove?.(...args),
    hover: (...args: any) => onHover?.(...args),
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop(),
      item: monitor.getItem()
    }),
  }), [])

  return (
    <div
      ref={drop}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {isFunction(children) ? children(collectedProps) : children}
    </div>
  )

}

export default Drop
