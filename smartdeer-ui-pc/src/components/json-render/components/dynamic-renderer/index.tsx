import { isObject, isString } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { EffectType } from '../../../../typing';
import { useJsonFetcher } from '../../hooks';
import { renderNode } from '../../render';

export default function DynamicRenderer({
  config,
  context = {},
  dataSource = {},
  effect,
  hooks = {},
}: {
  config?: string;
  dataSource?: Record<string, any>;
  context?: any;
  effect?: EffectType;
  hooks?: {
    onMounted?: () => void;
    onUpdated?: (...args: any[]) => void;
  };
}) {
  const [json, setJson] = useState<any>({});
  const [isPageInit, setIsPageInit] = useState(false);

  const { jsonFetcher } = useJsonFetcher();

  const fetchConf = async () => {
    let conf;

    if (effect) {
      try {
        const res = await jsonFetcher({
          ...effect,
        });
        conf = res.rs.json;
      } catch (e) {
        console.log('error', e);
      }
    } else if (config) {
      conf = config;
    }

    try {
      conf = isString(conf) ? JSON.parse(conf) : conf;
      if (conf?.components) {
        conf = conf.components[0];
        setJson(conf);
      }
    } catch (e) {
      console.log('error', e);
    }
  };

  useEffect(() => {
    if (!effect && !config) return;
    fetchConf();
  }, [effect, config]);

  const data = useMemo(() => {
    if (isObject(dataSource)) {
      return dataSource;
    }
    if (isString(dataSource)) {
      return JSON.parse(dataSource);
    }
  }, [dataSource]);

  useEffect(() => {
    if (!hooks?.onMounted || isPageInit) return;
    hooks?.onMounted();
    setIsPageInit(true);
  }, [hooks?.onMounted, isPageInit]);

  useEffect(() => {
    if (!hooks?.onUpdated) return;
    hooks?.onUpdated(dataSource);
  }, [hooks?.onUpdated, dataSource]);

  if (!config && !effect) return null;

  return renderNode(json as any, { ...context, ...data });
}
