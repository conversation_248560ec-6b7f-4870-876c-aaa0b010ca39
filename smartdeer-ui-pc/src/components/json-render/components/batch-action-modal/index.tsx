import { DeleteOutlined } from '@ant-design/icons';
import { List, Modal, Statistic, Tag, type ListProps } from 'antd';

// 批量操作结果展示弹窗：上传文件结果、批量发布撤回结果等
const BatchActionModal = (props: {
  open: boolean;
  title: string;
  description: string;
  dataSource: any[];
  okText: string;
  cancelText: string;
  successCount: number;
  failCount: number;
  showDeleteIcon: boolean;
  pendingText: string;
  onModalOk: () => void;
  onModalCancel: () => void;
  onDeleteListItem: (...args: any) => void;
}) => {
  const {
    open,
    title = '',
    description = '',
    dataSource = [],
    okText = '确定',
    cancelText = '取消',
    successCount = 0,
    failCount = 0,
    showDeleteIcon = false,
    pendingText = '',
    onModalOk,
    onModalCancel,
    onDeleteListItem,
    ...rest
  } = props;

  const renderListItem: ListProps<any>['renderItem'] = (item) => (
    <List.Item>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
        }}
      >
        <div>
          <span>{item.name}</span>
          <Tag
            color={
              item.status === 'success'
                ? 'green'
                : item.status === 'error'
                  ? 'red'
                  : '#ccc'
            }
            style={{ marginLeft: 10 }}
          >
            {item.status === 'success'
              ? '成功'
              : item.status === 'error'
                ? `失败：${item.reason || ''}`
                : pendingText}
          </Tag>
        </div>
        <div
          style={{
            cursor: 'pointer',
            color: '#f12f2f',
            display: showDeleteIcon ? 'inline-block' : 'none',
          }}
          onClick={() => {
            onDeleteListItem(item);
          }}
        >
          <DeleteOutlined />
        </div>
      </div>
    </List.Item>
  );

  return (
    <Modal
      title={title}
      open={open}
      onCancel={onModalCancel}
      onOk={onModalOk}
      width={800}
      okText={okText}
      cancelText={cancelText}
      {...rest}
    >
      <div>
        <p style={{ fontSize: '14px', fontWeight: '500' }}>{description}</p>

        <List dataSource={dataSource} renderItem={renderListItem} />

        <div style={{ display: 'flex', gap: '24px', marginTop: '16px' }}>
          <Statistic
            title="成功"
            value={successCount}
            valueStyle={{ color: '#52c41a' }}
          />
          <Statistic
            title="失败"
            value={failCount}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </div>
      </div>
    </Modal>
  );
};

export default BatchActionModal;
