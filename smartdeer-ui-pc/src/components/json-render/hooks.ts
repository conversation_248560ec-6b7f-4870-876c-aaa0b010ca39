import { Modal, message } from 'antd';
import dayjs from 'dayjs';
import { get, isArray, isNil, isNaN } from 'lodash';
import React, { useEffect, useReducer, useRef, useState } from 'react';
import { getEffectFetchConfig } from '../../utils/getEffectFetchConfig';
import swrFetcher from '../../utils/swrFetcher';
import { ConfigContext } from '../config-provider';
import { DynamicFunction } from './typing';
import { renderTemplateString } from './version2/utils/tools';

export const useFetcher = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const {
    useUrlParams,
    appRiverEntityFunction,
    appFunctionApi,
    appProcessApi,
    appRiverEntityProcess,
  } = React.useContext(ConfigContext);
  const urlParams = useUrlParams();
  const pathname = window.location.pathname;

  const isRiver = pathname.includes('/bm/');

  const fetcher = async (effectConfig: any) => {
    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(
        effectConfig,
        {
          appFunctionApi: isRiver ? appRiverEntityFunction : appFunctionApi,
          appProcessApi: isRiver ? appRiverEntityProcess : appProcessApi,
        } as any,
        {
          entityUUID: urlParams.entityUUID || '0',
          entityId: urlParams.entityId || '0',
        },
      );

      const { data } = await swrFetcher(api, method, params);

      let newData = dataIndex ? data[dataIndex] : data;

      return newData;
    } catch (e: any) {
      message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    fetcher,
    loading,
  };
};

export const useJsonFetcher = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const {
    useUrlParams,
    appRiverEntityFunction,
    appFunctionApi,
    appProcessApi,
    appRiverEntityProcess,
  } = React.useContext(ConfigContext);
  const urlParams = useUrlParams();
  const pathname = window.location.pathname;
  const isRiver = pathname.includes('/bm');
  const jsonFetcher = async (effectConfig: any) => {
    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(
        effectConfig,
        {
          appFunctionApi: isRiver ? appRiverEntityFunction : appFunctionApi,
          appProcessApi: isRiver ? appRiverEntityProcess : appProcessApi,
        } as any,
        {
          entityUUID: urlParams.entityUUID || '0',
          entityId: urlParams.entityId || '0',
        },
      );

      const { data } = await swrFetcher(api, method, params);

      let newData = dataIndex ? data[dataIndex] : data;

      return newData;
    } catch (e: any) {
      throw new Error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    jsonFetcher,
    loading,
  };
};

const fixFunctionFormat = (funcStr: string) => {
  let func = funcStr.trim();
  func = func.replace(/\s+/g, ' ');
  func = func.replace(/async\s+\(/g, 'async(');
  func = func.replace(/;\s*$/, '');
  return func;
};

// 动态函数执行器
export const useDynamicFunctions = (
  functions?: DynamicFunction[],
  context = {},
) => {
  const funcsRef = useRef<Record<string, (...args: any) => any>>({});
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const { router, useUrlParams } = React.useContext(ConfigContext);
  const urlParams = useUrlParams();

  // 创建安全沙箱
  const createSandbox = (
    code: string,
    functions: Record<string, (...args: any) => any>,
    dependencies: Record<string, any>,
    extraData: Record<string, any>,
    state: Record<string, any>,
  ) => {
    // const allowed = new Set([
    //   'setTableData', 'setPagination', 'setModalOpen', 'setModalData',
    //   'fetcher', 'form', 'pagination', 'modalData', 'funcs', 'renderComponent'
    // ]);
    // 替换 *{key} 的内容
    const replacePlaceholders = (code: string) => {
      if (!code) return '';
      return code.replace(/\*\{([^}]+)\}/g, (_, key) => {
        const value = get(context, key);
        return value !== undefined ? value : `*{${key}}`;
      });
    };
    const codeWithReplacedPlaceholders = replacePlaceholders(code);
    const codeString = fixFunctionFormat(codeWithReplacedPlaceholders);

    const sandbox = new Proxy(
      {
        ...context,
        dependencies,
        extraData,
        state,
        funcs: functions,
        log: console.log,
        dayjs: dayjs,
        Object: Object,
        message: message,
        history: router,
        entityUUID: urlParams.entityUUID,
        entityId: urlParams.entityId,
        urlParams: urlParams,
        JSON: JSON,
        Modal: Modal,
        localStorage: localStorage,
        Promise: Promise,
        isArray: isArray,
        isNil: isNil,
        isNaN: isNaN,
        renderTemplateString,
      },
      {
        get(target, prop) {
          // if (!allowed.has(prop as string)) throw new Error(`禁止访问 ${String(prop)}`);
          return target[prop as keyof typeof target];
        },
        has: () => true,
      },
    );

    try {
      return new Function(
        'sandbox',
        `
        with(sandbox) {
          return (${codeString});
        }
      `,
      )(sandbox);
    } catch (error) {
      console.error('函数编译失败:', error, '编译失败的函数:', codeString);
      return () => {};
    }
  };

  useEffect(() => {
    if (!functions?.length) return;

    const compiled = functions.reduce((acc, fn) => {
      acc[fn.name] = createSandbox(
        fn.code,
        acc,
        context.dependencies,
        context.extraData,
        context.state,
      );
      return acc;
    }, {} as Record<string, (...args: any) => any>);

    funcsRef.current = compiled;
    forceUpdate();

    return () => {
      Object.keys(compiled).forEach((k) => delete compiled[k]);
    };
  }, [
    functions,
    JSON.stringify(context.dependencies),
    JSON.stringify(context.extraData),
    JSON.stringify(context.state),
  ]);

  return { funcs: funcsRef.current };
};
