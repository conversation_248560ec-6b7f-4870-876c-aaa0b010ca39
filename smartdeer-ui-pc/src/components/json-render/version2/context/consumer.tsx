/**
 * JsonRenderer上下文消费者
 * 用于获取和使用上下文
 *
 * @module Context/Consumer
 */

import React, { useContext } from 'react';
import { JsonRendererContext, JsonRendererContextType } from './provider';

/**
 * 上下文消费者组件属性
 */
interface JsonRendererConsumerProps {
  children: (context: JsonRendererContextType) => React.ReactNode;
}

/**
 * JsonRenderer上下文消费者组件
 * 用于在函数组件外部访问上下文
 */
export const JsonRendererConsumer: React.FC<JsonRendererConsumerProps> = ({
  children
}) => {
  return (
    <JsonRendererContext.Consumer>
      {context => children(context)}
    </JsonRendererContext.Consumer>
  );
};

/**
 * 使用JsonRenderer上下文的钩子函数
 * 用于在函数组件内部访问上下文
 */
export const useJsonRenderer = (): JsonRendererContextType => {
  const context = useContext(JsonRendererContext);

  if (!context) {
    throw new Error('useJsonRenderer must be used within a JsonRendererProvider');
  }

  return context;
};

/**
 * 获取特定上下文数据的钩子函数
 * @param selector 选择器函数，从上下文中提取所需数据
 */
export function useJsonRendererSelector<T>(
  selector: (context: JsonRendererContextType) => T
): T {
  const context = useContext(JsonRendererContext);

  if (!context) {
    throw new Error('useJsonRendererSelector must be used within a JsonRendererProvider');
  }

  return selector(context);
}

/**
 * 获取状态的钩子函数
 */
export const useJsonRendererState = () => {
  const { state, setState } = useContext(JsonRendererContext);
  return [state, setState] as const;
};

/**
 * 获取额外数据的钩子函数
 */
export const useJsonRendererExtraData = () => {
  const { extraData, setExtraData } = useContext(JsonRendererContext);
  return [extraData, setExtraData] as const;
};

/**
 * 获取表格数据的钩子函数
 */
export const useJsonRendererTable = () => {
  const {
    tableData = [],
    setTableData = () => {},
    pagination = {},
    setPagination = () => {},
    filterParams = {},
    setFilterParams = () => {},
    handleTableSearch = () => {},
    handleTableChange = () => {},
  } = useContext(JsonRendererContext);

  return {
    tableData,
    setTableData,
    pagination,
    setPagination,
    filterParams,
    setFilterParams,
    handleTableSearch,
    handleTableChange,
  };
};

/**
 * 获取方法的钩子函数
 */
export const useJsonRendererMethods = () => {
  const { methods = {} } = useContext(JsonRendererContext);
  return methods;
};

export default {
  JsonRendererConsumer,
  useJsonRenderer,
  useJsonRendererSelector,
  useJsonRendererState,
  useJsonRendererExtraData,
  useJsonRendererTable,
  useJsonRendererMethods,
};
