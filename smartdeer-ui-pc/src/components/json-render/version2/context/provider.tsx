/**
 * JsonRenderer上下文提供者
 * 提供全局共享状态和方法
 *
 * @module Context/Provider
 */

import React, { createContext } from 'react';
import { PerformanceMonitor } from '../utils/performance';

/**
 * JsonRenderer上下文接口
 */
export interface JsonRendererContextType {
  // 核心状态
  state: Record<string, any>;
  setState: (data: any, replace?: boolean) => void;

  // 表单相关
  form?: any;

  // 表格相关
  tableData?: any[];
  setTableData?: (data: any[]) => void;
  pagination?: Record<string, any>;
  setPagination?: (data: any) => void;
  filterParams?: Record<string, any>;
  setFilterParams?: (data: any) => void;
  handleTableSearch?: (params: any) => void;
  handleTableChange?: (pagination: any, filters: any, sorter: any) => void;

  // 依赖数据
  dependencies?: Record<string, any>;
  setDependencies?: (data: any) => void;

  // API调用
  fetcher?: (config: any) => Promise<any>;

  // 方法映射
  methods?: Record<string, (...args: any[]) => any>;

  // 作用域
  scope?: any;
  createChildScope?: (name: string) => any;

  // 工具函数
  renderComponent?: (node: any, params: any) => React.ReactNode;

  // 性能监控
  perfMonitor?: PerformanceMonitor;

  // 其他
  [key: string]: any;
}

/**
 * 创建JsonRenderer上下文
 */
export const JsonRendererContext = createContext<JsonRendererContextType>({
  state: {},
  setState: () => {},
  methods: {},
});

/**
 * JsonRenderer上下文提供者
 */
export const JsonRendererProvider: React.FC<{
  value: JsonRendererContextType;
  children: React.ReactNode;
}> = ({ value, children }) => {
  return (
    <JsonRendererContext.Provider value={value}>
      {children}
    </JsonRendererContext.Provider>
  );
};

export default { JsonRendererContext, JsonRendererProvider };
