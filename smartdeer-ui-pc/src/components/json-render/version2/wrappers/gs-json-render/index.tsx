import { ConfigContext, JsonRender } from '@smartdeer-ui/pc';
import { Skeleton, Spin, message } from 'antd';
import { isArray } from 'lodash';
import React, { useEffect, useState } from 'react';
import { FetchType } from '../../../../../typing';
import { useParamsDict } from '../../../../../utils/hooks';
import { replaceVariablesInTemplate } from '../../../../../utils/replaceVariablesInTemplate';
import { cachedSwrFetcher } from '../../../../../utils/swrFetcher';
import { ConfType } from '../../../typing';
import { convertFunctionToString, resolveRefConfig } from '../../../utils';
import { useCacheFetcher } from '../../hooks/useFetcher';

interface ConfRefObjectType {
  conf: {
    components: any[];
    events: any[];
    [key: string]: any;
  };
  confMap: Record<string, any>;
}

interface JsonRenderResponse {
  json: string;
  innerScript: string;
}

interface GsJsonRenderProps {
  /** 页面配置标识 */
  confKey?: string;
  confKeyType?: string;
  /** 组件props */
  props?: Record<string, any>;
  /** 获取配置的接口 */
  confEffect?: FetchType;
  /** 配置对象 */
  confObject?: ConfType;
  /** 配置含有引用的对象，会对引用进行替换 */
  confRefObject?: ConfRefObjectType;
  /** 本地组件 */
  components?: Record<string, React.ComponentType>;
  /** 内置组件：builtInComponents 不会通过json render渲染 */
  builtInComponents?: Record<string, (props: any) => React.ReactNode>;
  /** 常量 */
  consts?: Record<string, any>;
  /** 配置加载完成回调 */
  onLoaded?: () => void;

  loading?: {
    className?: string;
    style?: React.CSSProperties;
    show: boolean
  }
  /** 初始状态 */
  initialState?: Record<string, any>;
}

const processedEvents = (events: any[]) => {
  if (!events) {
    return [];
  }
  return events.map((item: any) => {
    if (item.code && typeof item.code === 'function') {
      return {
        ...item,
        code: convertFunctionToString(item.code),
      };
    }
    return item;
  });
};

// 缓存内置组件
let buildIns: Record<string, (props: any) => React.ReactNode> = {};

/**
 * GsJsonRender - GS Json Render Wrapper
 *
 * 功能说明：
 * 1. 支持远程配置和本地mock配置的智能页面渲染
 * 2. 支持传入配置对象渲染
 * 3. 统一注入业务自定义组件
 * 4. 处理页面配置的获取、解析和状态管理
 *
 * @param confKey - 页面配置标识
 * @param props - 组件props
 * @param confEffect - 获取配置的接口
 * @param confObject - 配置对象
 * @param confRefObject - 配置含有引用的对象，会对引用进行替换
 */
const GsJsonRender: React.FC<GsJsonRenderProps> = (p) => {

  const {
    confKey,
    confKeyType,
    loading,
    props,
    confEffect,
    confObject,
    confRefObject,
    components = {},
    consts = {},
    builtInComponents = {},
    onLoaded,
    ...restProps
  } = p


  const { cacheFetcher, catchFetchLoading } = useCacheFetcher();
  const [json, setJson] = useState<string>('');
  const [events, setEvents] = useState<any[]>([]);
  const pathname = window.location.pathname;
  const isRiver = pathname.includes('/bm');

  buildIns = {
    ...buildIns,
    ...builtInComponents,
  };

  const gsComponents = confKey ? buildIns[confKey] : undefined;

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict({});
  let { appFunctionApi, appRiverEntityFunction } = configContext;
  appFunctionApi = replaceVariablesInTemplate(paramsDict, appFunctionApi!);
  appRiverEntityFunction = replaceVariablesInTemplate(
    paramsDict,
    appRiverEntityFunction!,
  );
  appFunctionApi = isRiver ? appRiverEntityFunction : appFunctionApi;

  const [initialState, setInitialState] = useState<Record<string, any>>(restProps.initialState || {})

  const fetchTableConf = async () => {

    const params: Record<string, any> = {
      key: confKey,
    }

    if (paramsDict.entityUUID) {
      params.namespace = paramsDict.entityUUID
    }

    const res = await cacheFetcher({
      type: 'function',
      functionKey: 'x_gs_memory_get_content',
      defaultParams: params,
    })
    const { content } = res.json.jsonRenderData
    setJson(content.json);
    setEvents(content.innerScripts)
    if (content.initialState) {
      setInitialState(values => ({ ...values, ...JSON.parse(content.initialState) }))
    }
  }

  const fetchProductConf = async () => {
    try {
      const { data } = await cachedSwrFetcher(appFunctionApi, 'POST', {
        functionKey: 'x_gs_get_service_ability_smart_page_conf',
        params: {
          areaCode: 'all',
          productCode: paramsDict.productCode,
          confKey: confKey,
          confType: '6',
        },
      });

      const { json, innerScript } = data?.rs || {};
      const script = JSON.parse(innerScript || '[]');
      setJson(json);
      setEvents(script);
    } catch (error) {
      message.error(`获取配置失败: ${confKey}`);
      setJson('');
      setEvents([]);
    }
  };

  const fetchConfEffect = async () => {
    try {
      const { rs } = (await cacheFetcher({
        ...confEffect,
      })) as { rs: JsonRenderResponse };
      const { json, innerScript } = rs;
      const script = JSON.parse(innerScript);
      setJson(json);
      setEvents(script);
    } catch (error) {
      message.error(`获取配置失败: ${confEffect?.functionKey}`);
      setJson('');
      setEvents([]);
    }
  };

  useEffect(() => {
    // 自定义组件优先使用
    if (!!gsComponents) {
      return;
    }
    // 使用配置对象(可用于本地调试)
    if (confObject) {
      if (!confObject.components) {
        // message.error('配置对象中缺少components');
        return;
      }
      if (confObject.events && !isArray(confObject.events)) {
        // message.error('配置对象中events必须是数组格式');
        return;
      }
      setJson(JSON.stringify({ components: confObject.components }));
      setEvents(
        confObject.events?.length ? processedEvents(confObject.events) : [],
      );
      if (confObject.initialState) {
        setInitialState(values => ({ ...values, ...confObject.initialState }))
      }
      return;
    }
    // 如果存在confRefObject，解析confRefObject，可用于本地调试，使用场景是复杂的含有ref引用的配置
    if (confRefObject) {
      const { conf, confMap } = confRefObject;
      const refConfig = resolveRefConfig(conf, confMap);
      setJson(refConfig.json);
      setEvents(refConfig.events);
      // 下方打印结果可以直接粘贴到智能页面 mock 中生成远程配置
      console.log({
        events: refConfig.events,
        components: JSON.parse(refConfig.json).components,
      });
      return;
    }

    // 如果存在confKey，获取智能页面配置
    if (confKey) {
      if (confKeyType && confKeyType === 'table') {
        fetchTableConf();
      } else {
        fetchProductConf();
      }
      return;
    }

    // 如果存在confEffect，根据confEffect获取配置
    if (confEffect) {
      fetchConfEffect();
      return;
    }

  }, [confKey, confObject, confEffect, confRefObject, !!gsComponents]);

  // 如果存在自定义组件，优先使用，不会再使用json render渲染
  if (gsComponents) {
    return gsComponents(props);
  }

  if (catchFetchLoading && loading?.show) {
    return <div className={`w-full ${loading?.className || ''}`} style={loading?.style}><Skeleton /></div>
  }

  if (!json || json === '{}') {
    return null;
  }

  if (!catchFetchLoading && confEffect && onLoaded) {
    onLoaded();
  }

  return (
    <JsonRender
      version="2"
      json={json}
      events={events || []}
      customComponents={{
        // 组件本身
        GsJsonRender: GsJsonRender,
        // 一些公共的业务组件和自定义组件
        customComponents: {} as any,
        // 通过参数传入的组件
        ...components,
      }}
      consts={consts}
      props={props}
      initialState={initialState}
    />
  );
};

export default GsJsonRender;
