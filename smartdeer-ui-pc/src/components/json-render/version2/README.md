# JsonRenderer

JsonRenderer 是一个高性能、可配置的 JSON 转 React 渲染引擎，支持动态渲染、生命周期管理、作用域隔离、按需加载等特性。

## 特性

- **声明式配置**: 通过JSON描述界面，无需编写React代码
- **组件生命周期**: 支持完整的组件生命周期钩子，类似Vue
- **作用域管理**: 组件间状态隔离与共享机制
- **按需加载**: 组件懒加载，优化性能
- **远程配置**: 支持从远程加载配置
- **性能监控**: 内置性能追踪和指标收集
- **调试友好**: 开发模式下详细的错误信息和状态追踪

## 安装

```bash
npm install @smartdeer-ui/json-renderer
```

## 基本用法

```jsx
import { JsonRenderer } from '@smartdeer-ui/json-renderer';

// JSON配置
const config = {
  components: [
    {
      component: 'Card',
      children: [
        {
          component: 'h3',
          props: {
            style: { fontSize: '20px' }
          },
          children: '标题'
        },
        {
          component: 'Button',
          props: {
            type: 'primary',
            onClick: 'fn:handleClick'
          },
          children: '点击我'
        }
      ]
    }
  ],
  events: [
    {
      name: 'handleClick',
      code: () => {
        alert('按钮被点击了!');
      }
    }
  ]
};

function App() {
  return (
    <JsonRenderer 
      json={JSON.stringify(config)} 
      options={{ 
        enablePerformance: true,
        showErrors: true
      }}
    />
  );
}
```

## 生命周期

每个组件配置可以包含生命周期钩子：

```json
{
  "component": "Card",
  "lifecycle": {
    "beforeCreate": "fn:handleBeforeCreate",
    "created": "fn:handleCreated",
    "mounted": "fn:handleMounted",
    "beforeUnmount": "fn:handleBeforeUnmount"
  }
}
```

## 作用域管理

```json
{
  "component": "Form",
  "scope": {
    "isolate": true,
    "expose": ["formData", "validate"]
  },
  "children": [...]
}
```

## 高级配置

```jsx
<JsonRenderer
  json={config}
  customComponents={{
    MyCustomComponent: MyCustomComponent
  }}
  options={{
    remote: true,
    remoteUrl: 'https://api.example.com/config',
    cache: 'session',
    debug: true
  }}
/>
```

## API文档

详细API文档请参考 [JsonRenderer API](https://docs.smartdeer.com/json-renderer/api)

## 调试工具

在开发环境下启用调试工具：

```jsx
import { enableDebugMode } from '@smartdeer-ui/json-renderer';

// 启用调试模式
enableDebugMode({ 
  logLevel: 'verbose',
  devTools: true
});
```
