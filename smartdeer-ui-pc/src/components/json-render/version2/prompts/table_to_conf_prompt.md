帮我把这个表格按照下面的要求改为json格式，这个json是用来渲染react+antd组件的，所以你需要完全遵守antd的参数定义，也可以使用html原生标签来描述页面。

表格相关规则如下：
1. 表格中每一行可以看做是一个json节点；
2. 每一行数据中，id是唯一标识，你可以当做下面的key，id不是一定存在，但如果一个节点的其他节点的父节点，那么这个节点一定存在id；
3. parentId指向父节点，如果节点A的parentId等于节点B的id，那么节点A就存在于节点B的children数组中。
4. children可能是字符串，也可能是一个节点，如果是节点，需要你根据id和parentId的关系去生成children。

每个json节点的ts定义如下：

```
type ComponentConfig = {
  component: string;
  props?: Record<string, any>;
  children?: ComponentConfig[] | string | number;
  key?: string | number;
};
```
注意: 每个节点中，children属性和props属性是同级，不是嵌套关系。

返回的json格式如下：

```
{
  "components": [],
  "events": [],
}
```

注意，在返回的json中：

1. 如果存在函数，用"fn:"开头的字符串代替，例如：`fn:handleSearch`。请注意，不要在fn后面加任何js逻辑，必须以"fn+方法名"的格式来描述全部事件。
2. 如果存在状态变量，用`*{}`表示，例如：`*{modalOpen}`；
3. 如果有图标，用"icon:"开头的字符串显示，例如：`icon:SearchOutlined`。
4. 把所有的fn都放到events数组中，code的值可以是一个函数。例如："events": [{ "name": "handleSearch", "code": ()=>{console.log('search')} }]。code里面只能使用ES5语法，不能使用扩展运算符(...)语法以及async await。
5. code里面的每一个方法的最后一个参数是context，context中有state和setState，你可以用来获取和更新数据。页面上所有的动态数据的展示和更新都来自state和setState。
6. 注意，code里面不能使用html标签或者react组件，需要用符合antd或者html规则的json来描述页面，比如Table的columns的render方法：
  - 这是错误写法，不要有<Button>标签。
  ```
    {
      name: 'renderAction',
      code: "(_, record) => <Button type='link' onClick={() => handleTableAction('edit', record)}>编辑</Button>",
    }
  ```
  - 这是正确写法
  ```
    {
      name: 'renderAction',
      code: (_, record) => return renderComponent({
        component: 'div',
        children: [
          {
            component: 'Button',
            props: {
              type: 'link',
              onClick: () => handleTableAction('edit', record),
            },
            children: '编辑',
          },
        ],
      }),
    }
  ```
7. 在Table的columns中，render属性的值必须是一个"fn+方法名"的字符串格式，例如："render": "fn:renderColumn"。
8. 务必返回标准的json格式，或者JavaScript对象格式。
9. 在components中，你可以使用 context.state 中的任意变量，注意要用`*{}`包裹，如果是表达式，需要用`{{}}`来包裹。是否渲染的逻辑用`vif:"*{变量}"`或者`vif:{{*{变量} ? true : false}}`


下面的变量和函数名你可以直接在events的函数中使用，也可以另外添加：

```
    fetcher,
    renderComponent,
    events, // 当前events数组中的函数
    mergeObjects, // 合并对象工具方法，代替扩展运算符(...)
    renderTime, // 时间格式化
```


