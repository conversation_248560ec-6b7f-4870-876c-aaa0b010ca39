帮我把这段代码按照下面的要求改为json格式，这个json是用来渲染react+antd组件的，所以你需要完全遵守antd的参数定义，也可以使用html原生标签来描述页面。
如果代码中有自定义组件，请将它转为antd组件的配置。

每个json节点的ts定义如下：

```
type ComponentConfig = {
  component: string;
  props?: Record<string, any>;
  children?: ComponentConfig[] | string | number;
  key?: string | number;
};
```
注意: 每个节点中，children属性和props属性是同级，不是嵌套关系。

返回的json格式如下：

```
{
  "components": [],
  "events": [],
}
```

注意，在返回的json中：

1. 如果存在函数，用"fn:"开头的字符串代替，例如：`fn:handleSearch`。请注意，不要在fn后面加任何js逻辑，必须以"fn+方法名"的格式来描述全部事件。
2. 如果存在状态变量，用`*{}`表示，例如：`*{modalOpen}`；
3. 如果有图标，用"icon:"开头的字符串显示，例如：`icon:SearchOutlined`。
4. 把所有的fn都放到events数组中，code的值可以是一个函数。例如："events": [{ "name": "handleSearch", "code": ()=>{console.log('search')} }]。code里面只能使用ES5语法，不能使用扩展运算符(...)语法以及async await。
5. code里面的每一个方法的最后一个参数是context，context中有state和setState，你可以用来获取和更新数据。页面上所有的动态数据的展示和更新都来自state和setState。
6. 注意，code里面不能使用html标签或者react组件，需要用符合antd或者html规则的json来描述页面，比如Table的columns的render方法：
  - 这是错误写法，不要有<Button>标签。
  ```
    {
      name: 'renderAction',
      code: "(_, record) => <Button type='link' onClick={() => handleTableAction('edit', record)}>编辑</Button>",
    }
  ```
  - 这是正确写法
  ```
    {
      name: 'renderAction',
      code: (_, record) => return renderComponent({
        component: 'div',
        children: [
          {
            component: 'Button',
            props: {
              type: 'link',
              onClick: () => handleTableAction('edit', record),
            },
            children: '编辑',
          },
        ],
      }),
    }
  ```
7. 在Table的columns中，render属性的值必须是一个"fn+方法名"的字符串格式，例如："render": "fn:renderColumn"。
8. 务必返回标准的json格式，或者JavaScript对象格式。
9. 在components中，你可以使用 context.state 中的任意变量，注意要用`*{}`包裹，如果是表达式，需要用`{{}}`来包裹。是否渲染的逻辑用`vif:"*{变量}"`或者`vif:{{*{变量} ? true : false}}`


下面的变量和函数名你可以直接在events的函数中使用，也可以另外添加：

```
    fetcher,
    renderComponent,
    events, // 当前events数组中的函数
    mergeObjects, // 合并对象工具方法，代替扩展运算符(...)
    renderTime, // 时间格式化
```


下面是一个完整的列表页面的例子：

```
<!-- index.tsx -->
import { AuditDetail } from '@/components/app';
import * as customComponents from '@/components/custom';
import { JsonRender } from '@smartdeer-ui/pc';
import { resolveRefConfig } from '@smartdeer-ui/pc/components/json-render/utils';
import conf from './conf';
import DetailView from './detail';
import EditView from './edit';
import tableConf from './table-conf';
import tableSearchConf from './table-search-conf';

const Page: React.FC = () => {
  const config = resolveConfig(conf as any, {
    tableSearchConf,
    tableConf,
  });

  if (!config?.json) return null;

  return (
    <JsonRender
      version="2"
      json={config.json}
      events={config.events}
      customComponents={{
        ...customComponents,
        DetailView,
        EditView,
        AuditDetail,
      }}
      consts={{
        dealTypes,
      }}
    />
  );
};

export default Page;


```

```
// @ts-nocheck
<!-- conf.ts -->
export default {
  components: [
    {
      component: 'Spin',
      props: {
        spinning: '*{initLoading}',
        style: {
          textAlign: 'center',
          marginTop: '100px',
          width: '100%',
        },
      },
    },
    {
      component: 'Card',
      props: {
        vif: '*{initLoading} === false',
      },
      children: [
        {
          component: 'div',
          props: {
            className: 'flex items-center justify-end',
          },
          children: [
            {
              component: 'PermissionButton',
              props: {
                type: 'primary',
                icon: 'icon:PlusOutlined',
                onClick: 'fn:handleCreate',
                role: 'bfm.deal.edit',
              },
              children: '新建EOR产品',
            },
          ],
        },
        {
          component: 'Card',
          props: {
            style: { marginTop: '20px' },
          },
          children: [
            {
              $ref: 'eorListTableSearch',
            },
          ],
        },
        {
          component: 'div',
          props: {
            style: { marginTop: '10px' },
          },
          children: [
            {
              $ref: 'eorListTable',
            },
          ],
        },
        {
          component: 'Drawer',
          props: {
            title: 'Deal产品详情',
            width: '80%',
            open: '*{detailDrawerData.open}',
            onClose: 'fn:handleCloseDrawer',
            destroyOnClose: true,
          },
          children: [
            {
              component: 'div',
              props: {
                className: 'flex items-center justify-end',
                style: {
                  position: 'absolute',
                  top: '12px',
                  right: '10px',
                  zIndex: 10,
                },
              },
              children: [
                {
                  component: 'Button',
                  props: {
                    type: 'link',
                    icon: 'icon:HistoryOutlined',
                    onClick: 'fn:handleViewLogs',
                    className: 'text-orange-500 hover:text-orange-600',
                  },
                  children: '查看日志',
                },
                {
                  component: 'div',
                  props: {
                    vif: '*{detailDrawerData.record.status} === 0',
                  },
                  children: [
                    {
                      component: 'Button',
                      props: {
                        vif: '*{detailDrawerData.status} !== "edit"',
                        type: 'primary',
                        icon: 'icon:EditOutlined',
                        loading: '*{detailDrawerData.editLoading}',
                        onClick: 'fn:handleEditClick',
                      },
                      children: '编辑',
                    },
                    {
                      component: 'Button',
                      props: {
                        vif: '*{detailDrawerData.status} === "edit"',
                        type: 'primary',
                        onClick: 'fn:handleCancelEdit',
                      },
                      children: '取消编辑',
                    },
                  ],
                },
              ],
            },
            {
              component: 'div',
              props: {
                vif: '*{detailDrawerData.status} === "view"',
              },
              children: [
                {
                  component: 'BfmJsonRender',
                  props: {
                    confKey: 'bfm.product.detail',
                    productCode: '*{consts.productCode}',
                    props: {
                      id: '*{detailDrawerData.id}',
                      onDataFetched: 'fn:handleDataFetched',
                    },
                  },
                },
              ],
            },
            {
              component: 'div',
              props: {
                vif: '*{detailDrawerData.status} === "edit"',
              },
              children: [
                {
                  component: 'BfmJsonRender',
                  props: {
                    confKey: 'bfm.product.edit',
                    productCode: '*{consts.productCode}',
                    props: {
                      id: '*{detailDrawerData.id}',
                      onSuccess: 'fn:handleEditSuccess',
                    },
                  },
                },
              ],
            },
          ],
        },
        {
          component: 'Drawer',
          props: {
            vif: '*{auditDetailStatus.open}',
            open: '*{auditDetailStatus.open}',
            placement: 'right',
            onClose: 'fn:handleAuditCancel',
            title: '审核进度',
            width: '80%',
          },
          children: [
            {
              component: 'BfmJsonRender',
              props: {
                confKey: 'bfm.product.audit.drawer',
                productCode: '*{consts.productCode}',
                props: {
                  open: '*{auditDetailStatus.open}',
                  getFunctionKey: 'x_bfm_deal_get_approve_info',
                  cancelFunctionKey: 'x_bfm_deal_cancel_approve',
                  onClose: 'fn:handleAuditClose',
                  id: '*{auditDetailStatus.record.id}',
                  onAuditCanceled: 'fn:handleAuditRejected',
                },
              },
            },
          ],
        },
      ],
    },
  ],
  events: [
    {
      name: 'fetchList',
      code: ({ pagination, filterParams }, context) => {
        context.setState({
          loading: true,
        });
        const searchItems = filterParams?.searchItems || [];
        searchItems.unshift({
          key: 'dealType',
          action: 'eq',
          value: '1',
        });
        fetcher({
          type: 'function',
          functionKey: 'search_bfm_deal_list',
          defaultParams: {
            current: pagination.current,
            size: pagination.pageSize,
            limit: pagination.pageSize,
            searchInfo: {
              searchItems,
              orders: [
                {
                  key: 'id',
                  asc: 'desc',
                },
              ],
            },
          },
        })
          .then((res) => {
            if (!res || !res.dataInfo) return;
            context.setState({
              tableData: res.dataInfo,
              pagination: Object.assign({}, pagination, {
                total: res.total,
                current: pagination.current,
                pageSize: pagination.pageSize,
              }),
            });
          })
          .catch((error) => {
            message.error(error?.message || 'error');
          })
          .finally(() => {
            context.setState({
              loading: false,
            });
          });
      },
    },
    {
      name: 'onInitialize',
      code: (context) => {
        context.setState({
          initLoading: true,
        });
        const productionId = localStorage.getItem('productionId');
        if (productionId) {
          localStorage.removeItem('productionId');
          context.setState({
            detailDrawerData: {
              open: true,
              id: Number(productionId),
              status: 'view',
            },
          });
        }

        fetcher({
          type: 'function',
          functionKey: 'search_bfm_vendor_list',
          defaultParams: {
            current: 1,
            limit: 500,
            size: 500,
            searchInfo: {
              searchItems: [],
              orders: [],
            },
          },
        })
          .then((data) => {
            let vendorList = data.dataInfo.map((item: any) => {
              return {
                value: `${item.id}`,
                label: item.name,
              };
            });
            vendorList.unshift({ value: '-1', label: '自营' });
            context.setState({
              vendorListOptions: vendorList,
              initLoading: false,
            });
          })
          .catch((error) => {
            message.error(error?.message || 'error');
            context.setState({
              initLoading: false,
            });
          });

        const pagination = {
          current: 1,
          pageSize: 10,
          total: 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number) => `共 ${total} 条`,
        };
        const filterParams = {};

        context.setState({
          pagination,
          filterParams,
        });
        events.fetchList(
          {
            pagination,
            filterParams,
          },
          context,
        );
      },
    },
    {
      name: 'refetchList',
      code: (context) => {
        events.fetchList(
          {
            pagination: context.state.pagination,
            filterParams: context.state.filterParams,
          },
          context,
        );
      },
    },
    {
      name: 'handleDataFetched',
      code: (data, context) => {
        context.setState({
          detailDrawerData: mergeObjects(context.state.detailDrawerData, {
            record: data,
          }),
        });
      },
    },
    {
      name: 'handleEditSuccess',
      code: (context) => {
        events.refetchList(context);
        context.setState({
          detailDrawerData: {
            open: false,
          },
        });
      },
    },
    {
      name: 'handleViewLogs',
      code: (_, context) => {
        localStorage.setItem(
          'productionId',
          context.state.detailDrawerData.id
            ? `${context.state.detailDrawerData.id}`
            : '',
        );
        if (!context.state.detailDrawerData.id) return;
        history.push(
          `/operation-log/deal/${context.state.detailDrawerData.id}/list`,
        );
      },
    },
    {
      name: 'handleAuditCancel',
      code: (_, context) => {
        context.setState({
          auditDetailStatus: {
            open: false,
          },
        });
      },
    },
    {
      name: 'handleAuditClose',
      code: (context) => {
        context.setState({
          auditDetailStatus: {
            open: false,
          },
        });
      },
    },
    {
      name: 'handleAuditRejected',
      code: (context) => {
        events.refetchList(context);
      },
    },
    {
      name: 'handleCreate',
      code: () => {
        history.push('/prod-manage-new/new');
      },
    },

    {
      name: 'handleEditClick',
      code: (_, context) => {
        const record = context.state.detailDrawerData.record;
        const id = record.id;
        let isInApprove = record.auditStatus === 1;
        if (isInApprove) {
          Modal.warning({
            title: '提示',
            content:
              '您有一个正在审批中的任务，编辑后将撤回审批中的信息，本次修改信息需要重新进行审批，确认撤销审批吗',
            closable: true,
            okText: '撤销审批，并编辑',
            onOk: () => {
              fetcher({
                type: 'function',
                functionKey: 'x_bfm_deal_cancel_approve',
                defaultParams: { id },
              })
                .then((res) => {
                  if (res?.rs) {
                    context.setState({
                      detailDrawerData: mergeObjects(
                        context.state.detailDrawerData,
                        {
                          status: 'edit',
                        },
                      ),
                    });
                    events.refetchList(context);
                  } else {
                    message.error(res?.message);
                  }
                })
                .catch((err: any) => {
                  message.error(err?.message);
                });
            },
          });
        } else {
          context.setState({
            detailDrawerData: mergeObjects(context.state.detailDrawerData, {
              status: 'edit',
            }),
          });
        }
      },
    },
    {
      name: 'handleCloseDrawer',
      code: (_, context) => {
        context.setState({
          detailDrawerData: {
            open: false,
          },
        });
      },
    },
    {
      name: 'handleCancelEdit',
      code: (_, context) => {
        context.setState({
          detailDrawerData: mergeObjects(context.state.detailDrawerData, {
            status: 'view',
          }),
        });
      },
    },
  ],
};

```

```
// @ts-nocheck
<!-- table-conf.ts -->
export default {
  components: [
    {
      component: 'Table',
      props: {
        dataSource: '*{tableData}',
        rowKey: 'id',
        loading: '*{loading}',
        columns: [
          {
            title: '产品名称',
            dataIndex: 'name',
            fixed: 'left',
            width: 100,
          },
          {
            title: 'ID',
            dataIndex: 'id',
            width: 80,
          },
          {
            title: '产品类型',
            dataIndex: 'dealType',
            width: 100,
            render: 'fn:renderDealTypes',
          },
          {
            title: '产品状态',
            dataIndex: 'status',
            width: 100,
            render: 'fn:renderStatus',
          },
          {
            title: '审批状态',
            dataIndex: 'auditStatus',
            width: 150,
            render: 'fn:renderAuditStatus',
          },
          {
            title: '服务来源',
            dataIndex: 'serviceSource',
            width: 100,
            render: 'fn:renderServiceSource',
          },
          {
            title: '国家/地区',
            dataIndex: 'country',
            width: 150,
            render: 'fn:renderCountry',
          },
          {
            title: '修改时间',
            dataIndex: 'updateTime',
            width: 180,
            render: 'fn:renderTime',
          },
          {
            title: '操作人',
            dataIndex: 'operatorName',
            width: 120,
          },
          {
            title: '操作',
            key: 'action',
            fixed: 'right',
            width: 220,
            render: 'fn:renderAction',
          },
        ],
        scroll: { x: 600 },
        pagination: '*{pagination}',
        onChange: 'fn:handleTableChange',
      },
    },
  ],
  events: [
    {
      name: 'handleCancelApprove',
      code: (record, onSuccess) => {
        const id = record.id;
        const isEffective = record.contractStatus === 1;
        const isChangeApproving = isEffective;
        try {
          if (isChangeApproving) {
            fetcher({
              type: 'function',
              functionKey: 'x_bfm_contract_change_cancel_approve',
              defaultParams: {
                id,
              },
            }).then((res) => {
              if (res?.rs) {
                message.success('取消审批成功');
                onSuccess();
              } else {
                message.error(res?.message);
              }
            });
          } else {
            fetcher({
              type: 'function',
              functionKey: 'x_bfm_contract_cancel_approve',
              defaultParams: {
                id,
              },
            }).then((res) => {
              if (res?.rs) {
                message.success('取消审批成功');
                onSuccess();
              } else {
                message.error(res?.message);
              }
            });
          }
        } catch (err: any) {
          message.error('取消审批失败' + err.message);
        }
      },
    },
    {
      name: 'renderAction',
      code: (text, record, _, context) => {
        const canAudit =
          (record.auditStatus === 0 || record.auditStatus === 3) &&
          record.status === 0;
        const canOnline =
          record.auditStatus === 2 &&
          [0, 2].includes(record.status);
        const canOffline = record.auditStatus === 2 && record.status === 1;

        const canAddToPlatform = record.auditStatus === 2 && record.status === 1;
        return renderComponent(
          {
            component: 'div',
            props: {
              style: { display: 'flex', gap: '8px' },
            },
            children: [
              {
                component: 'Button',
                props: {
                  type: 'link',
                  onClick: 'fn:handleOpenDetailDrawer',
                  style: { padding: 0 },
                  size: 'small',
                },
                children: '查看',
              },
              {
                component: 'div',
                props: {
                  vif: canAudit,
                },
                children: [
                  {
                    component: 'Popconfirm',
                    props: {
                      title: '发起审批',
                      description: `ID ${record.id}: 确认需要 [发起审批] 吗？`,
                      onConfirm: 'fn:handleConfirmAudit',
                      okText: '是',
                      cancelText: '否',
                    },
                    children: [
                      {
                        component: 'Button',
                        props: {
                          type: 'link',
                          size: 'small',
                          style: { padding: 0 },
                        },
                        children: '发起审批',
                      },
                    ],
                  },
                ],
              },
              {
                component: 'div',
                props: {
                  vif: canOnline,
                },
                children: [
                  {
                    component: 'Popconfirm',
                    props: {
                      title: '上架产品',
                      description: `ID ${record.id}: 确认需要 [上架产品] 吗？`,
                      onConfirm: 'fn:handleConfirmOnline',
                      okText: '是',
                      cancelText: '否',
                    },
                    children: [
                      {
                        component: 'Button',
                        props: {
                          type: 'link',
                          size: 'small',
                        },
                        children: '上架',
                      },
                    ],
                  },
                ],
              },
              {
                component: 'div',
                props: {
                  vif: canOffline,
                },
                children: [
                  {
                    component: 'Popconfirm',
                    props: {
                      title: '下架产品',
                      description: `ID ${record.id}: 确认需要 [下架产品] 吗？`,
                      onConfirm: 'fn:handleConfirmOffline',
                      okText: '是',
                      cancelText: '否',
                    },
                    children: [
                      {
                        component: 'Button',
                        props: {
                          type: 'link',
                          size: 'small',
                        },
                        children: '下架',
                      },
                    ],
                  },
                ],
              },
              {
                component: 'div',
                props: {
                  vif: canAddToPlatform,
                },
                children: [
                  {
                    component: 'Popconfirm',
                    props: {
                      title: '上架产品到平台',
                      description: `ID ${record.id}: 确认需要 [上架产品到平台] 吗？`,
                      onConfirm: 'fn:handleConfirmAddToPlatform',
                      okText: '是',
                      cancelText: '否',
                    },
                    children: [
                      {
                        component: 'Button',
                        props: {
                          type: 'link',
                          size: 'small',
                        },
                        children: '上架到平台',
                      },
                    ],
                  },
                ],
              },
              
            ],
          },
          {
            methods: {
              handleOpenDetailDrawer: () => {
                if (!record?.id) return;
                context.setState({
                  detailDrawerData: {
                    open: true,
                    id: record.id,
                    status: 'view',
                    record: record,
                  },
                });
              },
              handleConfirmAudit: () => {
                fetcher({
                  type: 'function',
                  functionKey: 'x_bfm_deal_start_approve',
                  defaultParams: { id: record.id },
                })
                  .then((res) => {
                    if (res?.rs) {
                      message.success(`${record.name} 成功发起审批`);
                      events.refetchList(context);
                    } else {
                      message.error(res?.message);
                    }
                  })
                  .catch((err: any) => {
                    message.error(err?.message);
                  });
              },
              handleConfirmOnline: () => {
                fetcher({
                  type: 'function',
                  functionKey: 'x_bfm_deal_online',
                  defaultParams: { id: record.id },
                })
                  .then((res) => {
                    if (res?.rs) {
                      message.success(`${record.name} 成功上架`);
                      events.refetchList(context);
                    } else {
                      message.error(res?.message);
                    }
                  })
                  .catch((err: any) => {
                    message.error(err?.message);
                  });
              },
              handleConfirmOffline: () => {
                fetcher({
                  type: 'function',
                  functionKey: 'x_bfm_deal_offline',
                  defaultParams: { id: record.id },
                })
                  .then((res) => {
                    if (res?.rs) {
                      message.success(`${record.name} 成功下架`);
                      events.refetchList(context);
                    } else {
                      message.error(res?.message);
                    }
                  })
                  .catch((err: any) => {
                    message.error(err?.message);
                  });
              },
              handleConfirmAddToPlatform: () => {
                fetcher({
                  type: 'function',
                  functionKey: 'x_bfm_deal_online_to_platform',
                  defaultParams: { id: record.id },
                })
                  .then((res) => {
                    if (res?.rs) {
                      message.success(`${record.name} 成功上架到平台`);
                      events.refetchList(context);
                    } else {
                      message.error(res?.message);
                    }
                  })
                  .catch((err: any) => {
                    message.error(err?.message);
                  });
              },
            },
          },
        );
      },
    },
    {
      name: 'renderDealTypes',
      code: (text) => {
        const typeMap = {
          1: 'EOR',
          2: 'Payroll',
          3: '工作签证',
          4: '其他一次性服务',
        };
        return typeMap[text] || '-';
      },
    },
    {
      name: 'renderAuditStatus',
      code: (text, record, _, context) => {
        const currentStatus = record.auditStatus;
        const statusMap = {
          0: { text: '待发起审批', color: 'default' },
          1: { text: '审核中', color: 'processing' },
          2: { text: '审核通过', color: 'success' },
          3: { text: '审核驳回', color: 'error' },
          4: { text: '审批取消', color: 'default' },
        };
        const conf = {
          component: 'Space',
          props: {
            size: 0,
            style: { display: 'flex', flexWrap: 'wrap', alignItems: 'center' },
          },
          children: [
            {
              component: 'Tag',
              props: {
                color: statusMap[currentStatus]?.color || '',
              },
              children: statusMap[currentStatus]?.text || '',
            },
            {
              component: 'Button',
              props: {
                type: 'link',
                size: 'small',
                style: { padding: 0 },
                onClick: 'fn:handleAuditProgressClick',
                vif: currentStatus === 1,
              },
              children: '查看进度',
            },
            {
              component: 'Button',
              props: {
                type: 'link',
                size: 'small',
                style: { padding: 0 },
                onClick: 'fn:handleAuditProgressClick',
                vif: currentStatus === 3,
              },
              children: '查看原因',
            },
          ],
        };

        return renderComponent(conf, {
          methods: {
            handleAuditProgressClick: () => {
              context.setState({
                auditDetailStatus: {
                  open: true,
                  record,
                  canAuditCancel: currentStatus === 1,
                },
                detailDrawerData: { open: false },
              });
            },
          },
        });
      },
    },
    {
      name: 'renderStatus',
      code: (text, record, _, context) => {
        const statusMap = {
          '0': {
            text: '未上架',
            color: 'gray',
          },
          '1': {
            text: '已上架',
            color: 'green',
          },
          '2': {
            text: '已下架',
            color: 'gray',
          },
        };
        const conf = {
          component: 'Space',
          props: {
            size: 0,
          },
          children: [
            {
              component: 'Tag',
              props: {
                color: statusMap[text]?.color || '',
              },
              children: statusMap[text]?.text || '',
            },
          ],
        };
        return renderComponent(conf, {});
      },
    },
    {
      name: 'renderTime',
      code: (text) => {
        if (!text) return '-';
        return dayjs(text).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      name: 'renderCountry',
      code: (text, record) => {
        const state = record.state === 'all' ? '' : record.state;
        return [record.countryNameZh, state].filter((x) => !!x).join('/');
      },
    },
    {
      name: 'renderServiceSource',
      code: (text, record) => {
        if (record.serviceSource === 1) {
          return '自营';
        }
        if (record.serviceSource === 2) {
          return record.vendorName ?? '--';
        }
        return '-';
      },
    },

    {
      name: 'handleTableChange',
      code: (pagination, filters, sorter, _, context) => {
        const newPagination = Object.assign({}, pagination, {
          current: pagination.current,
          pageSize: pagination.pageSize,
          showTotal: (total: number) => `共 ${total} 条`,
        });
        context.setState({
          pagination: newPagination,
        });
        events.fetchList(
          {
            pagination: newPagination,
            filterParams: context.state.filterParams,
          },
          context,
        );
      },
    },
  ],
};

```

```
// @ts-nocheck
<!-- table-search-conf.ts -->
export default {
  components: [
    {
      component: 'TableSearch',
      props: {
        downloadExcelRender: false,
        showSearchButton: true,
        onSearch: 'fn:handleTableSearch',
        onValuesChange: 'fn:handleTableSearchChange',
        columns: [
          {
            title: 'ID',
            type: 'input',
            field: 'id',
            action: { value: 'eq' },
            props: {
              placeholder: '请输入ID',
              allowClear: true,
            },
          },
          {
            title: '产品名称',
            type: 'input',
            field: 'name',
            action: {
              value: 'like_keyword',
            },
            props: {
              placeholder: '请输入产品名称',
              allowClear: true,
            },
          },
          {
            title: '服务来源',
            type: 'select',
            field: 'serviceSource',
            action: {
              value: 'eq',
            },
            props: {
              placeholder: '请选择服务来源',
              allowClear: true,
              showSearch: true,
              options: '*{state.vendorListOptions}',
            },
          },
          {
            type: 'select',
            field: 'countryCode',
            title: '国家',
            action: {
              value: 'eq',
            },
            props: {
              placeholder: '请选择国家',
              showSearch: 'true',
              fieldNames: {
                value: 'countryCode',
                label: 'countryNameZh',
              },
            },
            labelProps: {},
            effect: {
              fetch: {
                type: 'function',
                functionKey: 'x_bfm_area_country_list',
                defaultParams: {},
                data: 'rs',
              },
            },
          },
          {
            type: 'select',
            field: 'state',
            title: '地区',
            action: {
              value: 'eq',
            },
            props: {
              placeholder: '请选择地区',
              showSearch: 'true',
              fieldNames: {
                value: 'stateNameZh',
                label: 'stateNameZh',
              },
            },
            labelProps: {},
            dependencies: ['countryCode'],
            effect: {
              fetch: {
                if: '*{searchCountryCode}!==',
                type: 'function',
                functionKey: 'x_bfm_area_state_list',
                defaultParams: { countryCode: '*{searchCountryCode}' },
                data: 'rs',
              },
            },
          },
          {
            title: '审核状态',
            type: 'select',
            field: 'auditStatus',
            action: {
              value: 'eq',
            },
            props: {
              placeholder: '请选择状态',
              allowClear: true,
              showSearch: true,
              options: [
                {
                  label: '待发起审核',
                  value: '0',
                },
                {
                  label: '审核中',
                  value: '1',
                },
                {
                  label: '审核通过',
                  value: '2',
                },
                {
                  label: '审核驳回',
                  value: '3',
                },
                {
                  label: '审核取消',
                  value: '4',
                },
              ],
            },
          },
        ],
      },
    },
  ],
  events: [
    {
      name: 'handleTableSearchChange',
      code: (changedFields, allFields, context) => {
        if (changedFields.countryCode) {
          context.setState({
            searchCountryCode: changedFields.countryCode || 'all',
          });
        }
      },
    },
    {
      name: 'handleTableSearch',
      code: (newData, context) => {
        const newPagination = mergeObjects(context.state.pagination, {
          current: 1,
        });
        let searchItems = newData.searchItems;
        let vendorId;
        searchItems = searchItems.map((item: any) => {
          if (item.key === 'serviceSource') {
            if (item.value === '-1') {
              return mergeObjects(item, {
                value: '1',
              });
            } else {
              vendorId = item.value;
              return mergeObjects(item, {
                value: '2',
              });
            }
          }
          return item;
        });
        if (vendorId) {
          searchItems.push({
            key: 'vendorId',
            action: 'eq',
            value: Number(vendorId),
          });
        }
        context.setState({
          filterParams: {
            searchItems,
          },
          pagination: newPagination,
        });
        events.fetchList(
          {
            pagination: newPagination,
            filterParams: {
              searchItems,
            },
          },
          context,
        );
      },
    },
  ],
};

```



