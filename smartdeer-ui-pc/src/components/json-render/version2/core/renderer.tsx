/**
 * Renderer 组件
 * 负责将JSON配置转换为React组件树
 *
 * @module Core/Renderer
 */
import { isBoolean } from 'lodash';
import dayjs from 'dayjs';
import React, { Suspense, lazy, useContext, useEffect, useMemo } from 'react';
import { ErrorBoundary } from '../components/error-boundary';
import { JsonRendererContext } from '../context/provider';
import { ComponentConfig, RendererProps } from '../types';
import { evaluateExpression, resolvePathValue } from '../utils/evaluator';
import {
  getComponentByName,
  isValidComponent,
  loadedComponents,
} from '../utils/loader';
import { pipeFilters } from '../utils/pipe';

let cacheComponents: Record<string, React.ComponentType> = {};

// 使用模块模式避免循环引用
const RendererModule = (() => {
  // 声明模块级变量
  let renderNode: (
    node: ComponentConfig,
    context?: Record<string, any>,
    customComponents?: Record<string, React.ComponentType>,
    options?: Record<string, any>,
    level?: number, // 添加层级参数
    path?: string[], // 添加路径参数
  ) => React.ReactNode;

  // 处理变量引用结果
  const processReferenceResult = (
    result: any,
    context: Record<string, any>,
    processValue: (value: any) => any,
    level: number = 0, // 添加层级参数
    path: string[] = [], // 添加路径参数
  ): any => {
    // 处理结果是数组的情况
    if (Array.isArray(result)) {
      return result.map((item) => {
        if (typeof item === 'object' && item !== null) {
          // 如果数组元素是对象且包含 children，使用 renderNode 处理
          if ('component' in item) {
            return renderNode(
              item as ComponentConfig,
              context,
              context.customComponents || {},
              context.options || {},
              level, // 传递层级参数
              path, // 传递路径参数
            );
          }
          // 其他对象递归处理
          return processValue(item);
        }
        return item;
      });
    }

    // 处理结果是对象的情况
    if (typeof result === 'object' && result !== null) {
      // 如果对象是组件配置，使用 renderNode 处理
      if ('component' in result) {
        return renderNode(
          result as ComponentConfig,
          context,
          context.customComponents || {},
          context.options || {},
          level, // 传递层级参数
          path, // 传递路径参数
        );
      }
      // 如果对象包含 children
      if ('children' in result) {
        const processedResult = { ...result };
        if (Array.isArray(result.children)) {
          processedResult.children = result.children.map((child: any) => {
            if (
              typeof child === 'object' &&
              child !== null &&
              'component' in child
            ) {
              return renderNode(
                child as ComponentConfig,
                context,
                context.customComponents || {},
                context.options || {},
                level, // 传递层级参数
                path, // 传递路径参数
              );
            }
            return processValue(child);
          });
        } else if (typeof result.children === 'string') {
          processedResult.children = processValue(result.children);
        }
        return processedResult;
      }
      // 其他对象递归处理所有属性
      return processValue(result);
    }

    // 其他类型直接返回
    return result;
  };

  // 处理管道函数的公共逻辑
  const processPipeExpression = (
    value: string,
    context: Record<string, any>,
  ): any => {
    const pipeRegex = /^\*\{([^|]+)(\s*\|[^}]+)\}$/;
    const pipeMatch = value.match(pipeRegex);

    if (pipeMatch) {
      const [, path, pipeExpression] = pipeMatch;
      // 先获取路径对应的值
      let result = resolvePathValue(path.trim(), context);

      // 处理管道函数链，去掉开头的 | 符号
      const pipes = pipeExpression
        .trim()
        .slice(1)
        .split('|')
        .map((p) => p.trim());

      // 依次执行每个管道函数
      for (const pipe of pipes) {
        // 解析函数名和参数
        const [funcName, ...args] = pipe
          .split(/\s*\(\s*|\s*\)\s*/)
          .filter(Boolean);
        // 处理参数中的逗号分隔，并去除参数两端的引号
        const params = args.length
          ? args[0]
            .split(/\s*,\s*/)
            .map((arg) => arg.replace(/^['"]|['"]$/g, ''))
          : [];

        // 优先使用 pipeFilters 中的函数
        const filter = pipeFilters[funcName as keyof typeof pipeFilters];
        if (filter) {
          // 使用更具体的函数类型
          result = (filter as (value: any, ...args: any[]) => any)(
            result,
            ...params,
          );
        } else {
          // 如果 pipeFilters 中没有，尝试从 context.methods 中获取
          const method = context.methods?.[funcName];
          if (typeof method === 'function') {
            try {
              result = method(result, ...params, context);
            } catch (error) {
              console.error(
                `Error executing pipe function "${funcName}":`,
                error,
              );
            }
          } else {
            console.warn(
              `Pipe function "${funcName}" not found in both pipeFilters and context.methods`,
            );
          }
        }
      }

      return result;
    }

    return value;
  };

  /**
   * 处理Props属性
   */
  function processProps(
    props: Record<string, any>,
    context: Record<string, any>,
    level: number = 0, // 添加层级参数
    path: string[] = [], // 添加路径参数
  ) {
    if (props?.__scope__) {
      return props;
    }

    // 递归处理所有属性值
    const processValue = (value: any): any => {
      if (Array.isArray(value)) {
        return value.map((item) => processValue(item));
      }

      if (value && typeof value === 'object' && dayjs(value).isValid()) {
        return value
      }

      if (value && typeof value === 'object') {
        // 如果对象有 component 字段，将其作为组件渲染
        if ('component' in value) {
          return renderNode(
            value as ComponentConfig,
            context,
            context.customComponents || {},
            context.options || {},
            level, // 传递层级参数
            path, // 传递路径参数
          );
        }

        return Object.entries(value).reduce((acc, [k, v]) => {
          acc[k] = processValue(v);
          return acc;
        }, {} as Record<string, any>);
      }

      if (typeof value === 'string') {
        // 处理管道函数
        const result = processPipeExpression(value, context);
        if (result !== value) {
          return result;
        }

        // 处理变量引用: *{path}
        if (value.startsWith('*{') && value.endsWith('}')) {
          const pathStr = value.slice(2, -1).trim();
          const result = resolvePathValue(pathStr, context);
          return processReferenceResult(result, context, processValue, level, path); // 传递路径参数
        }

        // 处理表达式: {{expression}}
        if (value.startsWith('{{') && value.endsWith('}}')) {
          const expression = value.slice(2, -2).trim();
          return evaluateExpression(expression, context);
        }

        if (value.includes('*{') && !value.startsWith('fn:')) {
          return value.replace(/\*{(.*?)}/g, (_, pathStr) => {
            const resolvedValue = resolvePathValue(pathStr.trim(), context);
            return String(resolvedValue === undefined ? '' : resolvedValue);
          });
        }

        // 处理函数引用: fn:functionName 或 fn:functionName(args)
        if (value.startsWith('fn:')) {
          const [funcName, argsStr] = value.slice(3).split('(');
          const args = argsStr
            ? argsStr
              .slice(0, -1)
              .split(',')
              .map((arg) => {
                const trimmedArg = arg.trim();
                if (trimmedArg.startsWith('*{') && trimmedArg.endsWith('}')) {
                  const pathStr = trimmedArg.slice(2, -1).trim();
                  return resolvePathValue(pathStr, context);
                }
                return trimmedArg;
              })
            : [];

          if (context?.methods?.[funcName]) {
            return (...additionalArgs: any[]) => {
              return context?.methods?.[funcName]?.(
                ...args,
                ...additionalArgs,
                context,
              );
            };
          } else {
            return null;
          }
        }

        // 处理图标: icon:iconName
        if (value.startsWith('icon:')) {
          try {
            const IconComponent = loadedComponents[value.slice(5)];
            if (IconComponent) {
              return <IconComponent />;
            }
          } catch (e) {
            console.error('Icon parse error:', e);
          }
        }
      }

      return value;
    };

    return processValue(props);
  }

  /**
   * 获取组件
   */
  function getComponent(
    componentName: string,
    customComponents: Record<string, React.ComponentType>,
  ): React.ComponentType | string {
    // 1. 优先使用自定义组件
    if (customComponents[componentName] || cacheComponents[componentName]) {
      return customComponents[componentName] || cacheComponents[componentName];
    }

    // 2. 从预置组件中找
    if (
      componentName?.[0] === componentName?.[0]?.toUpperCase() &&
      loadedComponents[componentName]
    ) {
      return loadedComponents[componentName];
    }

    // 3. 作为原生html标签返回
    return componentName;
  }

  const LazyComponentLoader = ({
    componentName,
    props,
    children,
  }: {
    componentName: string;
    props: any;
    children: React.ReactNode;
  }) => {
    const Component = useMemo(
      () =>
        lazy(() =>
          getComponentByName(componentName).then((comp) => {
            if (!comp) {
              throw new Error(`组件 ${componentName} 未找到`);
            }
            return { default: comp };
          }),
        ),
      [componentName],
    );

    return (
      <Suspense fallback={<div className="loading-component">加载中...</div>}>
        <ErrorBoundary componentName={componentName}>
          <Component {...props}>{children}</Component>
        </ErrorBoundary>
      </Suspense>
    );
  };

  // 用于跟踪 afterRender 执行状态
  const afterRenderExecuted = new Set<string>();
  // 用于跟踪 beforeRender 执行状态
  const beforeRenderExecuted = new Set<string>();

  /**
   * 处理子节点
   */
  function processChildren(
    children: ComponentConfig['children'],
    context: Record<string, any>,
    customComponents: Record<string, React.ComponentType>,
    options: Record<string, any>,
    level: number = 0, // 添加层级参数
    path: string[] = [], // 添加路径参数
  ): React.ReactNode {
    if (Array.isArray(children)) {
      return children.map((child) =>
        renderNode(
          child as ComponentConfig,
          context,
          customComponents,
          options,
          level + 1, // 子节点层级加1
          path, // 传递路径参数
        ),
      );
    }

    if (typeof children === 'object' && children !== null) {
      return renderNode(
        children as ComponentConfig,
        context,
        customComponents,
        options,
        level + 1, // 子节点层级加1
        path, // 传递路径参数
      );
    }

    if (typeof children === 'string') {
      // 处理管道函数
      const result = processPipeExpression(children, context);
      if (result !== children) {
        return result;
      }

      // 处理模板字符串
      if (children.includes('*{')) {
        return children.replace(/\*{(.*?)}/g, (_, pathStr) => {
          const value = resolvePathValue(pathStr, context);
          return String(value === undefined ? '' : value);
        });
      }
      if (children.startsWith('fn:')) {
        const funcName = children.slice(3);
        const fn = (...args: any[]) =>
          context?.methods?.[funcName]?.(...args, context) || (() => { });
        return fn as unknown as React.ReactNode;
      }
    }

    return children;
  }

  /**
   * 渲染单个节点
   */
  renderNode = function (
    node: ComponentConfig,
    context: Record<string, any> = {},
    customComponents: Record<string, React.ComponentType> = {},
    options: Record<string, any> = {},
    level: number = 0, // 添加层级参数
    path: string[] = [], // 添加路径参数
  ): React.ReactNode {
    const { perfMonitor } = context;

    // 构建当前组件的完整路径
    const currentPath = [...path, node.component || 'Unknown'];
    const currentComponentName = node.component || 'Unknown';

    // 检查是否有 node.key，如果没有则打印警告
    if (!node.key) {
      const indent = '  '.repeat(level);
      const fullPath = currentPath.join(' > ');
      console.warn(
        `⚠️  组件缺少 key 属性\n` +
        `   组件名称: ${currentComponentName}\n` +
        `   组件层级: ${level}\n` +
        `   层级路径: ${indent}└─ ${currentComponentName}\n` +
        `   完整路径: ${fullPath}\n` +
        `   建议: 为组件添加唯一的 key 属性以提高渲染性能和避免潜在问题`
      );
    }

    const nodeId =
      node.key || `node-${Math.random().toString(36).substring(2, 11)}`;

    perfMonitor?.startMeasure(`render-node-${nodeId}`);

    try {
      const { component, props = {}, children, key, lifecycle = {} } = node;

      // 条件渲染处理
      if (props.vif !== undefined) {
        if (isBoolean(props.vif)) {
          props.vif = props.vif.toString();
        }
        const shouldRender = evaluateExpression(props.vif, context);
        if (!shouldRender) return null;
      }

      // 生命周期 - 渲染前
      if (
        lifecycle.beforeRender &&
        typeof lifecycle.beforeRender === 'string' &&
        context.methods?.[lifecycle.beforeRender]
      ) {
        const beforeRenderMethod = lifecycle.beforeRender as string;
        const beforeRenderKey = `${nodeId}-${beforeRenderMethod}`;

        if (!beforeRenderExecuted.has(beforeRenderKey)) {
          beforeRenderExecuted.add(beforeRenderKey);
          context.methods[beforeRenderMethod](props, context);
        }
      }

      // 处理属性
      const processedProps = processProps(props, context, level + 1, currentPath); // 传递路径参数

      if (processedProps?.vif !== undefined) {
        delete processedProps.vif;
      }

      // 处理子节点，传递层级信息和路径
      const processedChildren = processChildren(
        children,
        context,
        customComponents,
        options,
        level + 1, // 子节点层级加1
        currentPath, // 传递当前路径
      );

      // 获取组件
      const Component = getComponent(component, customComponents);

      // 生命周期 - 渲染后
      if (
        lifecycle.afterRender &&
        typeof lifecycle.afterRender === 'string' &&
        context.methods?.[lifecycle.afterRender]
      ) {
        const afterRenderMethod = lifecycle.afterRender as string;
        const afterRenderKey = `${nodeId}-${afterRenderMethod}`;

        if (!afterRenderExecuted.has(afterRenderKey)) {
          afterRenderExecuted.add(afterRenderKey);
          setTimeout(() => {
            context.methods[afterRenderMethod](processedProps, context);
          }, 0);
        }
      }

      // 渲染组件
      let renderedComponent = null;

      if (isValidComponent(Component)) {
        renderedComponent = (
          <Component {...processedProps} key={key || nodeId}>
            {processedChildren}
          </Component>
        );
      } else if (node.lazyLoad) {
        renderedComponent = (
          <LazyComponentLoader componentName={component} props={processedProps}>
            {processedChildren}
          </LazyComponentLoader>
        );
      }

      perfMonitor?.endMeasure(`render-node-${nodeId}`);
      return renderedComponent;
    } catch (error) {
      console.error(`Error rendering node:`, error, node);
      perfMonitor?.endMeasure(`render-node-${nodeId}`);

      return options.showErrors ? (
        <div className="renderer-error">
          <h4>渲染错误</h4>
          <pre>{String(error)}</pre>
        </div>
      ) : null;
    }
  };

  /**
   * 主渲染器组件
   */
  const Renderer: React.FC<RendererProps> = ({
    config,
    customComponents = {},
    options = {},
  }) => {
    cacheComponents = { ...cacheComponents, ...customComponents };
    const context = useContext(JsonRendererContext);
    const { perfMonitor } = context;

    useEffect(() => {
      perfMonitor?.markEvent('renderer-mounted');
      return () => {
        perfMonitor?.markEvent('renderer-unmounted');
      };
    }, [perfMonitor]);

    // 没有组件配置时显示空内容
    if (!config?.components || !Array.isArray(config.components)) {
      return options.showErrors ? (
        <div className="renderer-error">无效的配置</div>
      ) : null;
    }


    return (
      <>
        {config.components.map((component) =>
          renderNode(component, context, customComponents, options, 0, []),
        )}
      </>
    );
  };

  return {
    renderNode,
    Renderer,
  };
})();

export const renderNode = RendererModule.renderNode;
export const Renderer = RendererModule.Renderer;
export default RendererModule.Renderer;
