/**
 * <PERSON>son<PERSON><PERSON><PERSON> 核心引擎
 * 负责整个渲染过程和状态管理
 *
 * @module Core/Engine
 */

import { Form } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import { JsonRendererContext } from '../context/provider';
import { useDynamicFunctions } from '../hooks/useDynamicFunctions';
// import { useFetcher } from '../hooks/useFetcher';
import { ConfigContext } from '@smartdeer-ui/pc';
// import { DebugPanel } from '../components';
import { JsonRendererProps } from '../types';
import { parseConfiguration } from '../utils/parser';
import { initializePerformanceMonitor } from '../utils/performance';
import { Renderer } from './renderer';
import { addKeysToAllNodes } from '../utils/addKeysToAllNodes';

export const JsonRender: React.FC<JsonRendererProps> = ({
  json,
  events,
  methods,
  customComponents = {},
  options = {},
  consts = {},
  props = {},
  initialState = {},
}) => {
  // 初始化性能监控
  const perfMonitor = useMemo(
    () =>
      options.enablePerformance ? initializePerformanceMonitor() : undefined,
    [],
  );

  const { useUrlParams } = React.useContext(ConfigContext);
  const urlParams = useUrlParams();

  // 核心状态管理
  const [state, setState] = useState({ ...urlParams, ...initialState });

  // 表单和数据处理
  const [form] = Form.useForm();

  // 生命周期状态
  const [isInitialized, setIsInitialized] = useState(false);
  const [isRendered, setIsRendered] = useState(false);

  const prevPropsRef = useRef(props);
  const urlParamsRef = useRef(urlParams);

  // 处理 debugPanel 默认值：开发环境默认 true，生产环境默认 false
  // const debugPanelEnabled =
  //   typeof options.debugPanel === 'boolean'
  //     ? options.debugPanel
  //     : process.env.NODE_ENV !== 'production';

  // 上下文构建 - 所有可用的状态和函数
  const context = useMemo(() => {
    return {
      // 状态管理
      state,
      setState: (data: any, replace = false) => {
        setState((prev) => (replace ? data : { ...prev, ...data }));
      },
      form,
      consts,
      props,
    };
  }, [state, form, perfMonitor, consts, props]);

  // 动态函数处理
  const { funcs: dynamicFunctions } = useDynamicFunctions(
    events,
    context,
    consts,
    customComponents,
  );

  // 合并方法
  const combinedMethods = useMemo(() => {
    return {
      ...dynamicFunctions,
      ...methods,
    };
  }, [dynamicFunctions, methods]);

  // 配置解析
  const parsedConfig = useMemo(() => {
    try {
      perfMonitor?.startMeasure('config-parse');
      const config = parseConfiguration(json);

      const components = addKeysToAllNodes(config.components)

      perfMonitor?.endMeasure('config-parse');
      return {
        ...config,
        components
      };
    } catch (error) {
      console.error('Configuration parsing error:', error);
      return { components: [] };
    }
  }, [json, perfMonitor]);

  // 初始化生命周期
  useEffect(() => {
    if (isInitialized) return;

    perfMonitor?.startMeasure('component-initialize');

    // 调用初始化方法
    if (combinedMethods.onInitialize) {
      combinedMethods.onInitialize(context, combinedMethods);
      setIsInitialized(true);
    }

    perfMonitor?.endMeasure('component-initialize');

    // 组件卸载时清理
    return () => {
      if (combinedMethods.onDestroy) {
        combinedMethods.onDestroy(context, combinedMethods);
      }
      perfMonitor?.cleanup();
    };
  }, [combinedMethods, context, isInitialized, perfMonitor]);

  // 渲染完成生命周期
  useEffect(() => {
    if (!isInitialized || isRendered) return;

    perfMonitor?.markEvent('render-complete');

    if (combinedMethods.onRenderComplete) {
      combinedMethods.onRenderComplete(context);
      setIsRendered(true);
    }
  }, [combinedMethods, context, isInitialized, isRendered, perfMonitor]);

  useEffect(() => {
    // 跳过首次渲染，因为这时应该触发onInitialize
    if (!isInitialized && combinedMethods.onInitialize) return;

    // 检查props是否有实际变化
    const prevProps = prevPropsRef.current;
    const hasPropsChanged = JSON.stringify(prevProps) !== JSON.stringify(props);

    // 更新ref以便下次比较
    prevPropsRef.current = props;

    // 如果props变化且存在onPropsChange方法，则调用
    if (hasPropsChanged && combinedMethods.onPropsChange) {
      combinedMethods.onPropsChange(context, prevProps);
    }
  }, [props, isInitialized, combinedMethods, context]);


  useEffect(() => {
    const isEqual =
      JSON.stringify(urlParamsRef.current) === JSON.stringify(urlParams);
    if (!isEqual) {
      urlParamsRef.current = urlParams;
      setState((prev) => ({ ...prev, ...urlParams }));
      setIsInitialized(false)
    }
  }, [urlParams]);

  return (
    <JsonRendererContext.Provider
      value={{
        ...context,
        methods: combinedMethods,
        perfMonitor,
        form,
      }}
    >
      <Renderer
        config={parsedConfig}
        customComponents={customComponents}
        options={options}
      />
      {/* {debugPanelEnabled && <DebugPanel />} */}
    </JsonRendererContext.Provider>
  );
};

export default JsonRender;
