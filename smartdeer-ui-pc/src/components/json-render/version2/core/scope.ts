/**
 * 作用域管理
 * 管理组件之间的状态隔离和共享
 *
 * @module Core/Scope
 */

// 定义方法类型
type ScopeMethod = (...args: any[]) => any;

export interface Scope {
  id: string;
  name: string;
  parent: Scope | null;
  children: Scope[];
  state: Record<string, any>;
  methods: Record<string, ScopeMethod>;

  /** 获取或设置状态 */
  getState: (key?: string) => any;
  setState: (key: string | Record<string, any>, value?: any) => void;

  /** 注册或获取方法 */
  registerMethod: (name: string, method: ScopeMethod) => void;
  getMethod: (name: string) => ScopeMethod | undefined;

  /** 作用域管理 */
  createChild: (name: string) => Scope;
  getChild: (nameOrId: string) => Scope | undefined;
  getParent: () => Scope | null;
  getRoot: () => Scope;

  /** 状态继承与隔离 */
  isolate: () => void;
  inherit: (keys?: string[]) => void;
  expose: (keys: string[]) => void;
}

/**
 * 创建一个新的作用域
 */
export const createScope = (name = 'root', parent: Scope | null = null): Scope => {
  const id = `scope-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  const state: Record<string, any> = {};
  const methods: Record<string, ScopeMethod> = {};
  const children: Scope[] = [];
  let exposedKeys: string[] = [];
  let isolated = false;

  const scope: Scope = {
    id,
    name,
    parent,
    children,
    state,
    methods,

    // 状态管理
    getState: (key?: string) => {
      if (key === undefined) return state;

      // 如果当前作用域没有该状态，但未隔离，则查找父作用域
      if (!(key in state) && !isolated && parent) {
        return parent.getState(key);
      }

      return state[key];
    },

    setState: (key, value) => {
      if (typeof key === 'object') {
        Object.assign(state, key);
      } else {
        state[key] = value;
      }

      // 如果该状态是暴露的，同时更新父作用域
      if (parent && exposedKeys.includes(typeof key === 'string' ? key : '')) {
        parent.setState(key, value);
      }
    },

    // 方法管理
    registerMethod: (name, method) => {
      methods[name] = method;
    },

    getMethod: (name) => {
      // 如果当前作用域没有该方法，则查找父作用域
      if (!(name in methods) && parent) {
        return parent.getMethod(name);
      }

      return methods[name];
    },

    // 子作用域管理
    createChild: (childName) => {
      const child = createScope(`${name}.${childName}`, scope);
      children.push(child);
      return child;
    },

    getChild: (nameOrId) => {
      return children.find(child => child.name === nameOrId || child.id === nameOrId);
    },

    getParent: () => parent,

    getRoot: () => {
      let root: Scope = scope;
      while (root.parent) {
        root = root.parent;
      }
      return root;
    },

    // 状态隔离与继承
    isolate: () => {
      isolated = true;
    },

    inherit: (keys) => {
      if (!parent) return;

      if (keys) {
        // 继承指定的key
        keys.forEach(key => {
          state[key] = parent.getState(key);
        });
      } else {
        // 继承所有父作用域状态
        Object.assign(state, parent.getState());
      }
    },

    expose: (keys) => {
      if (!parent) return;
      exposedKeys = keys;

      // 将当前作用域中的这些状态暴露给父作用域
      keys.forEach(key => {
        if (key in state) {
          parent.setState(key, state[key]);
        }
      });
    }
  };

  return scope;
};

export default createScope;
