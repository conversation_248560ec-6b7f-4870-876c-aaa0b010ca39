/**
 * 生命周期管理
 * 提供组件级别的生命周期钩子实现
 *
 * @module Core/Lifecycle
 */

import { ComponentLifecycle } from '../types/lifecycle';

/**
 * 创建生命周期管理器
 */
export const createLifecycleManager = (
  lifecycle: Partial<ComponentLifecycle> = {},
  methods: Record<string, (...args: any[]) => any> = {}
) => {
  const callHook = (hookName: keyof ComponentLifecycle, ...args: any[]) => {
    const hook = lifecycle[hookName];
    if (!hook) return;

    if (typeof hook === 'string' && hook.startsWith('fn:')) {
      const methodName = hook.slice(3);
      if (methods[methodName] && typeof methods[methodName] === 'function') {
        return methods[methodName](...args);
      }
    } else if (typeof hook === 'function') {
      return hook(...args);
    }
  };

  return {
    // 组件初始化
    callBeforeCreate: (props: any, context: any) =>
      callHook('beforeCreate', props, context),

    callCreated: (instance: any) =>
      callHook('created', instance),

    // 组件挂载
    callBeforeMount: (instance: any) =>
      callHook('beforeMount', instance),

    callMounted: (instance: any, element: HTMLElement) =>
      callHook('mounted', instance, element),

    // 组件更新
    callBeforeUpdate: (oldProps: any, newProps: any) =>
      callHook('beforeUpdate', oldProps, newProps),

    callUpdated: (instance: any) =>
      callHook('updated', instance),

    // 组件卸载
    callBeforeUnmount: (instance: any) =>
      callHook('beforeUnmount', instance),

    callUnmounted: () =>
      callHook('unmounted'),

    // 组件激活/停用 (用于缓存场景)
    callActivated: (instance: any) =>
      callHook('activated', instance),

    callDeactivated: (instance: any) =>
      callHook('deactivated', instance),

    // 错误处理
    callErrorCaptured: (error: Error, instance: any, info: string) =>
      callHook('errorCaptured', error, instance, info),

    // 性能追踪
    callRenderTriggered: (event: any) =>
      callHook('renderTriggered', event),

    callRenderTracked: (event: any) =>
      callHook('renderTracked', event)
  };
};

export default createLifecycleManager;
