/**
 * 组件类型定义
 *
 * @module Types/Component
 */

import { ComponentLifecycle } from './lifecycle';

/**
 * 组件作用域配置
 */
export interface ComponentScope {
  /** 是否隔离状态 */
  isolate?: boolean;

  /** 父作用域 */
  parent?: string;

  /** 暴露给父组件的属性和方法 */
  expose?: string[];

  /** 从父作用域继承的属性 */
  inherit?: string[];
}

/**
 * 懒加载配置
 */
export interface LazyLoadOptions {
  /** 是否启用懒加载 */
  enabled?: boolean;

  /** 加载时的占位内容 */
  placeholder?: string;

  /** 加载完成回调 */
  onLoad?: string;

  /** 加载失败回调 */
  onError?: string;

  /** 预加载触发条件 */
  preload?: 'hover' | 'visible' | 'never';
}

/**
 * 基础组件配置接口
 */
export interface ComponentConfig {
  /** 组件名称，对应React组件或HTML标签 */
  component: string;

  /** 组件属性 */
  props?: Record<string, any>;

  /** 子组件或内容 */
  children?: ComponentConfig[] | ComponentConfig | string | number;

  /** 组件唯一标识，用于缓存和性能追踪 */
  key?: string | number;

  /** 生命周期钩子 */
  lifecycle?: Partial<ComponentLifecycle>;

  /** 作用域配置 */
  scope?: ComponentScope;

  /** 懒加载配置 */
  lazyLoad?: boolean | LazyLoadOptions;

  /** 条件渲染，值为假时不渲染 */
  vif?: string;

  /** 引用其他配置 */
  $ref?: string;

  /** 自定义属性，可用于扩展功能 */
  [key: string]: any;
}

/**
 * 动态渲染器配置
 */
export interface DynamicRendererConfig {
  /** 要渲染的配置 */
  config: {
    components: ComponentConfig[];
  };

  /** 数据源 */
  dataSource?: {
    data?: any;
    [key: string]: any;
  };

  /** 生命周期钩子 */
  lifecycle?: Partial<ComponentLifecycle>;

  /** 作用域配置 */
  scope?: ComponentScope;
}

/**
 * 容器组件配置
 * 用于管理一组相关组件
 */
export interface ContainerConfig extends ComponentConfig {
  /** 容器标题 */
  title?: string;

  /** 是否可折叠 */
  collapsible?: boolean;

  /** 是否默认折叠 */
  defaultCollapsed?: boolean;

  /** 是否有边框 */
  bordered?: boolean;

  /** 额外操作按钮 */
  actions?: ComponentConfig[];
}

/**
 * 表单字段组件配置
 */
export interface FormFieldConfig extends ComponentConfig {
  /** 字段名 */
  name: string;

  /** 标签文本 */
  label?: string;

  /** 是否必填 */
  required?: boolean;

  /** 校验规则 */
  rules?: any[];

  /** 默认值 */
  defaultValue?: any;
}

/**
 * 表格列配置
 */
export interface TableColumnConfig {
  /** 列标题 */
  title: string;

  /** 数据字段名 */
  dataIndex: string;

  /** 列宽 */
  width?: number | string;

  /** 自定义渲染函数 */
  render?: string;

  /** 是否可排序 */
  sorter?: boolean | string;

  /** 是否可过滤 */
  filterable?: boolean;

  /** 对齐方式 */
  align?: 'left' | 'center' | 'right';
}
