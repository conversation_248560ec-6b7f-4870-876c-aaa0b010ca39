/**
 * 类型定义
 * 导出所有公共API的类型定义
 *
 * @module Types
 */

import { ComponentConfig } from './component';

export * from './component';
export * from './lifecycle';
export * from './config';

/**
 * JsonRenderer 主组件的属性
 */
export interface JsonRendererProps {
  /** JSON配置字符串或对象 */
  json: string | object;

  /** 事件处理函数定义 */
  events?: Array<{
    name: string;
    code: string;
  }>;

  /** 方法映射 */
  methods?: Record<string, (...args: any[]) => any>;

  /** 自定义组件映射 */
  customComponents?: Record<string, React.ComponentType<any>>;

  /** 配置选项 */
  options?: {
    /** 是否启用性能监控 */
    enablePerformance?: boolean;

    /** 是否显示错误 */
    showErrors?: boolean;

    /** 是否使用严格模式 */
    strict?: boolean;

    /** 是否从远程加载配置 */
    remote?: boolean;

    /** 远程配置地址 */
    remoteUrl?: string;

    /** 缓存策略 */
    cache?: 'memory' | 'session' | 'local' | 'none';

    /** 调试模式 */
    debug?: boolean;

    /** 是否显示调试工具面板 */
    debugPanel?: boolean;
  };

  /** 常量 */
  consts?: Record<string, any>;

  /** 属性 */
  props?: Record<string, any>;

  /** 初始状态 */
  initialState?: Record<string, any>;
}

/**
 * 渲染器组件的属性
 */
export interface RendererProps {
  /** 组件配置 */
  config: {
    components: ComponentConfig[];
  };

  /** 自定义组件映射 */
  customComponents?: Record<string, React.ComponentType<any>>;

  /** 配置选项 */
  options?: Record<string, any>;
}

export type DynamicFunction = {
  name: string;
  code: string;
};
