/**
 * 配置类型定义
 *
 * @module Types/Config
 */

import { ComponentConfig } from './component';

/**
 * 事件处理函数定义
 */
export type EventType = {
  /** 事件名称 */
  name: string;

  /** 事件处理函数或代码字符串 */
  code: string | ((...args: any[]) => any);
};

/**
 * 动态函数定义
 */
export type DynamicFunction = {
  /** 函数名称 */
  name: string;

  /** 函数代码字符串 */
  code: string;
};

/**
 * JSON渲染器配置
 */
export interface JsonRendererConfig {
  /** 组件配置列表 */
  components: ComponentConfig[];

  /** 事件处理函数列表 */
  events?: EventType[];

  /** 依赖配置 */
  dependencies?: {
    /** 依赖项名称 */
    key: string;

    /** 获取依赖数据的方法 */
    effect: any;
  }[];

  /** 引用配置 */
  refs?: Record<string, JsonRendererConfig>;

  /** 全局配置 */
  config?: {
    /** 主题 */
    theme?: 'light' | 'dark' | Record<string, any>;

    /** 国际化配置 */
    i18n?: {
      locale: string;
      messages: Record<string, Record<string, string>>;
    };

    /** 自定义样式 */
    styles?: Record<string, any>;
  };

  /** 版本号 */
  version?: string;
}

/**
 * 远程配置选项
 */
export interface RemoteConfigOptions {
  /** 远程配置URL */
  url: string;

  /** 请求方法 */
  method?: 'GET' | 'POST';

  /** 请求头 */
  headers?: Record<string, string>;

  /** 请求参数 */
  params?: Record<string, any>;

  /** 缓存策略 */
  cache?: 'memory' | 'session' | 'local' | 'none';

  /** 缓存过期时间(毫秒) */
  cacheExpiration?: number;

  /** 重试次数 */
  retries?: number;

  /** 超时时间(毫秒) */
  timeout?: number;
}

/**
 * 调试配置选项
 */
export interface DebugOptions {
  /** 日志级别 */
  logLevel?: 'error' | 'warn' | 'info' | 'debug' | 'verbose';

  /** 是否启用开发者工具 */
  devTools?: boolean;

  /** 是否显示性能指标 */
  showPerformance?: boolean;

  /** 是否显示组件边界 */
  showBoundaries?: boolean;

  /** 是否显示组件ID */
  showIds?: boolean;

  /** 是否在控制台打印配置 */
  logConfig?: boolean;

  /** 是否在控制台打印状态变更 */
  logStateChanges?: boolean;
}

/**
 * JSON渲染器全局配置
 */
export interface JsonRendererGlobalConfig {
  /** 主题配置 */
  theme?: 'light' | 'dark' | Record<string, any>;

  /** 远程配置 */
  remote?: RemoteConfigOptions;

  /** 调试选项 */
  debug?: DebugOptions;

  /** 自定义组件路径映射 */
  componentPaths?: Record<string, string>;

  /** 默认作用域隔离设置 */
  defaultScopeIsolation?: boolean;

  /** 是否启用性能监控 */
  enablePerformance?: boolean;

  /** 是否启用严格模式 */
  strict?: boolean;
}
