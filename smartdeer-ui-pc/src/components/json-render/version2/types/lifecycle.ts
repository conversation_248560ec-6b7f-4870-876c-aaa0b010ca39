/**
 * 组件生命周期类型定义
 *
 * @module Types/Lifecycle
 */

/**
 * 组件生命周期钩子函数类型
 */
export type LifecycleHook = string | Function;

/**
 * 组件生命周期配置接口
 */
export interface ComponentLifecycle {
  /**
   * 组件实例创建之前调用
   * 此时尚未初始化props和内部状态
   */
  beforeCreate: LifecycleHook;

  /**
   * 组件实例创建完成后调用
   * 此时已完成初始化props和内部状态，但DOM尚未渲染
   */
  created: LifecycleHook;

  /**
   * 组件挂载到DOM之前调用
   */
  beforeMount: LifecycleHook;

  /**
   * 组件挂载到DOM之后调用
   * 此时可以访问和操作DOM元素
   */
  mounted: LifecycleHook;

  /**
   * 组件更新前调用
   * 在props或state变化导致组件重新渲染前调用
   */
  beforeUpdate: LifecycleHook;

  /**
   * 组件更新后调用
   * 在组件重新渲染完成后调用
   */
  updated: LifecycleHook;

  /**
   * 组件卸载前调用
   * 可以在此进行清理操作，如取消网络请求、清除定时器等
   */
  beforeUnmount: LifecycleHook;

  /**
   * 组件卸载后调用
   */
  unmounted: LifecycleHook;

  /**
   * 组件在keep-alive中被激活时调用
   */
  activated: LifecycleHook;

  /**
   * 组件在keep-alive中被停用时调用
   */
  deactivated: LifecycleHook;

  /**
   * 捕获一个来自子孙组件的错误时被调用
   */
  errorCaptured: LifecycleHook;

  /**
   * 组件渲染被追踪时调用
   * 用于调试性能问题
   */
  renderTracked: LifecycleHook;

  /**
   * 组件渲染被触发时调用
   * 用于调试性能问题
   */
  renderTriggered: LifecycleHook;

  /**
   * 渲染前调用
   * 可用于预处理props或进行其他准备工作
   */
  beforeRender: LifecycleHook;

  /**
   * 渲染后调用
   * 可用于后处理渲染结果
   */
  afterRender: LifecycleHook;
}

/**
 * 生命周期事件类型
 */
export type LifecycleEvent = {
  name: keyof ComponentLifecycle;
  timestamp: number;
  componentId: string;
  duration?: number;
};
