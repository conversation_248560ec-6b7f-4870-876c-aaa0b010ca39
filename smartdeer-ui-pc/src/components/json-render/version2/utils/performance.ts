/**
 * 性能监控工具
 * 用于跟踪组件渲染性能和操作时间
 *
 * @module Utils/Performance
 */

// 性能监控接口
export interface PerformanceMonitor {
  startMeasure: (id: string) => void;
  endMeasure: (id: string) => number;
  markEvent: (name: string) => void;
  getRenderTime: () => number;
  getTimings: () => Record<string, number>;
  getMetrics: () => PerformanceMetrics;
  cleanup: () => void;
}

// 性能指标接口
export interface PerformanceMetrics {
  renderTime: number;
  parseTime: number;
  eventCount: number;
  componentCount: number;
  deepestNesting: number;
  measures: Record<string, number>;
  events: Array<{name: string, timestamp: number}>;
}

/**
 * 初始化性能监控
 */
export const initializePerformanceMonitor = (): PerformanceMonitor => {
  const startTime = performance.now();
  const measures: Record<string, {start: number, end?: number}> = {};
  const events: Array<{name: string, timestamp: number}> = [];
  let componentCount = 0;
  let deepestNesting = 0;

  // 检测是否支持Performance API
  const isPerformanceSupported = typeof performance !== 'undefined';

  return {
    // 开始测量一个操作
    startMeasure: (id: string) => {
      if (!isPerformanceSupported) return;
      measures[id] = { start: performance.now() };

      // 跟踪组件数量和嵌套深度
      if (id.startsWith('render-node-')) {
        componentCount++;
        const nestingLevel = id.split('-').length;
        deepestNesting = Math.max(deepestNesting, nestingLevel);
      }
    },

    // 结束测量并返回耗时
    endMeasure: (id: string) => {
      if (!isPerformanceSupported || !measures[id]) return 0;

      const endTime = performance.now();
      measures[id].end = endTime;
      const duration = endTime - measures[id].start;

      // 调试信息
      if (process.env.NODE_ENV === 'development') {
        console.debug(`[Performance] ${id}: ${duration.toFixed(2)}ms`);
      }

      return duration;
    },

    // 标记事件发生
    markEvent: (name: string) => {
      if (!isPerformanceSupported) return;
      events.push({ name, timestamp: performance.now() });

      // 调试信息
      if (process.env.NODE_ENV === 'development') {
        console.debug(`[Event] ${name} at ${performance.now() - startTime}ms`);
      }
    },

    // 获取总渲染时间
    getRenderTime: () => {
      if (!isPerformanceSupported) return 0;
      return performance.now() - startTime;
    },

    // 获取所有计时数据
    getTimings: () => {
      const timings: Record<string, number> = {};

      Object.entries(measures).forEach(([id, { start, end }]) => {
        if (end) {
          timings[id] = end - start;
        }
      });

      return timings;
    },

    // 获取完整性能指标
    getMetrics: () => {
      const timings = Object.entries(measures).reduce((acc, [id, { start, end }]) => {
        if (end) {
          acc[id] = end - start;
        }
        return acc;
      }, {} as Record<string, number>);

      return {
        renderTime: performance.now() - startTime,
        parseTime: timings['config-parse'] || 0,
        eventCount: events.length,
        componentCount,
        deepestNesting,
        measures: timings,
        events
      };
    },

    // 清理
    cleanup: () => {
      // 可以进行一些清理工作
    }
  };
};

// 启动性能监控
export const startPerformanceMonitoring = (options: {
  debug?: boolean,
  reportUrl?: string
} = {}) => {
  const monitor = initializePerformanceMonitor();

  // 自动上报
  if (options.reportUrl) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const metrics = monitor.getMetrics();
        fetch(options.reportUrl!, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(metrics)
        }).catch(e => console.error('Failed to report metrics:', e));
      }, 0);
    });
  }

  return monitor;
};

export default {
  initializePerformanceMonitor,
  startPerformanceMonitoring
};
