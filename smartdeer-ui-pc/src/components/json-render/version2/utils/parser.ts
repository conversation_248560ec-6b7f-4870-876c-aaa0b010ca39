/**
 * 配置解析器
 * 负责解析和规范化JSON配置
 *
 * @module Utils/Parser
 */

import { isArray, isEmpty, isObject, isString } from 'lodash';
import { ComponentConfig, JsonRendererConfig } from '../types';

/**
 * 解析JSON配置
 * @param config JSON配置字符串或对象
 * @returns 解析后的配置对象
 */
export const parseConfiguration = (
  config: string | object,
): JsonRendererConfig => {
  let parsedConfig: JsonRendererConfig;

  if (!config) {
    return {
      components: [],
    };
  }

  // 解析JSON字符串
  if (isString(config)) {
    try {
      parsedConfig = JSON.parse(config as string);
    } catch (error) {
      console.error('Error parsing JSON configuration:', error);
      throw new Error('Invalid JSON configuration');
    }
  } else if (isObject(config)) {
    parsedConfig = config as JsonRendererConfig;
  } else {
    throw new Error('Configuration must be a JSON string or object');
  }

  // 确保基本结构
  if (!parsedConfig.components || !isArray(parsedConfig.components)) {
    parsedConfig.components = [];
  }

  // 规范化配置
  return normalizeConfig(parsedConfig);
};

/**
 * 规范化配置，添加默认值和唯一ID
 * @param config 原始配置
 * @returns 规范化后的配置
 */
const normalizeConfig = (config: JsonRendererConfig): JsonRendererConfig => {
  // 确保events数组存在
  if (!config.events) {
    config.events = [];
  }

  // 递归处理组件
  const processComponentConfig = (
    component: ComponentConfig,
  ): ComponentConfig => {
    if (!component.key) {
      component.key = `comp-${Math.random().toString(36).substring(2, 9)}`;
    }
    // 确保props对象存在
    if (!component.props) {
      component.props = {};
    }

    // 处理子组件
    if (isArray(component.children)) {
      component.children = component.children.map((child) => {
        if (isObject(child) && !isArray(child)) {
          return processComponentConfig(child as ComponentConfig);
        }
        return child;
      });
    } else if (isObject(component.children) && !isArray(component.children)) {
      component.children = processComponentConfig(
        component.children as ComponentConfig,
      );
    }

    // 处理引用
    if (component.$ref && config.refs && config.refs[component.$ref]) {
      // 保留原有的key和props
      const { key, props } = component;
      const refComponent = JSON.parse(
        JSON.stringify(config.refs[component.$ref]),
      );

      // 合并属性
      if (props && !isEmpty(props)) {
        refComponent.props = { ...refComponent.props, ...props };
      }

      // 保留原有的key
      if (key) {
        refComponent.key = key;
      }

      return processComponentConfig(refComponent);
    }

    return component;
  };

  // 处理所有顶级组件
  config.components = config.components.map(processComponentConfig);

  return config;
};

/**
 * 验证配置是否合法
 * @param config 配置对象
 * @returns 验证结果
 */
export const validateConfig = (
  config: JsonRendererConfig,
): {
  valid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  // 验证基本结构
  if (!config.components || !isArray(config.components)) {
    errors.push('Configuration must have a components array');
  }

  // 递归验证组件
  const validateComponent = (component: ComponentConfig, path: string) => {
    // 必须有component属性
    if (!component.component) {
      errors.push(`Component at ${path} must have a component property`);
    }

    // 验证子组件
    if (isArray(component.children)) {
      component.children.forEach((child, index) => {
        if (isObject(child) && !isArray(child)) {
          validateComponent(
            child as ComponentConfig,
            `${path}.children[${index}]`,
          );
        }
      });
    } else if (isObject(component.children) && !isArray(component.children)) {
      validateComponent(
        component.children as ComponentConfig,
        `${path}.children`,
      );
    }

    // 验证引用
    if (component.$ref && (!config.refs || !config.refs[component.$ref])) {
      errors.push(
        `Referenced component "${component.$ref}" at ${path} does not exist`,
      );
    }
  };

  // 验证所有顶级组件
  if (config.components && isArray(config.components)) {
    config.components.forEach((component, index) => {
      validateComponent(component, `components[${index}]`);
    });
  }

  return {
    valid: errors.length === 0,
    errors,
  };
};

/**
 * 从远程URL获取配置
 * @param url 配置URL
 * @param options 请求选项
 * @returns 解析后的配置
 */
export const fetchRemoteConfig = async (
  url: string,
  options: RequestInit = {},
): Promise<JsonRendererConfig> => {
  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`Failed to fetch config: ${response.statusText}`);
    }

    const configData = await response.json();
    return parseConfiguration(configData);
  } catch (error) {
    console.error('Error fetching remote configuration:', error);
    throw error;
  }
};

/**
 * 序列化配置为字符串
 * @param config 配置对象
 * @returns 序列化后的JSON字符串
 */
export const serializeConfig = (config: JsonRendererConfig): string => {
  try {
    return JSON.stringify(config, null, 2);
  } catch (error) {
    console.error('Error serializing configuration:', error);
    throw error;
  }
};

export default {
  parseConfiguration,
  validateConfig,
  fetchRemoteConfig,
  serializeConfig,
};
