import { isArray } from "lodash";

/**
 * 为所有节点添加 key
 * 
 * @param components 组件对象或组件数组
 * @returns 添加 key 后的组件对象或组件数组
 */
export const addKeysToAllNodes = (components: any | any[]) => {
  const addKeyRecursively = (node: any): any => {
    if (node && typeof node === 'object' && !Array.isArray(node)) {
      // 创建新对象，不修改原始数据
      const newNode = { ...node };

      // 为当前节点添加 key（如果没有）
      if (!newNode.key && newNode.component) {
        newNode.key = `comp-${Math.random().toString(36).substring(2, 9)}`;
      }

      // 递归处理所有字段
      Object.keys(newNode).forEach((key) => {
        const value = newNode[key];
        if (Array.isArray(value)) {
          // 如果是数组，使用 map 创建新数组
          newNode[key] = value.map((item) => {
            if (item && typeof item === 'object') {
              return addKeyRecursively(item);
            }
            return item;
          });
        } else if (value && typeof value === 'object') {
          // 如果是对象，递归处理
          newNode[key] = addKeyRecursively(value);
        }
      });

      return newNode;
    }
    return node;
  };

  // 检查输入是否为数组
  if (isArray(components)) {
    // 如果是数组，使用 map 创建新数组
    return components.map((item: any) => addKeyRecursively(item));
  } else {
    // 如果是单个对象，直接处理
    return addKeyRecursively(components);
  }
}
