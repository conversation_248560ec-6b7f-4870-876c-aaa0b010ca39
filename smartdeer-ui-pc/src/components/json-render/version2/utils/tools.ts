import dayjs from 'dayjs';
import { renderNode } from '../core/renderer';

// 定义样式映射
const classNameMap: Record<string, string> = {
  'number-style': 'color: #fe9111; font-weight: 600;padding: 0 2px;',
  'currency-style':
    'color: #fe9111; font-weight: 400; font-style: italic;padding: 0 2px;',
  'type-style': 'color: #fe9111; font-weight: 400;padding: 0 2px;',
};

/**
 * 合并对象，替代展开运算符
 * @param objects 要合并的对象数组
 * @returns 合并后的新对象
 */
export function mergeObjects<T extends Record<string, any>>(
  ...objects: T[]
): T {
  return objects.reduce((result, current) => {
    if (current && typeof current === 'object') {
      Object.entries(current).forEach(([key, value]) => {
        if (value !== undefined) {
          result[key as keyof T] = value;
        }
      });
    }
    return result;
  }, {} as T);
}

export const mergeArray = (...args: any[]) => {
  const result: any[] = [];
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    if (Array.isArray(arg)) {
      for (let j = 0; j < arg.length; j++) {
        result.push(arg[j]);
      }
    } else {
      result.push(arg);
    }
  }
  return result;
};

export const renderTime = (
  text: string,
  _: Record<string, any>,
  format: string = 'YYYY-MM-DD HH:mm',
) => {
  const time = dayjs(text).format(format);
  return time;
};

export const convertedClassNameToStyle = (string: string) => {
  return string
    .replace(/className=['"]([^'"]+)['"]/g, (match, className) => {
      const style = classNameMap[className] || '';
      return `style='${style}'`;
    })
    .replace(/\n/g, '<br/>');
};

export const renderTemplateString = (string: string) => {
  // 将className转换为style
  const convertedString = convertedClassNameToStyle(string);

  return renderNode({
    component: 'div',
    props: {
      dangerouslySetInnerHTML: {
        __html: convertedString,
      },
    },
  });
};
