import * as icons from '@ant-design/icons';
import {
  CodeEditor,
  JsonEditor,
  PageHeader,
  SchemaPage,
} from '@smartdeer-ui/pc';
import * as antd from 'antd';
import React from 'react';
import DynamicPage from '../../../dynamic-page';
import Loading from '../../../loading';
import QuillEditor from '../../../quill';
import QuillView from '../../../quill-view';
import Upload from '../../../schema-form/field-components/upload';
import ListItemFiles from '../../../schema-page/field-components/list-item-files';
import TableSearch from '../../../table-search';
import DynamicComponent from '../components/dynamic-component';
import ProSelect from '../components/fields/pro-select';
import ProTreeSelect from '../components/fields/pro-tree-select';
import FilePreviewModal from '../components/file-preview-modal';

/**
 * 组件加载器
 * 负责组件的注册、按需加载和获取
 *
 * @module Utils/Loader
 */

// 组件注册表
const componentRegistry: Record<
  string,
  () => Promise<{ default: React.ComponentType<any> }>
> = {};

// 调试函数
const debug = (message: string, data?: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[JsonRenderer Loader] ${message}`, data || '');
  }
};

// 验证组件
const validateComponent = (component: any, name: string): boolean => {
  if (!component) {
    debug(`Invalid component "${name}": component is null or undefined`);
    return false;
  }

  // 原生标签
  if (typeof component === 'string') return true;
  // 函数组件/类组件
  if (typeof component === 'function') return true;
  // React.memo/forwardRef 等
  if (
    typeof component === 'object' &&
    component !== null &&
    '$$typeof' in component
  )
    return true;

  // 其他情况不再尝试 createElement，直接返回 false
  debug(`Invalid component "${name}":`, component);
  return false;
};

// 已加载的组件缓存
export const loadedComponents: Record<string, React.ComponentType<any>> = {};

// 初始化基础组件
const initializeBaseComponents = () => {
  // 注册 antd 组件
  Object.entries(antd).forEach(([key, value]) => {
    // 只注册合法的 React 组件
    if (
      typeof value === 'function' ||
      (typeof value === 'object' && value !== null && '$$typeof' in value)
    ) {
      loadedComponents[key] = value as React.ComponentType<any>;
    }
    // 否则不注册，也不输出 debug
  });

  // 注册图标组件
  Object.entries(icons).forEach(([name, IconComponent]) => {
    if (
      typeof IconComponent === 'function' ||
      (typeof IconComponent === 'object' &&
        IconComponent !== null &&
        '$$typeof' in IconComponent)
    ) {
      loadedComponents[name] = IconComponent as React.ComponentType<any>;
    }
  });

  // 注册嵌套组件
  const nestedComponents = {
    'Typography.Text': antd.Typography.Text,
    'Typography.Title': antd.Typography.Title,
    'Typography.Paragraph': antd.Typography.Paragraph,
    'Form.Item': antd.Form.Item,
    'Table.Column': antd.Table.Column,
    'Collapse.Panel': antd.Collapse.Panel,
    'Descriptions.Item': antd.Descriptions.Item,
    'Radio.Button': antd.Radio.Button,
    'Radio.Group': antd.Radio.Group,
    'Select.Option': antd.Select.Option,
    'Select.OptGroup': antd.Select.OptGroup,
    'Input.TextArea': antd.Input.TextArea,
    'DatePicker.RangePicker': antd.DatePicker.RangePicker,
    'Input.Password': antd.Input.Password,
    'List.Item': antd.List.Item,
  };

  Object.entries(nestedComponents).forEach(([name, component]) => {
    if (validateComponent(component, name)) {
      loadedComponents[name] = component;
    }
  });

  // 注册自定义组件
  const customComponents = {
    CodeEditor,
    SchemaFormUpload: Upload,
    SchemaPage,
    DynamicPage,
    TableSearch,
    Quill: QuillEditor,
    QuillView,
    ListItemFiles,
    JsonEditor,
    PageHeader,
    DynamicComponent,
    ProSelect,
    ProTreeSelect,
    Loading,
    FilePreviewModal,
  };

  Object.entries(customComponents).forEach(([name, component]) => {
    if (validateComponent(component, name)) {
      loadedComponents[name] = component;
    }
  });
};

// 初始化组件
initializeBaseComponents();

/**
 * 注册组件
 */
export const registerComponent = (
  name: string,
  componentOrLoader:
    | React.ComponentType<any>
    | (() => Promise<{ default: React.ComponentType<any> }>),
): void => {
  if (!name) {
    debug('Cannot register component: name is required');
    return;
  }

  if (
    typeof componentOrLoader === 'function' &&
    !validateComponent(componentOrLoader, name)
  ) {
    // 是一个异步加载函数
    componentRegistry[name] = componentOrLoader as () => Promise<{
      default: React.ComponentType<any>;
    }>;
    debug(`Registered async component: ${name}`);
  } else if (validateComponent(componentOrLoader, name)) {
    // 是一个组件
    loadedComponents[name] = componentOrLoader as React.ComponentType<any>;
    debug(`Registered component: ${name}`);
  } else {
    debug(`Failed to register component: ${name}`);
  }
};

/**
 * 注册多个组件
 */
export const registerComponents = (
  components: Record<
    string,
    | React.ComponentType<any>
    | (() => Promise<{ default: React.ComponentType<any> }>)
  >,
): void => {
  Object.entries(components).forEach(([name, component]) => {
    registerComponent(name, component);
  });
};

/**
 * 检查是否是有效的React组件
 */
export const isValidComponent = (component: any): boolean => {
  return validateComponent(component, 'unknown');
};

/**
 * 获取已注册的组件
 */
export const getComponentByName = async (
  name: string,
): Promise<React.ComponentType<any> | null> => {
  if (!name) {
    debug('Cannot get component: name is required');
    return null;
  }

  // 1. 检查是否已加载
  if (loadedComponents[name]) {
    debug(`Using cached component: ${name}`);
    return loadedComponents[name];
  }

  // 2. 检查是否已注册但未加载
  if (componentRegistry[name]) {
    try {
      debug(`Loading async component: ${name}`);
      const module = await componentRegistry[name]();
      const component = module.default || module;

      if (validateComponent(component, name)) {
        loadedComponents[name] = component;
        debug(`Loaded async component: ${name}`);
        return component;
      }
    } catch (error) {
      debug(`Failed to load async component "${name}":`, error);
    }
    return null;
  }

  // 3. 检查嵌套路径
  if (name.includes('.')) {
    const parts = name.split('.');
    let current: any = loadedComponents;

    for (const part of parts) {
      current = current?.[part];
      if (!current) break;
    }

    if (validateComponent(current, name)) {
      loadedComponents[name] = current;
      debug(`Resolved nested component: ${name}`);
      return current;
    }
  }

  // 4. 作为HTML标签返回
  debug(`Using HTML tag: ${name}`);
  return name;
};

/**
 * 预加载组件
 */
export const preloadComponents = async (
  componentNames: string[],
): Promise<Record<string, React.ComponentType<any>>> => {
  const loadPromises = componentNames.map(async (name) => {
    const component = await getComponentByName(name);
    return [name, component];
  });

  const results = await Promise.all(loadPromises);
  return results.reduce((acc, [name, component]) => {
    if (component) {
      acc[name as string] = component as React.ComponentType<any>;
    }
    return acc;
  }, {} as Record<string, React.ComponentType<any>>);
};

export default {
  registerComponent,
  registerComponents,
  getComponentByName,
  preloadComponents,
  isValidComponent,
  loadedComponents,
};
