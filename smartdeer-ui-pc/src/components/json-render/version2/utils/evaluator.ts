/**
 * 表达式评估工具
 * 用于计算模板字符串和条件表达式
 *
 * @module Utils/Evaluator
 */

/**
 * 解析路径获取上下文中的值
 * @param path 路径字符串，如 "user.profile.name"
 * @param context 上下文对象
 * @returns 对应路径的值
 */
export const resolvePathValue = (
  path: string,
  context: Record<string, any>,
): any => {
  // 处理特殊路径前缀
  if (path.startsWith('context.')) {
    path = path.slice(8); // 移除"context."前缀
  }

  // 处理数组路径，如 "items[0].name"
  const normalizedPath = path.replace(/\[(\w+)\]/g, '.$1');
  const parts = normalizedPath.split('.');

  let result = context;
  for (const part of parts) {
    if (result !== undefined && result !== null) {
      result = result[part];
    }
  }

  // 先从context中取值，如果不存在就从context.state中取值
  if (!result) {
    result = context.state || {};

    for (const part of parts) {
      if (result === undefined || result === null) {
        return undefined;
      }
      result = result[part];
    }
  }

  return result;
};

/**
 * 判断是否是三元表达式
 * @param str 待检查的字符串
 * @returns 是否是三元表达式
 */
export const isTernaryExpression = (str: string): boolean => {
  return (
    typeof str === 'string' &&
    str.includes('?') &&
    str.includes(':') &&
    (str.includes('*{') ||
      str.includes('{{') ||
      str.includes('&&') ||
      str.includes('||'))
  );
};

/**
 * 安全的表达式求值
 * @param expression 表达式字符串
 * @param context 上下文对象
 * @returns 表达式求值结果
 */
export const evaluateExpression = (
  expression: string,
  context: Record<string, any>,
): any => {
  if (!expression) return undefined;

  if (expression === 'true') {
    return true;
  }
  if (expression === 'false') {
    return false;
  }

  try {
    // 替换变量引用 *{path}
    let processedExpression = expression.replace(
      /\*\{([^}]+)\}/g,
      (_, path) => {
        const value = resolvePathValue(path.trim(), context);

        // 转换为JS字面量
        if (typeof value === 'string') return `'${value.replace(/'/g, "\\'")}'`;
        if (value === null) return 'null';
        if (value === undefined) return 'undefined';
        if (typeof value === 'object') return JSON.stringify(value);

        return String(value);
      },
    );

    // 替换函数引用 fn:functionName
    processedExpression = processedExpression.replace(
      /fn:(\w+)/g,
      (_, name) => {
        return context.methods && context.methods[name] ? 'true' : 'false';
      },
    );

    // 使用Function构造函数求值
    return new Function(
      'context',
      `with(context) { return ${processedExpression}; }`,
    )(context);
  } catch (error) {
    console.error(`Expression evaluation error: "${expression}"`, error);
    return undefined;
  }
};

/**
 * 解析插值字符串
 * @param template 模板字符串，如 "Hello, *{user.name}!"
 * @param context 上下文对象
 * @returns 解析后的字符串
 */
export const interpolateString = (
  template: string,
  context: Record<string, any>,
): string => {
  if (typeof template !== 'string') return String(template);

  // 处理 *{path} 格式的变量
  return template.replace(
    /\*\{([^}|]+)(?:\|([^}]+))?\}/g,
    (match, path, formatter) => {
      let value = resolvePathValue(path.trim(), context);

      // 值为空时显示占位符
      if (value === undefined || value === null) {
        return '-';
      }

      // 处理格式化函数
      if (formatter && context.methods && context.methods[formatter.trim()]) {
        const formatterFn = context.methods[formatter.trim()];
        value = formatterFn(value);
      }

      return String(value);
    },
  );
};

/**
 * 解析函数表达式
 * @param funcExpression 函数表达式字符串，如 "fn:handleClick"
 * @param context 上下文对象
 * @returns 解析后的函数
 */
export const parseFunctionExpression = (
  funcExpression: string,
  context: Record<string, any>,
): Function | undefined => {
  if (!funcExpression || typeof funcExpression !== 'string') {
    return undefined;
  }

  // 处理 fn:functionName 格式
  if (funcExpression.startsWith('fn:')) {
    const funcName = funcExpression.slice(3);
    if (context.methods && typeof context.methods[funcName] === 'function') {
      return context.methods[funcName];
    }
  }

  // 处理内联函数定义
  if (
    funcExpression.startsWith('function(') ||
    funcExpression.startsWith('(')
  ) {
    try {
      // 使用Function构造函数创建函数
      // 注意：这里的安全性取决于输入函数的可信度
      const fn = new Function(
        'context',
        `with(context) { return ${funcExpression}; }`,
      )(context);
      return typeof fn === 'function' ? fn : undefined;
    } catch (error) {
      console.error(`Function parsing error: "${funcExpression}"`, error);
      return undefined;
    }
  }

  return undefined;
};

/**
 * 处理条件渲染表达式
 * @param condition 条件表达式
 * @param context 上下文对象
 * @returns 表达式的布尔值结果
 */
export const evaluateCondition = (
  condition: any,
  context: Record<string, any>,
): boolean => {
  if (!condition) return false;

  // 处理直接的布尔值
  if (typeof condition === 'boolean') return condition;

  // 处理字符串表达式
  if (typeof condition === 'string') {
    return evaluateExpression(condition, context) === true;
  }

  // 处理函数
  if (typeof condition === 'function') {
    try {
      return condition(context) === true;
    } catch (error) {
      console.error('Error evaluating condition function:', error);
      return false;
    }
  }

  return Boolean(condition);
};

export default {
  resolvePathValue,
  isTernaryExpression,
  evaluateExpression,
  interpolateString,
  parseFunctionExpression,
  evaluateCondition,
};
