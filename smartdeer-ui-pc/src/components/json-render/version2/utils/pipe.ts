import dayjs from 'dayjs';
import { evaluate } from 'mathjs';

export const pipeFilters = {
  // 如果值为空，显示默认值
  displayIfEmpty: (value: any, defaultValue = '-') => {
    if (value === null || value === undefined || value === '') {
      return defaultValue;
    }
    return value;
  },

  // 格式化日期
  formatDate: (value: any, format = 'YYYY-MM-DD') => {
    if (isNaN(Number(value)) || Number(value) === 0) return '-';

    // 使用dayjs库来格式化日期，并返回格式化后的结果
    return dayjs(Number(value)).format(format || 'YYYY-MM-DD');
  },

  getRegionEmoji: (str: string): string => {
    if (!str) return '';
    let newStr = str;
    if (str === 'TW') newStr = 'CN';

    const firstLetter = newStr[0].toUpperCase().charCodeAt(0) - 0x41 + 0x1f1e6;
    const secondLetter = newStr[1].toUpperCase().charCodeAt(0) - 0x41 + 0x1f1e6;

    return (
      String.fromCodePoint(firstLetter) + String.fromCodePoint(secondLetter)
    );
  },

  mathjs(value: string, param: string | number = 0, operator = '+'): string {
    if (isNaN(Number(value))) return '0';
    if (isNaN(Number(param))) return '0';

    return evaluate(`${value} ${operator} ${param}`);
  },
};
