/**
 * 调试工具
 * 提供开发环境下的调试功能
 *
 * @module Utils/Debug
 */

import { DebugOptions } from '../types';

// 日志级别定义
const LOG_LEVELS = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
  verbose: 4,
};

// 默认配置
const DEFAULT_CONFIG: DebugOptions = {
  logLevel: 'info',
  devTools: false,
  showPerformance: false,
  showBoundaries: false,
  showIds: false,
  logConfig: false,
  logStateChanges: false,
};

// 当前配置
let debugConfig: DebugOptions = { ...DEFAULT_CONFIG };
let debugEnabled = false;

// 函数声明
let log: (
  level: keyof typeof LOG_LEVELS | 'log',
  message: string,
  data?: any,
) => void;
let enableDebugMode: (options?: Partial<DebugOptions>) => void;
let disableDebugMode: () => void;
let updateDebugConfig: (options: Partial<DebugOptions>) => void;
let getDebugConfig: () => DebugOptions;

function injectDevTools(): void {
  if (typeof window === 'undefined') return;
  window.__SMARTDEER_JSONRENDERER__ = window.__SMARTDEER_JSONRENDERER__ || {
    instances: [],
    configs: {},
    debugConfig,
    api: {
      log,
      enableDebugMode,
      disableDebugMode,
      updateDebugConfig,
      getDebugConfig,
      getInstances: () => window.__SMARTDEER_JSONRENDERER__?.instances ?? [],
      getConfig: (id: string) =>
        window.__SMARTDEER_JSONRENDERER__?.configs?.[id],
    },
  };
  log(
    'info',
    '🔧 JsonRenderer DevTools Ready - access via window.__SMARTDEER_JSONRENDERER__',
  );
}

function removeDevTools(): void {
  if (typeof window === 'undefined') return;
  const debugStyles = document.getElementById(
    'smartdeer-jsonrenderer-debug-styles',
  );
  if (debugStyles) {
    debugStyles.remove();
  }
  log('info', '🔧 JsonRenderer DevTools Removed');
}

// 函数实现
log = (level, message, data) => {
  if (!debugEnabled && level !== 'error') return;
  const configLogLevel = LOG_LEVELS[debugConfig.logLevel || 'info'];
  const messageLogLevel = LOG_LEVELS[level as keyof typeof LOG_LEVELS] || 2;
  if (messageLogLevel > configLogLevel) return;

  const consoleMethod =
    level === 'error'
      ? console.error
      : level === 'warn'
      ? console.warn
      : level === 'info'
      ? console.info
      : level === 'debug'
      ? console.debug
      : console.log;

  const prefix = `[JsonRenderer]`;
  if (data !== undefined) {
    consoleMethod(`${prefix} ${message}`, data);
  } else {
    consoleMethod(`${prefix} ${message}`);
  }
};

enableDebugMode = (options = {}) => {
  debugConfig = { ...DEFAULT_CONFIG, ...options };
  debugEnabled = true;
  log('debug', '🔧 JsonRenderer Debug Mode Enabled', debugConfig);
  if (debugConfig.devTools && typeof window !== 'undefined') {
    injectDevTools();
  }
};

disableDebugMode = () => {
  debugEnabled = false;
  log('info', '🔧 JsonRenderer Debug Mode Disabled');
  if (debugConfig.devTools && typeof window !== 'undefined') {
    removeDevTools();
  }
};

updateDebugConfig = (options) => {
  const oldConfig = { ...debugConfig };
  debugConfig = { ...debugConfig, ...options };
  log('debug', '🔄 Debug config updated', { from: oldConfig, to: debugConfig });
  if (
    debugConfig.devTools !== oldConfig.devTools &&
    typeof window !== 'undefined'
  ) {
    if (debugConfig.devTools) {
      injectDevTools();
    } else {
      removeDevTools();
    }
  }
};

getDebugConfig = () => {
  return { ...debugConfig };
};

export {
  disableDebugMode,
  enableDebugMode,
  getDebugConfig,
  log,
  updateDebugConfig,
};

/**
 * 注册渲染实例
 * @param id 实例ID
 * @param config 配置
 * @param context 上下文
 */
export const registerInstance = (
  id: string,
  config: any,
  context: any,
): void => {
  if (!debugEnabled || typeof window === 'undefined') return;

  window.__SMARTDEER_JSONRENDERER__ = window.__SMARTDEER_JSONRENDERER__ || {
    instances: [],
    configs: {},
    debugConfig,
    api: {},
  };

  // 存储实例信息
  window.__SMARTDEER_JSONRENDERER__.instances.push({
    id,
    registeredAt: new Date(),
    context,
  });

  window.__SMARTDEER_JSONRENDERER__.configs[id] = config;

  log('debug', `📦 Registered JsonRenderer instance: ${id}`);
};

/**
 * 记录状态变更
 * @param id 实例ID
 * @param path 状态路径
 * @param oldValue 旧值
 * @param newValue 新值
 */
export const logStateChange = (
  id: string,
  path: string,
  oldValue: any,
  newValue: any,
): void => {
  if (!debugEnabled || !debugConfig.logStateChanges) return;

  log('debug', `🔄 State changed: ${path}`, {
    instanceId: id,
    from: oldValue,
    to: newValue,
  });
};

/**
 * 记录组件渲染
 * @param componentId 组件ID
 * @param componentType 组件类型
 * @param props 组件属性
 */
export const logComponentRender = (
  componentId: string,
  componentType: string,
  props: any,
): void => {
  if (
    !debugEnabled ||
    LOG_LEVELS[debugConfig.logLevel || 'info'] < LOG_LEVELS.debug
  )
    return;

  log('debug', `🔄 Rendering component: ${componentType} (${componentId})`, {
    props,
  });
};

/**
 * 添加调试属性到组件
 * @param props 原始属性
 * @param componentId 组件ID
 * @param componentType 组件类型
 * @returns 添加调试信息后的属性
 */
export const addDebugProps = (
  props: Record<string, any>,
  componentId: string,
  componentType: string,
): Record<string, any> => {
  if (!debugEnabled || !debugConfig.showBoundaries) return props;

  return {
    ...props,
    className: `${props.className || ''} jsonrenderer-component`.trim(),
    'data-component-id': componentId,
    'data-component-type': componentType,
    children: [
      ...(Array.isArray(props.children) ? props.children : [props.children]),
      debugConfig.showIds
        ? {
            type: 'div',
            key: '__debug_id',
            className: 'jsonrenderer-component-id',
            children: `${componentType}:${componentId}`,
          }
        : null,
    ].filter(Boolean),
  };
};

// 声明全局类型
declare global {
  interface Window {
    __SMARTDEER_JSONRENDERER__?: {
      instances: Array<{
        id: string;
        registeredAt: Date;
        context: any;
      }>;
      configs: Record<string, any>;
      debugConfig: DebugOptions;
      api: Record<string, any>;
    };
  }
}
