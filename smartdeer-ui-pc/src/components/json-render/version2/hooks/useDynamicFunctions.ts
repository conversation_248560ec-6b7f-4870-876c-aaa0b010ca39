import { Modal, message } from 'antd';
import dayjs from 'dayjs';
import { get, set, isArray, isString, isEmpty } from 'lodash';
import React, { useEffect, useReducer, useRef } from 'react';
import { ConfigContext } from '../../../config-provider';
import { renderNode } from '../core/renderer';
import { DynamicFunction } from '../types';
import * as tools from '../utils/tools';
import { useJsonFetcher } from './useFetcher';
import { isDeepEqual } from '../../../../utils/hooks/useDeepCompareEffect';
import { tableController } from '../../../../components/BFM/components/custom/bfm-table-list';
import { getSearchParams } from '../../../../utils/getSearchParams';
import { generateRandomPassword } from '../../../../utils/generateRandomPassword';
import { sha512Hash } from '../../../../utils/hash';
import { getDomainName, swrDownloadFile, swrUploadFile } from '../../../../utils/swrFetcher';
import useFileAccessToken from '../../../../utils/hooks/useFileAccessToken';
import { getUtcTimestamp } from '../../../../utils/time';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { addKeysToAllNodes } from '../utils/addKeysToAllNodes';

dayjs.extend(utc);
dayjs.extend(timezone);

const fixFunctionFormat = (funcStr: string) => {
  let func = funcStr.trim();
  func = func.replace(/\s+/g, ' ');
  func = func.replace(/async\s+\(/g, 'async(');
  func = func.replace(/;\s*$/, '');
  return func;
};

// 动态函数执行器
export const useDynamicFunctions = (
  functions?: DynamicFunction[],
  context = {},
  consts = {},
  customComponents = {},
) => {
  const funcsRef = useRef<Record<string, (...args: any) => any>>({});
  const [, forceUpdate] = useReducer((x) => x + 1, 0);
  const { router, useUrlParams } = React.useContext(ConfigContext);
  const urlParams = useUrlParams();

  const { jsonFetcher, jsonFetcherAll } = useJsonFetcher(consts);

  const { getTheCompleteFileUrl } = useFileAccessToken();

  // 创建安全沙箱
  const createSandbox = (
    code: string,
    functions: Record<string, (...args: any) => any>,
  ) => {
    // const allowed = new Set([
    //   'setTableData', 'setPagination', 'setModalOpen', 'setModalData',
    //   'fetcher', 'form', 'pagination', 'modalData', 'funcs', 'renderComponent'
    // ]);
    // 替换 *{key} 的内容
    const replacePlaceholders = (code: string) => {
      if (!code) return '';
      return code.replace(/\*\{([^}]+)\}/g, (_, key) => {
        const value = get(context, key);
        return value !== undefined ? value : `*{${key}}`;
      });
    };
    const codeWithReplacedPlaceholders = replacePlaceholders(code);
    const codeString = fixFunctionFormat(codeWithReplacedPlaceholders);

    const sandbox = new Proxy(
      {
        renderComponent: (node: any, context: any) => {
          const newNode = addKeysToAllNodes(node)

          return renderNode(newNode, context, customComponents)
        },
        fetcher: jsonFetcher,
        fetcherAll: jsonFetcherAll,
        events: functions,
        log: console.log,
        console: console,
        dayjs: dayjs,
        Object: Object,
        message: message,
        history: router,
        entityUUID: urlParams.entityUUID,
        entityId: urlParams.entityId,
        urlParams: urlParams,
        getSearchParams: getSearchParams,
        JSON: JSON,
        encodeURIComponent: encodeURIComponent,
        decodeURIComponent: decodeURIComponent,
        window: window,
        Modal: Modal,
        localStorage: localStorage,
        Promise: Promise,
        isArray: isArray,
        isString: isString,
        isEmpty: isEmpty,
        consts,
        Number: Number,
        isDeepEqual: isDeepEqual,
        tableController,
        set,
        get,
        generateRandomPassword,
        sha512Hash,
        swrDownloadFile,
        getDomainName,
        swrUploadFile,
        getTheCompleteFileUrl,
        getUtcTimestamp,
        ...tools,
      },
      {
        get(target, prop) {
          // if (!allowed.has(prop as string)) throw new Error(`禁止访问 ${String(prop)}`);
          return target[prop as keyof typeof target];
        },
        has: () => true,
      },
    );

    try {
      return new Function(
        'sandbox',
        `
        with(sandbox) {
          return (${codeString});
        }
      `,
      )(sandbox);
    } catch (error) {
      console.error('函数编译失败:', error, '编译失败的函数:', codeString);
      return () => { };
    }
  };

  useEffect(() => {
    if (!Array.isArray(functions) || !functions?.length) return;

    const compiled = functions?.reduce((acc, fn) => {
      acc[fn.name] = createSandbox(fn.code, acc);
      return acc;
    }, {} as Record<string, (...args: any) => any>);

    funcsRef.current = compiled;
    forceUpdate();

    return () => {
      Object.keys(compiled).forEach((k) => delete compiled[k]);
    };
  }, [functions]);

  return { funcs: funcsRef.current };
};
