import { message } from 'antd';
import React, { useState } from 'react';
import { getSearchParams } from '../../../../utils/getSearchParams';
import swrFetcher, { cachedSwrFetcher } from '../../../../utils/swrFetcher';
import { ConfigContext } from '../../../config-provider';
import { getEffectFetchConfig } from '../utils/getEffectFetchConfig';

const isRiverPath = (pathname: string) => {
  return pathname.includes('/bm/') || pathname.includes('/rcn/');
};

export const useFetcher = (consts?: Record<string, any>) => {
  const [loading, setLoading] = useState<boolean>(true);
  const {
    useUrlParams,
    appRiverEntityFunction,
    appFunctionApi,
    appProcessApi,
    appRiverEntityProcess,
  } = React.useContext(ConfigContext);
  const urlParams = useUrlParams();
  const pathname = window.location.pathname;

  const isRiver = isRiverPath(pathname);

  const fetcher = async (effectConfig: any) => {
    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(
        effectConfig,
        {
          appFunctionApi: isRiver ? appRiverEntityFunction : appFunctionApi,
          appProcessApi: isRiver ? appRiverEntityProcess : appProcessApi,
        } as any,
        {
          entityUUID: consts?.entityUUID || urlParams.entityUUID || '0',
          entityId: consts?.entityId || urlParams.entityId || '0',
        },
      );

      const { data } = await swrFetcher(api, method, params);

      let newData = dataIndex ? data[dataIndex] : data;

      return newData;
    } catch (e: any) {
      message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    fetcher,
    loading,
  };
};

export const useJsonFetcher = (consts?: Record<string, any>) => {
  const [loading, setLoading] = useState<boolean>(true);
  const {
    useUrlParams,
    appRiverEntityFunction,
    appFunctionApi,
    appProcessApi,
    appRiverEntityProcess,
  } = React.useContext(ConfigContext);
  const urlParams = useUrlParams();
  const pathname = window.location.pathname;
  const searchParams = getSearchParams();
  const isRiver = isRiverPath(pathname);

  const entityUUID =
    consts?.entityUUID ||
    urlParams.entityUUID ||
    searchParams.entityUUID ||
    '0';
  const entityId =
    consts?.entityId || urlParams.entityId || searchParams.entityId || '0';

  const jsonFetcher = async (effectConfig: any) => {
    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(
        effectConfig,
        {
          appFunctionApi: isRiver ? appRiverEntityFunction : appFunctionApi,
          appProcessApi: isRiver ? appRiverEntityProcess : appProcessApi,
        } as any,
        {
          entityUUID,
          entityId,
        },
      );

      const { data } = await swrFetcher(api, method, params);

      let newData = dataIndex ? data[dataIndex] : data;

      return newData;
    } catch (e: any) {
      throw new Error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const jsonFetcherAll = async (effectConfigs: any[]) => {
    try {
      const promises = effectConfigs.map(config => jsonFetcher(config));
      const results = await Promise.all(promises);
      return results;
    } catch (e: any) {
      throw new Error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    jsonFetcher,
    jsonFetcherAll,
    loading,
  };
};

export const useCacheFetcher = (consts?: Record<string, any>) => {
  const [loading, setLoading] = useState<boolean>(true);
  const {
    useUrlParams,
    appRiverEntityFunction,
    appFunctionApi,
    appProcessApi,
    appRiverEntityProcess,
  } = React.useContext(ConfigContext);
  const urlParams = useUrlParams();
  const pathname = window.location.pathname;
  const searchParams = getSearchParams();
  const isRiver = pathname.includes('/bm') || pathname.includes('/rcn');

  const entityUUID =
    consts?.entityUUID ||
    urlParams.entityUUID ||
    searchParams.entityUUID ||
    '0';
  const entityId =
    consts?.entityId || urlParams.entityId || searchParams.entityId || '0';

  const cacheFetcher = async (effectConfig: any) => {
    try {
      const { api, method, params, dataIndex } = getEffectFetchConfig(
        effectConfig,
        {
          appFunctionApi: isRiver ? appRiverEntityFunction : appFunctionApi,
          appProcessApi: isRiver ? appRiverEntityProcess : appProcessApi,
        } as any,
        {
          entityUUID,
          entityId,
        },
      );

      const { data } = await cachedSwrFetcher(api, method, params);

      let newData = dataIndex ? data[dataIndex] : data;

      return newData;
    } catch (e: any) {
      throw new Error(e.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    cacheFetcher,
    catchFetchLoading: loading,
  };
};
