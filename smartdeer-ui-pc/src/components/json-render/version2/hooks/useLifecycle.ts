/**
 * 生命周期钩子
 * 提供组件级别的生命周期管理
 *
 * @module Hooks/UseLifecycle
 */

import { useEffect, useRef, useState } from 'react';
import { ComponentLifecycle, LifecycleEvent } from '../types/lifecycle';
import { log } from '../utils/debug';

/**
 * 组件生命周期钩子参数
 */
interface UseComponentLifecycleParams {
  /** 组件唯一标识 */
  componentId: string;

  /** 组件类型 */
  componentType: string;

  /** 生命周期配置 */
  lifecycle?: Partial<ComponentLifecycle>;

  /** 方法映射 */
  methods?: Record<string, Function>;

  /** 性能监控实例 */
  perfMonitor?: any;

  /** 是否启用调试日志 */
  debug?: boolean;
}

/**
 * 调用生命周期钩子函数
 * @param hookName 钩子名称
 * @param hook 钩子函数或函数名
 * @param methods 方法映射
 * @param args 参数
 * @returns 钩子函数执行结果
 */
const callHook = (
  hookName: string,
  hook: string | Function | undefined,
  methods: Record<string, Function> = {},
  ...args: any[]
): any => {
  if (!hook) return;

  try {
    if (typeof hook === 'string' && hook.startsWith('fn:')) {
      const methodName = hook.slice(3);
      if (methods[methodName] && typeof methods[methodName] === 'function') {
        return methods[methodName](...args);
      }
    } else if (typeof hook === 'function') {
      return hook(...args);
    }
  } catch (error) {
    log('error', `Error calling lifecycle hook ${hookName}:`, error);
  }
};

/**
 * 组件生命周期钩子
 * 管理组件级别的生命周期事件
 */
export const useComponentLifecycle = ({
  componentId,
  componentType,
  lifecycle = {},
  methods = {},
  perfMonitor,
  debug = false
}: UseComponentLifecycleParams) => {
  const [isCreated, setIsCreated] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // 用于保存上一次的props
  const prevPropsRef = useRef<any>(null);

  // 持久化组件实例引用
  const instanceRef = useRef<{
    id: string;
    type: string;
    element: HTMLElement | null;
    lifecycle: Partial<ComponentLifecycle>;
    events: LifecycleEvent[];
  }>({
    id: componentId,
    type: componentType,
    element: null,
    lifecycle,
    events: []
  });

  // 记录生命周期事件
  const recordEvent = (name: keyof ComponentLifecycle, duration?: number) => {
    const event: LifecycleEvent = {
      name,
      timestamp: Date.now(),
      componentId,
      duration
    };

    instanceRef.current.events.push(event);

    if (debug) {
      log('debug', `📅 Lifecycle event: ${name}`, {
        componentId,
        componentType,
        duration
      });
    }

    // 性能监控
    perfMonitor?.markEvent(`lifecycle-${componentId}-${name}`);
  };

  // 组件创建
  useEffect(() => {
    if (isCreated) return;

    // beforeCreate 生命周期
    if (perfMonitor) perfMonitor.startMeasure(`lifecycle-${componentId}-beforeCreate`);
    callHook('beforeCreate', lifecycle.beforeCreate, methods, { id: componentId, type: componentType });
    if (perfMonitor) {
      const duration = perfMonitor.endMeasure(`lifecycle-${componentId}-beforeCreate`);
      recordEvent('beforeCreate', duration);
    } else {
      recordEvent('beforeCreate');
    }

    setIsCreated(true);

    // created 生命周期
    if (perfMonitor) perfMonitor.startMeasure(`lifecycle-${componentId}-created`);
    callHook('created', lifecycle.created, methods, instanceRef.current);
    if (perfMonitor) {
      const duration = perfMonitor.endMeasure(`lifecycle-${componentId}-created`);
      recordEvent('created', duration);
    } else {
      recordEvent('created');
    }

    // 组件销毁时调用 unmounted 生命周期
    return () => {
      if (perfMonitor) perfMonitor.startMeasure(`lifecycle-${componentId}-beforeUnmount`);
      callHook('beforeUnmount', lifecycle.beforeUnmount, methods, instanceRef.current);
      if (perfMonitor) {
        const duration = perfMonitor.endMeasure(`lifecycle-${componentId}-beforeUnmount`);
        recordEvent('beforeUnmount', duration);
      } else {
        recordEvent('beforeUnmount');
      }

      // 最后调用 unmounted
      if (perfMonitor) perfMonitor.startMeasure(`lifecycle-${componentId}-unmounted`);
      callHook('unmounted', lifecycle.unmounted, methods);
      if (perfMonitor) {
        const duration = perfMonitor.endMeasure(`lifecycle-${componentId}-unmounted`);
        recordEvent('unmounted', duration);
      } else {
        recordEvent('unmounted');
      }
    };
  }, [
    componentId,
    componentType,
    isCreated,
    lifecycle,
    methods,
    perfMonitor,
    debug
  ]);

  // 组件挂载
  useEffect(() => {
    if (!isCreated || isMounted) return;

    // 延迟到下一帧执行，确保组件已渲染
    const timeoutId = setTimeout(() => {
      // beforeMount 生命周期
      if (perfMonitor) perfMonitor.startMeasure(`lifecycle-${componentId}-beforeMount`);
      callHook('beforeMount', lifecycle.beforeMount, methods, instanceRef.current);
      if (perfMonitor) {
        const duration = perfMonitor.endMeasure(`lifecycle-${componentId}-beforeMount`);
        recordEvent('beforeMount', duration);
      } else {
        recordEvent('beforeMount');
      }

      // 查找组件DOM元素
      const element = document.querySelector(`[data-component-id="${componentId}"]`);
      if (element) {
        instanceRef.current.element = element as HTMLElement;
      }

      // mounted 生命周期
      if (perfMonitor) perfMonitor.startMeasure(`lifecycle-${componentId}-mounted`);
      callHook('mounted', lifecycle.mounted, methods, instanceRef.current, instanceRef.current.element);
      if (perfMonitor) {
        const duration = perfMonitor.endMeasure(`lifecycle-${componentId}-mounted`);
        recordEvent('mounted', duration);
      } else {
        recordEvent('mounted');
      }

      setIsMounted(true);
    }, 0);

    return () => clearTimeout(timeoutId);
  }, [
    componentId,
    isCreated,
    isMounted,
    lifecycle,
    methods,
    perfMonitor
  ]);

  // 提供更新处理函数
  const handleUpdate = (newProps: any) => {
    if (!isCreated || !isMounted) return;

    const prevProps = prevPropsRef.current;
    prevPropsRef.current = newProps;

    if (prevProps === null) return;

    // 只在属性真正变化时触发更新生命周期
    if (JSON.stringify(prevProps) === JSON.stringify(newProps)) return;

    // beforeUpdate 生命周期
    if (perfMonitor) perfMonitor.startMeasure(`lifecycle-${componentId}-beforeUpdate`);
    callHook('beforeUpdate', lifecycle.beforeUpdate, methods, prevProps, newProps);
    if (perfMonitor) {
      const duration = perfMonitor.endMeasure(`lifecycle-${componentId}-beforeUpdate`);
      recordEvent('beforeUpdate', duration);
    } else {
      recordEvent('beforeUpdate');
    }

    // 在渲染完成后调用 updated
    setTimeout(() => {
      if (perfMonitor) perfMonitor.startMeasure(`lifecycle-${componentId}-updated`);
      callHook('updated', lifecycle.updated, methods, instanceRef.current);
      if (perfMonitor) {
        const duration = perfMonitor.endMeasure(`lifecycle-${componentId}-updated`);
        recordEvent('updated', duration);
      } else {
        recordEvent('updated');
      }
    }, 0);
  };

  // 提供渲染前调用的钩子
  const beforeRender = (props: any) => {
    if (!isCreated) return props;

    // beforeRender 生命周期
    const processedProps = callHook('beforeRender', lifecycle.beforeRender, methods, props, instanceRef.current) || props;
    recordEvent('beforeRender');

    return processedProps;
  };

  // 提供渲染后调用的钩子
  const afterRender = (result: React.ReactNode) => {
    if (!isCreated) return result;

    // afterRender 生命周期
    const processedResult = callHook('afterRender', lifecycle.afterRender, methods, result, instanceRef.current) || result;
    recordEvent('afterRender');

    return processedResult;
  };

  // 提供激活/停用钩子(用于缓存组件)
  const activate = () => {
    if (!isCreated || !isMounted) return;

    // activated 生命周期
    callHook('activated', lifecycle.activated, methods, instanceRef.current);
    recordEvent('activated');
  };

  const deactivate = () => {
    if (!isCreated || !isMounted) return;

    // deactivated 生命周期
    callHook('deactivated', lifecycle.deactivated, methods, instanceRef.current);
    recordEvent('deactivated');
  };

  // 提供错误捕获钩子
  const errorCaptured = (error: Error, info: string) => {
    // errorCaptured 生命周期
    const shouldPropagate = callHook('errorCaptured', lifecycle.errorCaptured, methods, error, instanceRef.current, info);
    recordEvent('errorCaptured');

    // 默认继续传播错误，除非钩子明确返回false
    return shouldPropagate !== false;
  };

  return {
    isCreated,
    isMounted,
    instanceRef: instanceRef.current,
    handleUpdate,
    beforeRender,
    afterRender,
    activate,
    deactivate,
    errorCaptured,

    // 获取生命周期事件历史
    getLifecycleEvents: () => [...instanceRef.current.events]
  };
};

export default useComponentLifecycle;
