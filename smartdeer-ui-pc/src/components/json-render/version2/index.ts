import { JsonR<PERSON> } from './core/engine';
import { DynamicRenderer } from './components/dynamic-renderer';
import { useComponentLifecycle } from './hooks/useLifecycle';
import { createScope } from './core/scope';
import { registerComponent } from './utils/loader';
import { enableDebugMode } from './utils/debug';
import { startPerformanceMonitoring } from './utils/performance';
import type { JsonRendererConfig, ComponentConfig } from './types';

export {
  JsonR<PERSON>,
  DynamicRenderer,
  useComponentLifecycle,
  createScope,
  registerComponent,
  enableDebugMode,
  startPerformanceMonitoring,
};

export type {
  JsonRendererConfig,
  ComponentConfig,
};

export default JsonRender;
