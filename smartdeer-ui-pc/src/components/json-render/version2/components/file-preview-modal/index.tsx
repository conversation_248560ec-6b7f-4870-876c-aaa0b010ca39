import { DownloadOutlined, FileTextOutlined } from '@ant-design/icons';
import { Empty, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { useFileAccessToken } from '../../../../../utils/hooks';

const FilePreviewModal = (props: any) => {
  const {
    open,
    onModalCancel,
    title,
    fileName,
    fileUrl,
    footer = null,
    width = 800,
    download = false,
    handleDownload,
    isCompletedUrl = false,
  } = props;
  const [fileType, setFileType] = useState('');
  const [completeUrl, setCompleteUrl] = useState(fileUrl);
  const { getTheCompleteFileUrl } = useFileAccessToken();

  const getCompleteUrl = async () => {
    if (isCompletedUrl) return;
    const url = await getTheCompleteFileUrl(fileUrl);
    setCompleteUrl(url);
  };

  const previewImage = (url: string) => {
    return <img src={url} alt="Preview" style={{ width: '100%' }} />;
  };

  const previewPDF = (url: string) => {
    return <iframe src={url} width="100%" height="500px" title="PDF Preview" />;
  };

  const previewDoc = (url: string) => {
    const officeUrl = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(
      url,
    )}`;
    return (
      <iframe src={officeUrl} width="100%" height="500px" title="DOC Preview" />
    );
  };

  useEffect(() => {
    if (fileName && fileUrl) {
      getCompleteUrl();
      const fileNameParts = fileName.split('.');
      const ext = fileNameParts[fileNameParts.length - 1].toLowerCase();
      if (['jpg', 'jpeg', 'png'].includes(ext)) {
        setFileType('image');
      } else if (ext === 'pdf') {
        setFileType('pdf');
      } else if (['doc', 'docx'].includes(ext)) {
        setFileType('doc');
      }
    }
  }, [fileName, fileUrl]);

  const renderChildren = () => {
    if (!completeUrl) return null;

    switch (fileType) {
      case 'image':
        return previewImage(completeUrl);
      case 'pdf':
        return previewPDF(completeUrl);
      case 'doc':
        return previewDoc(completeUrl);
      default:
        return (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="该文件类型暂不支持预览"
          />
        );
    }
  };
  return (
    <Modal
      open={open}
      title={title}
      onCancel={onModalCancel}
      footer={footer}
      width={width}
    >
      <div className="mt-[24px]">
        <div className="flex justify-between h-[40px] items-center p-[10px] bg-slate-50 rounded-t-8">
          <div>
            <FileTextOutlined />
            <span className="ml-2">{fileName || title}</span>
          </div>
          {download ? (
            <DownloadOutlined
              style={{ fontSize: '18px', cursor: 'pointer' }}
              onClick={handleDownload}
            />
          ) : (
            <span></span>
          )}
        </div>
        {renderChildren()}
      </div>
    </Modal>
  );
};

export default FilePreviewModal;
