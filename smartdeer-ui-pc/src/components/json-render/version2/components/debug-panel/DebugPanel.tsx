import React, { useState, useEffect } from 'react';
import { Drawer, Tabs, Timeline, List, Tag, Button, Tooltip } from 'antd';
import { BugOutlined } from '@ant-design/icons';
import { isEqual } from 'lodash';

// 这里假设 window.__SMARTDEER_JSONRENDERER__ 作为全局调试信息收集点
// 实际项目可根据 debug.ts 进一步扩展

const { TabPane } = Tabs;

const getDebugData = () => {
  if (typeof window !== 'undefined' && window.__SMARTDEER_JSONRENDERER__) {
    const d = window.__SMARTDEER_JSONRENDERER__ as any;
    return {
      stateChanges: d.stateChanges || [],
      errors: d.errors || [],
      lifecycle: d.lifecycle || [],
      logs: d.logs || [],
    };
  }
  return {
    stateChanges: [],
    errors: [],
    lifecycle: [],
    logs: [],
  };
};

// 递归渲染高亮 diff
const renderDiff = (from: any, to: any, indent = 2): React.ReactNode => {
  if (typeof to === 'object' && to !== null) {
    if (Array.isArray(to)) {
      return (
        <pre style={{ display: 'inline', background: '#f6f8fa', borderRadius: 4, padding: 4, margin: 0, fontSize: 12 }}>
          [
          {to.map((item, idx) => (
            <div key={idx} style={{ paddingLeft: indent * 2 }}>
              {renderDiff(from?.[idx], item, indent + 2)}{idx < to.length - 1 ? ',' : ''}
            </div>
          ))}
          ]
        </pre>
      );
    }
    // 对象
    const keys = Array.from(new Set([...(Object.keys(from || {})), ...(Object.keys(to || {}))]));
    return (
      <pre style={{ display: 'inline', background: '#f6f8fa', borderRadius: 4, padding: 4, margin: 0, fontSize: 12 }}>
        {'{'}
        {keys.map((key, idx) => (
          <div key={key} style={{ paddingLeft: indent * 2 }}>
            <span style={{ color: '#888' }}>
              &quot;{key}&quot;:
            </span>{' '}
            {isEqual(from?.[key], to?.[key]) ? (
              <span>{renderDiff(from?.[key], to?.[key], indent + 2)}</span>
            ) : (
              <span style={{ color: '#cf1322', fontWeight: 'bold' }}>{renderDiff(from?.[key], to?.[key], indent + 2)}</span>
            )}
            {idx < keys.length - 1 ? ',' : ''}
          </div>
        ))}
        {'}'}
      </pre>
    );
  }
  // 普通值
  if (!isEqual(from, to)) {
    return <span style={{ color: '#cf1322', fontWeight: 'bold' }}>{String(to)}</span>;
  }
  return <span>{String(to)}</span>;
};

const CollapsibleJson: React.FC<{ value: any; label: string; highlight?: any }> = ({ value, label, highlight }) => {
  const [open, setOpen] = useState(false);
  if (typeof value === 'object' && value !== null) {
    const jsonStr = JSON.stringify(value, null, 2);
    const isLong = jsonStr.length > 60 || jsonStr.split('\n').length > 2;
    return (
      <div style={{ margin: '2px 0' }}>
        <span style={{ color: '#888', marginRight: 4 }}>{label}</span>
        <span
          style={{ cursor: isLong ? 'pointer' : 'default', color: isLong ? '#fa8c16' : undefined }}
          onClick={() => isLong && setOpen((v) => !v)}
        >
          {isLong && (
            <b style={{ marginRight: 4 }}>{open ? '▼' : '▶'}</b>
          )}
        </span>
        <span style={{ verticalAlign: 'top' }}>
          {isLong && !open ? (
            <pre style={{ display: 'inline', background: '#f6f8fa', borderRadius: 4, padding: 4, margin: 0, fontSize: 12, maxWidth: 320, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{jsonStr.split('\n')[0]} ...</pre>
          ) : (
            highlight ? renderDiff(highlight.from, highlight.to) : <pre style={{ display: 'inline', background: '#f6f8fa', borderRadius: 4, padding: 4, margin: 0, fontSize: 12 }}>{jsonStr}</pre>
          )}
        </span>
      </div>
    );
  }
  return (
    <div style={{ margin: '2px 0' }}>
      <span style={{ color: '#888', marginRight: 4 }}>{label}</span>
      {highlight ? renderDiff(highlight.from, highlight.to) : <span>{String(value)}</span>}
    </div>
  );
};

const DebugPanel: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [debugData, setDebugData] = useState(getDebugData());

  // 定时刷新调试数据
  useEffect(() => {
    if (!visible) return;
    const timer = setInterval(() => {
      setDebugData(getDebugData());
    }, 1000);
    return () => clearInterval(timer);
  }, [visible]);

  return (
    <>
      {/* 右下角悬浮按钮 */}
      <div
        style={{
          position: 'fixed',
          right: 32,
          bottom: 32,
          zIndex: 9999,
        }}
      >
        <Tooltip title="调试工具">
          <Button
            type="primary"
            shape="circle"
            icon={<BugOutlined />}
            size="large"
            style={{ background: '#fa8c16', border: 'none' }}
            onClick={() => setVisible(true)}
          />
        </Tooltip>
      </div>
      {/* 侧边抽屉 */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span><BugOutlined style={{ color: '#fa8c16' }} /> 调试工具</span>
            <Button size="small" onClick={() => {
              if (typeof window !== 'undefined') {
                const globalObj = window as any;
                if (globalObj.__SMARTDEER_JSONRENDERER__) {
                  globalObj.__SMARTDEER_JSONRENDERER__.stateChanges = [];
                }
                setDebugData(getDebugData());
              }
            }}>清空</Button>
          </div>
        }
        placement="right"
        width={480}
        onClose={() => setVisible(false)}
        open={visible}
        zIndex={20000}
      >
        <Tabs defaultActiveKey="state" style={{ height: '100%' }}>
          <TabPane tab={<span>State变化</span>} key="state">
            <Timeline style={{ height: '100%' }}>
              {debugData.stateChanges.filter((item: any) => !isEqual(item.from, item.to)).map((item: any) => (
                <Timeline.Item key={item.time + item.path}>
                  <div>
                    <Tag color="blue">{item.path}</Tag>
                    <CollapsibleJson value={item.from} label="旧值:" />
                    <CollapsibleJson value={item.to} label="新值:" highlight={{ from: item.from, to: item.to }} />
                    <div style={{ color: '#aaa', fontSize: 12 }}>{item.time}</div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </TabPane>
          <TabPane tab="报错信息" key="error">
            <List
              dataSource={debugData.errors}
              renderItem={(item: any) => (
                <List.Item>
                  <div>
                    <Tag color="red">{item.type || 'Error'}</Tag>
                    <div style={{ color: '#cf1322' }}>{item.message}</div>
                    <pre style={{ background: '#f5f5f5', padding: 8 }}>{item.stack}</pre>
                    <div style={{ color: '#aaa', fontSize: 12 }}>{item.time}</div>
                  </div>
                </List.Item>
              )}
            />
          </TabPane>
          <TabPane tab="生命周期" key="lifecycle">
            <Timeline style={{ maxHeight: 320, overflow: 'auto' }}>
              {debugData.lifecycle.map((item: any) => (
                <Timeline.Item key={item.time + item.event} color="green">
                  <div>
                    <Tag color="green">{item.event}</Tag>
                    <div>{item.detail}</div>
                    <div style={{ color: '#aaa', fontSize: 12 }}>{item.time}</div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </TabPane>
          <TabPane tab="日志" key="log">
            <List
              dataSource={debugData.logs}
              renderItem={(item: any) => (
                <List.Item>
                  <Tag color={item.level === 'error' ? 'red' : item.level === 'warn' ? 'orange' : 'blue'}>{item.level}</Tag>
                  <span>{item.message}</span>
                  <div style={{ color: '#aaa', fontSize: 12 }}>{item.time}</div>
                </List.Item>
              )}
            />
          </TabPane>
        </Tabs>
      </Drawer>
    </>
  );
};

export default DebugPanel;
