/**
 * 组件导出文件
 * 导出所有内置组件
 *
 * @module Components
 */

// 动态渲染器组件
export { default as DynamicRenderer } from './dynamic-renderer';

// 错误边界组件
export { default as ErrorBoundary } from './error-boundary';

// 性能监控组件
export { default as PerformanceMonitor } from './performance-monitor';

// 批量操作弹窗组件
export { default as BatchActionModal } from './batch-action-modal';

// 文件预览弹窗组件
export { default as FilePreviewModal } from './file-preview-modal';

// 调试面板组件
export { default as DebugPanel } from './debug-panel';

/**
 * 注册所有内置组件
 */
export const registerBuiltinComponents = (registry: Record<string, any>) => {
  const components = {
    DynamicRenderer: require('./dynamic-renderer').default,
    ErrorBoundary: require('./error-boundary').default,
    PerformanceMonitor: require('./performance-monitor').default,
    BatchActionModal: require('./batch-action-modal').default,
    FilePreviewModal: require('./file-preview-modal').default,
    DebugPanel: require('./debug-panel').default,
    DynamicComponent: require('./dynamic-component').default,
  };

  Object.entries(components).forEach(([name, component]) => {
    registry[name] = component;
  });

  return registry;
};

export default {
  DynamicRenderer: require('./dynamic-renderer').default,
  ErrorBoundary: require('./error-boundary').default,
  PerformanceMonitor: require('./performance-monitor').default,
  BatchActionModal: require('./batch-action-modal').default,
  FilePreviewModal: require('./file-preview-modal').default,
  DebugPanel: require('./debug-panel').default,
  registerBuiltinComponents
};
