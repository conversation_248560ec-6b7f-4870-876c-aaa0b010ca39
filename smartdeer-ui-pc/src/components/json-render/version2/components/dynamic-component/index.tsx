import { message } from 'antd';
import { isObject, isString } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { EffectType } from '../../../../../typing';
import JsonRender from '../../../version1';
import { useJsonFetcher } from '../../hooks/useFetcher';

export default function DynamicComponent({
  config,
  dataSource = {},
  effect,
}: {
  config?: string;
  dataSource?: Record<string, any>;
  effect?: EffectType;
}) {
  const [conf, setConf] = useState<any>({});

  const { jsonFetcher } = useJsonFetcher();

  const fetchConf = async () => {
    if (effect) {
      try {
        const res = await jsonFetcher({
          ...effect,
        });

        let confJson;

        if (isString(res)) {
          try {
            confJson = JSON.parse(res);
          } catch (e: any) {
            message.error('配置文件解析失败：' + e.message);
          }
        } else {
          confJson = res;
        }
        setConf(confJson);
      } catch (e) {
        console.log('error', e);
      }
    } else if (config) {
      if (isString(config)) {
        setConf(JSON.parse(config));
      } else {
        setConf(config);
      }
    }
  };

  useEffect(() => {
    if (!effect && !config) return;
    fetchConf();
  }, [effect, config]);

  const data = useMemo(() => {
    if (isObject(dataSource)) {
      return dataSource;
    }
    if (isString(dataSource)) {
      return JSON.parse(dataSource);
    }
  }, [dataSource]);

  if (!config && !effect) return null;
  if (!conf?.components) return null;

  return (
    <JsonRender
      json={JSON.stringify({ components: conf?.components })}
      events={conf?.events}
    />
  );
}
