/**
 * 性能监控组件
 * 显示JsonRenderer渲染性能指标
 *
 * @module Components/PerformanceMonitor
 */

import React, { useEffect, useState } from 'react';
import { useJsonRenderer } from '../../context/consumer';
import { PerformanceMetrics } from '../../utils/performance';

/**
 * 性能监控组件属性
 */
interface PerformanceMonitorProps {
  /** 是否自动更新 */
  autoUpdate?: boolean;

  /** 更新间隔(毫秒) */
  updateInterval?: number;

  /** 自定义样式 */
  style?: React.CSSProperties;

  /** 自定义类名 */
  className?: string;

  /** 显示的指标 */
  metrics?: Array<keyof PerformanceMetrics>;

  /** 自定义渲染函数 */
  render?: (metrics: PerformanceMetrics) => React.ReactNode;

  /** 是否可折叠 */
  collapsible?: boolean;
}

/**
 * 格式化时间
 * @param time 时间(毫秒)
 * @returns 格式化后的时间字符串
 */
const formatTime = (time: number): string => {
  if (time < 1) {
    return `${(time * 1000).toFixed(2)}μs`;
  }
  if (time < 1000) {
    return `${time.toFixed(2)}ms`;
  }
  return `${(time / 1000).toFixed(2)}s`;
};

/**
 * 性能监控组件
 * 用于展示JsonRenderer的性能指标
 */
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  autoUpdate = true,
  updateInterval = 1000,
  style = {},
  className = '',
  metrics = ['renderTime', 'parseTime', 'componentCount', 'deepestNesting', 'eventCount'],
  render,
  collapsible = true
}) => {
  const { perfMonitor } = useJsonRenderer();
  const [metricsData, setMetricsData] = useState<PerformanceMetrics | null>(null);
  const [expanded, setExpanded] = useState(!collapsible);

  // 更新性能指标
  const updateMetrics = () => {
    if (perfMonitor) {
      setMetricsData(perfMonitor.getMetrics());
    }
  };

  // 自动更新 - 放在组件顶层
  useEffect(() => {
    if (autoUpdate && perfMonitor) {
      updateMetrics();

      const intervalId = setInterval(updateMetrics, updateInterval);

      return () => {
        clearInterval(intervalId);
      };
    }
  }, [autoUpdate, updateInterval, perfMonitor]);

  // 性能监控不可用时的提示
  if (!perfMonitor) {
    return (
      <div
        className={`performance-monitor-unavailable ${className}`}
        style={{
          padding: '8px',
          margin: '8px 0',
          backgroundColor: '#f5f5f5',
          border: '1px solid #d9d9d9',
          borderRadius: '2px',
          ...style
        }}
      >
        性能监控未启用。请在选项中启用 enablePerformance。
      </div>
    );
  }

  // 使用自定义渲染函数
  if (render && metricsData) {
    return render(metricsData);
  }

  // 无数据时显示加载中
  if (!metricsData) {
    return (
      <div
        className={`performance-monitor-loading ${className}`}
        style={{
          padding: '8px',
          margin: '8px 0',
          backgroundColor: '#f5f5f5',
          border: '1px solid #d9d9d9',
          borderRadius: '2px',
          ...style
        }}
      >
        加载性能指标...
      </div>
    );
  }

  // 折叠状态下只显示总渲染时间
  if (collapsible && !expanded) {
    return (
      <div
        className={`performance-monitor-collapsed ${className}`}
        style={{
          padding: '8px',
          margin: '8px 0',
          backgroundColor: '#f5f5f5',
          border: '1px solid #d9d9d9',
          borderRadius: '2px',
          cursor: 'pointer',
          ...style
        }}
        onClick={() => setExpanded(true)}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>JsonRenderer 性能监控</span>
          <span>总渲染时间: {formatTime(metricsData.renderTime)}</span>
        </div>
      </div>
    );
  }

  // 完整性能指标
  return (
    <div
      className={`performance-monitor ${className}`}
      style={{
        padding: '8px',
        margin: '8px 0',
        backgroundColor: '#f5f5f5',
        border: '1px solid #d9d9d9',
        borderRadius: '2px',
        ...style
      }}
    >
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '8px',
        borderBottom: '1px solid #d9d9d9',
        paddingBottom: '4px'
      }}>
        <h4 style={{ margin: 0 }}>JsonRenderer 性能监控</h4>

        {collapsible && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              setExpanded(false);
            }}
            style={{
              padding: '2px 4px',
              backgroundColor: '#fff',
              border: '1px solid #d9d9d9',
              borderRadius: '2px',
              cursor: 'pointer'
            }}
          >
            折叠
          </button>
        )}
      </div>

      <div>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <tbody>
            {metrics.includes('renderTime') && (
              <tr>
                <td style={{ padding: '4px', fontWeight: 'bold' }}>总渲染时间</td>
                <td style={{ padding: '4px' }}>{formatTime(metricsData.renderTime)}</td>
              </tr>
            )}

            {metrics.includes('parseTime') && (
              <tr>
                <td style={{ padding: '4px', fontWeight: 'bold' }}>配置解析时间</td>
                <td style={{ padding: '4px' }}>{formatTime(metricsData.parseTime)}</td>
              </tr>
            )}

            {metrics.includes('componentCount') && (
              <tr>
                <td style={{ padding: '4px', fontWeight: 'bold' }}>组件数量</td>
                <td style={{ padding: '4px' }}>{metricsData.componentCount}</td>
              </tr>
            )}

            {metrics.includes('deepestNesting') && (
              <tr>
                <td style={{ padding: '4px', fontWeight: 'bold' }}>最大嵌套深度</td>
                <td style={{ padding: '4px' }}>{metricsData.deepestNesting}</td>
              </tr>
            )}

            {metrics.includes('eventCount') && (
              <tr>
                <td style={{ padding: '4px', fontWeight: 'bold' }}>事件数量</td>
                <td style={{ padding: '4px' }}>{metricsData.eventCount}</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {metrics.includes('measures') && (
        <div style={{ marginTop: '8px' }}>
          <h5 style={{ margin: '0 0 4px' }}>详细计时</h5>
          <div style={{
            maxHeight: '150px',
            overflow: 'auto',
            border: '1px solid #d9d9d9',
            padding: '4px'
          }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr>
                  <th style={{ padding: '4px', textAlign: 'left' }}>操作</th>
                  <th style={{ padding: '4px', textAlign: 'right' }}>耗时</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(metricsData.measures)
                  .sort(([, a], [, b]) => b - a)
                  .map(([id, time]) => (
                    <tr key={id}>
                      <td style={{ padding: '4px' }}>{id}</td>
                      <td style={{ padding: '4px', textAlign: 'right' }}>{formatTime(time)}</td>
                    </tr>
                  ))
                }
              </tbody>
            </table>
          </div>
        </div>
      )}

      <div style={{ marginTop: '8px', textAlign: 'right' }}>
        <button
          type="button"
          onClick={updateMetrics}
          style={{
            padding: '4px 8px',
            backgroundColor: '#fff',
            border: '1px solid #d9d9d9',
            borderRadius: '2px',
            cursor: 'pointer'
          }}
        >
          刷新
        </button>
      </div>
    </div>
  );
};

export default PerformanceMonitor;
