/**
 * 错误边界组件
 * 捕获渲染过程中的JavaScript错误
 *
 * @module Components/ErrorBoundary
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { log } from '../../utils/debug';

/**
 * 错误边界组件属性
 */
interface ErrorBoundaryProps {
  /** 子元素 */
  children: ReactNode;

  /** 捕获错误时的回调 */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;

  /** 显示错误的自定义组件 */
  fallback?: ReactNode | ((error: Error, errorInfo: ErrorInfo) => ReactNode);

  /** 组件名称，用于调试 */
  componentName?: string;

  /** 是否在开发环境显示调试信息 */
  showDevInfo?: boolean;
}

/**
 * 错误边界组件状态
 */
interface ErrorBoundaryState {
  /** 是否发生错误 */
  hasError: boolean;

  /** 错误信息 */
  error: Error | null;

  /** 错误详情 */
  errorInfo: ErrorInfo | null;
}

/**
 * 错误边界组件
 * 捕获渲染过程中的JavaScript错误并优雅降级
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  /**
   * 捕获渲染过程中的错误
   * 返回状态更新用于降级UI
   */
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  /**
   * 错误发生后执行side effects
   * 例如记录错误日志
   */
  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    this.setState({
      errorInfo
    });

    // 记录错误日志
    log('error', `Error caught by ErrorBoundary${this.props.componentName ? ` (${this.props.componentName})` : ''}:`, {
      error,
      errorInfo,
      componentStack: errorInfo.componentStack
    });

    // 调用自定义错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  /**
   * 重置错误状态
   */
  resetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render(): ReactNode {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback, componentName, showDevInfo } = this.props;

    // 如果没有错误，正常渲染子组件
    if (!hasError) {
      return children;
    }

    // 渲染自定义错误界面
    if (fallback) {
      if (typeof fallback === 'function' && error && errorInfo) {
        return fallback(error, errorInfo);
      }
      return fallback;
    }

    // 默认错误界面
    return (
      <div
        style={{
          padding: '16px',
          margin: '8px 0',
          backgroundColor: '#fff1f0',
          border: '1px solid #ffccc7',
          borderRadius: '2px',
          color: '#434343'
        }}
      >
        <h3 style={{ margin: '0 0 8px', color: '#cf1322' }}>
          渲染错误{componentName ? ` (${componentName})` : ''}
        </h3>

        <div style={{ margin: '8px 0' }}>
          {error?.message}
        </div>

        {(process.env.NODE_ENV !== 'production' || showDevInfo) && errorInfo && (
          <details style={{ whiteSpace: 'pre-wrap', marginTop: '8px' }}>
            <summary style={{ cursor: 'pointer' }}>错误详情</summary>
            <pre style={{
              margin: '8px 0 0',
              padding: '8px',
              backgroundColor: '#f5f5f5',
              overflow: 'auto',
              maxHeight: '300px'
            }}>
              {errorInfo.componentStack}
            </pre>
          </details>
        )}

        <button
          type="button"
          onClick={this.resetError}
          style={{
            marginTop: '8px',
            padding: '4px 8px',
            backgroundColor: '#fff',
            border: '1px solid #d9d9d9',
            borderRadius: '2px',
            cursor: 'pointer'
          }}
        >
          重试
        </button>
      </div>
    );
  }
}

export default ErrorBoundary;
