import { Form, Input, Select } from 'antd';
import React from 'react';
import { EffectType } from '../../../../../../typing';
import omit from '../../../../../../utils/omit';
import { useCacheFetcher } from '../../../hooks/useFetcher';

import 'antd/lib/select/style';

interface FieldMappingItem {
  dataKey: string;
  fieldKey: string;
}

interface FieldNames {
  value?: string;
  label?: string;
  [key: string]: string | undefined;
}

interface ProSelectProps
  extends Omit<React.ComponentProps<typeof Select>, 'options'> {
  value?: any;
  effect?: EffectType;
  transform?: (data: any[]) => any[];
  fieldMapping?: FieldMappingItem[];
  fieldNames?: FieldNames;
  options?: any[];
  form?: any;
}

/**
 * ProSelect 组件: 支持远程加载 Options，补充 Str及自定义key的数据
 * @param props
 * @returns
 */
const ProSelect: React.FC<ProSelectProps> = (props) => {
  const {
    value,
    effect,
    transform,
    fieldMapping,
    fieldNames,
    options: defaultOptions,
    form,
  } = props;
  const fetchInitRef = React.useRef(false);
  const [options, setOptions] = React.useState<any[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);

  const { cacheFetcher } = useCacheFetcher();

  const fetchOptions = async () => {
    setIsLoading(true);
    try {
      let data = await cacheFetcher(effect);

      setOptions(data);
    } catch (error) {
      console.error('fetchOptions error', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isTransformExist = React.useMemo(() => {
    return typeof transform === 'function';
  }, [transform]);

  const transformOptions = React.useMemo(() => {
    if (transform) {
      return transform(options);
    }
    return options;
  }, [options, isTransformExist]);

  React.useEffect(() => {
    if (!effect || fetchInitRef.current) {
      setOptions(defaultOptions || []);
      return;
    }

    fetchOptions();
  }, [JSON.stringify(effect)]);

  const selectProps = React.useMemo(() => {
    return omit(props, ['effect', 'transform', 'fieldMapping', 'form']);
  }, [props]);

  const currentOption = React.useMemo(() => {
    if (!transformOptions || !transformOptions.length) {
      return;
    }
    const valueKey = fieldNames?.value || 'value';

    const result = transformOptions.find(
      (item: any) => item[valueKey] === value,
    );

    return result;
  }, [transformOptions, value, fieldNames]);

  React.useEffect(() => {
    if (!form || !fieldMapping?.length) {
      return;
    }

    fieldMapping?.forEach((item: any) => {
      form.setFieldValue(item.fieldKey, currentOption?.[item.dataKey]);
    });
  }, [form, fieldMapping, currentOption]);

  return (
    <>
      <Select
        {...selectProps}
        showSearch={true}
        value={value}
        style={{ width: '100%' }}
        loading={isLoading}
        options={transformOptions}
      />
      {fieldMapping &&
        fieldMapping.length > 0 &&
        currentOption &&
        fieldMapping.map((item: any) => {
          return (
            <Form.Item
              hidden
              noStyle
              name={item.fieldKey}
              key={item.fieldKey}
            >
              <Input />
            </Form.Item>
          );
        })}
    </>
  );
};

export default ProSelect;
