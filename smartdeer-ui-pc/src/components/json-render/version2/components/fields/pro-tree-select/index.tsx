import { Select, TreeSelect } from 'antd';
import React from 'react';
import { EffectType } from '../../../../../../typing';
import omit from '../../../../../../utils/omit';
import { useCacheFetcher } from '../../../hooks/useFetcher';

import 'antd/lib/select/style';

interface ProTreeSelectProps
  extends Omit<React.ComponentProps<typeof Select>, 'options'> {
  value?: any;
  effect?: EffectType;
  transform?: (data: any[]) => any[];
}

/**
 * TreeProSelect 组件: 支持远程加载 Options
 * @param props
 * @returns
 */
const ProTreeSelect: React.FC<ProTreeSelectProps> = (props) => {
  const { value, effect, transform } = props;
  const fetchInitRef = React.useRef(false);
  const [options, setOptions] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(false);

  const { cacheFetcher } = useCacheFetcher();

  const fetchOptions = async () => {
    setIsLoading(true);
    try {
      let data = await cacheFetcher(effect);

      setOptions(data);
    } catch (error) {
      console.error('fetchOptions error', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isTransformExist = React.useMemo(() => {
    return typeof transform === 'function';
  }, [transform]);

  const transformOptions = React.useMemo(() => {
    if (transform) {
      return transform(options);
    }
    return options;
  }, [options, isTransformExist]);

  React.useEffect(() => {
    if (!effect || fetchInitRef.current) return;

    fetchOptions();
  }, [JSON.stringify(effect)]);

  const selectProps = React.useMemo(() => {
    return omit(props, ['effect', 'transform']);
  }, [props]);

  return (
    <>
      <TreeSelect
        {...selectProps}
        showSearch={true}
        value={value}
        style={{ width: '100%' }}
        loading={isLoading}
        treeData={transformOptions}
      />
    </>
  );
};

export default ProTreeSelect;
