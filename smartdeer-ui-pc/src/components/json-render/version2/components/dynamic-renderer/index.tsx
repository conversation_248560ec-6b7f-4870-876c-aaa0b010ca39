/**
 * 动态渲染器组件
 * 用于在运行时根据配置渲染组件树
 *
 * @module Components/DynamicRenderer
 */

import React, { useEffect, useRef, useState } from 'react';
import { useJsonRenderer } from '../../context/consumer';
import { renderNode } from '../../core/renderer';
import { createScope } from '../../core/scope';
import { useComponentLifecycle } from '../../hooks/useLifecycle';
import { DynamicRendererConfig } from '../../types/component';
import { log } from '../../utils/debug';

/**
 * 动态渲染器组件属性
 */
interface DynamicRendererProps {
  /** 要渲染的配置 */
  config: DynamicRendererConfig['config'];

  /** 数据源 */
  dataSource?: DynamicRendererConfig['dataSource'];

  /** 生命周期钩子 */
  lifecycle?: DynamicRendererConfig['lifecycle'];

  /** 作用域配置 */
  scope?: DynamicRendererConfig['scope'];

  /** 自定义类名 */
  className?: string;

  /** 自定义样式 */
  style?: React.CSSProperties;

  /** 是否懒加载 */
  lazyLoad?: boolean;

  /** 是否显示加载状态 */
  showLoading?: boolean;

  /** 刷新的key，变化时会触发重新渲染 */
  refreshKey?: string | number;
}

/**
 * 动态渲染器组件
 * 根据传入的JSON配置动态渲染组件树
 */
export const DynamicRenderer: React.FC<DynamicRendererProps> = ({
  config,
  dataSource = {},
  lifecycle = {},
  scope: scopeConfig,
  className = '',
  style = {},
  lazyLoad = false,
  showLoading = true,
  refreshKey,
}) => {
  // 获取全局上下文
  const globalContext = useJsonRenderer();

  // 组件唯一标识
  const componentId = useRef(
    `dynamic-renderer-${Math.random().toString(36).substring(2, 11)}`,
  ).current;

  // 本地状态
  const [isLoading, setIsLoading] = useState(lazyLoad);
  const [error, setError] = useState<Error | null>(null);

  // 创建隔离的作用域
  const scopeRef = useRef(
    createScope(
      `dynamic-renderer-${componentId.split('-').pop()}`,
      scopeConfig?.parent
        ? globalContext.scope?.getChild(scopeConfig.parent)
        : globalContext.scope,
    ),
  );

  // 如果配置了隔离作用域
  useEffect(() => {
    if (scopeConfig?.isolate) {
      scopeRef.current.isolate();
    }

    // 继承指定的属性
    if (scopeConfig?.inherit && scopeConfig.inherit.length > 0) {
      scopeRef.current.inherit(scopeConfig.inherit);
    }

    // 暴露指定的属性
    if (scopeConfig?.expose && scopeConfig.expose.length > 0) {
      scopeRef.current.expose(scopeConfig.expose);
    }
  }, [scopeConfig]);

  // 数据源混合到本地状态
  useEffect(() => {
    if (dataSource && Object.keys(dataSource).length > 0) {
      Object.entries(dataSource).forEach(([key, value]) => {
        scopeRef.current.setState(key, value);
      });
    }
  }, [dataSource]);

  // 组件生命周期管理
  const {
    isCreated,
    isMounted,
    handleUpdate,
    beforeRender,
    afterRender,
    errorCaptured,
  } = useComponentLifecycle({
    componentId,
    componentType: 'DynamicRenderer',
    lifecycle,
    methods: globalContext.methods,
    perfMonitor: globalContext.perfMonitor,
    debug: true,
  });

  // 处理配置变化
  useEffect(() => {
    if (isCreated && isMounted) {
      handleUpdate({ config, dataSource, lifecycle, scopeConfig });
    }
  }, [
    config,
    dataSource,
    lifecycle,
    scopeConfig,
    refreshKey,
    isCreated,
    isMounted,
  ]);

  // 懒加载处理
  useEffect(() => {
    if (!lazyLoad) {
      setIsLoading(false);
      return;
    }

    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 100);

    return () => clearTimeout(timer);
  }, [lazyLoad]);

  // 错误边界
  if (error) {
    return (
      <div className={`dynamic-renderer-error ${className}`} style={style}>
        <h4>渲染错误</h4>
        <pre>{error.message}</pre>
        <button type="button" onClick={() => setError(null)}>
          重试
        </button>
      </div>
    );
  }

  // 加载状态
  if (isLoading && showLoading) {
    return (
      <div className={`dynamic-renderer-loading ${className}`} style={style}>
        加载中...
      </div>
    );
  }

  try {
    // 创建渲染上下文
    const renderContext = {
      ...globalContext,
      // 合并作用域状态
      state: { ...globalContext.state, ...scopeRef.current.state },
      // 使用本地作用域
      scope: scopeRef.current,
      // 添加本地方法
      renderComponent: (node: any, params: any) =>
        renderNode(
          node,
          { ...renderContext, ...params },
          globalContext.customComponents,
        ),
    };

    // 前置渲染处理
    beforeRender(renderContext);

    // 渲染组件树
    const rendered = (
      <div
        className={`dynamic-renderer ${className}`}
        style={style}
        data-component-id={componentId}
        data-component-type="DynamicRenderer"
      >
        {config.components.map((component, index) =>
          renderNode(component, renderContext, globalContext.customComponents, {
            showErrors: true,
          }),
        )}
      </div>
    );

    // 后置渲染处理
    const finalRendered = afterRender(rendered) || rendered;

    return finalRendered;
  } catch (err) {
    log('error', `Error rendering DynamicRenderer (${componentId}):`, err);

    // 错误捕获处理
    const shouldPropagate = errorCaptured(err as Error, 'render');

    if (shouldPropagate !== false) {
      setError(err as Error);
    }

    return (
      <div className={`dynamic-renderer-error ${className}`} style={style}>
        <h4>渲染错误</h4>
        <pre>{(err as Error).message}</pre>
        <button type="button" onClick={() => setError(null)}>
          重试
        </button>
      </div>
    );
  }
};

export default DynamicRenderer;
