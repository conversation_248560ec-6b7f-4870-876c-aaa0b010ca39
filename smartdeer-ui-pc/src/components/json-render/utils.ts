import generate from '@babel/generator';
import * as parser from '@babel/parser';
import traverse from '@babel/traverse';
import * as t from '@babel/types';
import * as ts from 'typescript';
import { ComponentConfig, ConfType } from './typing';

export const validateJson = (json: any): string[] => {
  const errors: string[] = [];

  // 1. 判断 json 中是否存在 components 数组
  if (!json || !Array.isArray(json.components)) {
    errors.push("Error: 'components' must be an array.");
  } else {
    // 2. 判断 components 数组中每个节点是否符合 ComponentConfig 格式
    json.components.forEach((component: any, index: number) => {
      // 检查 component 是否有必需的属性
      if (typeof component.component !== 'string') {
        errors.push(
          `Error: 'component' is required and must be a string at index ${index}.`,
        );
      }

      // 检查 props 是否为对象类型
      if (
        component.props &&
        typeof component.props !== 'object' &&
        !component.props.includes('*{')
      ) {
        errors.push(`Error: 'props' should be an object at index ${index}.`);
      }

      // 检查 children 是否正确
      if (
        component.children &&
        !(
          Array.isArray(component.children) ||
          typeof component.children === 'string' ||
          typeof component.children === 'number'
        )
      ) {
        errors.push(
          `Error: 'children' should be an array, string, or number at index ${index}.`,
        );
      }

      // 3. 判断每个节点中，如果 children 属性在 props 中，而不是与 props 同级，提示错误
      if (component.props && component.props.children !== undefined) {
        errors.push(
          `Error: 'children' cannot be inside 'props' at index ${index}.`,
        );
      }
    });
  }

  return errors;
};

/**
 * 移除代码中的注释
 */
function removeComments(code: string): string {
  return (
    code
      // 移除单行注释
      .replace(/\/\/.*$/gm, '')
      // 移除多行注释
      .replace(/\/\*[\s\S]*?\*\//g, '')
      // 移除多余的空行
      .replace(/\n\s*\n/g, '\n')
      // 移除行首行尾空白
      .trim()
  );
}

/**
 * 使用 Babel 将函数体转为字符串
 */
export function convertFnToString(fn: (...args: any[]) => string) {
  try {
    // 获取函数源码
    const functionString = fn.toString();

    // 解析为 AST
    const ast = parser.parse(functionString, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript', 'objectRestSpread'],
    });

    // 提取函数参数和函数体
    let params: string[] = [];
    let body: string = '';

    traverse(ast, {
      FunctionDeclaration(path) {
        params = path.node.params.map((param) => {
          if (t.isIdentifier(param)) return param.name;
          return param.toString();
        });
        body = generate(path.node.body, {
          retainLines: true,
          compact: false,
          jsescOption: { minimal: true },
        }).code;
        path.stop();
      },
      FunctionExpression(path) {
        params = path.node.params.map((param) => {
          if (t.isIdentifier(param)) return param.name;
          return param.toString();
        });
        body = generate(path.node.body, {
          retainLines: true,
          compact: false,
          jsescOption: { minimal: true },
        }).code;
        path.stop();
      },
      ArrowFunctionExpression(path) {
        params = path.node.params.map((param) => {
          if (t.isIdentifier(param)) return param.name;
          return param.toString();
        });
        body = generate(path.node.body, {
          retainLines: true,
          compact: false,
          jsescOption: { minimal: true },
        }).code;
        path.stop();
      },
    });

    // 移除注释
    let bodyWithoutComments = removeComments(body);

    // 还原 _objectSpread2 及 webpack 产物
    bodyWithoutComments = bodyWithoutComments.replace(
      /(?:[_a-zA-Z0-9$]+_WEBPACK_IMPORTED_MODULE_\d+___default\(\)|_objectSpread2)\(\{\},\s*([^)]+)\)/g,
      (_, args: string) => {
        return `{${args
          .split(',')
          .map((s: string) => '...' + s.trim())
          .join(', ')}}`;
      },
    );

    // 还原 toConsumableArray (数组展开语法)
    bodyWithoutComments = bodyWithoutComments.replace(
      /(?:[_a-zA-Z0-9$]+_WEBPACK_IMPORTED_MODULE_\d+___default\(\)|toConsumableArray)\(([^)]+)\)/g,
      (_, args: string) => {
        return `[...${args.trim()}]`;
      },
    );

    // 构建箭头函数字符串
    const arrowFunction = `(${params.join(
      ', ',
    )}) => {${bodyWithoutComments.trim()}}`;

    // console.log('函数体->字符串转换结果:', arrowFunction);

    return arrowFunction;
  } catch (error: any) {
    console.warn('函数体->字符串转换失败', error.message);
  }
}

/**
 * 该方法已废弃，请使用 convertFnToString
 * 将函数转换为保留原始格式的字符串
 * @param {Function} fn 需要转换的函数
 * @returns {string} 保留原始格式的函数字符串
 */
export function convertFunctionToString(fn: (...args: any[]) => string) {
  try {
    const functionString = fn.toString();

    // 先移除注释
    const codeWithoutComments = removeComments(functionString);

    // 解析函数为 AST
    const ast = parser.parse(codeWithoutComments, {
      sourceType: 'module',
      plugins: ['jsx', 'typescript', 'objectRestSpread'],
    });

    // 遍历 AST 并转换
    traverse(ast, {
      CallExpression(path) {
        // 检查是否是 _objectSpread2 调用
        if (
          t.isIdentifier(path.node.callee) &&
          path.node.callee.name === '_objectSpread2'
        ) {
          // 获取参数
          const args = path.node.arguments;
          if (args.length >= 2) {
            // 创建对象表达式
            const properties = args.map((arg) => {
              if (
                t.isCallExpression(arg) &&
                t.isIdentifier(arg.callee) &&
                arg.callee.name === '_objectSpread2'
              ) {
                // 处理嵌套的 _objectSpread2 调用
                const nestedArgs = arg.arguments.map((nestedArg) =>
                  t.spreadElement(nestedArg),
                );
                return t.objectExpression(nestedArgs);
              }
              return t.spreadElement(arg);
            });

            // 替换为对象表达式
            path.replaceWith(t.objectExpression(properties));
          }
        }
      },
      // 处理 Webpack 模块引用
      MemberExpression(path) {
        if (
          t.isIdentifier(path.node.object) &&
          path.node.object.name.includes('WEBPACK_IMPORTED_MODULE')
        ) {
          path.replaceWith(t.identifier('_objectSpread2'));
        }
      },
    });

    const output = generate(
      ast,
      {
        retainLines: true,
        compact: false,
        jsescOption: {
          minimal: true,
        },
      },
      codeWithoutComments,
    );

    // 清理生成的代码
    const cleanedCode = output.code
      .replace(/^function\s*\(([^)]*)\)\s*\{([\s\S]*)\}$/m, (_, args, body) => {
        return `(${args}) => ${body.trim()}`;
      })
      .replace(/\s+/g, ' ')
      .trim();

    return cleanedCode;
  } catch (error) {
    // console.error('Error converting function to string:', fn.toString());
    return fn.toString();
  }
}

export function convertTsToJson(tsCode: string): string {
  try {
    // 使用 TypeScript 编译器 API 解析代码
    const sourceFile = ts.createSourceFile(
      'temp.ts',
      tsCode,
      ts.ScriptTarget.ES2015,
      true,
    );

    // 递归遍历 AST 并提取数据
    const extractData = (node: ts.Node): any => {
      if (ts.isObjectLiteralExpression(node)) {
        const result: any = {};
        node.properties.forEach((prop) => {
          if (ts.isPropertyAssignment(prop) && ts.isIdentifier(prop.name)) {
            result[prop.name.text] = extractData(prop.initializer);
          }
        });
        return result;
      } else if (ts.isArrayLiteralExpression(node)) {
        return node.elements.map(extractData);
      } else if (ts.isStringLiteral(node)) {
        return node.text;
      } else if (ts.isNumericLiteral(node)) {
        return Number(node.text);
      } else if (node.kind === ts.SyntaxKind.TrueKeyword) {
        return true;
      } else if (node.kind === ts.SyntaxKind.FalseKeyword) {
        return false;
      } else if (ts.isArrowFunction(node) || ts.isFunctionExpression(node)) {
        return node.getText(sourceFile);
      }
      return null;
    };

    // 提取导出的对象
    let exportedObject = null;
    ts.forEachChild(sourceFile, (node) => {
      if (
        ts.isExportAssignment(node) &&
        ts.isObjectLiteralExpression(node.expression)
      ) {
        exportedObject = extractData(node.expression);
      }
    });

    if (!exportedObject) {
      throw new Error('未找到导出的对象');
    }

    // 生成标准 JSON
    return JSON.stringify(exportedObject, null, 2);
  } catch (error) {
    throw new Error(`转换失败: ${error.message}`);
  }
}

export function resolveReferences(
  conf: ComponentConfig[],
  context: Record<string, ConfType>,
): any[] {
  return conf.flatMap((item) => {
    // 检查是否是对象且包含 $ref 字段
    if (
      item &&
      typeof item === 'object' &&
      !Array.isArray(item) &&
      '$ref' in item
    ) {
      const refKey = item.$ref as string;
      const refValue = context[refKey];
      if (refValue === undefined) {
        throw new Error(`引用的变量 ${refKey} 未在上下文中找到`);
      }
      // 递归处理引用值（确保引用值中的嵌套引用也被解析）
      return resolveReferences(
        (Array.isArray(refValue) ? refValue : [refValue]) as ComponentConfig[],
        context,
      );
    }

    // 如果是数组或对象，递归处理嵌套结构
    if (Array.isArray(item)) {
      return [resolveReferences(item, context)];
    } else if (item && typeof item === 'object') {
      const processedObj: Record<string, any> = {};
      for (const [key, value] of Object.entries(item)) {
        processedObj[key] = Array.isArray(value)
          ? resolveReferences(value, context)
          : typeof value === 'object'
          ? resolveReferences([value] as ComponentConfig[], context)[0]
          : value;
      }
      return [processedObj];
    }

    // 基础类型直接返回
    return [item];
  });
}

/**
 * 解析并合并配置
 * @param conf 配置入口文件，包含对其他配置(可以理解为子组件配置)的引用
 * @param context 配置上下文，包含所有可引用的配置
 * @param mixins 公共方法
 * @returns
 */
export function resolveConfig(
  conf: ConfType,
  context: Record<string, ConfType>,
  mixins: Record<string, any>[] = [],
) {
  const contextKeys = Object.keys(context) || [];
  let compsContext = {};
  let eventsContext: any = [];
  contextKeys.forEach((key) => {
    const item = context[key];
    if (item && typeof item === 'object') {
      compsContext = {
        ...compsContext,
        [key]: item.components,
      };
      eventsContext = [...eventsContext, ...item.events];
    }
  });

  const resolvedComps = resolveReferences(conf.components, compsContext);
  const newConf = {
    components: resolvedComps,
    events: [...mixins, ...conf.events, ...eventsContext],
  };
  newConf.events = newConf.events.map((item: any) => {
    return {
      ...item,
      code: convertFunctionToString(item.code),
    };
  });
  return {
    json: JSON.stringify({ components: newConf.components }),
    events: newConf.events,
  };
}

/**
 * 自定义的resolveConfig函数
 * 确保只引入在conf中通过$ref引用的conf的event
 */
export function resolveRefConfig(
  conf: any,
  context: Record<string, any>,
  mixins: Record<string, any>[] = [],
) {
  // 先创建components上下文
  const compsContext: Record<string, any> = {};
  Object.keys(context).forEach((key) => {
    if (context[key] && typeof context[key] === 'object') {
      compsContext[key] = context[key].components;
    }
  });

  // 查找conf.components中所有的$ref引用
  const usedRefs = new Set<string>();

  // 递归查找$ref引用
  function findRefs(item: any) {
    if (!item || typeof item !== 'object') return;

    if (!Array.isArray(item) && '$ref' in item) {
      usedRefs.add(item.$ref as string);
      return;
    }

    if (Array.isArray(item)) {
      item.forEach(findRefs);
    } else {
      Object.values(item).forEach(findRefs);
    }
  }

  // 遍历conf.components查找所有引用
  findRefs(conf.components);

  // 只收集被引用的context的events
  const eventsContext: any[] = [];
  usedRefs.forEach((refKey) => {
    const refConf = context[refKey];
    if (refConf && Array.isArray(refConf.events)) {
      eventsContext.push(...refConf.events);
    }
  });

  // 解析组件引用
  const resolvedComps = resolveReferences(conf.components, compsContext);

  // 合并配置
  const newConf = {
    components: resolvedComps,
    events: [...mixins, ...conf.events, ...eventsContext],
  };

  // 处理事件代码
  const processedEvents = newConf.events.map((item: any) => {
    if (item.code && typeof item.code === 'function') {
      return {
        ...item,
        code: convertFunctionToString(item.code),
      };
    }
    return item;
  });

  return {
    json: JSON.stringify({ components: newConf.components }),
    events: processedEvents,
  };
}

export function isTernary(expr: string): boolean {
  return expr.includes('?') && expr.includes(':');
}

/**
 * 将字符串格式的函数体转换为可执行的函数对象
 * @param codeString 函数体字符串，例如"(context) => { ... }"
 * @returns 可执行的函数
 */
export const stringToFunction = (
  codeString: string,
): ((...args: any[]) => any) => {
  try {
    // 处理箭头函数
    if (codeString.includes('=>')) {
      // 如果是箭头函数，需要构造完整表达式
      const funcExpression = `return ${codeString}`;
      // 使用Function构造器创建函数
      return new Function(funcExpression)();
    }
    // 处理普通函数
    else if (codeString.startsWith('function')) {
      return new Function(`return ${codeString}`)();
    }
    // 处理可能只有函数体部分的情况
    else {
      // 尝试推断参数
      const possibleArgsMatch = codeString.match(/^\s*\(([^)]*)\)\s*{/);
      if (possibleArgsMatch) {
        const args = possibleArgsMatch[1].split(',').map((a) => a.trim());
        return new Function(
          ...args,
          codeString
            .substring(codeString.indexOf('{') + 1)
            .replace(/}\s*$/, ''),
        ) as (...args: any[]) => any;
      }
      // 如果无法推断，假设是单参数的函数体
      const fn = new Function('context', codeString) as (...args: any[]) => any;
      return fn;
    }
  } catch (error) {
    console.error('无法解析函数字符串:', error);
    // 返回一个空函数防止错误
    return () => {
      console.error('函数解析失败');
    };
  }
};

export const processedEvents = (events: any[]) => {
  if (!events) {
    return [];
  }
  return events.map((item: any) => {
    if (item.code && typeof item.code === 'function') {
      return {
        ...item,
        code: convertFunctionToString(item.code),
      };
    }
    return item;
  });
};
