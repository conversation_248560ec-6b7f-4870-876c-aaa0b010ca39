export type DynamicFunction = {
  name: string;
  code: string;
};

export type EventType = {
  name: string;
  code: string | ((...args: any[]) => any);
};

export type JsonRenderProps = {
  json?: string;
  events?: Array<DynamicFunction>;
  methods?: Record<string, any>;
  customComponents?: Record<string, React.ComponentType<any>>;
  version?: string;
  consts?: Record<string, any>;
  props?: Record<string, any>;
  initialState?: Record<string, any>;
  confObject?: Record<string, any>;
  confEffect?: Record<string, any>;
  onLoaded?: () => void;
};

export type ComponentConfig = {
  component: string;
  props?: Record<string, any>;
  children?: ComponentConfig[] | ComponentConfig | string | number;
  key?: string | number;
};

export type RendererProps = {
  config: {
    components: ComponentConfig[];
  };
  methods?: Record<string, any>;
  customComponents?: Record<string, React.ComponentType<any>>;
};

export type ConfType = {
  components: ComponentConfig[];
  events: Array<EventType>;
  initialState?: Record<string, any>;
};
