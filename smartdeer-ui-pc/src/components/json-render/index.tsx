import { message } from 'antd';
import { isArray } from 'lodash';
import React, { useEffect, useState } from 'react';
import { JsonRenderProps } from './typing';
import { processedEvents } from './utils';
import Version1 from './version1';
import Version2 from './version2';
import { useCacheFetcher } from './version2/hooks/useFetcher';

export { JsonRenderProps };

const Page = ({
  version = '1',
  confObject,
  confEffect,
  ...restProps
}: JsonRenderProps) => {
  const { cacheFetcher, catchFetchLoading } = useCacheFetcher();
  const [json, setJson] = useState<string>('');
  const [events, setEvents] = useState<any[]>([]);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const fetchConfEffect = async () => {
    try {
      const { rs } = await cacheFetcher({
        ...confEffect,
      });
      const { json, innerScript } = rs;
      const script = JSON.parse(innerScript);
      setJson(json);
      setEvents(script);
    } catch (error) {
      setErrorMessage(
        `获取配置失败: [FUNCTION_KEY] ${confEffect?.functionKey} - [CONF_KEY] ${confEffect?.defaultParams?.confKey}`,
      );
      setJson('');
      setEvents([]);
    }
  };

  useEffect(() => {
    if (confEffect) {
      fetchConfEffect();
    }
  }, [confEffect]);

  if (!catchFetchLoading && confEffect && restProps.onLoaded) {
    restProps.onLoaded();
  }

  if (version === '1') {
    return <Version1 {...restProps} />;
  }

  if (version === '2') {
    if (confObject) {
      if (!confObject.components) {
        message.error('配置对象中缺少components');
        return;
      }
      if (confObject.events && !isArray(confObject.events)) {
        message.error('配置对象中events必须是数组格式');
        return;
      }

      const newJson = JSON.stringify({ components: confObject.components });
      const newEvents = processedEvents(confObject.events || []);
      return (
        <Version2
          {...restProps}
          json={newJson}
          events={newEvents}
          consts={{ ...(restProps.consts || {}), ...(confObject.consts || {}) }}
          initialState={{
            ...(restProps.initialState || {}),
            ...(confObject.initialState || {}),
          }}
        />
      );
    }
    if (!confEffect && restProps.json) {
      return <Version2 {...restProps} json={restProps.json} />;
    }
  }

  if (errorMessage) {
    return <div>{errorMessage}</div>;
  }

  return <Version2 {...restProps} json={json} events={events} />;
};

export default Page;
