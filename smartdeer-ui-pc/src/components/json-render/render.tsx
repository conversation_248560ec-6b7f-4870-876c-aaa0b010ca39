import * as icons from '@ant-design/icons';
import { <PERSON><PERSON><PERSON>or, JsonEditor, SchemaPage, Loading } from '@smartdeer-ui/pc';
import * as antd from 'antd';
import { message } from 'antd';
import React, { useContext } from 'react';
import pipe from '../../utils/pipe';
import QuillEditor from '../quill';
import QuillView from '../quill-view';
import Upload from '../schema-form/field-components/upload';
import ListItemFiles from '../schema-page/field-components/list-item-files';
import TableSearch from '../table-search';
import BatchActionModal from './components/batch-action-modal';
import DynamicRenderer from './components/dynamic-renderer';
import FilePreviewModal from './components/file-preview-modal';
import { JsonRenderContext } from './context';
import { ComponentConfig, RendererProps } from './typing';
import { isTernary, validate<PERSON><PERSON> } from './utils';
import DynamicTable from '../dynamic-table/table';
import CrudTable from '../crud-table/table';

const componentMap: Record<string, any> = {
  // antd 组件
  ...antd,
  ...icons,
  'Typography.Text': antd.Typography.Text,
  'Typography.Title': antd.Typography.Title,
  'Typography.Paragraph': antd.Typography.Paragraph,
  'Form.Item': antd.Form.Item,
  'Table.Column': antd.Table.Column,
  'Table.ColumnGroup': antd.Table.ColumnGroup,

  // smartdeer-ui 组件
  CodeEditor,
  SchemaFormUpload: Upload,
  SchemaPage: SchemaPage,
  TableSearch,
  Quill: QuillEditor,
  QuillView,
  ListItemFiles,
  DynamicTable,
  CrudTable,
  Loading,

  // 自定义组件
  JsonEditor,
  BatchActionModal,
  FilePreviewModal,
  DynamicRenderer,

  ...Object.entries(icons).reduce((acc, [name, IconComponent]) => {
    acc[`Icon${name}`] = IconComponent;
    return acc;
  }, {} as Record<string, any>),
};

const resolveContextPath = (path: string, context: any) => {
  return path.split('.').reduce((acc, part) => {
    if (part === 'context') return acc; // 跳过context前缀
    return acc?.[part];
  }, context);
};

const getPipeValue = (value: any, functionName: string, context: any) => {
  return pipe.render(value, [functionName], context);
};

const parseFunction = (fnStr: string, context: Record<string, any>) => {
  const funcName = fnStr.split(':')[1];

  if (context[funcName]) {
    return context[funcName];
  }
  if (fnStr.startsWith('fn:')) {
    try {
      return new Function(
        'React',
        'componentMap',
        'context',
        `
        return ${fnStr.slice(3).replace(/<(\w+)/g, (m, tag) => {
          return `React.createElement(componentMap.${tag} || '${tag}')`;
        })}
      `,
      )(React, componentMap, context);
    } catch (e) {
      // console.error('Function parse error:', e);
      return () => null;
    }
  }
  return fnStr;
};

export const renderNode = (
  node: ComponentConfig,
  context = {},
  customComponents = {},
): React.ReactNode => {
  try {
    const { component, props = {}, children, key } = node;

    if (props.vif !== undefined) {
      let replacedStr = props.vif.replace(/\*\{([^}]+)\}/g, (_, path) => {
        const value = resolveContextPath(path.trim(), context);
        if (typeof value === 'string') return `'${value.replace(/'/g, "\\'")}'`;
        if (value === null) return 'null';
        if (value === undefined) return 'undefined';
        return JSON.stringify(value);
      });

      try {
        // 求值判断是否应该渲染
        const shouldRender = new Function(`return ${replacedStr}`)();
        if (!shouldRender) {
          // 如果条件为false，直接返回null不渲染
          return null;
        }
      } catch (e) {
        console.error('vif expression evaluation error:', e);
        // 发生错误时，保守处理，继续渲染
      }
    }

    const deepProcessProps = (
      props: any,
      context: Record<string, any>,
    ): any => {
      if (Array.isArray(props)) {
        return props.map((item) => deepProcessProps(item, context));
      }

      if (props && typeof props === 'object') {
        return Object.keys(props).reduce((acc, key) => {
          acc[key] = deepProcessProps(props[key], context);
          return acc;
        }, {} as any);
      }

      if (typeof props === 'string') {
        // 处理上下文路径引用
        if (props.startsWith('*{') && props.endsWith('}')) {
          const path = props.slice(2, -1).trim();
          return resolveContextPath(path, context);
        }

        // 处理含有变量的三元表达式
        if (isTernary(props)) {
          let replacedStr = props.replace(/\*\{([^}]+)\}/g, (_, path) => {
            const value = resolveContextPath(path.trim(), context);
            // 转换为JS字面量
            if (typeof value === 'string')
              return `'${value.replace(/'/g, "\\'")}'`;
            if (value === null) return 'null';
            if (value === undefined) return 'undefined';
            return JSON.stringify(value);
          });
          try {
            return new Function(`return ${replacedStr}`)();
          } catch (e) {
            console.error('Ternary expression evaluation error:', e);
            return replacedStr;
          }
        }

        // 处理函数字符串
        if (props.startsWith('fn:')) {
          try {
            return parseFunction(props, context);
          } catch (e) {
            console.error('Function parse error:', e);
            return () => null;
          }
        }

        if (props.startsWith('icon:')) {
          try {
            const IconComponent = componentMap[props.slice(5)];
            return <IconComponent />;
          } catch (e) {
            console.error('Icon parse error:', e);
          }
        }
      }

      return props;
    };

    const processedProps = deepProcessProps(props, context);

    // 递归处理子节点（关键修改）
    const renderChildren = (
      child: ComponentConfig['children'],
    ): React.ReactNode => {
      if (Array.isArray(child)) {
        return child.map((c) =>
          renderNode(c as ComponentConfig, context, customComponents),
        );
      }
      if (typeof child === 'object' && child !== null) {
        return renderNode(child as ComponentConfig, context, customComponents);
      }
      if (typeof child === 'string' && child.startsWith('fn:')) {
        try {
          return parseFunction(child, context);
        } catch (e) {
          console.error('Function parse error:', e);
        }
      }
      if (typeof child === 'string' && child.startsWith('icon:')) {
        const IconComponent = componentMap[child.slice(5)];
        return <IconComponent />;
      }
      if (typeof child === 'string' && child.includes('*{')) {
        const regex = /\*{(.*?)(\|([^}]*))?}/g; // 匹配 *{path|function()}

        // 使用 replace 来替换所有匹配的部分
        return child.replace(regex, (match, path, _, functionName) => {
          // 从 context 中解析路径
          let value = resolveContextPath(path, context) || '-';

          // 如果有函数名（例如 format()），则执行相应的格式化
          if (functionName) {
            // value = pipe.render(value, [functionName], context);
            value = getPipeValue(value, functionName, context);
          }

          return value;
        });
      }

      return child;
    };

    let Component = component
      ? component
        .split('.')
        .reduce((acc, part) => acc?.[part], componentMap as any)
      : 'div';

    if (
      customComponents &&
      component &&
      customComponents[component as keyof typeof customComponents]
    ) {
      Component = customComponents[component as keyof typeof customComponents];
    }

    if (!Component && document.createElement(component)) {
      return React.createElement(
        component,
        { ...processedProps, key: key || JSON.stringify(node) },
        renderChildren(children),
      );
    }

    if (!Component) {
      console.warn(`Component ${component} not found`);
      return null;
    }

    return (
      <Component key={key || JSON.stringify(node)} {...processedProps}>
        {renderChildren(children)
          ? renderChildren(children)
          : processedProps?.children}
      </Component>
    );
  } catch (error) {
    console.error('Render error:', error);
    return null;
  }
};

const JsonRender: React.FC<RendererProps> = ({
  config,
  methods,
  customComponents,
}) => {
  const jsonContext = useContext(JsonRenderContext);
  const context = { ...jsonContext, ...methods };

  const errors = validateJson(config);
  if (errors.length > 0) {
    message.error(errors.join('\n'));
    return null;
  }

  return (
    <>
      {config.components?.map?.((node: ComponentConfig, index: number) => {
        return (
          <React.Fragment key={index}>
            {renderNode(node, context, customComponents)}
          </React.Fragment>
        );
      })}
    </>
  );
};

export default JsonRender;
