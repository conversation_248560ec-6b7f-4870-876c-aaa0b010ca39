import { JsonRender } from '../version2';
import React from 'react';

const EventsExample: React.FC = () => {
  const json = {
    components: [
      {
        component: 'div',
        props: {
          className: 'events-example',
          style: {
            padding: '20px',
            border: '1px solid #eee',
            borderRadius: '4px',
          },
        },
        children: [
          {
            component: 'Typography.Title',
            props: {
              level: 2,
            },
            children: '事件处理示例',
          },
          {
            component: 'div',
            props: {
              style: {
                display: 'flex',
                gap: '10px',
                marginTop: '10px',
              },
            },
            children: [
              {
                component: 'Input',
                props: {
                  type: 'text',
                  placeholder: '请输入内容',
                  onChange: 'fn:handleInputChange',
                  value: '*{inputValue}',
                },
              },
              {
                component: 'Button',
                props: {
                  onClick: 'fn:handleClick',
                },
                children: '点击我',
              },
            ],
          },
          {
            component: 'div',
            props: {
              style: {
                marginTop: '10px',
              },
            },
            children: [
              {
                component: 'p',
                props: {},
                children: '输入值: *{inputValue}',
              },
              {
                component: 'p',
                props: {},
                children: '点击次数: *{clickCount}',
              },
            ],
          },
        ],
      },
    ],
  };

  const methods = {
    handleInputChange: (props: any, context: any) => {
      const value = props.target.value;
      context.setState({ inputValue: value });
    },
    handleClick: (props: any, context: any) => {
      const currentCount = context.state.clickCount || 0;
      context.setState({ clickCount: currentCount + 1 });
    },
  };

  return (
    <JsonRender
      json={json}
      methods={methods}
      options={{
        showErrors: true,
      }}
    />
  );
};

export default EventsExample;
