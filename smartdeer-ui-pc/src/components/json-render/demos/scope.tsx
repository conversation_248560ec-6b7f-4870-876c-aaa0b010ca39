import React from 'react';
import { JsonRender } from '../version2';

const ScopeExample: React.FC = () => {
  const json = {
    components: [
      {
        component: 'div',
        props: {
          className: 'scope-example',
          style: {
            padding: '20px',
            border: '1px solid #eee',
            borderRadius: '4px',
          },
        },
        children: [
          {
            component: 'Typography.Title',
            props: {
              level: 2,
            },
            children: '作用域示例',
          },
          {
            component: 'div',
            props: {
              style: {
                marginBottom: '20px',
                padding: '10px',
                border: '1px solid #ddd',
              },
            },
            children: [
            ],
          },
        ],
      },
    ],
  };

  const methods = {
    onInitialize: (context: any) => {
      context.setState({
        sharedData: '初始共享数据',
        localData: '初始本地数据',
      });
    },
    updateLocalData: (props: any, context: any) => {
      const newValue = `更新后的本地数据 ${Date.now()}`;
      // 使用全局状态更新
      context.setState({ localData: newValue });
    },
    updateShareData: (props: any, context: any) => {
      const newValue = `更新后的共享数据 ${Date.now()}`;
      // 使用全局状态更新
      context.setState({ sharedData: newValue });
    },
  };

  return <JsonRender json={json} methods={methods} />;
};

export default ScopeExample;
