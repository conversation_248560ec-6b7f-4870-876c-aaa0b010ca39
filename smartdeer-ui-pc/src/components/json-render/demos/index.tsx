import React from 'react';
import { Tabs } from 'antd';
import BasicExample from './basic';
import LifecycleExample from './lifecycle';
import ScopeExample from './scope';
import EventsExample from './events';

const JsonRenderDemos: React.FC = () => {
  const items = [
    {
      key: 'basic',
      label: '基础示例',
      children: <BasicExample />,
    },
    {
      key: 'lifecycle',
      label: '生命周期',
      children: <LifecycleExample />,
    },
    {
      key: 'scope',
      label: '作用域',
      children: <ScopeExample />,
    },
    {
      key: 'events',
      label: '事件处理',
      children: <EventsExample />,
    },
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{marginBottom:'20px'}}>JsonRender 示例</h1>
      <Tabs defaultActiveKey="basic" destroyInactiveTabPane items={items} />
    </div>
  );
};

export default JsonRenderDemos;