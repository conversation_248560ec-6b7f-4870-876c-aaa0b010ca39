import { JsonRender } from '../version2';
import React from 'react';

const LifecycleExample: React.FC = () => {
  const json = {
    components: [
      {
        component: 'div',
        props: {
          className: 'lifecycle-example',
          style: {
            padding: '20px',
            border: '1px solid #eee',
            borderRadius: '4px',
          },
        },
        lifecycle: {
          beforeRender: 'handleBeforeRender',
          afterRender: 'handleAfterRender',
        },
        children: [
          {
            component: 'h2',
            props: {},
            children: '生命周期示例',
          },
          {
            component: 'p',
            props: {},
            children: '这个组件展示了生命周期钩子的使用',
          },
        ],
      },
    ],
  };

  const methods = {
    handleBeforeRender: (props: any, context: any) => {
      console.log('节点渲染前:', props, context);
    },
    handleAfterRender: (props: any, context: any) => {
      console.log('节点渲染后:', props, context);
    },
    onInitialize: (context: any) => {
      console.log('组件初始化:', context);
    },
    onRenderComplete: (context: any) => {
      console.log('组件渲染完成:', context);
    },
  };

  return (
    <JsonRender
      json={json}
      methods={methods}
      options={{
        showErrors: true,
      }}
    />
  );
};

export default LifecycleExample;
