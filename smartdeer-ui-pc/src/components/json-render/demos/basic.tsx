import { JsonRender } from '../version2';
import React from 'react';

const BasicExample: React.FC = () => {
  const json = {
    components: [
      {
        component: 'div',
        props: {
          className: 'basic-example',
          style: {
            padding: '20px',
            border: '1px solid #eee',
            borderRadius: '4px',
          },
        },
        children: [
          {
            component: 'h2',
            props: {},
            children: '基础示例',
          },
          {
            component: 'p',
            props: {},
            children: '这是一个基础的 JsonRenderer 示例',
          },
        ],
      },
    ],
  };

  return <JsonRender json={json} />;
};

export default BasicExample;
