import { Form, message } from 'antd';
import dayjs from 'dayjs';
import { isArray, isEmpty, isString } from 'lodash';
import React, { useEffect, useMemo, useReducer, useState } from 'react';
import { EffectType } from '../../typing';
import { JsonRenderContext } from './context';
import { useDynamicFunctions, useFetcher, useJsonFetcher } from './hooks';
import JsonRender, { renderNode } from './render';
import { JsonRenderProps } from './typing';

export { JsonRenderProps };

const Page = ({ json, events, methods, customComponents = {} }: JsonRenderProps) => {
  const [tabKey, setTabKey] = useState('');
  const [editFormData, setEditFormData] = useState<any>({});
  const [tableData, setTableData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [confLoading, setConfLoading] = useState(false);

  const [pagination, setPagination] = useState<any>({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条`,
  });
  const [filterParams, setFilterParams] = useState<any>({});
  const [modalOpen, setModalOpen] = useState(false);
  const [modalData, setModalData] = useState<any>({});
  // 新增，编辑，详情侧边抽屉
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerData, setDrawerData] = useState<any>({});

  // 依赖数据
  const [dependencies, setDependencies] = useState<any>({});

  // 其他数据
  const [extraData, setExtraData] = useState<any>({});

  const [form] = Form.useForm();
  const [refetchList, setRefetchList] = useReducer((x) => x + 1, 0);

  const [isPageInit, setIsPageInit] = useState(false);

  const { fetcher } = useFetcher();

  const { jsonFetcher } = useJsonFetcher();

  const [state, setState] = useState<any>({})

  const handleTableAction = async (
    actionType: string,
    record: Record<string, any>,
    effect: EffectType,
  ) => {
    if (actionType === 'edit') {
      form.setFieldsValue(record);
      setDrawerOpen(true);
      setDrawerData(record);
    }
    if (actionType === 'delete') {
      try {
        await jsonFetcher(effect);
        setRefetchList();
      } catch (e: any) {
        message.error(e.message);
      }
    }
  };

  const fetchDependencies = async (
    dependenciesData: Array<{
      key: string;
      effect: EffectType;
    }>,
  ) => {
    let res = await Promise.all(
      dependenciesData.map(({ key, effect }) => {
        return fetcher(effect).then((res) => {
          if (isArray(res) && res.length > 0 && isString(res[0])) {
            return {
              key,
              data: res.map((item: string) => ({ label: item, value: item })),
            };
          }
          return {
            key,
            data: res,
          };
        });
      }),
    );

    const deps = { ...dependencies };
    res.forEach((item: any) => {
      deps[item.key] = item.data;
    });

    setDependencies(deps);

    return res;
  };

  const context: Record<string, any> = {
    state,
    setState,
    setState: (data: any, replace = false) => {
      if (replace) {
        setState({
          ...data,
        });
        return;
      }
      setState({
        ...state,
        ...data,
      });
    },
    fetcher: jsonFetcher,
    fetchDependencies,
    loading,
    setLoading: (loading: boolean) => {
      setLoading(loading);
    },
    confLoading,
    setConfLoading,
    tabKey,
    setTabKey: (key: string) => {
      setTabKey(key);
    },
    form,
    editFormData,
    setEditFormData,
    tableData,
    setTableData: (newData: any) => {
      setTableData(newData);
    },
    pagination,
    setPagination: (newData: any) => {
      setPagination({
        ...pagination,
        ...newData,
      });
    },
    filterParams,
    setFilterParams: (newData: any) => {
      setFilterParams({
        ...newData,
      });
    },
    handleTableSearch: (newData: any) => {
      setFilterParams({
        ...newData,
      });
      setPagination({
        ...pagination,
        current: 1,
      });
    },
    modalOpen,
    setModalOpen: (open: boolean) => {
      setModalOpen(open);
    },
    modalData,
    setModalData,
    drawerOpen,
    setDrawerOpen: (open: boolean) => {
      setDrawerOpen(open);
    },
    drawerData,
    setDrawerData: (data: any, replace = false) => {
      if (replace) {
        setDrawerData({
          ...data,
        });
        return;
      }
      setDrawerData({
        ...drawerData,
        ...data,
      });
    },
    extraData,
    setExtraData: (data: any, replace = false) => {
      if (replace) {
        setExtraData({
          ...data,
        });
        return;
      }
      setExtraData({
        ...extraData,
        ...data,
      });
    },
    dependencies,
    setDependencies: (data: any) => {
      setDependencies({
        ...dependencies,
        ...data,
      });
    },
    handleTableAction,
    handleSearch: () => {},
    setRefetchList,
    handleOpenEditDrawer: () => {
      form.resetFields();
      setDrawerOpen(true);
      setDrawerData({});
    },
    handleCloseEditDrawer: () => {
      form.resetFields();
      setDrawerOpen(false);
      setDrawerData({});
    },
    handleResetForm: () => {
      form.resetFields();
      form.setFieldsValue({});
    },
    handleTableChange: (pagination: any, filters: any, sorter: any) => {
      setPagination({
        ...pagination,
      });
    },

    dateNormalize: (value: any) => value && `${dayjs(value).valueOf()}`,

    getDateValueProps: (value: any) => ({
      value: value && dayjs(Number(value)),
    }),

    renderComponent: (node: any, params: any) => {
      return renderNode(node, {
        ...context,
        ...params,
      });
    },
  };

  const { funcs } = useDynamicFunctions(events, context);

  const eventMap = useMemo(() => {
    if (!isEmpty(funcs)) {
      return funcs;
    }
    if (!isEmpty(methods)) {
      return methods;
    }
    return {};
  }, [funcs, methods]);

  const jsonContext = useMemo(() => {
    if (!isEmpty(funcs)) {
      return {
        ...context,
        ...funcs,
      };
    }
    if (!isEmpty(methods)) {
      return {
        ...methods,
      };
    }
  }, [context, funcs, methods]);

  const isFetchListExist = useMemo(() => {
    return !!funcs?.fetchList;
  }, [funcs?.fetchList]);

  useEffect(() => {
    if (!funcs?.fetchList) return;

    funcs?.fetchList({
      current: pagination.current,
      size: pagination.pageSize,
      limit: pagination.pageSize,
      pagination,
      filterParams,
    });
  }, [
    isFetchListExist,
    pagination.current,
    pagination.pageSize,
    refetchList,
    filterParams,
  ]);

  useEffect(() => {
    if (!funcs?.initPage || isPageInit) return;
    funcs?.initPage();
    setIsPageInit(true);
  }, [funcs?.initPage, isPageInit]);

  return (
    <JsonRenderContext.Provider
      value={{
        ...jsonContext,
        form,
      }}
    >
      <JsonRender config={JSON.parse(json)} methods={eventMap} customComponents={customComponents} />
    </JsonRenderContext.Provider>
  );
};

export default Page;
