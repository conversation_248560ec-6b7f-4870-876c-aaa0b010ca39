import {
  CheckCircleOutlined,
  FunctionOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>hain } from '@ant-design/x';
import { useGetState } from 'ahooks';
import { Collapse } from 'antd';
import { isArray } from 'lodash';
import React, { useEffect } from 'react';
import CrudTable from '../../crud-table';
import DynamicTable from '../../dynamic-table';
import JsonRender from '../../json-render';

type WorkflowProps = {
  items: { function_key: string; params: Record<string, string | number> }[];
  history?: boolean;
  share?: boolean;
  fieldNames?: {
    function_key: string;
    params: string;
    data: string;
  };
  onComplete?: () => void;
};

interface DataItem {
  title: string | React.ReactNode;
  status: 'success' | 'pending';
  description?: string | React.ReactNode;
  icon: JSX.Element;
  content?: string;
}

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

function Table(props: {
  data: any[];
  title?: string;
  style?: React.CSSProperties;
}) {
  const { data, title, style } = props;

  return (
    <>
      {title && (
        <div style={{ fontWeight: 'bold', marginBottom: '4px', ...style }}>
          {title}:
        </div>
      )}
      <table
        style={{
          border: '1px solid #f0f0f0',
          width: 'object-content',
          borderCollapse: 'collapse',
          background: 'rgba(255,255,255,0.8)',
          borderRadius: '4px',
          marginTop: '4px',
        }}
      >
        <tbody>
          {data.map((item, index) => (
            <tr key={index} style={{ borderBottom: '1px solid #f0f0f0' }}>
              <td style={{ padding: '8px', color: '#666' }}>{item.label}:</td>
              <td style={{ padding: '8px' }}>{item.children}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </>
  );
}

let tempApi = 1

export default function Workflow(props: WorkflowProps) {
  const {
    items = [],
    history = false,
    onComplete,
    fieldNames = {
      function_key: 'function_key',
      params: 'params',
      data: 'data',
    },
  } = props;

  console.log("items", items);

  const [list, setList] = useGetState<DataItem[]>([]);
  console.log("list", list);
  

  const startGenerating = async (items: any) => {
    // setList([]);

    if (items.length <= list.length) {
      return;
    }

    const startIndex = list.length;

    let newItems: any = [...items].slice(startIndex);

    newItems = [...newItems].map((item) => {
      if (item.type === 'workflow') {
        return {
          title: item.text,
          status: 'pending',
          icon: <LoadingOutlined />,
        };
      }
      if (item.type === 'api') {
        // 将参数转换为描述列表格式
        const paramsList: any[] = Object.entries(item[fieldNames.params]).map(
          ([key, value]) => ({
            label: key,
            children: value,
          }),
        );
        const formattedOutput = item[fieldNames.params] && (
          <Table data={paramsList} />
        );
        return {
          title: '正在请求函数',
          description: (
            <div>
              <div>
                <FunctionOutlined />
                {`: ${item[fieldNames.function_key]}`}
                {formattedOutput}
              </div>
            </div>
          ),
          status: 'pending',
          icon: <LoadingOutlined />,
        };
      }
      if (item.type === 'crudTable') {
        const dataSource = isArray(item.rs) ? item.rs : item.dataSource;
        const columns = item.conf;
        return {
          title: '已获取数据，结果如下：',
          description: (
            <div style={{marginTop: '10px'}}>
              <Collapse
              items={[
                {
                  key: '1',
                  label: '查看数据',
                  children: (
                    <CrudTable dataSource={dataSource} columns={columns} />
                  ),
                },
              ]}
            />
            </div>
          ),
          status: 'pending',
          icon: <LoadingOutlined />,
        };
      }

      if (item.type === 'dynamicTable') {
        console.log("item", item);
        
        return {
          title: '已获取数据，结果如下：',
          description: (
            <div style={{marginTop: '10px'}}>
              <Collapse
              items={[
                {
                  key: '1',
                  label: '查看数据',
                  children: (
                    <DynamicTable tableConf={item.tableConf} />
                  ),
                },
              ]}
            />
            </div>
          ),
          status: 'pending',
          icon: <LoadingOutlined />,
        };
      }  
      
      if (item.type === 'jsonRender') {
        console.log("item", item);
        return {
          title: '已获取数据，结果如下：',
          description: (
            <div style={{marginTop: '10px'}}>
              <Collapse
              items={[
                {
                  key: '1',
                  label: '查看数据',
                  children: (
                    <JsonRender
                    json={item.json}
                    events={[]}
                  />
                  ),
                },
              ]}
            />
            </div>
          ),
          status: 'pending',
          icon: <LoadingOutlined />,
        };
      }

    });

    for (let index = 0; index < newItems.length; index++) {
      const item = newItems[index];
      const newItem: any = {
        ...item,
      };

      // 插入新的 pending 数据
      setList((prevList) => {
        return [...prevList, newItem]
      });

      // 等待 1 秒后更新为 success
      await sleep(1000); // 延迟 500ms

      setList((prevList) =>
        prevList.map((item, i) => {
          return {
            ...item,
            status: 'success',
            icon: <CheckCircleOutlined />,
          };
        }),
      );

      // 等待 1 秒
      await sleep(500); // 延迟 500ms
    }
    onComplete?.();
  };

  useEffect(() => {
    // setTimeout(
    //   () => {
    //     startGenerating();
    //   },
    //   history ? 0 : 1000,
    // );
    startGenerating([...items]);
  }, [JSON.stringify(items)]);

  return <ThoughtChain size="small" items={list} />;
}
