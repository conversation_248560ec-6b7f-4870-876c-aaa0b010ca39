interface People {
  uuid: string;
  name?: string;
}

interface Api {
  ai: {
    workflow: (params: { apiKey: string; params: { prompt: string } }) => Promise<{ text: string }>;
  };
  corehr: {
    mcp: {
      getEorModuleGroups: (userUuid: string, entityId: string) => Promise<any[]>;
      getModuleMcpConfigList: (moduleKey: string, entityId: string, aiPeopleUuid: string) => Promise<any[]>;
    };
  };
}

interface RecognizeIntentParams {
  people: People;
  message: string;
  entityId: string;
  apiKey: string;
}

const extractJsonFromText = (text: string): string | null => {
  const match = text.match(/({[\s\S]*}|\[[\s\S]*])/);
  return match?.[1] ?? null;
};

const callAiAndParseJson = async (
  prompt: string,
  apiKey: string,
  errorHint: string,
  api: Api
): Promise<any> => {
  const res = await api.ai.workflow({apiKey, params: {prompt}});

  console.log("res", res);
  
  const jsonStr = extractJsonFromText(res.text);

  console.log("jsonStr", jsonStr);

  if (!jsonStr) throw new Error(`${errorHint}：AI 返回内容中未找到 JSON 数据`);

  try {
    return JSON.parse(jsonStr);
  } catch (error) {
    throw new Error(`${errorHint}：JSON 解析失败 - ${error}`);
  }
};

const generateAiPrompt = (message: string, data: any[], field: string): string => {
  return `根据关键词【${message}】，在这组数据${JSON.stringify(data)}，根据每项的 ${field} 字段，找出最符合意图的一条记录，不要返回mcp对象，只返回该记录的完整 JSON 数据`;
  // return `根据关键词【${message}】，在这组数据${JSON.stringify(data)}中，分析每项的 ${field} 字段，判断其功能类型（填写表单或查询记录）。如果用户意图是提交申请（如报销、请假等），优先返回填写表单类型的数据；如果用户意图是查看记录（如报销记录、请假记录等），优先返回查询记录类型的数据。请避免仅凭关键词匹配，确保返回的记录与用户意图一致。不要返回mcp对象，只返回该记录的完整 JSON 数据。`;
};

export default async function resolveIntentToConfig({people, message, apiKey, entityId, }: RecognizeIntentParams, api: Api): Promise<any> {
  try {
    const moduleGroups = await api.corehr.mcp.getEorModuleGroups(people.uuid, entityId);

    console.log("moduleGroups", moduleGroups)

    if (!Array.isArray(moduleGroups) || moduleGroups.length === 0) {
      throw new Error('当前用户未授权访问任何模块组，请检查用户权限或数据配置');
    }

    const matchedModuleGroup = await callAiAndParseJson(
      generateAiPrompt(message, moduleGroups, 'moduleDesc'),
      apiKey,
      '模块组识别失败',
      api
    );

    if (!matchedModuleGroup?.moduleKey) {
      throw new Error('模块组识别失败：缺少 moduleKey');
    }

    const moduleConfigs = await api.corehr.mcp.getModuleMcpConfigList(matchedModuleGroup.moduleKey, entityId, people.uuid);

    console.log("moduleConfigs", moduleConfigs)

    if (!Array.isArray(moduleConfigs) || moduleConfigs.length === 0) {
      throw new Error('模块组配置列表为空，请检查配置数据');
    }

    const matchedModuleConfig = await callAiAndParseJson(
      generateAiPrompt(message, moduleConfigs, 'mcpDesc'),
      apiKey,
      '模块配置识别失败',
      api
    );

    console.log("matchedModuleConfig", matchedModuleConfig);
    

    // if (!matchedModuleConfig?.uuid) {
    //   throw new Error('模块配置识别失败：缺少 uuid');
    // }

    // 获取最终匹配的模块配置详情
    // const finalMatchedConfig = await api.corehr.mcp.getMcpConfigInfo(matchedModuleConfig.uuid, entityId);
    // console.log('finalMatchedConfig', finalMatchedConfig);

    return {
      role: "match",
      response: matchedModuleConfig
    };
  } catch (err: any) {
    console.log('1', err);
    throw new Error(`识别意图失败：${err.message || err}`);
  }
}
