import { useAsyncEffect } from 'ahooks';
import React from 'react';
import { useGetFetchApi } from "../../../utils/hooks";
import swrFetcher from '../../../utils/swrFetcher';
import <PERSON>sonR<PERSON> from '../../json-render';
import MCPWorkflow from "../mcp-workflow";
import DynamicTable from "../../dynamic-table";
import DynamicForm from "../../dynamic-form";

export default function MatchView(props: any) {
  const {bubble} = props;
  const {response} = bubble;

  const {functionApi} = useGetFetchApi()

  const [data, setData] = React.useState<any>(null); // 错误信息

  useAsyncEffect(async () => {
    if (response?.mcpKey) {
      const res = await swrFetcher(functionApi, 'POST', {
        functionKey: 'x_gs_get_page_conf_by_mcp',
        params: {
          mcpKey: response.mcpKey,
        },
      });
      const data = res.data
      setData(data);
    }
  }, []);

  const renderContent = (configType: string) => {

    switch (configType) {
      case 'SERVICE_ABILITY': {
        const config = JSON.parse(data.json);

        if(config.page?.form) {
          return <DynamicForm type={"add"} confObject={config.page} />
        }

        return <DynamicTable tableConfObject={config.page} />
      }
      case 'MCP_CONFIG':
        return <MCPWorkflow localServer={{}} workflowParams={{
          apiKey: 'app-bOyM1nngb36Ef0EHTqjkg1ay',
          params: JSON.parse(data.mcpConfig),
        }} {...props.mcpProps} />
      default:
        return <JsonRender
          {...props?.jsonRenderProps}
          json={data.json}
          events={data.innerScript && data.innerScript.length > 0 ? JSON.parse(data.innerScript) : []}
        />
    }
  }

  return (
    <>
      {data && renderContent(data.configType)}
    </>
  );
}
