import React from 'react';
import ChatContent from '../../content';

// export default function Chat(props: { guide?: React.ReactNode }) {
//   const { guide } = props;
//   return (
//     <div
//       className="w-full flex flex-col overflow-hidden"
//       style={{ height: 'calc(100vh - 120px)', background: '#fff', width: "100%" }}
//     >
//       <div className="flex-1 overflow-y-auto w-full" style={{padding: '24px'}}>{guide}</div>
//       <div className="h-[50px] w-full"></div>
//     </div>
//   );
// }


interface ChatProps {
  guide?: React.ReactNode;
  people: {
    uuid: string;
    name: string;
    avatar?: string;
    type?: string;
    signature?: string;
    promptsItems?: Array<{content: string}>;
    vision?: boolean;
    steam?: boolean;
  };
  api: {
    ai: {
      historyChats: (params: {size: number; peopleUuid: string}) => Promise<any[]>;
      historyChat: (uuid: string) => Promise<any>;
      initClient: () => any;
      chat: (conversationId: string, content: any) => Promise<any>;
      newChat: (uuid: string) => Promise<{chatUuid: string}>;
      mark: (uuid: string, status: 'good' | 'bad') => Promise<void>;
      getShare: (id: string) => Promise<{uuid: string}>;
    }
  }
}


export default function Chat(props: ChatProps) {

  const {api, people} = props

  return (
    <>
      {people && <ChatContent api={api} people={people} />}
    </>
  )
}
