import {
  ArrowRightOutlined,
  CalendarOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { Button, Card, Space, Typography } from 'antd';
import React, { useState } from 'react';

const { Text, Paragraph } = Typography;

const aiBlocks = [
  {
    image:
      'https://global-image.smartdeer.work/p/images/0x8b1f5eca688a4e1ebf8d808572373a01.png',
    title: '工资单',
    desc: '查询工资单、分析薪资构成等。',
    actions: [
      { text: '查看最新工资单', prompt: '帮我查看最新的工资单' },
      { text: '了解薪资构成', prompt: '帮我分析我的薪资构成' },
    ],
    enterPrompt: '进入工资单模块',
  },
  {
    image: "https://global-image.smartdeer.work/test/images/0x50fbac57985e44a5aaafe10ef968fce7.png",
    
    title: '请假',
    desc: '查看假期余额、提交请假申请等。',
    actions: [
      { text: '查看休假余额', prompt: '我想查看我的休假余额' },
      { text: '申请请假', prompt: '我要申请请假' },
    ],
    enterPrompt: '进入请假模块',
  },
  {
    image: "https://global-image.smartdeer.work/test/images/0x9a6ab754b18a4d49bc8a257965058f48.png",
    title: '报销',
    desc: '了解报销流程、提交报销申请等。',
    actions: [
      { text: '了解报销流程', prompt: '请介绍一下报销流程' },
      { text: '提交报销申请', prompt: '我要提交报销申请' },
    ],
    enterPrompt: '进入报销模块',
  },
];

export default function Guide({
  onClose,
  onSelectTopic,
}: {
  onClose: () => void;
  onSelectTopic: (topic: string) => void;
}) {
  const [step, setStep] = useState(0);

  React.useEffect(() => {
    if (step < aiBlocks.length) {
      const timer = setTimeout(() => setStep(step + 1), 600);
      return () => clearTimeout(timer);
    }
  }, [step]);

  return (
    <div
      className="bg-[#f7f9fa] rounded-2xl px-7 py-7 relative overflow-hidden"
      style={{ width: '640px' }}
    >
      {/* <div className="flex items-center mb-2">
        <SmileOutlined className="text-2xl text-blue-400  mr-2" />
        <Text strong className="text-base text-blue-700">{``}</Text>
      </div> */}
      <Paragraph className="text-gray-700 text-sm mb-4">
        👋
        欢迎来到智能服务！请选择下方的业务模块或快捷操作，轻松开启您的专属服务体验。
      </Paragraph>
      <Space direction="vertical" size={16} className="w-full">
        {aiBlocks.slice(0, step + 1).map((block, idx) => (
          <Card
            key={idx}
            size="small"
            bordered={false}
            className="transition-all hover:shadow-lg group"
            style={{
              background: '#fff',
              borderRadius: 16,
              boxShadow: '0 2px 8px #e6eaf1',
              cursor: 'pointer',
            }}
            bodyStyle={{ padding: '18px 20px' }}
          >
            <div className="flex">
              <div
                className="flex items-center justify-center mr-4 mt-[8px]"
                style={{ height: 32, width: 32 }}
              >
                {block.image ? <img src={block.image} /> : block.icon}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <Text strong className="text-base">
                    {block.title}
                  </Text>
                  <Button
                    type="text"
                    shape="circle"
                    icon={<ArrowRightOutlined />}
                    className="ml-2 transition-all group-hover:bg-blue-50"
                    onClick={() => {
                      onSelectTopic(block.enterPrompt);
                      onClose();
                    }}
                  />
                </div>
                <Paragraph
                  className="text-xs text-gray-500 mb-2 mt-1"
                  style={{ marginBottom: 6 }}
                >
                  {block.desc}
                </Paragraph>
                <Space>
                  {block.actions.map((action, i) => (
                    <Button
                      key={i}
                      // type="primary"
                      // ghost
                      size="small"
                      shape="round"
                      className="border-blue-200 text-blue-600 hover:bg-blue-100"
                      onClick={() => {
                        onSelectTopic(action.prompt);
                        onClose();
                      }}
                    >
                      {action.text}
                    </Button>
                  ))}
                </Space>
              </div>
            </div>
          </Card>
        ))}
      </Space>
    </div>
  );
}
