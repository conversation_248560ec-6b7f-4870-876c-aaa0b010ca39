import React from 'react';

export default function Chat(props: { guide?: React.ReactNode }) {
  const { guide } = props;
  return (
    <div
      className="w-full flex flex-col overflow-hidden"
      style={{ height: 'calc(100vh - 120px)', background: '#fff', width: "100%" }}
    >
      <div className="flex-1 overflow-y-auto w-full" style={{padding: '24px'}}>{guide}</div>
      <div className="h-[50px] w-full"></div>
      123
    </div>
  );
}
