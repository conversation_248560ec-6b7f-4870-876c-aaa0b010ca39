import React, {useEffect} from "react";
import {CloudUploadOutlined, LinkOutlined} from '@ant-design/icons';
import {Attachments, AttachmentsProps, Sender as XSender, SenderProps as XSenderProps} from '@ant-design/x';
import {Button, type GetProp, type GetRef, Upload} from 'antd';
import S from "../../../utils/storage";
import { ConfigContext } from '../../config-provider'


type SenderProps = {
    value: string,
    onSubmit: (content: string) => void,
    onChange: (content: string) => void,
    loading?: boolean
    showUpload?: boolean
    maxCount?: number
    items?: any[]
    onChangeAttachments?: (fileList: any[]) => void
}

export default function Sender(props: SenderProps) {

    const {items, onChangeAttachments, value, onSubmit: onSubmitProps, onChange, loading, showUpload, maxCount = 5} = props;

    const [open, setOpen] = React.useState(false);

    const attachmentsRef = React.useRef<any>(null);

    const senderRef = React.useRef<any>(null);

    const {appApiBaseUrl, appFileTokenCommonUploadApi} = React.useContext(ConfigContext)

    const token = S.getAuthToken()

    useEffect(() => {

        if(open && items?.length === 0 ) setOpen(false)

    }, [items])

    const senderHeader = (
        <XSender.Header
            title="Attachments"
            styles={{
                content: {
                    padding: 0,
                },
            }}
            open={open}
            onOpenChange={setOpen}
            forceRender
        >
            <Attachments
                ref={attachmentsRef}
                beforeUpload={(file, fileList) => {
                    const imageFiles = fileList.filter(f => f.type.startsWith('image/'));
                    if (imageFiles.length > maxCount) {
                        console.warn(`最多只能上传 ${maxCount} 个图片文件`);
                        return Upload.LIST_IGNORE;
                    }
                    return imageFiles.includes(file) || Upload.LIST_IGNORE;
                }}
                items={items}
                onChange={({fileList}) => onChangeAttachments?.(fileList)}
                placeholder={(type) =>
                    type === 'drop'
                        ? {
                            title: 'Drop file here',
                        }
                        : {
                            icon: <CloudUploadOutlined/>,
                            title: 'Upload files',
                            description: 'Click or drag files to this area to upload',
                        }
                }
                getDropContainer={() => senderRef.current?.nativeElement}
                maxCount={maxCount}
                accept={'.png,.jpg,.jpeg'}
                action={`${appApiBaseUrl}${appFileTokenCommonUploadApi}`}
                headers={{'Authorization': token}}
            />
        </XSender.Header>
    );



  let senderProps: any = {value, onSubmit: onSubmitProps, onChange, loading}

  if (showUpload) {
    senderProps = {
      ...senderProps,
      header: senderHeader,
      prefix: <Button
        type="text"
        icon={<LinkOutlined/>}
        onClick={() => {
          setOpen(!open);
        }}
      />,
      onPasteFile: (file) => {
        attachmentsRef.current?.upload(file);
        setOpen(true);
      }
    }
  }

  return <XSender ref={senderRef} {...senderProps}/>
}
