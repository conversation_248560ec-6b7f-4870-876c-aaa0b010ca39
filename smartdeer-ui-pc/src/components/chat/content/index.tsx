import {
  ArrowDownOutlined,
  <PERSON>LeftOutlined,
  CopyOutlined,
  DislikeOutlined,
  LikeOutlined,
  LinkOutlined,
  ShareAltOutlined,
  SyncOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { AttachmentsProps, Bubble, Prompts } from '@ant-design/x';
import { Image, Loading } from '@smartdeer-ui/pc';
import {
  Avatar,
  Button,
  Flex,
  Modal,
  Space,
  Tag,
  Typography,
  message,
  type GetProp,
} from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import { ProCard } from '@ant-design/pro-components';
import markdownit from 'markdown-it';

import { useGetState } from 'ahooks';

import dayjs from 'dayjs';
import { isMobile } from 'react-device-detect';
import MatchView from '../match-view';
import Sender from '../sender';
import Workflow from '../workflow';

import './index.module.scss';
import resolveIntentToConfig from '../utils/resolveIntentToConfig';

function generateUUID() {
  const timestamp = Date.now().toString(16); // 当前时间戳转为16进制
  const randomPart = Math.random().toString(16).substring(2, 10); // 生成随机数并转为16进制
  return `${timestamp}-${randomPart}`;
}

function isOneMinutePassed(timestamp: number): boolean {
  const now = dayjs();
  const inputTime = dayjs(timestamp);
  const diffInSeconds = now.diff(inputTime, 'second');
  return diffInSeconds >= 60;
}

export function transformChatData(message: string, files?: string[]) {
  if (files && files.length > 0) {
    return [
      { type: 'text', text: message.trim() },
      ...files.map((fileUrl) => ({
        type: 'image_url',
        image_url: { url: fileUrl },
      })),
    ];
  }
  return message.trim();
}

const md = markdownit({ html: true, breaks: true });

const renderMarkdown = (content: any) => (
  <Typography>
    <div
      dangerouslySetInnerHTML={{
        __html: md.render(content),
      }}
    />
  </Typography>
);

let client: any;

interface ChatContentProps {
  /** 对话的人物信息 */
  people: {
    uuid: string;
    name: string;
    avatar?: string;
    type?: string;
    signature?: string;
    promptsItems?: Array<{ content: string }>;
    vision?: boolean;
    steam?: boolean;
  };
  /** 返回上一页的回调 */
  onBack?: () => void;
  /** 是否为模态框模式 */
  modal?: boolean;
  /** 容器高度 */
  height?: number | string;
  /** 实体ID */
  entityId?: string;
  /** API接口对象 */
  api: any;

  user: any;
  title?: React.ReactNode;

  matchProps?: any;
  jsonRenderProps?: any;
}

export default function ChatContent(props: ChatContentProps) {
  const {
    title,
    people,
    onBack,
    modal = false,
    height,
    entityId,
    api,
    user,
    matchProps,
    jsonRenderProps,
  } = props as ChatContentProps;
  const [promptsItems, setPromptsItems] = useState<any[]>([]);
  const [messages, setMessages, getMessages] = useGetState<any[]>([]);
  const [content, setContent] = React.useState('');
  const [items, setItems] = React.useState<GetProp<AttachmentsProps, 'items'>>(
    [],
  );

  const conversationIdRef = React.useRef<string | undefined>();
  //  历史的ID
  const historyConversationIdRef = React.useRef<string | undefined>();

  const [chatLoading, setChatLoading] = useState(false);

  const [showPrompt, setShowPrompt] = useState(false);

  const getHistoryChat = async () => {
    setChatLoading(true);

    const charts = await api.ai.historyChats({
      size: 1,
      peopleUuid: people.uuid,
    });

    if (charts && charts.length > 0) {
      // 有历史
      const chart = charts[charts.length - 1]; // 取最后一条
      const res = await api.ai.historyChat(chart.uuid);

      historyConversationIdRef.current = chart.uuid;

      const histories: any[] = res.conversation.map((item: any) => ({
        ...item,
        history: true,
      }));

      histories.unshift({ type: 'time', time: chart.createTime });

      setMessages(histories);
      setShowPrompt(true);
    }

    setChatLoading(false);
  };

  async function getPeopleInfo() {
    await getHistoryChat();

    if (people.promptsItems?.length > 0) {
      setPromptsItems(
        people.promptsItems.map((item: any, index: number) => ({
          key: index,
          description: item.content,
        })),
      );
    }
  }

  useEffect(() => {
    conversationIdRef.current = undefined;
    setMessages([]);
    setContent('');
    setItems([]);

    if (people) {
      getPeopleInfo();
    }
  }, [people]);

  const currentContentRef = useRef<any>('');

  const sendMessage = async (tempMessage?: any[]) => {
    const messages = tempMessage || getMessages();

    if (messages.length === 0) return;

    const index = messages.length - 1;

    if (messages[index].status === 'error') {
      messages[index] = {
        ...messages[index],
        status: 'loading',
        content: '',
      };
      setMessages([...messages]);
    }

    if (entityId) {
      try {
        const res = await resolveIntentToConfig({
          people,
          entityId: entityId,
          message: currentContentRef.current,
          apiKey: 'app-bOyM1nngb36Ef0EHTqjkg1ay',
        }, api);

        console.log("res", res);


        messages[index] = {
          ...messages[index],
          ...res,
          status: 'success',
        };
        setMessages([...messages]);
      } catch (e) {
        messages[index] = {
          ...messages[index],
          status: 'error',
          content: "很抱歉,您没有访问权限!"
        };
        setMessages([...messages]);
      }
      return
    }

    const conversationId = conversationIdRef.current;

    try {
      if (people.steam) {
        if (!client) client = api.ai.initClient();
        client.create(
          {
            uuid: conversationId,
            message: currentContentRef.current,
            stream: 1,
          },
          {
            onSuccess: (msgs: any[]) => {
              console.log('msgs', msgs);

              if (msgs && msgs.length > 0) {
                try {
                  messages[index] = {
                    ...messages[index],
                    status: 'success',
                  };
                  setMessages([...messages]);
                } catch (e) {
                  console.log('e', e);
                }
              } else if (msgs?.length === 0) {
                messages[index] = {
                  ...messages[index],
                  content: '请求超时,请稍后重试!',
                  status: 'error',
                };
                setMessages([...messages]);
              }
            },
            onUpdate: (msg: any) => {
              let obj = JSON.parse(msg.data);
              obj = obj?.data ?? obj;
              if (!obj.uuid) {
                messages[index] = {
                  ...messages[index],
                  status: 'pending',
                  content: `${messages[index].content ?? ''}${obj.content}`,
                };
                setMessages([...messages]);
              } else if (obj.uuid && obj.code) {
                messages[index] = {
                  ...messages[index],
                  status: 'pending',
                  code: obj.code,
                };
                setMessages([...messages]);
              }
            },
            onError: () => {
              messages[index] = {
                ...messages[index],
                content: '网络异常, 请稍后重试!',
                status: 'error',
              };
              setMessages([...messages]);
            },
          },
        );
        return;
      }

      const res = await api.ai.chat(conversationId!, currentContentRef.current);

      if (res) {
        messages[index] = {
          ...messages[index],
          content: res.content ?? res.resp,
          uuid: res.uuid,
          status: 'success',
        };
        currentContentRef.current = '';
      } else {
        throw new Error('Network Error');
      }
    } catch (e) {
      messages[index] = {
        ...messages[index],
        content: (e as Error).message,
        status: 'error',
      };
    } finally {
      setMessages([...messages]);
    }
  };

  const onSubmit = async (nextContent: string) => {
    if (!nextContent) {
      return;
    }

    if (showPrompt) setShowPrompt(false);

    currentContentRef.current = transformChatData(
      nextContent,
      items.map((item) => item.response.data),
    );

    if (!conversationIdRef.current) {
      // 有对话ID
      const res = await api.ai.newChat(people.uuid);
      conversationIdRef.current = res.chatUuid;
    }

    const preMessage = getMessages();

    const newMessage = [...preMessage];

    const currentTime = new Date().getTime(); // 提前计算当前时间戳

    const findLastIndex = preMessage.findLastIndex(
      (find) => find.type === 'time',
    );

    if (
      findLastIndex === -1 ||
      isOneMinutePassed(preMessage[findLastIndex].time)
    ) {
      newMessage.push({ type: 'time', time: currentTime });
    }

    newMessage.push(
      {
        key: 'user_' + generateUUID(),
        content: currentContentRef.current,
        role: 'user',
      },
      {
        key: 'assistant_' + generateUUID(),
        content: '',
        status: 'loading',
        role: 'assistant',
        mark: '',
      },
    );

    setMessages(newMessage);

    setContent('');
    setItems([]);

    await sendMessage(newMessage);
  };

  const onMark = async (uuid: string, status: 'good' | 'bad') => {
    try {
      await api.ai.mark(uuid, status);

      setMessages((pre) => {
        const findIndex = pre.findIndex((find) => find.uuid === uuid);
        pre[findIndex].mark = status;
        return [...pre];
      });
    } catch (e) {}
  };

  const [share, setShare] = useState<{
    open: boolean;
    url?: string;
    title?: string;
  }>({ open: false });

  const [shareLoading, setShareLoading] = useState(false);

  const onShare = async () => {
    setShareLoading(true);

    const shareId =
      conversationIdRef.current || historyConversationIdRef.current;

    const res = await api.ai.getShare(shareId!);
    setShare((pre) => ({
      ...pre,
      url: `${window.location.origin}/ai/chat/share/${res.uuid}`,
    }));
    setShareLoading(false);
  };

  const getRoles: any = (bubble: any) => {
    if (bubble.role === 'match') {
      return {
        placement: 'start',
        styles: { content: { maxWidth: isMobile || modal ? '86%' : '80%' } },
        typing: !bubble.history && !people?.stream && { step: 4, interval: 30 },
        loading: bubble.status === 'loading',
        content: (
          <MatchView bubble={bubble} {...matchProps} jsonRenderProps={jsonRenderProps} />
        ),
        footer: (
          <Space>
            {bubble.status === 'success' && (
              <Button type="link" size="small" onClick={() => sendMessage()}>
                内容不符合预期? 重新生成 <SyncOutlined />
              </Button>
            )}
          </Space>
        ),
        avatar: !modal && {
          icon: people?.avatar ? (
            <Image
              src={people?.avatar}
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                objectFit: 'cover',
              }}
            />
          ) : (
            <Avatar size={30} icon={<UserOutlined />} />
          ),
        },
      };
    }

    let msg = bubble.content ?? bubble.message;

    let text = msg;
    const files: string[] = [];

    const regex = /^\[.*\]$/;

    if (typeof msg === 'string' && regex.test(msg)) {
      try {
        msg = JSON.parse(msg);
      } catch (e) {
        let formattedStr = msg.replace(/([{,]\s*)(\w+)=/g, '$1"$2": ');
        // 步骤2：将非对象的值用双引号包裹
        formattedStr = formattedStr.replace(
          /(:\s*)([^,{}]+?)(?=\s*[,}])/g,
          '$1"$2"',
        );
        // 解析为JSON对象
        msg = JSON.parse(formattedStr);
      }
    }

    if (Array.isArray(msg)) {
      text = '';
      //  是一个数组对象
      msg.forEach((item) => {
        if (item.type === 'text' && item.text) {
          text += item.text + ' ';
        }
        if (item.type === 'image_url' && item.image_url?.url) {
          files.push(item.image_url.url);
        }
      });
      text = text.trim();
    }

    const role: any = {
      assistant: {
        placement: 'start',
        styles: { content: { maxWidth: isMobile || modal ? '86%' : '50%' } },
        typing: !bubble.history && !people?.stream && { step: 4, interval: 30 },
        loading: bubble.status === 'loading',
        content: (
          <>
            {renderMarkdown(text)}
            {bubble.code && (
              <div className="mt-[8px]">
                <Workflow history={bubble.history} items={bubble.code} />
              </div>
            )}
          </>
        ),
        avatar: !modal && {
          icon: people?.avatar ? (
            <Image
              src={people?.avatar}
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                objectFit: 'cover',
              }}
            />
          ) : (
            <Avatar size={30} icon={<UserOutlined />} />
          ),
        },
        footer: !bubble.code && (
          <Flex>
            {bubble.status === 'success' && (
              <>
                <Button
                  size="small"
                  type="text"
                  disabled={bubble.mark === 'good'}
                  icon={
                    <LikeOutlined
                      style={{
                        color: bubble.mark === 'good' ? '#fe9111' : '#999',
                      }}
                    />
                  }
                  onClick={() => onMark(bubble.uuid, 'good')}
                />
                <Button
                  size="small"
                  type="text"
                  disabled={bubble.mark === 'bad'}
                  icon={
                    <DislikeOutlined
                      style={{
                        color: bubble.mark === 'bad' ? '#fe9111' : '#999',
                      }}
                    />
                  }
                  onClick={() => onMark(bubble.uuid, 'bad')}
                />
              </>
            )}
            {bubble.status === 'error' && (
              <Button
                type="text"
                size="small"
                icon={<SyncOutlined />}
                onClick={() => sendMessage()}
              />
            )}
            {bubble.status === 'success' && bubble.content.length > 0 && (
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined style={{ color: '#999' }} />}
                onClick={async () => {
                  await navigator.clipboard.writeText(bubble.content);
                  message.success('Copied to clipboard');
                }}
              />
            )}
          </Flex>
        ),
      },
      suggestion: {
        placement: 'start',
        avatar: { style: { visibility: 'hidden' } },
        variant: 'borderless',
        messageRender: (content: string[]) => (
          <Prompts
            vertical
            onItemClick={({ data }) => {
              onSubmit(data.description as string);
            }}
            items={content.map((text) => ({
              key: text,
              description: text,
            }))}
          />
        ),
      },
      user: {
        placement: 'end',
        content: text,
        styles: { content: { maxWidth: isMobile || modal ? '86%' : '50%' } },
        messageRender: renderMarkdown,
        avatar: !modal &&
          user?.avatar && {
            icon: (
              <Image
                src={user?.avatar}
                style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  objectFit: 'cover',
                }}
              />
            ),
          },
        header: files.length > 0 && (
          <Flex gap="middle">
            {files.map((fileUrl: any, index: number) => (
              <div key={index} className={'max-w-96'}>
                <Image src={fileUrl} preview />
              </div>
            ))}
          </Flex>
        ),
      },
    };

    return role[bubble.role] as any;
  };

  const sendMsgLoading = useMemo(() => {
    const target = messages[messages.length - 1];

    return (
      target &&
      target.status &&
      (target.status === 'loading' || target.status === 'pending')
    );
  }, [messages]);

  const [isAtBottom, setIsAtBottom] = useState(true);
  const messagesRef = useRef<HTMLDivElement>(null);

  const handleScroll = () => {
    if (messagesRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesRef.current;

      // 如果没有滚动条，或者滚动到底部
      const atBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setIsAtBottom(atBottom);
    }
  };

  const scrollToBottom = () => {
    if (messagesRef.current) {
      messagesRef.current.scrollTo({
        top: messagesRef.current.scrollHeight,
        behavior: 'smooth', // 设置滚动行为为平滑过渡
      });
    }
  };

  useEffect(() => {
    const element = messagesRef.current;

    if (element) {
      const { scrollTop, scrollHeight, clientHeight } = element;
      const initialAtBottom =
        scrollHeight <= clientHeight ||
        scrollTop + clientHeight >= scrollHeight;

      setIsAtBottom(initialAtBottom);

      if (!initialAtBottom) scrollToBottom();

      element.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (element) {
        element.removeEventListener('scroll', handleScroll);
      }
    };
  }, [messages]);

  const renderContent = (
    <ProCard
      loading={chatLoading && <Loading />}
      bodyStyle={{
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        width: '100%',
        height:
          height ||
          (isMobile
            ? 'calc(100vh - 58px)'
            : modal
            ? '500px'
            : 'calc(100vh - 200px)'),
        alignItems: 'center',
        justifyContent: isMobile ? '' : 'center',
        padding: 0,
        position: 'relative',
      }}
    >
      {messages.length > 0 && (
        <div
          ref={messagesRef}
          style={{
            flex: 1,
            width: '100%',
            padding: isMobile ? '12px' : '16px 24px',
            overflowY: 'auto',
          }}
        >
          {messages.map((item, index) => {
            switch (item.type) {
              case 'time':
                return (
                  <div
                    key={index}
                    className="text-[12px] w-full text-center text-[#999]"
                    style={{ paddingBottom: '8px' }}
                  >
                    {dayjs(item.time).format('YYYY-MM-DD HH:mm')}
                  </div>
                );
              default:
                return (
                  <Bubble
                    style={{ width: '100%', marginBottom: '16px' }}
                    key={index}
                    {...getRoles(item)}
                  />
                );
            }
          })}
        </div>
      )}

      {!isAtBottom && (
        <div style={{ position: 'absolute', bottom: '90px', zIndex: 1 }}>
          <Button
            shape="circle"
            icon={<ArrowDownOutlined />}
            onClick={scrollToBottom}
          ></Button>
        </div>
      )}

      {messages.length === 0 && (
        <div
          className={`flex-1 flex flex-col justify-center items-center`}
          style={{
            width: messages.length > 0 || isMobile || modal ? '100%' : '50%',
          }}
        >
          {people?.avatar && people.signature && (
            <div className="font-bold text-[26px] mb-[12px] flex flex-col items-center w-[80%]">
              <img
                src={people?.avatar}
                alt="avatar"
                className="rounded-full w-[140px] h-[140px]"
              />
              <div className="mt-[16px] text-[12px] text-[#666] leading-4 p-[12px] text-center bg-[#f1f1f1] rounded-[8px]">
                {people.signature}
              </div>
            </div>
          )}
        </div>
      )}

      <div
        className={'w-full'}
        style={{
          width: '100%',
          padding: '0 12px 8px',
        }}
      >
        {(messages.length === 0 || showPrompt) && promptsItems.length > 0 && (
          <div className="mb-[8px]">
            <Prompts
              items={promptsItems}
              wrap
              onItemClick={(info) => {
                onSubmit(info.data.description as string);
              }}
            />
          </div>
        )}
        { people &&
          <Sender
            showUpload={people.vision}
            items={items}
            onChangeAttachments={setItems}
            value={content}
            onSubmit={onSubmit}
            onChange={setContent}
            loading={sendMsgLoading}
          />
        }
      </div>
    </ProCard>
  );

  return (
    <>
      {!modal ? (
        <ProCard
          headerBordered
          bordered
          split={'vertical'}
          title={
            title || (
              <Space className="cursor-pointer" onClick={onBack}>
                <ArrowLeftOutlined />
                <div>{people?.name}</div>
                {people?.type && <Tag>{people?.type}</Tag>}
              </Space>
            )
          }
          extra={
            messages.length > 0 && (
              <div
                onClick={() => {
                  setShare({ open: true });
                  setShareLoading(false);
                }}
                className="cursor-pointer"
              >
                <ShareAltOutlined />
              </div>
            )
          }
        >
          {renderContent}
        </ProCard>
      ) : (
        renderContent
      )}

      <Modal
        open={share.open}
        footer={false}
        centered
        width={isMobile ? '70%' : '450px'}
        title={<div className="text-left">分享会话</div>}
        onCancel={() => setShare({ open: false, url: '' })}
        styles={{
          body: {
            padding: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          },
        }}
      >
        {share.url && share.url.length > 0 ? (
          <>
            <div className="w-full text-left">
              已创建。 点击下方按钮复制分享, 有效期为一天!
            </div>

            <div className="w-full text-left overflow-hidden whitespace-nowrap text-ellipsis text-[#fe9111] pt-[8px] pb-[32px] text-[12px]">
              {share.url}
            </div>

            <Button
              icon={<CopyOutlined />}
              type="primary"
              onClick={async () => {
                try {
                  await navigator.clipboard.writeText(share.url!);
                  message.success('Copied to clipboard');
                  setShare({ open: false, url: '' });
                } catch (err) {
                  message.success((err as Error).message);
                }
              }}
            >
              复制链接
            </Button>
          </>
        ) : (
          <>
            <div className="w-full text-left">
              您的姓名、以及您在分享后添加的任何消息都将予以保密处理。
            </div>
            <div className="w-full text-left overflow-hidden whitespace-nowrap text-ellipsis text-[#fe9111] pt-[8px] pb-[32px] text-[12px]">{`${window.location.origin}/ai/chat/share/...`}</div>
            <Button
              icon={<LinkOutlined />}
              type="primary"
              onClick={onShare}
              loading={shareLoading}
            >
              创建链接
            </Button>
          </>
        )}
      </Modal>
    </>
  );
}
