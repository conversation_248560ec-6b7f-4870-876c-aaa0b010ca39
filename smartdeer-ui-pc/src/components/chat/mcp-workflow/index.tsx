import { useGetState } from 'ahooks';
import React, { useEffect, useRef } from 'react';
import useGetFetchApi from '../../../utils/hooks/useGetFetchApi';
import { MCPClient, MCPServer } from '../../../utils/mcp';
import Workflow from '../workflow';

import './index.css';

interface MCPWorkflowRunnerProps {
  workflowParams: {
    apiKey: string;
    params: {
      prompt: string;
      mcp: string;
    };
  };
  localServer?: Record<string, (params: any) => any>;
  onComplete?: (resp: any, params: any) => void;
  onError?: (err: any) => void;
  maxRetries?: number; // 最大重试次数
  workflowRequest?: any
}

const MCPWorkflow: React.FC<MCPWorkflowRunnerProps> = (props) => {
  const {
    workflowParams,
    localServer,
    onComplete: onCompleteProps,
    onError: onErrorProps,
    maxRetries = 3,
    workflowRequest
  } = props;

  const [steps, setSteps, getSteps] = useGetState<any[]>([]); // UI 渲染的步骤
  const { functionApi } = useGetFetchApi();
  const [loading, setLoading, getLoading] = useGetState(false); // 是否正在加载中
  const originalSteps = useRef<any[]>([]);
  const [error, setError, getError] = useGetState<{
    message: string;
    retryCount: number;
  } | null>(null); // 错误信息

  const workflowCallback = (resp: any) => {

    console.log("resp: ", resp);
    originalSteps.current.push(resp);
    setSteps([...originalSteps.current]);

    if (getLoading()) {
      setLoading(false);
    }
  };

  // 执行工作流的核心逻辑
  const executeWorkflow = () => {
    originalSteps.current = [];
    setSteps([]);
    setLoading(true);
    setError(null);

    const client = new MCPClient(
      functionApi,
      localServer && {
        local: new MCPServer(localServer),
      },
    );

    client.runWorkflowAsync(workflowParams, {
      onStep: workflowCallback,
      onComplete: onCompleteProps,
      onError: (err) => {
        const currentError = getError();
        const retryCount = (currentError?.retryCount || 0) + 1;

        if (retryCount <= maxRetries) {
          setError({
            message: `执行失败 (${retryCount}/${maxRetries}次): ${
              err.message || '未知错误'
            }`,
            retryCount,
          });
        } else {
          setError({
            message: `已达到最大重试次数 (${maxRetries}次), 请检查配置后重试`,
            retryCount,
          });
          onErrorProps?.(err);
        }
        setLoading(false);
      },
    }, workflowRequest);
  };

  useEffect(() => {
    if (!workflowParams) return;
    executeWorkflow();
  }, []);

  // const handleNext = () => {
  //   const nextIndex = getSteps().length;
  //   if (nextIndex < originalSteps.current.length) {
  //     setSteps((prev) => [...prev, originalSteps.current[nextIndex]]);
  //   }
  // };

  // const handleRetry = () => {
  //   executeWorkflow();
  // };

  if (loading) {
    return '正在思考...';
  }

  return (
    <Workflow
      items={steps}
      // onNext={handleNext}
      // error={
      //   error
      //     ? {
      //         message: error.message,
      //         // retry: handleRetry
      //       }
      //     : undefined
      // }
    />
  );
};

export default MCPWorkflow;
