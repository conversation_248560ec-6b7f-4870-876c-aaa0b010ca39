import React from 'react';

export type RenderComponentsType = {
  type: string;
  render: React.ReactNode | ((props: any) => React.ReactNode);
};

export type ComponentsContextType = {
  children?: React.ReactNode;
  formRenderComponents?: Array<RenderComponentsType>;
  pageRenderComponents?: Array<RenderComponentsType>;
  tableRenderComponents?: Array<RenderComponentsType>;
};

export const ComponentsContext = React.createContext<ComponentsContextType>({
  formRenderComponents: [],
  pageRenderComponents: [],
  tableRenderComponents: [],
});

const ComponentsProvider: React.FC<ComponentsContextType> = ({
  children,
  formRenderComponents,
  pageRenderComponents,
  tableRenderComponents,
}) => {
  return (
    <ComponentsContext.Provider
      value={{
        formRenderComponents,
        pageRenderComponents,
        tableRenderComponents,
      }}
    >
      {children}
    </ComponentsContext.Provider>
  );
};

export default ComponentsProvider;
