import { Modal } from 'antd';
import { STORE } from '../../consts';
import S from '../../utils/storage';
import type { ErrorProps } from './error';
import InternalError from './error';

export { type ErrorProps };

type CompoundedComponent = React.FC<ErrorProps> & {
  reloading: () => void;
};

const Error = InternalError as CompoundedComponent;

Error.reloading = function reloading() {
  const locale = S.get(STORE.LOCALE, true) || 'en-US';

  const dict: Record<string, any> = {
    'en-US': {
      title: 'System Alert: Data Load Failed',
      content:
        'Sorry, there was an error loading the data. This may be due to a network issue or system error. Please refresh the page and try again. If the problem persists, feel free to contact our support team, and we’ll assist you as soon as possible!',
      okText: 'Refresh',
    },
    'zh-CN': {
      title: '系统提示：数据加载失败',
      content:
        '抱歉，数据加载出现错误，可能是网络问题或系统异常。请尝试刷新页面后重新操作。如果问题仍未解决，请联系我们的客服团队，我们将尽快为您提供帮助！',
      okText: '刷新页面',
    },
    'zh-TW': {
      title: '系統提示：數據加載失敗',
      content:
        '抱歉，數據加載出現錯誤，可能是網絡問題或系統異常。請嘗試刷新頁面後重新操作。如果問題仍未解決，請聯系我們的客服團隊，我們將盡快為您提供幫助！',
      okText: '刷新頁面',
    },
    'ja-JP': {
      title: 'ページを更新してください',
      content:
        '申し訳ありません、データの読み込み中にエラーが発生しました。ネットワークの問題やシステムの異常が原因である可能性があります。ページを更新して再度お試しください。問題が解決しない場合は、カスタマーサポートまでご連絡ください。できるだけ早く対応いたします！',
      okText: 'ページを更新',
    },
  };

  return Modal.error({
    title: dict[locale].title,
    content: dict[locale].content,
    okText: dict[locale].okText,
    onOk() {
      window.location.reload();
    },
  });
};

export default Error;
