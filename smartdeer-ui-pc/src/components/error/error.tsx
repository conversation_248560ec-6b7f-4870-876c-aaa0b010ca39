import React from 'react';
import { Result, Button } from 'antd';
import { ConfigContext } from '../config-provider';

export const classPrefix = `deer-error`;

export interface ErrorProps {
  className?: string;
  style?: React.CSSProperties;
  title?: string;
  description?: string;
  buttonText?: string;
  onRefresh?: () => void;
}

const Error: React.FC<ErrorProps> = (props) => {
  const {
    className,
    style,
    onRefresh
  } = props;

  const { router, language } = React.useContext(ConfigContext);

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      return;
    }

    router?.go(0)
  };

  const title = props.title || language?.error.title
  const description = props.description || language?.error.description;
  const buttonText = props.buttonText || language?.error.button;

  return (
    <div className={className} style={{ ...style }}>
      <Result
        status="error"
        title={title}
        subTitle={description}
        extra={[
          <Button size='large' type='primary' key='console' onClick={handleRefresh}>
            {buttonText}
          </Button>,
        ]}
      />
    </div>
  )
};

export default Error;
