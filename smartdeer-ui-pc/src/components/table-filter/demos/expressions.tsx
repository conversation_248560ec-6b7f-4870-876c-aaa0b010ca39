import React from 'react';
import { TableFilter } from '@smartdeer-ui/pc'

const columns = [
  {
    "value": "fullText",
    "label": "全文",
    "type": "multipleInput",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "key=match_phrase",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "key=match_phrase",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "name",
    "label": "姓名",
    "type": "multipleInput",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "mobile",
    "label": "手机号",
    "type": "multipleInput",
    "actions": [
      {
        "value": "eq.after",
        "label": "模糊匹配（后缀）",
        "options": "wildcard=after",
        "operator": ":"
      },
      {
        "value": "eq.before",
        "label": "模糊匹配（前缀）",
        "options": "wildcard=before",
        "operator": ":"
      },
      {
        "value": "eq.both",
        "label": "模糊匹配（所有）",
        "options": "wildcard=both",
        "operator": ":"
      }
    ]
  },
  {
    "value": "email",
    "label": "邮箱",
    "type": "multipleInput",
    "actions": [
      {
        "value": "eq.after",
        "label": "模糊匹配（后缀）",
        "options": "wildcard=after",
        "operator": ":"
      },
      {
        "value": "eq.before",
        "label": "模糊匹配（前缀）",
        "options": "wildcard=before",
        "operator": ":"
      },
      {
        "value": "eq.both",
        "label": "模糊匹配（所有）",
        "options": "wildcard=both",
        "operator": ":"
      }
    ]
  },
  {
    "value": "birthday.keyword",
    "label": "出生日期",
    "type": "datePicker",
    "actions": [
      {
        "value": "eq.gt",
        "label": "大于",
        "options": "format=yyyyMMddHHmmss",
        "operator": ">"
      },
      {
        "value": "eq.lt",
        "label": "小于",
        "options": "format=yyyyMMddHHmmss",
        "operator": "<"
      }
    ]
  },
  {
    "value": "vocation",
    "label": "行业",
    "type": "multipleInput",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "lastCompany",
    "label": "最近一份工作公司",
    "type": "multipleInput",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "lastTitle",
    "label": "最近一份工作职位",
    "type": "multipleInput",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "key=match_phrase",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "key=match_phrase",
        "operator": ":",
        "logic": "OR"
      },
      {
        "value": "ne.and",
        "label": "不包含（并且）",
        "options": "key=match_phrase",
        "operator": ":!",
        "logic": "MUST_NOT"
      }
    ]
  },
  {
    "value": "bachelorSchool",
    "label": "教育经历-本科学校(bachelorSchool)",
    "type": "multipleInput",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "education",
    "label": "教育经历-最高学历(highestDegree)",
    "type": "multipleInput",
    "isNested": "false",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "schoolLevel",
    "label": "教育经历-最高学校等级(schoolLevel)",
    "type": "select-default",
    "isNested": "false",
    "props": {
      "options": [
        {
          "label": "普通",
          "value": "0"
        },
        {
          "label": "211",
          "value": "1"
        },
        {
          "label": "985",
          "value": "2"
        },
        {
          "label": "211/985",
          "value": "3"
        }
      ]
    },
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "bachelorSchoolLevel",
    "label": "教育经历-本科学校等级(bachelorSchoolLevel)",
    "type": "select-default",
    "isNested": "false",
    "props": {
      "options": [
        {
          "label": "普通",
          "value": "0"
        },
        {
          "label": "211",
          "value": "1"
        },
        {
          "label": "985",
          "value": "2"
        },
        {
          "label": "211/985",
          "value": "3"
        }
      ]
    },
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "experienceInfo.company",
    "label": "项目经历-公司名称 (company)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "experienceInfo.title",
    "label": "项目经历-职位 (title)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "experienceInfo.summary",
    "label": "项目经历-描述 (summary)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "key=match_phrase",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "key=match_phrase",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "educationInfo.school",
    "label": "教育经历-学校 (school)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "educationInfo.speciality",
    "label": "教育经历-专业 (speciality)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "educationInfo.studentType",
    "label": "教育经历-招生方式 (studentType)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "jobExpectations.forwardVocation",
    "label": "求职期望-行业 (forwardVocation)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "jobExpectations.title",
    "label": "求职期望-职位 (title)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "jobExpectations.forwardLocation",
    "label": "求职期望-城市 (forwardLocation)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "jiguan",
    "label": "籍贯-城市 (jiguan)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  },
  {
    "value": "nowLocation",
    "label": "居住地-城市 (nowLocation)",
    "type": "multipleInput",
    "isNested": "true",
    "actions": [
      {
        "value": "eq.and",
        "label": "包含（并且）",
        "options": "",
        "operator": ":",
        "logic": "AND"
      },
      {
        "value": "eq.or",
        "label": "包含（或者）",
        "options": "",
        "operator": ":",
        "logic": "OR"
      }
    ]
  }
]

export default () => {

  const handleClickSearch = (values: Record<string, any>) => {
    console.log('onSearch', values)
  }

  return (
    <TableFilter
      type='expressions'
      columns={columns}
      onSearch={handleClickSearch}
    />
  )
}
