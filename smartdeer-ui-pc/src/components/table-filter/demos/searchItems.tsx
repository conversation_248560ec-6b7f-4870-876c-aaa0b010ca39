import React from 'react';
import { TableFilter } from '@smartdeer-ui/pc'

const columns = [
  {
    "value": "pageName",
    "label": "页面名称",
    "type": "inputSearch"
  },
  {
    "value": "pageKey",
    "label": "Key",
    "type": "inputSearch"
  },
  {
    "value": "productLine",
    "label": "产品线",
    "type": "inputSearch",
  }
]

export default () => {

  const handleClickSearch = (values: Record<string, any>) => {
    console.log('onSearch', values)
  }

  return (
    <TableFilter
      columns={columns}
      onSearch={handleClickSearch}
    />
  )
}
