import React, { FC } from 'react'
import { DeleteOutlined } from '@ant-design/icons';
import { Flex, Form, Select, Input, Button, Dropdown, DatePicker } from 'antd';
import type { SelectProps, MenuProps } from 'antd';
import { isArray } from 'lodash';
import classNames from 'classnames'
import { classPrefix } from '.'
import { getDefauleActionOptions } from './urils'
import type { ColumnsType, ActionType, TableFilterType } from './typing'
import { ConfigContext } from '../config-provider'
import type { EffectType } from '../../typing';
import { usePropsOptions } from '../../utils/hooks'
import { isNil } from 'lodash'

export interface TableFilterItemProps {
  className?: string;
  style?: React.CSSProperties;

  index: number;
  columns?: ColumnsType[];
  type?: TableFilterType,
  onSearch?: () => void;
  onRemove?: () => void;
  onEditFieldValue?: (name: string, value: string | null) => void
}

const EFAULE_ACTION_COLOR = '#ea961e'

export interface SelectProProps {
  effect?: EffectType;
  fieldProps: SelectProps;
  onSearch?: () => void;
  onChange?: (value: string) => void;
  value?: any;
}

const SelectPro: React.FC<SelectProProps> = (props) => {
  const { value, fieldProps, onChange, onSearch } = props

  const { isLoading, options } = usePropsOptions(props)

  const handleChange = (value: string) => {
    onChange?.(value)
    onSearch?.()
  }

  const {fieldNames, ...rest} = fieldProps;

  return (
    <Select
      {...rest}
      style={{ width: '100%' }}
      value={!isNil(value) ? value.toString() : undefined}
      loading={isLoading}
      options={options}
      onChange={handleChange}
    />
  )
}

const CustomActionSelect: React.FC<SelectProps> = (props) => {
  const {
    style,

    value,
    options,
    onChange
  } = props

  const items: MenuProps['items'] = React.useMemo(() => {
    return options?.map(item => {
      return {
        key: item.value as string,
        label: item.label,
        onClick: () => onChange?.(item.value, options)
      }
    })
  }, [options])

  const option = React.useMemo(() => {
    const option = options?.find(item => item.value === value)

    return option
  }, [options, value])

  const backgroundColor = option?.color || EFAULE_ACTION_COLOR

  return (
    <Dropdown menu={{ items }} placement='bottomLeft'>
      <Button type='primary'>
        {option?.label}
      </Button>
    </Dropdown>
  );
};

const TableFilterItem: FC<TableFilterItemProps> = (props) => {

  const {
    className,
    style,
    type,

    index,
    columns = [],

    onSearch,
    onRemove,
    onEditFieldValue,
  } = props

  const { language } = React.useContext(ConfigContext)


  const [actionOptions, setActionOptions] = React.useState<ActionType[]>([])
  const [valueElementType, setValueElementType] = React.useState<string>(columns[0].type || 'inputSearch')
  const [column, setColumn] = React.useState<ColumnsType>(columns[0])


  React.useEffect(() => {
    let options: ActionType[] = []

    switch (type) {
      case 'searchItems':
        if (columns[0].actions) {
          options = columns[0].actions
        } else {
          options = getDefauleActionOptions(language.tableFilter.actions)
        }
        break;

      case 'expressions':
        if (columns[0].actions) {
          options = columns[0].actions
        } else {
          console.error(`[smartdeer-ui: TableFilterItem] columns[0].actions is undefined`);
        }
        break;

      default:
        console.error(`[smartdeer-ui: TableFilterItem] type not exist : ${type}`);
        break;
    }

    setActionOptions(options)
  }, [])

  const handelChangeKey = (value: string) => {
    const columnIndex = columns.findIndex((item) => item.value === value);
    if (columnIndex === -1) {
      console.error(`[smartdeer-ui: TableFilterItem] columns[${columnIndex}].actions is undefined`);
    }

    const column = columns[columnIndex];
    const elementType = column.type || '';

    let options: ActionType[] = [];
    let actionValue = '';

    if (type === 'expressions' && isArray(column.actions)) {
      options = column.actions;
      actionValue = options[0]?.value ?? '';
      setActionOptions(options);
      onEditFieldValue?.('action', actionValue);
    }

    setValueElementType(elementType)
    setColumn(column)
    onEditFieldValue?.('value', null);
  }


  const handleClickSearch = () => {
    onSearch?.()
  }

  const handleClickRemove = () => {
    onRemove?.()
  }

  const renderValueElement = (valueElementType: string, column: ColumnsType) => {
    let childNode: React.ReactNode = null

    switch (valueElementType) {
      case 'inputSearch':
        childNode = <Input.Search style={{ width: '100%' }} allowClear enterButton onSearch={handleClickSearch} />

        break;

      case 'multipleInput':
        childNode = (
          <Select
            mode='tags'
            tokenSeparators={[',']}
            options={[]}
            allowClear
            onChange={handleClickSearch}
            style={{ width: '100%' }}
          />
        )

        break;

      case 'datePicker':
        childNode = (
          <DatePicker placeholder=' ' style={{ width: '100%' }} onChange={handleClickSearch} />
        )
        break;

      case 'select':
        childNode = (
          <SelectPro fieldProps={{ ...column.props }} effect={column.effect} onSearch={handleClickSearch} />
        )
        break;

      default:
        childNode = <Input.Search style={{ width: '100%' }} allowClear enterButton onSearch={handleClickSearch} />

        break;
    }

    return childNode
  }

  return (
    <div
      className={classNames(`${classPrefix}-item`, className)}
      style={{ ...style }}
    >
      <Flex className={`${classPrefix}-flex`} >
        <Form.Item name={[index, 'key']} noStyle>
          <Select
            options={columns}
            // style={{ minWidth: '156px' }}
            onChange={handelChangeKey}
          />
        </Form.Item>

        <Form.Item name={[index, 'action']} noStyle>
          <CustomActionSelect options={actionOptions} />
        </Form.Item>

        <Form.Item name={[index, 'value']} noStyle>
          {renderValueElement(valueElementType, column)}
        </Form.Item>

        <Form.Item noStyle>
          <DeleteOutlined
            className={`${classPrefix}-item-del`}
            style={{ fontSize: '20px' }}
            onClick={handleClickRemove}
          />
        </Form.Item>
      </Flex>
    </div >
  )
}

export default TableFilterItem
