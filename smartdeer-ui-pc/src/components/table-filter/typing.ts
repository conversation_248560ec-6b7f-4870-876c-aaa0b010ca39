import type { EffectType } from '../../typing';
export type ActionType = {
  value: string;
  label: string;
  options?: string;
  operator?: string;
  logic?: string;
  color?: string;
};

export type ColumnsType = {
  value: string;
  label: string;
  type?: string;
  actions?: ActionType[];
  props?: Record<string, any>;

  effect?: EffectType;
};

export type TableFilterType = 'expressions' | 'searchItems';
