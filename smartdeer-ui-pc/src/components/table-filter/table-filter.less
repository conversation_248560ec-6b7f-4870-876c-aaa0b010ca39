.deer-table-filter {
  background: white;
  padding: 14px 12px;
  border-radius: 12px;
  box-shadow: 0px 2px 14px 0px rgba(0, 0, 0, 0.06);

  &-head {
    width: 100%;
  }

  &-list {
    margin-top: 12px;
    width: 100%;
  }

  &-item {
    margin-top: 12px;

    &:first-child {
      margin-bottom: 0;
    }

    &-del {
      color: var(--icb-color-71);
      cursor: pointer;

      &:hover {
        opacity: 0.75;
      }
    }
  }

  &-flex {
    gap: 12px;
  }

  &-custom-action-select {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;

    &-button {
      padding: 9px 18px;
      border-radius: 6px;
      cursor: pointer;
      color: white;
      width: 100%;
      text-align: center;
    }
  }

}

@media (min-width: 576px) {
  .deer-table-filter {
    padding: 24px 20px;

    &-head {
      width: 90%;
    }
    &-list {
      width: 90%;
      margin-top: 17px;
    }

    &-flex {
      gap: 26px;
    }
  }
}
