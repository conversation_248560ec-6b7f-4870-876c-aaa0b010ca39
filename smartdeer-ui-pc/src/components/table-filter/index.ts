import type { TableFilterProps } from './table-filter';
import InternalTableFilter from './table-filter';
import TableFilterItem from './table-filter-item';

export type { TableFilterProps } from './table-filter';

export type {
  ActionType as TableFilterActionType,
  ColumnsType as TableFilterColumnsType,
  TableFilterType,
} from './typing';

export const classPrefix = `deer-table-filter`;

type CompoundedComponent = React.FC<TableFilterProps> & {
  Item: typeof TableFilterItem;
};

const TableFilter = InternalTableFilter as CompoundedComponent;

TableFilter.Item = TableFilterItem;

export default TableFilter;
