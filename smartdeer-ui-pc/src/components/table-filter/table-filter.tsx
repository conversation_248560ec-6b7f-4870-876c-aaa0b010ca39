import React, { FC } from 'react'
import { Space, Form, Flex } from 'antd';
import { isArray, isString } from 'lodash';
import classNames from 'classnames'
import TableFilterItem from './table-filter-item'
import { classPrefix } from '.'
import { getDefauleActionOptions } from './urils'
import { ConfigContext } from '../config-provider'
import type { ColumnsType, TableFilterType } from './typing'
import { useSize } from '../../utils/hooks'

import './table-filter.less'
import dayjs from 'dayjs';

const formListName = 'filters'

export interface TableFilterProps {
  className?: string;
  style?: React.CSSProperties;

  type?: TableFilterType,
  columns?: ColumnsType[];
  onSearch?: (values: Record<string, any>) => void
}

const TableFilter: FC<TableFilterProps> = (props) => {

  const {
    className,
    style,

    type = 'searchItems',
    columns = [],

    onSearch
  } = props

  const { language } = React.useContext(ConfigContext)

  const [form] = Form.useForm();

  const size = useSize()

  const getDefaultFilter = () => {
    let defauleAction: string = ''

    switch (type) {
      case 'searchItems':
        if (isArray(columns[0]?.actions)) {
          defauleAction = columns[0]?.actions[0].value
        } else {
          defauleAction = getDefauleActionOptions(language.tableFilter.actions)[0].value
        }
        break;
      case 'expressions':
        if (isArray(columns[0]?.actions)) {
          defauleAction = columns[0]?.actions[0].value
        } else {
          console.error(`[smartdeer-ui: TableFilter] columns[0].actions is undefined`);
        }
        break;
      default:
        console.error(`[smartdeer-ui: TableFilter] type not exist : ${type}`);
        break;
    }

    return {
      key: columns[0]?.value,
      action: defauleAction,
    }
  }

  React.useEffect(() => {
    const newFilter = getDefaultFilter()

    form.setFieldValue(formListName, [newFilter])
  }, [])

  const handleClickAddFilter = () => {
    const filters = form.getFieldValue(formListName)

    const newFilter = getDefaultFilter()

    form.setFieldValue(formListName, [...filters, newFilter])
  }

  const handleClickEditFieldValue = (index: number, name: string, value: string | null) => {
    const filters: any[] = form.getFieldValue(formListName)

    const filterItem = filters[index]

    const newFilterItem = {
      ...filterItem,
      [name]: value
    }

    filters.splice(index, 1, newFilterItem)

    form.setFieldValue(formListName, JSON.parse(JSON.stringify(filters)))
  }

  const handleFinish = (values: Record<string, any>) => {
    if (!isArray(values[formListName])) return

    const filters = values[formListName].filter(item => item.key && item.action && item.value);

    let returnValue: any[] = [];

    if (type === 'searchItems') {
      returnValue = filters
    }

    if (type === 'expressions') {
      const resultJson: string[] = [];

      filters.forEach(item => {
        const column = columns.find(col => col.value === item.key)

        if (!column) {
          throw new Error(`Column not found for key: ${item.key}`);
        }

        const action = column.actions?.find((act) => act.value === item.action);
        if (!action) {
          throw new Error(`Action not found for key: ${item.key}, action: ${item.action}`);
        }

        const { options, operator, logic = 'AND' } = action;
        let value = item.value;


        if (dayjs.isDayjs(value)) {
          value = dayjs(value).valueOf().toString();
        }

        let expression: string = '';

        if (isArray(value)) {
          const expressions = value.map((v) => {
            return options ?
              `${options}=>${item.key}${operator}${v}` :
              `${item.key}${operator}${v}`;
          });

          expression = expressions.join(` ${logic} `);
        }

        if (isString(value)) {
          expression = options ?
            `${options}=>${item.key}${operator}${value}` :
            `${item.key}${operator}${value}`;
        }

        resultJson.push(expression);
      })

      returnValue = resultJson
    }

    onSearch?.({
      [type]: returnValue
    })
  }

  const handleClickSearch = () => {
    form.submit()
  }

  const filterCriteriaText = language.tableFilter.filterCriteria
  const addCriteriaText = language.tableFilter.addCriteria

  if (columns.length === 0) return <></>

  return (
    <div
      className={classNames(classPrefix, className)}
      style={{ ...style }}
    >

      <div className={`${classPrefix}-head`}>
        <Flex align='center' justify='space-between'>
          <strong>{filterCriteriaText}</strong>
          <a onClick={handleClickAddFilter}>{addCriteriaText}</a>
        </Flex>
      </div>

      <div className={`${classPrefix}-list`}>
        <Form
          form={form}
          onFinish={handleFinish}
          autoComplete='off'
          size={size}
        >
          <Form.List name={formListName}>
            {(fields, { remove }) => {
              return (
                <>
                  {fields.map(({ key, name: index }) => {
                    return (
                      <TableFilterItem
                        key={key}
                        index={index}
                        columns={columns}
                        onSearch={handleClickSearch}
                        onRemove={() => {
                          remove(index)
                          setTimeout(() => {
                            handleClickSearch()
                          }, 0)
                        }}
                        onEditFieldValue={(name, value) => handleClickEditFieldValue(index, name, value)}
                        type={type}
                      />
                    )
                  })}
                </>
              )
            }}
          </Form.List>
        </Form>
      </div>
    </div >
  )
}

export default TableFilter
