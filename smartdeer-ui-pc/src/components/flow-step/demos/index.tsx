import React from 'react';
import { ApprovalTimeline } from '@smartdeer-ui/pc'

const dataInfo = [
  {
    "subFlowIds": [],
    "finishTime": 0,
    "variables": {
      "applyId": "1227",
      "system.status": "1",
      "audit": "1181",
      "userLeaveIds": [
        "101"
      ]
    },
    "search_key": "1227,1181,[101],1",
    "auditRealName": "-",
    "applyRealName": "郭靖",
    "entityId": 2,
    "updateTime": *************,
    "flowKey": "audit_leave",
    "version": "7d8e557f06134b10",
    "accountId": 1181,
    "parentFlowId": 128,
    "createTime": *************,
    "taskVariables": {
      "system.status": "1"
    },
    "system_status": "1",
    "applyRealNameStr": "郭靖提交的请假",
    "id": 129,
    "groupKeyStr": "-",
    "complete": 1,
    "tasks": [
      {
        "stepKey": "flow.leave",
        "accountId": 1181,
        "variables": {
          "system.status": 1
        },
        "createTime": *************,
        "entityId": 2,
        "updateTime": *************,
        "id": 83,
        "flowKey": "audit_leave",
        "flowId": 129,
        "version": "7d8e557f06134b10"
      }
    ],
    "status": 1
  },
  {
    "subFlowIds": [
      129
    ],
    "finishTime": 0,
    "variables": {
      "auditId": "1181",
      "system.status": "1",
      "startFrom": "*************",
      "leaveConfigGroupKey": "annual.leave",
      "endTo": "*************",
      "audits": [
        "1181"
      ],
      "userLeaveIds": [
        "101"
      ],
      "userLeaves": [
        {
          "originToTime": "*************",
          "sign": "1",
          "originFromTime": "*************",
          "timeZone": "Asia/Shanghai",
          "entityId": "2",
          "updateTime": "*************",
          "uuid": "80fd5a307840489ab9e08874de9d11de",
          "parentId": "0",
          "accountId": "1227",
          "leaveConfId": "1",
          "deleted": "0",
          "createTime": "*************",
          "deleteTime": "0",
          "fromTime": "*************",
          "id": "101",
          "flowId": "0",
          "toTime": "*************",
          "status": "0"
        }
      ]
    },
    "search_key": "*************,annual.leave,*************,[1181],[101],[{id=101, uuid=80fd5a307840489ab9e08874de9d11de, entityId=2, accountId=1227, leaveConfId=1, parentId=0, sign=1, timeZone=Asia/Shanghai, fromTime=*************, toTime=*************, originFromTime=*************, originToTime=*************, flowId=0, createTime=*************, updateTime=*************, deleted=0, deleteTime=0, status=0}],[{id=101,uuid=80fd5a307840489ab9e08874de9d11de,entityId=2,accountId=1227,leaveConfId=1,parentId=0,sign=1,timeZone=Asia/Shanghai,fromTime=*************,toTime=*************,originFromTime=*************,originToTime=*************,flowId=0,createTime=*************,updateTime=*************,deleted=0,deleteTime=0,status=0}],1,1181",
    "auditRealName": "郭靖",
    "applyRealName": "田佳",
    "entityId": 2,
    "updateTime": *************,
    "flowKey": "create_leave",
    "version": "33f0a96bdf2746e9",
    "accountId": 1227,
    "parentFlowId": 0,
    "createTime": *************,
    "taskVariables": {},
    "system_status": "1",
    "applyRealNameStr": "田佳提交的请假",
    "id": 128,
    "groupKeyStr": "年假",
    "complete": 0,
    "tasks": [],
    "status": 1
  }
]

export default () => {
  return (
    <ApprovalTimeline
      dataInfo={dataInfo}
    />
  )
}
