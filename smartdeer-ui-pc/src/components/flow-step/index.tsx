import { Empty, StepProps, Steps, StepsProps } from 'antd';
import classNames from 'classnames';
import React, { FC } from 'react';
import { mergeProps } from '../../utils/withDefaultProps';

import { TransformDataRules } from '../../typing';
import { transformDataBasedOnRules } from '../../utils/transformDataBasedOnRules';
import './index.less';

const classPrefix = `deer-flow-step`;

export interface FlowStepProps {
  className?: string;
  style?: React.CSSProperties;

  steps?: any[];
  transforms?: TransformDataRules;
}

const defaultProps = {
  size: 'small',
  labelPlacement: 'vertical',
};

const FlowStep: FC<FlowStepProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const {
    className,
    style,

    steps = [],
    transforms,
    ...rest
  } = props;

  let items = transforms
    ? (transformDataBasedOnRules(steps, transforms) as StepProps[])
    : steps;

  items = items.map((item) => {
    return {
      title: item.title,
      description: item.description,
      status: item.status,
      icon: item.icon,
      subTitle: item.subTitle,
    };
  });

  if (items.length === 0) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <div className={`${classPrefix}-item`}>
        <Steps
          className={`${classPrefix}-item-container`}
          direction="horizontal"
          items={items}
          {...(rest as StepsProps)}
        />
      </div>
    </div>
  );
};

export default FlowStep;
