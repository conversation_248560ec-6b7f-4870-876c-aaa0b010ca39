import React from 'react';
import ZhTW from '../language/zh-CN';

import type { LanguageType } from '../language';

export type LocaleType = 'zh-CN' | 'en-US' | 'zh-TW' | 'ja-JP';
export type TimezoneType = 'Asia/Shanghai' | 'America/New_York';
export type EncryptType = 'DISABLE' | 'BASE64' | 'RSA';

type ListenType = ({
  location,
  action,
}: {
  location: Location;
  action: 'PUSH' | 'POP' | 'REPLACE';
}) => void;

export interface ConfigContextProps {
  isRequesId?: boolean; // url 加密
  isEncrypt?: boolean; // 加密
  encryptType?: EncryptType; // 加密类型
  rsaPublicKey?: string; // rsa 加密 key
  rsaPrivateKey?: string; // rea 解密 key
  userAccountId?: string; // 用户id
  locale?: LocaleType; // 语言
  timezone?: TimezoneType; // 时区
  appDefaultEntityId?: string; // 默认实体 id
  appDefaultEntityUUID?: string; // 默认实体 uuid
  appApiBaseUrl?: string; // api 地址
  appFunctionApi?: string; // 函数 api 地址
  appProcessApi?: string; // 过程 api 地址
  appRiverFunction?: string; // River函数 api 地址
  appRiverProcess?: string; // River过程 api 地址
  appRiverAuthFunction?: string; // River权限函数 api 地址
  appRiverAuthProcess?: string; // River权限过程 api 地址
  appRiverEntityFunction?: string; // River实体函数 api 地址
  appRiverEntityProcess?: string; // River实体过程 api 地址
  appObjectApi?: string; // 对象 api 地址
  isFileToken?: boolean; // 文件是否加 token
  appFileTokenUploadApi?: string; // 文件上传地址
  appFileTokenCommonUploadApi?: string; // 公共文件上传地址
  appFileTokenApi?: string; // 文件 token api 地址
  appFileTokenExpireTime?: string; // 文件 token 过期时间
  appFileDownloadApi?: string; // 私密文件下载
  appFlowTokenFileUploadApi?: string; // 流程文件上传

  authorization?: string; // token
  authorizationExpired?: number; // token 过期时间

  formatterUiByKey?: string; // 获取全局页面 key
  formatterFormByKey?: string; // 获取全局表单 key
  formatterElementByKey?: string; // 获取全局组件 key

  appModuleCheck?: string; // 是否开启两步验证

  loginModalOpen?: boolean; // 是否打开登录验证弹窗

  isSageEntityAuth?: boolean; // 是否开启 sage 实体权限
  securityModalOpen?: boolean; // 是否打开 2fa 弹窗

  serviceAbilityKey?: string; // 获取服务能力配置 key

  isStorageEncrypt?: boolean; // 是否加密存储

  functionKeys?: {
    getProFlowAuditPreview?: string; // 获取审批流程预览
  };

  router?: {
    push: (to: string, state?: any) => void;
    replace: (to: string, state?: any) => void;
    back: () => void;
    listen: (fn: ListenType) => void;
    go: (delta: number) => void;
  };
  useUrlParams: () => Record<string, string | undefined>;
  theme?: {
    // 主题
  };
  language: LanguageType;

  systemName?: 'GS' | 'SAGE' | 'RIVER'; // 系统名称
}

export const ConfigContext = React.createContext<ConfigContextProps>({
  timezone: 'Asia/Shanghai',
  useUrlParams: () => {
    return {};
  },
  language: ZhTW,
  isStorageEncrypt: true,
});
