import React from 'react';
import { mergeProps } from '../../utils/withDefaultProps'
import { ConfigContext } from './context';
import type { ConfigContextProps, LocaleType, TimezoneType, EncryptType } from './context';
import S from '../../utils/storage'
import { STORE } from '../../consts'
import LanguageDict from '../language'
import { useFileAccessToken } from '../../utils/hooks'
import { clearFetchPromisesCache } from '../../utils/swrFetcher';
import { event, LANG_CHANGE_EVENT, RESET_STORAGE_EVENT, USER_ACCOUNT_ID_CHANGE_EVENT, SECURITY_CHECK_EVENT, ENTITY_ID_CHANGE_EVENT, LOGIN_EVENT, ENTITY_UUID_CHANGE_EVENT, setModule2FA, setSecurityCheckModal, SAGE_ENTITY_AUTH, TO_LOGIN } from './utils';
import { SecurityCheckModal } from '../business-component';
import pageEventBus from '../../bus/pageEventHub';

export {
  ConfigContext,
  type ConfigContextProps,
  type LocaleType,
  type TimezoneType,
  type EncryptType,
};

export type ConfigProviderProps = {
  children?: React.ReactNode;
  toLogin?: () => void;
} & Omit<ConfigContextProps, 'language'>

const defaultProps = {
  locale: 'zh-CN',
  isRequesId: false,
  isEncrypt: false,
  encryptType: 'BASE64',
  rsaPublicKey: '',
  rsaPrivateKey: '',
  isFileToken: false
}
const ConfigProvider: React.FC<ConfigProviderProps> = (p) => {
  const props = mergeProps(defaultProps, p)
  const {
    locale,
    isRequesId,
    isEncrypt,
    encryptType,
    rsaPublicKey,
    rsaPrivateKey,
    userAccountId,
    timezone,
    appDefaultEntityId,
    appDefaultEntityUUID,
    appApiBaseUrl,
    appFunctionApi,
    appProcessApi,
    appObjectApi,
    appRiverFunction,
    appRiverProcess,
    appRiverAuthFunction,
    appRiverAuthProcess,
    appRiverEntityFunction,
    appRiverEntityProcess,

    isFileToken,
    appFileTokenUploadApi,
    appFileTokenCommonUploadApi,
    appFileTokenApi,
    appFileTokenExpireTime,
    appFileDownloadApi,
    appFlowTokenFileUploadApi,

    formatterUiByKey,
    formatterFormByKey,
    formatterElementByKey,

    authorization,
    authorizationExpired,
    router,
    useUrlParams,
    theme,
    appModuleCheck,
    systemName,

    serviceAbilityKey,

    functionKeys,

    toLogin,

    children
  } = props

  const { reset: fileAccessTokenReset } = useFileAccessToken()

  const [lang, setLang] = React.useState(locale);
  const [accountId, setAccountId] = React.useState(userAccountId);
  const [checkModal, setCheckModal] = React.useState<Record<string, any>>({ open: false });
  const [entityId, setEntityId] = React.useState(appDefaultEntityId);
  const [loginModalOpen, setLoginModalOpen] = React.useState(false);
  const [entityUUID, setEntityUUID] = React.useState(appDefaultEntityUUID);
  const [isSageEntityAuth, setIsSageEntityAuth] = React.useState(false);
  const previousPathnameRef = React.useRef('');

  const handleSetStorage = () => {
    S.set(STORE.IS_REQUES_ID, isRequesId)

    S.set(STORE.IS_ENCRYPT, isEncrypt)

    S.set(STORE.ENCRYPT_TYPE, encryptType)

    S.set(STORE.RSA_PUBLIC_KEY, rsaPublicKey)
    S.set(STORE.RSA_PRIVATE_KEY, rsaPrivateKey)

    if (userAccountId) {
      S.set(STORE.USER_ACCOUNT_ID, userAccountId)
    }

    if (authorization && authorizationExpired) {
      S.setAuthToken(authorization, authorizationExpired)
    }

    if (locale) {
      S.set(STORE.LOCALE, locale, true)
    }

    if (timezone) {
      S.set(STORE.TIMEZONE, timezone, true)
    }

    if (appApiBaseUrl) {
      S.set(STORE.APP_API_BASE_URL, appApiBaseUrl, true)
    }
  }

  React.useEffect(() => {
    handleSetStorage()
  }, [])

  React.useEffect(() => {
    if (!router) return
    fileAccessTokenReset()

    router.listen(({ location }) => {
      pageEventBus.unsubscribeAll();

      fileAccessTokenReset()
      clearFetchPromisesCache()
      if (!previousPathnameRef.current) {
        previousPathnameRef.current = location.pathname;
        return;
      }
      if (location.pathname !== previousPathnameRef.current) {
        setModule2FA('');
        previousPathnameRef.current = location.pathname;
      }
    })
  }, [])

  const handleResetStorage = () => {
    handleSetStorage()
  }

  const handleLangChange = (lang: LocaleType) => {
    setLang(lang)
  }

  const handlesAccountIdChange = (id: string) => {
    setAccountId(id)
  }

  const handleCheckSecurity = (isOpen: boolean, params = {}) => {
    setCheckModal({ ...params, open: isOpen })
  }

  const handleChangeEntityId = (id: string) => {
    setEntityId(id);
  }

  const handleLoginModalChange = (isOpen: boolean) => {
    setLoginModalOpen(isOpen);
  }

  const handleChangeEntityUUID = (uuid: string) => {
    setEntityUUID(uuid);
  }

  const handleChangeSageEntityAuth = (isAuth: boolean) => {
    setIsSageEntityAuth(isAuth);
  }

  const handleToLogin = () => {
    toLogin?.()
  }

  React.useEffect(() => {
    event.on(RESET_STORAGE_EVENT, handleResetStorage);
    event.on(LANG_CHANGE_EVENT, handleLangChange);
    event.on(USER_ACCOUNT_ID_CHANGE_EVENT, handlesAccountIdChange);
    event.on(SECURITY_CHECK_EVENT, handleCheckSecurity);
    event.on(ENTITY_ID_CHANGE_EVENT, handleChangeEntityId);
    event.on(LOGIN_EVENT, handleLoginModalChange);
    event.on(ENTITY_UUID_CHANGE_EVENT, handleChangeEntityUUID);
    event.on(SAGE_ENTITY_AUTH, handleChangeSageEntityAuth);

    event.on(TO_LOGIN, handleToLogin);

    return () => {
      event.off(RESET_STORAGE_EVENT, handleResetStorage);
      event.off(LANG_CHANGE_EVENT, handleLangChange);
      event.off(USER_ACCOUNT_ID_CHANGE_EVENT, handlesAccountIdChange);
      event.off(SECURITY_CHECK_EVENT, handleCheckSecurity);
      event.off(ENTITY_ID_CHANGE_EVENT, handleChangeEntityId);
      event.off(LOGIN_EVENT, handleLoginModalChange);
      event.off(ENTITY_UUID_CHANGE_EVENT, handleChangeEntityUUID);
      event.off(SAGE_ENTITY_AUTH, handleChangeSageEntityAuth);

      event.off(TO_LOGIN, handleToLogin);
    };
  }, []);

  return (
    <ConfigContext.Provider
      value={{
        isRequesId,
        isEncrypt,
        encryptType,
        rsaPublicKey,
        rsaPrivateKey,
        userAccountId: accountId,
        locale: lang,
        timezone,
        appDefaultEntityId: entityId,
        appDefaultEntityUUID: entityUUID,
        appApiBaseUrl,
        appFunctionApi,
        appProcessApi,
        appObjectApi,
        appRiverFunction,
        appRiverProcess,
        appRiverAuthFunction,
        appRiverAuthProcess,
        appRiverEntityFunction,
        appRiverEntityProcess,

        isFileToken,
        appFileTokenUploadApi,
        appFileTokenCommonUploadApi,
        appFileTokenApi,
        appFileTokenExpireTime,
        appFileDownloadApi,
        appFlowTokenFileUploadApi,

        formatterUiByKey,
        formatterFormByKey,
        formatterElementByKey,

        authorization,

        router,
        useUrlParams,
        theme,
        language: LanguageDict[lang],
        loginModalOpen,
        securityModalOpen: checkModal?.open,
        systemName,
        isSageEntityAuth,

        functionKeys,

        serviceAbilityKey,
      }}
    >
      {children}

      {checkModal.open && appModuleCheck === 'true' && (
        <SecurityCheckModal
          {...checkModal}
          open={checkModal.open}
          onCancel={() => setSecurityCheckModal(false)}
        />
      )}
    </ConfigContext.Provider>
  );
};

export default ConfigProvider;
