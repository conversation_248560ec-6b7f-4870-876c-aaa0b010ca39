import { EventEmitter } from 'events';
import { STORE } from '../../consts';
import S from '../../utils/storage';
import type { EncryptType, LocaleType } from './context';

export const event = new EventEmitter();

export const RESET_STORAGE_EVENT = Symbol('RESET_STORAGE_EVENT');

export const LANG_CHANGE_EVENT = Symbol('LANG_CHANGE_EVENT');

export const USER_ACCOUNT_ID_CHANGE_EVENT = Symbol(
  'USER_ACCOUNT_ID_CHANGE_EVENT',
);

export const SECURITY_CHECK_EVENT = Symbol('SECURITY_CHECK_EVENT');

export const ENTITY_ID_CHANGE_EVENT = Symbol('ENTITY_ID_CHANGE_EVENT');

export const ENTITY_UUID_CHANGE_EVENT = Symbol('ENTITY_UUID_CHANGE_EVENT');

export const LOGIN_EVENT = Symbol('LOGIN_EVENT');

export const SAGE_ENTITY_AUTH = Symbol('SAGE_ENTITY_AUTH');

export const TO_LOGIN = Symbol('TO_LOGIN');

/**
 * 存储 AuthToken
 *
 * @param token token
 * @param expiredDay 过期时间
 * @memberof Sx
 */
export const setAuthorization = (token: string, expiredDay: number) => {
  S.setAuthToken(token, expiredDay);
};

/**
 * 获取 AuthToken
 *
 * @memberof Sx
 */
export const getAuthorization = () => {
  return S.getAuthToken();
};

/**
 * 删除 AuthToken
 *
 * @param token token
 * @param expiredDay 过期时间
 * @memberof Sx
 */
export const removeAuthorization = () => {
  S.removeAuthToken();
};

export const setEncryptType = (value: EncryptType) => {
  S.set(STORE.ENCRYPT_TYPE, value);
};

export const getEncryptType = () => {
  return S.get(STORE.ENCRYPT_TYPE);
};

export const getLocale = () => {
  return S.get(STORE.LOCALE, true);
};

export const setLocale = (lang: LocaleType) => {
  const updater = () => {
    if (getLocale() !== lang) {
      S.set(STORE.LOCALE, lang, true);

      event.emit(LANG_CHANGE_EVENT, lang);

      // chrome 不支持这个事件。所以人肉触发一下
      // if (window.dispatchEvent) {
      //   const event = new Event('languagechange');
      //   window.dispatchEvent(event);
      // }
    }
  };

  updater();
};

export const resetConfigProviderRtorage = () => {
  event.emit(RESET_STORAGE_EVENT);
};

export const setUserAccountId = (id: string | number) => {
  S.set(STORE.USER_ACCOUNT_ID, id.toString());

  // event.emit(USER_ACCOUNT_ID_CHANGE_EVENT, id.toString());
};

export const setUserAccountInfo = (info: Record<string, any>) => {
  S.set(STORE.USER_ACCOUNT_INFO, info);
};

export const setSecurityCheckModal = (
  isOpen: boolean,
  params?: Record<string, any>,
) => {
  event.emit(SECURITY_CHECK_EVENT, isOpen, params);
};

export const setSecurityToken = (token: string) => {
  S.set(STORE.TOKEN_SECURITY, token, true);
};

export const getSecurityToken = () => {
  return S.get(STORE.TOKEN_SECURITY, true);
};

export const setEntityId = (id: string) => {
  event.emit(ENTITY_ID_CHANGE_EVENT, id);
};

export const setLoginModalOpen = (isOpen: boolean) => {
  event.emit(LOGIN_EVENT, isOpen);
};

export const setEntityUUID = (id: string) => {
  event.emit(ENTITY_UUID_CHANGE_EVENT, id);
};

export const setTemporaryToken = (token: string) => {
  S.set(STORE.TEMPORARY_TOKEN, token);
};

export const setModule2FA = (value: string) => {
  S.set(STORE.MODULE_2FA, value);
};

export const getModule2FA = () => {
  return S.get(STORE.MODULE_2FA);
};

export const setSageEntityAuthTokens = (value: Record<string, any>) => {
  S.set(STORE.SAGE_ENTITY_AUTH, value, true);
};

export const getSageEntityAuthTokens = () => {
  return S.get(STORE.SAGE_ENTITY_AUTH, true) || {};
};

export const setSageEntityAuth = (isAuth: boolean) => {
  event.emit(SAGE_ENTITY_AUTH, isAuth);
};

export const toLogin = () => {
  event.emit(TO_LOGIN);
};
