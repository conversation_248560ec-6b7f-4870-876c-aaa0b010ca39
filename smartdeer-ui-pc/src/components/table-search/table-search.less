.deer-table-search {
  &-multiple-input {

    .ant-select-arrow {
      display: none !important;
    }
  }

  &-item {

    &-hidden {
      display: none !important;
    }
  }

  &-clear {
    display: flex;
    align-items: center;
    white-space: nowrap;

    span {
      margin-left: 4px;
    }
  }

  &-download {
    display: flex;
    align-items: center;
    white-space: nowrap;
    cursor: pointer;
    // transition: all 0.8s;
    // user-select: none;

    span {
      margin-left: 4px;
      // color: #94A3B8;
    }

    // &:hover {
    //   opacity: 0.8;
    // }
  }

  &-collapse {
    display: flex;
    align-items: center;
    white-space: nowrap;
    cursor: pointer;
    // transition: all 0.8s;
    // user-select: none;

    span {
      margin-left: 4px;
      // color: #94A3B8;
    }

    // &:hover {
    //   opacity: 0.8;
    // }
  }
}

