---
toc: content
group: 
  title: 过滤器
  order: 8
---

# TableSearch 搜索器

配合 CrudTable

## 示例

### 返回 searchItems

<code src="./demos/searchItems.tsx"></code>

### 返回 expressions

<code src="./demos/expressions.tsx"></code>

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| type | 标题 | `expressions` \| `searchItems` | `searchItems` | |
| columns | 列的配置 | `ColumnsType[]` | `--` | |
| onSearch | 点击搜索图标、清除图标，或按下回车键时的回调	 | `(values) => void` | `--` | |


```ts
export type ActionType = {
  value: string;
  label: string;
  options?: string;
  operator?: string;
  logic?: string;
};

export type ColumnsType = {
  value: string;
  label: string;
  type?: string;
  actions?: ActionType[];
};
```
