
import React from 'react';
import type { ColumnsType } from '../typing';
import FormItem from '../form-item';
import FormDependencies from '../form-dependencies';

const renderValueType = (
  item: ColumnsType,
  helpers: Record<string, any>,
) => {

  const getField = (values?: Record<string, any>) => {

    return (
      <FormItem
        key={[item.field, helpers.index || 0].join('_')}
        {...item}
        dependenciesValues={values}
      >
      </FormItem>
    );
  }

  if (item.dependencies && Array.isArray(item.dependencies) && item.dependencies.length > 0) {
    return (
      <FormDependencies
        names={item.dependencies}
        key={[item.field, helpers.index || 0].join('-')}
      >
        {getField}
      </FormDependencies>
    )
  }

  return getField()
}

export default renderValueType
