import { LoadingOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Modal } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { ConfigContext } from '../config-provider';

export interface TableDownloadHandle {
  open: () => void;
  close: () => void;
  executing: () => void;
  executed: () => void;
  downloading: () => void;
  onSuccess: () => void;
  onFail: () => void;
}

interface TableModalProps {
  downloadUrl?: string;
}

const TableDownloadModal = forwardRef<TableDownloadHandle, TableModalProps>(
  (props, ref) => {
    const [downloadLoading, setDownloadLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [modalTitle, setModalTitle] = useState('');
    const downloadUrl = props?.downloadUrl;

    const { language } = React.useContext(ConfigContext);

    const close = () => {
      setShowModal(false);
      setDownloadLoading(true);
    };

    const executing = () => {
      setModalTitle(language.TableSearch.actions.download.executing);
      setShowModal(true);
    };

    const downloading = () => {
      setModalTitle(language.TableSearch.actions.download.downloading);
    };

    const open = () => {
      setModalTitle(language.TableSearch.actions.download.downloading);
      setShowModal(true);
    };

    const executed = () => {
      setModalTitle(language.TableSearch.actions.download.generated);
      setDownloadLoading(false);
    };

    const onSuccess = () => {
      setDownloadLoading(false);
      setModalTitle(language.TableSearch.actions.download.successfully);
    };

    const onFail = () => {
      setDownloadLoading(false);
      setModalTitle(language.TableSearch.actions.download.failed);
    };

    useImperativeHandle(ref, () => ({
      executing,
      open,
      close,
      onSuccess,
      executed,
      onFail,
      downloading,
    }));

    const renderFooter = () => {
      if (downloadLoading) {
        return null;
      }
      if (!downloadUrl) {
        return (
          <Button type="primary" onClick={close}>
            {language.TableSearch.actions.download.gotIt}
          </Button>
        );
      }
      return (
        <Button type="primary" className="mt-[20px]">
          <a target="_blank" href={downloadUrl} rel="noopener noreferrer">
            {language.TableSearch.actions.download.downloadNow}
          </a>
        </Button>
      );
    };

    return (
      showModal && (
        <Modal
          open={showModal}
          footer={renderFooter()}
          width={320}
          onCancel={close}
        >
          <div className="flex justify-center flex-col items-center">
            {downloadLoading ? (
              <>
                <LoadingOutlined className="text-[28px] font-bold text-[#979797] mb-[15px]" />
                <div className="text-[16px] font-bold leading-[22px] mb-[8px]">
                  {modalTitle}
                </div>
                <div className="text-[14px] text-[#64748B] leading-[20px]">
                  {language.TableSearch.actions.download.downloadingTip}
                </div>
              </>
            ) : (
              <div className="text-[16px] font-bold leading-[22px] mb-[8px] self-start">
                {modalTitle}
              </div>
            )}
          </div>
        </Modal>
      )
    );
  },
);

export default TableDownloadModal;
