import { useMountMergeState } from '@ant-design/pro-utils';
import type { FormProps } from 'antd';
import { Form } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { isArray, isEmpty, isObject, isString } from 'lodash';
import RcResizeObserver from 'rc-resize-observer';
import React, { FC } from 'react';
import { classPrefix } from '.';
import { useFileAccessToken, useParamsDict, useSize } from '../../utils/hooks';
import { isBrowser } from '../../utils/isBrowser';
import { mergeNestedObjectValues } from '../../utils/mergeNestedObjectValues';
import { mergeProps } from '../../utils/withDefaultProps';
import TableSearchContent from './table-search-content';
import type { ColumnsType, SpanConfigType, TableSearchType } from './typing';
import { getSpanConfig } from './utils';

import { FetchMethodType } from '../../typing';
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate';
import swrFetcher, { swrDownloadFile } from '../../utils/swrFetcher';
import { ConfigContext } from '../config-provider';
import { TableSearchContext, defaultTableSearchContext } from './context';
import TableDownloadModal, {
  TableDownloadHandle,
} from './table-download-modal';
import './table-search.less';

const defaultWidth = isBrowser() ? document?.body?.clientWidth : 1024;

type RecordType = Record<string, any>;

export interface TableSearchProps {
  className?: string;
  style?: React.CSSProperties;

  labelCol?: RecordType;
  layout?: FormProps['layout'];
  labelAlign?: FormProps['labelAlign'];
  labelWrap?: boolean;

  size?: FormProps['size'];
  gutter?: number;
  labelWidth?: string;

  defaultColsNumber?: number;
  span?: SpanConfigType;
  collapseRender?: boolean;
  downloadExcelRender?: boolean;
  downloadProps?: {
    type?: 'async' | 'sync';
    searchParams?: boolean;
    searchParamsInjectionLocation?: string[];
    url?: string;
    executeKey?: string;
    method?: FetchMethodType;
    fileName?: string;
    params?: RecordType;
  };

  type?: TableSearchType;
  columns?: ColumnsType[];
  initialValues?: RecordType;
  onSearch?: (values: Record<string, any>) => void;

  onCollapse?: (collapsed: boolean) => void;
  showSearchButton?: boolean;
  onValuesChange?: (changedFields: any, allFields: any) => void;
}

const defaultProps = {
  gutter: 16,
  layout: 'horizontal',
  labelAlign: 'right',
  labelWrap: true,
  type: 'searchItems',
  size: 'middle',
  defaultColsNumber: 6,
  collapseRender: true,
  downloadExcelRender: true,
  columns: [],
  showSearchButton: defaultTableSearchContext.showSearchButton,
};

const TableSearch: FC<TableSearchProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const {
    className,
    style,

    layout,
    labelCol,
    labelAlign,
    labelWrap,

    gutter,
    labelWidth,

    defaultColsNumber,
    span,
    collapseRender,
    downloadExcelRender,
    downloadProps,

    type,
    columns,
    initialValues,

    onSearch,
    onCollapse,
    onValuesChange,

    showSearchButton,

    ...restProps
  } = props;

  const searchType = type;

  const [form] = Form.useForm();

  const downloadModalRef = React.useRef<TableDownloadHandle>(null);

  const { appFunctionApi } = React.useContext(ConfigContext);

  const paramsDict = useParamsDict();

  const { getTheCompleteFileUrl } = useFileAccessToken();

  const size = useSize(restProps.size);

  const [collapsed, setCollapsed] = React.useState(true);

  const [downloadUrl, setDownloadUrl] = React.useState('');

  const searchParamsRef = React.useRef<RecordType>({});

  const [width, setWidth] = useMountMergeState(
    () =>
      (typeof style?.width === 'number'
        ? style?.width
        : defaultWidth) as number,
  );

  const spanSize = React.useMemo(
    () => getSpanConfig(layout, width + gutter, span),
    [layout, width, span],
  );

  const showLength = React.useMemo(() => {
    // 查询重置按钮也会占一个spanSize格子，需要减掉计算
    if (spanSize.span !== 24 && defaultColsNumber !== undefined) {
      return defaultColsNumber - 1;
    }
    return Math.max(1, 24 / spanSize.span - 1);
  }, [defaultColsNumber, spanSize.span]);

  const handleFinish = (values: Record<string, any>) => {
    const reallyColumns = columns.filter((item) => {
      return values[item.field];
    });

    let resultValue: any = [];

    if (type === 'searchItems') {
      resultValue = reallyColumns.reduce((acc: any[], item) => {
        if (item.type === 'dateRangePicker') {
          if (values[item.field][1]) {
            const endTime = dayjs(Number(values[item.field][1]))
              .endOf('day')
              .valueOf()
              .toString();
            acc.push({
              key: item.field,
              action: 'gt',
              value: values[item.field][0],
            });
            acc.push({
              key: item.field,
              action: 'lt',
              value: endTime,
            });
          } else {
            // empty
          }
        } else {
          acc.push({
            action: item.action ? (item.action?.value as string) : 'like',
            key: item.field,
            value: values[item.field],
          });
        }
        return acc;
      }, []);
    } else if (type === 'expressions') {
      resultValue = reallyColumns.map((item) => {
        const resultJson: string[] = [];

        const { options, operator = ':', logic = 'AND' } = item.action || {};

        let value = values[item.field];

        if (dayjs.isDayjs(value)) {
          value = dayjs(value).valueOf().toString();
        }

        let expression: string = '';

        if (isArray(value)) {
          const expressions = value.map((v) => {
            return options
              ? `${options}=>${item.field}${operator}${v}`
              : `${item.field}${operator}${v}`;
          });

          expression = expressions.join(` ${logic} `);
        }

        if (isString(value)) {
          expression = options
            ? `${options}=>${item.field}${operator}${value}`
            : `${item.field}${operator}${value}`;
        }

        resultJson.push(expression);

        return resultJson;
      });

      resultValue = resultValue.flat(1);
    } else if (type === 'params') {
      resultValue = reallyColumns
        .map((item) => {
          return {
            [item.field]: values[item.field],
          };
        })
        .reduce((acc, curr) => {
          return { ...acc, ...curr };
        }, {});
    }

    if (type === 'params') {
      onSearch?.({
        ...resultValue,
      });
      searchParamsRef.current = { ...resultValue };
    } else {
      onSearch?.({
        [type]: resultValue,
      });
      searchParamsRef.current = {
        [type]: resultValue,
      };
    }
  };

  const handleFinishFailed = (errorInfo: any) => {
    console.log('Validate failed:', errorInfo);
    if (type === 'params') {
      onSearch?.({});
      searchParamsRef.current = {};
    } else {
      onSearch?.({
        [type]: [],
      });
      searchParamsRef.current = {
        [type]: [],
      };
    }
  };

  const handleDownloadExcel = async () => {
    const {
      type = 'sync',
      searchParams = false,
      searchParamsInjectionLocation = [],
      executeKey,
      url,
      fileName,
      method = 'POST',
    } = downloadProps || {};

    let { params = {} } = downloadProps || {};

    if (type === 'sync') {
      try {
        downloadModalRef?.current?.open();
        const newUrl = replaceVariablesInTemplate(paramsDict, url!);

        if (searchParams && !isEmpty(searchParamsRef.current)) {
          let searchParamsCurrent = searchParamsRef.current;
          if (searchType === 'expressions' || searchType === 'searchItems') {
            searchParamsCurrent = searchParamsCurrent[searchType];
          }
          params = mergeNestedObjectValues(
            params,
            searchParamsCurrent,
            searchParamsInjectionLocation,
          );
        }

        const paramsJson = replaceVariablesInTemplate(
          paramsDict,
          JSON.stringify(params),
        );

        await swrDownloadFile(
          newUrl!,
          fileName!,
          method,
          JSON.parse(paramsJson),
        );
        downloadModalRef?.current?.onSuccess();
      } catch (e) {
        downloadModalRef?.current?.onFail();
      }
      return;
    }
    // 异步下载
    let interval: number = 0;
    let uuid: string;
    try {
      downloadModalRef?.current?.executing();
      const { data: fetchUuid } = await swrFetcher(url!, method, params);
      uuid = fetchUuid;

      const getFileExecuteStatus = async (uuid: string) => {
        const getStatusUrl = replaceVariablesInTemplate(
          paramsDict,
          appFunctionApi!,
        );
        const { data } = await swrFetcher(getStatusUrl, method, {
          functionKey: executeKey,
          params: {
            uuid,
          },
        });
        return data.rs;
      };

      const startPolling = () => {
        interval = window.setInterval(async () => {
          const fileStatus = await getFileExecuteStatus(uuid);
          const { status, uuid: fetchUuid, filePath } = fileStatus;
          if (status === 1) {
            clearInterval(interval);
            let downloadUrl = await getTheCompleteFileUrl(filePath);
            downloadModalRef?.current?.executed();
            setDownloadUrl(downloadUrl);
          } else if (status === 2) {
            clearInterval(interval);
            downloadModalRef?.current?.onFail();
          } else {
            uuid = fetchUuid;
          }
        }, 1000);
      };

      startPolling();
    } catch (e) {
      clearInterval(interval);
      downloadModalRef?.current?.onFail();
    }
  };

  const handleSubmit = () => {
    if (showSearchButton) return;

    setTimeout(() => {
      form.submit();
    }, 10);
  };

  const handleClear = () => {
    form.resetFields();

    if (isObject(initialValues)) {
      const resetObject = Object.fromEntries(
        Object.keys(initialValues).map((key) => [key, undefined]),
      );
      form.setFieldsValue(resetObject);
    }

    form.submit();
  };

  const formContextValue = React.useMemo(() => {
    return {
      submit: handleSubmit,
      showSearchButton,
    };
  }, [showSearchButton]);

  // console.log('initialValues', initialValues)

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <RcResizeObserver
        key="table-search-resize-observer"
        onResize={(offset) => {
          if (width !== offset.width && offset.width > 17) {
            setWidth(offset.width);
          }
        }}
      >
        <Form
          layout={spanSize.layout}
          size={size}
          form={form}
          labelCol={labelCol}
          labelAlign={labelAlign}
          labelWrap={labelWrap}
          initialValues={initialValues}
          onFinish={handleFinish}
          onFinishFailed={handleFinishFailed}
          autoComplete="off"
          onValuesChange={onValuesChange}
        >
          <TableSearchContext.Provider value={formContextValue}>
            <TableSearchContent
              gutter={gutter}
              spanSize={spanSize}
              showLength={showLength}
              columns={columns}
              collapsed={collapsed}
              setCollapsed={setCollapsed}
              labelWidth={labelWidth}
              collapseRender={collapseRender}
              downloadExcelRender={downloadExcelRender}
              onCollapse={(value) => onCollapse?.(value)}
              onSearch={() => form.submit()}
              onClear={handleClear}
              onDownloadExcel={handleDownloadExcel}
              formSize={size}
              formLayout={spanSize.layout}
              showSearchButton={showSearchButton}
            />
          </TableSearchContext.Provider>
        </Form>
      </RcResizeObserver>

      <TableDownloadModal ref={downloadModalRef} downloadUrl={downloadUrl} />
    </div>
  );
};

export default TableSearch;
