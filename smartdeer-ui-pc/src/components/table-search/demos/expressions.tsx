import React from 'react';
import { TableSearch } from '@smartdeer-ui/pc'
import type { TableSearchProps } from '@smartdeer-ui/pc';

const columns: TableSearchProps['columns'] = [
  {
    "field": "fullText",
    "title": "全文",
    "type": "multipleInput",
    "action": {
      "value": "eq.and",
      "label": "包含（并且）",
      "options": "key=match_phrase",
      "operator": ":",
      "logic": "AND"
    }
  },
  {
    "field": "name",
    "title": "姓名",
    "type": "multipleInput",
    "action": {
      "value": "eq.and",
      "label": "包含（并且）",
      "options": "",
      "operator": ":",
      "logic": "AND"
    },
  },
  {
    "field": "mobile",
    "title": "手机号",
    "type": "multipleInput",
    "action": {
      "value": "eq.after",
      "label": "模糊匹配（后缀）",
      "options": "wildcard=after",
      "operator": ":"
    },
  },
  {
    "field": "email",
    "title": "邮箱",
    "type": "multipleInput",
  },
  {
    "field": "birthday.keyword",
    "title": "出生日期",
    "type": "datePicker",
  },
  {
    "field": "vocation",
    "title": "行业",
    "type": "multipleInput",
  },
  {
    "field": "lastCompany",
    "title": "最近一份工作公司",
    "type": "multipleInput",
  },
  {
    "field": "lastTitle",
    "title": "最近一份工作职位",
    "type": "multipleInput",
  },
  {
    "field": "bachelorSchool",
    "title": "本科学校",
    "type": "multipleInput",
  },
  {
    "field": "education",
    "title": "最高学历",
    "type": "multipleInput",
  },
  {
    "field": "schoolLevel",
    "title": "最高学校等级",
    "type": "select",
    "props": {
      "options": [
        {
          "label": "普通",
          "value": "0"
        },
        {
          "label": "211",
          "value": "1"
        },
        {
          "label": "985",
          "value": "2"
        },
        {
          "label": "211/985",
          "value": "3"
        }
      ]
    },
  },
  {
    "field": "bachelorSchoolLevel",
    "title": "本科学校等级",
    "type": "select",
    "props": {
      "options": [
        {
          "label": "普通",
          "value": "0"
        },
        {
          "label": "211",
          "value": "1"
        },
        {
          "label": "985",
          "value": "2"
        },
        {
          "label": "211/985",
          "value": "3"
        }
      ]
    },
  },
  {
    "field": "experienceInfo.company",
    "title": "公司名称",
    "type": "multipleInput",
  },
  {
    "field": "experienceInfo.title",
    "title": "职位",
    "type": "multipleInput",
  },
  {
    "field": "experienceInfo.summary",
    "title": "描述",
    "type": "multipleInput",
  },
  {
    "field": "educationInfo.school",
    "title": "学校",
    "type": "multipleInput",
  },
  {
    "field": "educationInfo.speciality",
    "title": "专业",
    "type": "multipleInput",
  },
  {
    "field": "dateRangePicker",
    "title": "时间断",
    "type": "dateRangePicker",
    "colSize": "2",
    // "labelProps": {
    //   "labelWidth": "120px"
    // }
  }
]

export default () => {

  const handleClickSearch = (values: Record<string, any>) => {
    console.log('onSearch', values)
  }

  return (
    <TableSearch
      type='expressions'
      columns={columns}
      onSearch={handleClickSearch}
    />
  )
}
