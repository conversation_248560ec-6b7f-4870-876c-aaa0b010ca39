import React from 'react';
import { TableSearch } from '@smartdeer-ui/pc'
import type { TableSearchProps } from '@smartdeer-ui/pc';

const columns: TableSearchProps['columns'] = [
  {
    "title": "页面名称",
    "type": "input",
    "field": "pageName"
  },
  {
    "title": "Key",
    "field": "pageKey",
    "type": "input",
  },
  {
    "title": "产品线",
    "field": "productLine",
    "type": "input",
  },
  {
    "title": "Tree Select",
    "field": "treeSelect",
    "type": "treeSelect",
    "props": {
      "options": [
        {
          id: 'parent 1',
          name: 'parent 1',
          childrens: [
            {
              id: 'parent 1-0',
              name: 'parent 1-0',
              childrens: [
                {
                  id: 'leaf1',
                  name: 'leaf1',
                },
                {
                  id: 'leaf2',
                  name: 'leaf2',
                },
                {
                  id: 'leaf3',
                  name: 'leaf3',
                },
                {
                  id: 'leaf4',
                  name: 'leaf4',
                },
                {
                  id: 'leaf5',
                  name: 'leaf5',
                },
                {
                  id: 'leaf6',
                  name: 'leaf6',
                },
              ],
            },
            {
              id: 'parent 1-1',
              name: 'parent 1-1',
              childrens: [
                {
                  id: 'leaf11',
                  name: 'leaf11'
                },
              ],
            },
          ],
        },
      ],
      fieldNames: {
        label: 'name',
        value: 'id',
        children: 'childrens'
      }
    }
  },
  {
    "field": "dateKey",
    "title": "出生日期",
    "type": "datePicker",
    "props": {
      "format": "YYYYMMDD"
    }
  },
  {
    "field": "createTime",
    "title": "时间断",
    "type": "dateRangePicker",
    "colSize": "2",
    "props": {
      showTime: { format: 'HH' }
    }
  }
]

export default () => {

  const handleClickSearch = (values: Record<string, any>) => {
    console.log('onSearch', values)
  }

  return (
    <TableSearch
      columns={columns}
      onSearch={handleClickSearch}
    />
  )
}
