import React, { FC } from 'react'
import { Col, Row } from 'antd';
import type { FormProps } from 'antd';
import { isString } from 'lodash';
import type { ColumnsType } from './typing'
import { useDeepCompareMemo } from '../../utils/hooks'
import { useRefFunction } from '../../utils/hooks'
import { omitUndefined } from '../../utils/omitUndefined'
import renderValueType from './value-type'
import TableSearchActions from './table-search-actions'

type RecordType = Record<string, any>

export interface TableSearchContentProps {
  className?: string;
  style?: React.CSSProperties;

  formSize?: FormProps['size'];
  formLayout?: FormProps['layout'];

  gutter: number;

  spanSize: {
    span: number;
    layout: FormProps['layout'];
  };
  showLength: number;

  labelWidth?: string;
  collapseRender: boolean;
  downloadExcelRender: boolean;

  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  setCollapsed: (collapse: boolean) => void;

  onSearch: () => void;
  onDownloadExcel: () => void;
  onClear: () => void;

  columns?: ColumnsType[];

  showSearchButton: boolean;
}

const TableSearchContent: FC<TableSearchContentProps> = (props) => {

  const {
    className,
    style,

    columns = [],

    formSize,
    gutter,
    formLayout,

    spanSize,
    showLength,

    labelWidth,
    collapseRender,
    downloadExcelRender,

    collapsed,
    setCollapsed,
    onCollapse,

    onSearch,
    onDownloadExcel,
    onClear,

    showSearchButton,
    ...restProps
  } = props

  // totalSpan 统计控件占的位置，计算 offset 保证查询按钮在最后一列
  const totalSpan = React.useRef(0);
  const itemLength = React.useRef(0);
  //首个表单项是否占满第一行
  const firstRowFull = React.useRef(false);
  // totalSize 统计控件占的份数
  const totalSize = React.useRef(0);

  // 用于分割计算
  const currentSpan = React.useRef(0);

  /**
   * 生成子项
   *
   * @param items
   */
  const genItems = useRefFunction((items: ColumnsType[]) => {
    return items
      .map((originItem, index) => {
        const colSize = isString(originItem.colSize) ? Number(originItem.colSize) : originItem.colSize || 1;

        const colSpan = Math.min(spanSize.span * (colSize || 1), 24);

        // 计算总的 totalSpan 长度
        totalSpan.current += colSpan;
        // 计算总的 colSize 长度
        totalSize.current += colSize;

        if (index === 0) {
          firstRowFull.current = colSpan === 24 && !originItem?.hidden;
        }

        const hidden: boolean =
          originItem?.hidden ||        // 如果收起了
          (collapsed && (firstRowFull.current ||            // 如果 超过显示长度 且 总长度超过了 24
            totalSize.current > showLength) &&
            !!index &&
            totalSpan.current >= 24);

        itemLength.current += 1;

        const item = omitUndefined({
          type: originItem.type,
          field: originItem.field,
          title: originItem.title,
          hidden: hidden,
          props: omitUndefined({
            ...originItem.props,
          }),
          effect: originItem.effect,
          labelProps: omitUndefined({
            labelWidth: labelWidth,
            ...originItem?.labelProps,
          }),
          dependencies: originItem.dependencies,
          colSpan: colSpan,
        })


        if (!hidden) {
          if (24 - (currentSpan.current % 24) < colSpan) {
            // 如果当前行空余位置放不下，那么折行
            totalSpan.current += 24 - (currentSpan.current % 24);
            currentSpan.current += 24 - (currentSpan.current % 24);
          }

          currentSpan.current += colSpan;
        }

        return renderValueType(item, {
          originItem,
          index
        })

      }).filter((field) => {
        return Boolean(field);
      });
  });

  const childNode = useDeepCompareMemo(() => {
    if (columns.length === 0) return null

    totalSpan.current = 0;
    itemLength.current = 0;
    firstRowFull.current = false;
    totalSize.current = 0;
    currentSpan.current = 0;

    return genItems(columns);
  }, [columns, collapsed, spanSize.span, showLength]);

  const offset = React.useMemo(() => {
    const offsetSpan = (currentSpan.current % 24) + spanSize.span

    if (offsetSpan > 24) {
      return 24 - spanSize.span
    }

    return 24 - offsetSpan;
  }, [currentSpan.current, (currentSpan.current % 24) + spanSize.span]);

  /** 是否需要展示 collapseRender */
  const needCollapseRender = React.useMemo(() => {
    if (totalSpan.current < 24 || totalSize.current <= showLength) {
      return false;
    }
    return true;
  }, [totalSize.current, showLength, totalSpan.current]);

  return (
    <Row gutter={gutter}>
      {childNode}

      <Col
        key='submitter'
        span={spanSize.span} offset={offset} style={{ textAlign: 'end' }}>
        <TableSearchActions
          spanSize={spanSize}
          formSize={formSize}
          formLayout={formLayout}
          collapsed={collapsed}
          setCollapsed={setCollapsed}
          onCollapse={onCollapse}
          collapseRender={needCollapseRender ? collapseRender : false}
          gutter={gutter}
          onSearch={onSearch}
          onClear={onClear}
          onDownloadExcel={onDownloadExcel}
          downloadExcelRender={downloadExcelRender}
          showSearchButton={showSearchButton}
        />
      </Col>
    </Row>
  )
}

export default TableSearchContent
