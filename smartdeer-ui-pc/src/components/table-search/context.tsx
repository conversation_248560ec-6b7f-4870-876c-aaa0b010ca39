import React from 'react'
import type { FormInstance } from 'antd';

type RecordType = Record<string, any>

export type TableSearchContextType = {
  form?: FormInstance<any>;
  submit?: () => void;
  formVariables?: RecordType;
  showSearchButton: boolean;
}

export const defaultTableSearchContext: TableSearchContextType = {
  formVariables: {},
  showSearchButton: false,
}

export const TableSearchContext =
  React.createContext<TableSearchContextType>(defaultTableSearchContext)



