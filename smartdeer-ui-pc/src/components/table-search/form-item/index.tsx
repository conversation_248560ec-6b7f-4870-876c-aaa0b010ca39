import React from 'react';
import classNames from 'classnames'
import { Form, Col } from 'antd';
import { mergeProps } from '../../../utils/withDefaultProps'
import { isDeepEqualReact } from '../../../utils'
import type { ColumnsType } from '../typing'
import FieldComponent from '../field-components'
import { isString } from 'lodash';
import { classPrefix } from '../.'

// const classPrefix = `deer-form-item`

export type FormItemProps = ColumnsType & {
  colSpan?: number | string;
  dependenciesValues?: Record<string, any>,
}

const defaultProps = {
  labelProps: {},
  dependenciesValues: {}
}

const FormItem: React.FC<FormItemProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    field,
    title,
    labelProps,
    type,
    colSpan,
    hidden,
    dependenciesValues,
    ...extra
  } = props

  const dependencies = labelProps.dependencies || [];

  return (
    <Col
      span={isString(colSpan) ? Number(colSpan) : colSpan}
      className={classNames(
        `${classPrefix}-item`,
        { [`${classPrefix}-item-hidden`]: hidden },
      )}
    >
      <Form.Item
        className={labelProps?.className}
        style={{ ...labelProps?.style }}
        name={field}
        label={!!title && (
          <div style={{ width: labelProps?.labelWidth }}>{title}</div>
        )}
        dependencies={dependencies}
      >
        <FieldComponent
          field={field!}
          valueType={type}
          fieldProps={extra.props}
          effect={extra.effect}
          dependenciesValues={dependenciesValues}
        />
      </Form.Item>
    </Col >
  )
};

export default React.memo(FormItem, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
