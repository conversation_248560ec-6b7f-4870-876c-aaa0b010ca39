import type { TableSearchProps } from './table-search';
import InternalTableSearch from './table-search';

export type { TableSearchProps } from './table-search';

export const classPrefix = `deer-table-search`;

type CompoundedComponent = React.FC<TableSearchProps> & {
  // Item: typeof TableFilterItem;
};

const TableSearch = InternalTableSearch as CompoundedComponent;

// TableSearch.Item = TableFilterItem;

export default TableSearch;
