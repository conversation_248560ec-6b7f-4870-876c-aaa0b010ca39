import React from 'react';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import type { FieldProps } from '../typing'
import { ConfigContext } from '../../../config-provider'
import { convertTimezoneToUTC, convertUTCToTimezone } from '../../../../utils/time';
import { TableSearchContext } from '../../context'

const FieldDateRangePicker: React.FC<FieldProps> = (props) => {
  const {
    value,
    onChange,
    fieldProps
  } = props

  const { timezone } = React.useContext(ConfigContext)

  const { submit } = React.useContext(TableSearchContext)

  const handleChange = (_: any, timeString: string | string[]) => {
    let newTimeString: string | string[] | undefined = ''
    let startDate: string = '';
    let endDate: string = '';

    if (Array.isArray(timeString)) {
      if (timezone) {
        startDate = convertTimezoneToUTC(timeString[0], timezone, '') + ''
        endDate = convertTimezoneToUTC(timeString[1], timezone, '') + ''
      } else {
        startDate = dayjs(timeString[0] as string).valueOf() + ''
        endDate = dayjs(timeString[1] as string).valueOf() + ''
      }

      if (startDate !== 'NaN' && endDate !== 'NaN') {
        newTimeString = [startDate, endDate];
      } else {
        newTimeString = undefined;
      }
    }
    onChange?.(newTimeString)

    submit?.()
  }

  let newValue: any = Array.isArray(value) ? [dayjs(Number(value[0])), dayjs(Number(value[1]))] : ''

  if (Array.isArray(newValue) && timezone) {
    newValue = [convertUTCToTimezone(newValue[0], timezone), convertUTCToTimezone(newValue[1], timezone)]
  }

  return (
    <DatePicker.RangePicker
      // value={newValue}
      placeholder={[' ', ' ']}
      {...fieldProps}
      style={{ width: '100%', ...fieldProps?.style }}
      onChange={handleChange}
    />
  );
};

export default FieldDateRangePicker
