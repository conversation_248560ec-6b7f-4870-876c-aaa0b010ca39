import React from 'react';
import { Select } from 'antd';
import { isArray, isNil } from 'lodash';
import { isString } from 'lodash';

import type { FieldProps } from '../typing'

import { mergeProps } from '../../../../utils/withDefaultProps'
import { usePropsOptions } from '../../../../utils/hooks'
import { TableSearchContext } from '../../context'

// 兼容代码-----------
import 'antd/lib/select/style';
//------------

const filterOption = (input: string, option?: { label: string; value: string }) =>
  (option?.label ?? '').toLowerCase().includes(input.toLowerCase());

const defaultProps = {
  fieldProps: {
    showSearch: true
  }
}

const FieldSelect: React.FC<FieldProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const { value, fieldProps, onChange } = props

  const { mode } = fieldProps || {};

  const { submit } = React.useContext(TableSearchContext)

  const { isLoading, options } = usePropsOptions(props)

  const handleChange = (value: string) => {
    onChange?.(value)

    submit?.()
  }

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  const selectedValue = React.useMemo(() => {
    if (mode === 'multiple' || mode === 'tags') {
      let newValue;
      if (isArray(value)) {
        return value;
      }
      if (!value) return [];
      try {
        newValue = value.split(',');
      } catch (e) {
        newValue = [];
      }
      return newValue;
    }
    if (isNil(value)) return undefined;
    return isArray(value) ? value : value.toString();
  }, [value, mode]);

  return (
    <Select
      {...fieldProps}
      value={selectedValue}
      fieldNames={{
        value: 'value',
        label: 'label',
      }}
      allowClear={true}
      disabled={isDisabled}
      style={{ width: '100%' }}
      loading={isLoading}
      options={options}
      filterOption={filterOption}
      onChange={handleChange}
    />
  );
};


export default FieldSelect

