import React from 'react';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import { ConfigContext } from '../../../config-provider'
import type { FieldProps } from '../typing'
import { convertTimezoneToUTC, convertUTCToTimezone } from '../../../../utils/time';
import { isNumber, isString } from 'lodash';
import { TableSearchContext } from '../../context'

const FieldDatePicker: React.FC<FieldProps> = (props) => {
  const {
    value,
    onChange,
    fieldProps
  } = props

  const { timezone } = React.useContext(ConfigContext)

  const { submit } = React.useContext(TableSearchContext)

  const handleChange = (_: any, timeString: string | string[]) => {
    let newTimeString: string = '';

    if (timeString) {
      if (timezone) {
        newTimeString = convertTimezoneToUTC(timeString, timezone, 'x') + ''
      } else {
        newTimeString = dayjs(timeString as string).valueOf() + ''
      }

      if (fieldProps?.defaultHours) {
        newTimeString = dayjs(timeString as string).add(fieldProps?.defaultHours, 'hour').valueOf() + ''
      }

      if (fieldProps?.format) {
        newTimeString = dayjs(timeString as string).format(fieldProps?.format)
      }
    }

    onChange?.(newTimeString)

    submit?.()
  }

  const newValue: any = React.useMemo(() => {
    let newValue: any = value ? fieldProps?.format ? value : dayjs(Number(value)) : ''

    if (newValue && timezone) {
      newValue = convertUTCToTimezone(newValue, timezone)
    }

    return newValue
  }, [value])

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <DatePicker
      value={newValue}
      placeholder=' '
      {...fieldProps}
      disabled={isDisabled}
      style={{ width: '100%', ...fieldProps?.style }}
      onChange={handleChange}
    />
  );
};

export default FieldDatePicker
