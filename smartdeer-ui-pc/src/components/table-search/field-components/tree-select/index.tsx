import { TreeSelect } from 'antd';
import { isNil } from 'lodash';
import React from 'react';

import omit from '../../../../utils/omit';

import type { FieldProps } from '../typing';

import { isString } from 'lodash';
import { usePropsOptions } from '../../../../utils/hooks';
import { mergeProps } from '../../../../utils/withDefaultProps';
import { TableSearchContext } from '../../context'
import { evaluateLogicalExpression } from '../../../../utils/evaluateLogicalExpression';
import SvgIcon from '../../../../components/schema-page/field-components/svg-icon';

const defaultProps = {
  fieldProps: {
    showSearch: true,
    treeDefaultExpandAll: true,
    allowClear: true,
  },
};

const FieldTreeSelect: React.FC<FieldProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const { value, fieldProps, onChange } = props;

  const { showIcon, showIconIf, icon } = fieldProps;

  const { submit } = React.useContext(TableSearchContext)

  const { isLoading, options } = usePropsOptions(props);

  const handleChange = (value: string) => {
    const val = !isNil(value)
      ? Array.isArray(value)
        ? value
        : value.toString()
      : undefined;
    onChange?.(val);

    submit?.();
  };

  const isDisabled = isString(fieldProps?.disabled)
    ? fieldProps?.disabled === 'true'
    : fieldProps?.disabled;

  const treeSelectProps = React.useMemo(() => {
    return omit(fieldProps, [
      'supplementingTheString',
      'supplementingTheKey',
      'fieldNames',
      'parentSelectable',
      'showIconIf',
      'icon',
      'showIcon',
    ]);
  }, [fieldProps]);

  const newValue = !isNil(value)
    ? Array.isArray(value)
      ? value
      : value.toString()
    : undefined

  const renderTreeTitle = (node: any) => {
    if (!showIcon || !showIconIf || !icon) {
      return node.label;
    }
    const show = evaluateLogicalExpression(node, showIconIf);
    if (show) {
      return (
        <span className="flex items-center">
          {node.label}
          <SvgIcon name={icon} style={{ marginLeft: '6px', fontSize: '13px', color: '#999' }}/>
        </span>
      );
    }
    return node.label;
  }

  return (
    <TreeSelect
      treeDefaultExpandAll
      allowClear
      {...treeSelectProps}
      fieldNames={{
        value: 'value',
        label: 'label',
        children: 'children',
      }}
      value={newValue}
      loading={isLoading}
      disabled={isDisabled}
      treeData={options}
      style={{ width: '100%' }}
      onChange={handleChange}
      treeTitleRender={renderTreeTitle}
    />
  );
};

export default FieldTreeSelect;
