import type { EffectType } from '../../../typing';

export type FieldValueType =
  | 'multipleInput'
  | 'select'
  | 'treeSelect'
  | 'input'
  | 'datePicker'
  | 'dateRangePicker';

export type FieldEffectType = EffectType;

export type FieldComponentProps = {
  field: string;

  valueType: FieldValueType;

  fieldProps?: FieldPropsType;

  effect?: FieldEffectType;

  dependenciesValues: Record<string, any>;

  onChange?: (...args: any) => void;
  value?: any;
};

export type FieldOptionExtraType = {
  elementKey?: string;
};

export type FieldOptionType = {
  value?: string;
  label?: string;
  children?: FieldOptionType[];
  extra?: FieldOptionExtraType | null;
  template?: string;
  [key: string]: any;
};

export type FieldProps = {
  fieldProps?: FieldPropsType;

  effect?: FieldEffectType;

  dependenciesValues?: Record<string, any>;

  value?: any;
  onChange?: any;
};

export type FieldPropsType = {
  options?: FieldOptionType[];
  fieldNames?: {
    label?: string;
    value?: string;
    children?: string;
  };

  [key: string]: any;
};
