import React from 'react';
import { Select } from 'antd';
import type { FieldProps } from '../typing'
import { isString } from 'lodash';
import { TableSearchContext } from '../../context'

// 兼容代码-----------
import 'antd/lib/select/style';
//------------

const FieldMultipleInput: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps } = props

  const { submit } = React.useContext(TableSearchContext)

  const handleChange = (value: string) => {
    onChange?.(value)
  }

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Select
      className='deer-table-search-multiple-input'
      value={value}
      maxTagCount={3}
      mode='tags'
      tokenSeparators={[',']}
      options={[]}
      allowClear
      disabled={isDisabled}
      onChange={handleChange}
      onBlur={() => {
        submit?.()
      }}
    />
  );
};

export default FieldMultipleInput
