import React from 'react';
import { Input } from 'antd';
import type { FieldProps } from '../typing'
import { isString } from 'lodash';
import { TableSearchContext } from '../../context'

// 兼容代码-----------
import 'antd/lib/input/style';
//------------

const FieldInput: React.FC<FieldProps> = (props) => {
  const { value, onChange, fieldProps } = props

  const { submit } = React.useContext(TableSearchContext)

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Input
      {...fieldProps}
      disabled={isDisabled}
      value={value}
      onChange={onChange}
      allowClear
      onBlur={() => {
        submit?.()
      }}
    />
  );
};

export default FieldInput
