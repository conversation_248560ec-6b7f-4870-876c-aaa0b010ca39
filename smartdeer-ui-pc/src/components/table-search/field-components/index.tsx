import React from 'react';
import { isDeepEqualReact, omitUndefined, omit } from '../../../utils'
import type { FieldProps, FieldComponentProps } from './typing'
import { classPrefix } from '../.'
import classNames from 'classnames';

// 表单组件
import FieldInput from './input'
import FieldMultipleInput from './multiple-input'
import FieldSelect from './select'
import FieldDatePicker from './date-picker'
import FieldDateRangePicker from './date-range-picker'
import FieldTreeSelect from './tree-select'

const defaultRenderComponent = (
  valueType: string,
  props: FieldProps,
) => {
  let childNode: React.ReactNode = null;

  switch (valueType) {
    case 'input':
      childNode = <FieldInput {...props} />;
      break;
    case 'multipleInput':
      childNode = <FieldMultipleInput {...props} />;
      break;
    case 'select':
      childNode = <FieldSelect {...props} />;
      break;
    case 'treeSelect':
      childNode = <FieldTreeSelect {...props} />;
      break;
    case 'datePicker':
      childNode = <FieldDatePicker {...props} />;
      break;
    case 'dateRangePicker':
      childNode = <FieldDateRangePicker {...props} />;
      break;

    default:
      childNode = <div className={`bg-red-500 text-white rounded px-2 py-1 mb-[20px]`}>没有实现的组件: {valueType}</div>
  }

  return <>{childNode}</>
}

const FieldComponent: React.FC<FieldComponentProps> = (props) => {
  const {
    valueType,
    fieldProps,
    ...extra
  } = props

  const renderedDom = defaultRenderComponent(valueType, omitUndefined({
    fieldProps: fieldProps && omit(fieldProps, ['className', 'style']),
    value: extra.value,
    onChange: extra.onChange,
    effect: extra.effect,
    dependenciesValues: extra.dependenciesValues,
  }))

  return (
    <div className={classNames(`${classPrefix}-field`, fieldProps?.className)} style={{ ...fieldProps?.style }}>
      {renderedDom}
    </div>
  )
}

export default React.memo(FieldComponent, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
});
