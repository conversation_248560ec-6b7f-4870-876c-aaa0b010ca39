import React from 'react';
import { Form } from 'antd';
import { isDeepEqualReact } from '../../../utils'

export interface FormDependenciesProps {
  names: string[];
  children: (values: { [key: string]: any }) => React.ReactNode;
};

const FormDependencies: React.FC<FormDependenciesProps> = (props) => {

  const { children, names: nameList } = props

  return (
    <Form.Item
      noStyle
      shouldUpdate={(prevValues, currentValues) => {
        return nameList.some((name) => {
          return !isDeepEqualReact(prevValues[name], currentValues[name]);
        });
      }}
    >
      {(form) => {
        const values: Record<string, any> = {} as Record<string, any>;

        for (let i = 0; i < nameList.length; i++) {
          const itemName = nameList[i]

          values[itemName] = form?.getFieldValue?.(itemName);
        }

        return <>{children(values)}</>
      }}
    </Form.Item>
  )
};

export default FormDependencies
