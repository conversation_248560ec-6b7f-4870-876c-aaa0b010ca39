import React from 'react'
import { Space, Button, Popover } from 'antd';
import type { FormProps } from 'antd';
import { DownOutlined, UpOutlined, MoreOutlined } from '@ant-design/icons';
import Icon from '../icon'
import { classPrefix } from '.'
import { ConfigContext } from '../config-provider'

export interface ActionsProps {
  gutter: number;
  formSize: FormProps['size'];
  formLayout: FormProps['layout'];

  spanSize: {
    span: number;
    layout: FormProps['layout'];
  };

  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  setCollapsed: (collapse: boolean) => void;

  onSearch: () => void;
  onDownloadExcel: () => void;
  onClear: () => void;

  downloadExcelRender: boolean;
  collapseRender: boolean;

  isForm?: boolean;
  showSearchButton: boolean
};

const TableSearchActions: React.FC<ActionsProps> = (props) => {

  const {
    gutter,
    formSize,
    formLayout,
    spanSize,

    collapsed,
    setCollapsed,
    onCollapse,

    downloadExcelRender,
    collapseRender,

    onSearch,
    onClear,
    onDownloadExcel,

    showSearchButton,

    ...restProps
  } = props

  const { language } = React.useContext(ConfigContext)

  return (
    <Space
      size={gutter}
      wrap
      style={{ paddingTop: spanSize.span !== 24 && formLayout === 'vertical' ? '30px' : '' }}
    >
      <Button
        // icon={<Icon.ClearOutline />}
        onClick={onClear}
      >
        <span>{language.TableSearch.actions.clear}</span>
      </Button>

      {showSearchButton && (
        <Button
          // icon={<SearchOutlined />}
          type='primary'
          onClick={onSearch}
        >
          {language.TableSearch.actions.search}
        </Button>
      )}

      {downloadExcelRender && (
        <Popover placement='bottom' content={
          <a className={`${classPrefix}-download`} onClick={onDownloadExcel}>
            <Icon.DownloadOutlined />
            <span>{language.TableSearch.actions.downloadExcel}</span>
          </a>
        }>
          <Button type='primary'>
            <MoreOutlined />
          </Button>
        </Popover>
      )}

      {
        collapseRender && (
          <React.Fragment>
            {collapsed ? (
              <a className={`${classPrefix}-collapse`} onClick={() => {
                setCollapsed?.(!collapsed)
                onCollapse?.(!collapsed)
              }}>
                <span>{language.TableSearch.actions.expand}</span>
                <DownOutlined />
              </a>
            ) : (
              <a className={`${classPrefix}-collapse`} onClick={() => {
                setCollapsed?.(!collapsed)
                onCollapse?.(!collapsed)
              }}>
                <span>{language.TableSearch.actions.collapse}</span>
                <UpOutlined />
              </a>
            )}
          </React.Fragment>
        )
      }
    </Space >
  )
}

export default TableSearchActions
