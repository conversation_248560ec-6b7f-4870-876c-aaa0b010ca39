import type { EffectType } from '../../typing';
import type { FieldPropsType, FieldValueType } from './field-components/typing';

export type SpanConfigType =
  | number
  | {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
      xxl: number;
    };

export type ActionType = {
  value: string | string[];
  options?: string;
  operator?: string;
  logic?: string;
  label?: string;
};

export type ColumnsType = {
  // 组件类型
  type: FieldValueType;

  // 唯一标识
  field: string;

  // 标签的文本
  title: string | React.ReactElement;

  action?: ActionType;

  colSize?: number | string;

  // 显示隐藏
  hidden?: boolean;

  props?: FieldPropsType;

  labelProps?: {
    labelWidth?: string;
    className?: string;
    style?: React.CSSProperties;
    [keyof: string]: any;
  };

  effect?: EffectType;

  dependencies?: string[];
};

export type TableSearchType = 'expressions' | 'searchItems' | 'params';
