import {
  Timeline as AntdTimeline,
  TimelineProps as AntdTimelineProps,
  TimelineItemProps,
} from 'antd';
import classNames from 'classnames';
import React, { FC } from 'react';
import { transformDataBasedOnRules } from '../../utils/transformDataBasedOnRules';
import { mergeProps } from '../../utils/withDefaultProps';

import { TransformDataRules } from '../../typing';
import './index.less';

const classPrefix = `deer-timeline`;

export interface TimelineProps extends AntdTimelineProps {
  className?: string;
  style?: React.CSSProperties;
  transforms?: TransformDataRules;
  labelInChildren?: boolean;
}

const defaultProps = {
  items: [],
};

const Timeline: FC<TimelineProps> = (p) => {
  const props = mergeProps(defaultProps, p);

  const {
    className,
    style,
    transforms,
    items,
    labelInChildren,

    ...timelineProps
  } = props;

  let children = transforms
    ? (transformDataBasedOnRules(items, transforms) as TimelineItemProps[])
    : items;

  if (labelInChildren && children?.length) {
    children = children.map((item) => {
      const { label, children, ...rest } = item;
      return {
        ...rest,
        children: (
          <>
            <div>{label}</div>
            <div>{children}</div>
          </>
        ),
      };
    });
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <AntdTimeline {...timelineProps} items={children} />
    </div>
  );
};

export default Timeline;
