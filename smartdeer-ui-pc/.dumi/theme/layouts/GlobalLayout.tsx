
import React from 'react';
import { ConfigProvider } from '@smartdeer-ui/pc';
import { history, useOutlet, useParams, useSearchParams, useServerInsertedHTML } from 'dumi';


const APP_API_BASE_URL = process.env.APP_API_BASE_URL
const APP_FUNCTION_API = process.env.APP_FUNCTION_API
const APP_PROCESS_API = process.env.APP_PROCESS_API

const Layout: React.FC = () => {
  const outlet = useOutlet();

  return (
    <ConfigProvider
      userAccountId='1331'
      locale='en-US'
      appApiBaseUrl={APP_API_BASE_URL}
      appFunctionApi={APP_FUNCTION_API}
      appProcessApi={APP_PROCESS_API}
      timezone={'Asia/Shanghai'}
      router={history}
      useUrlParams={useParams}
    >
      {outlet}
    </ConfigProvider>
  );
};

export default Layout;
