# @smartdeer-ui/pc

## 环境准备

确保正确安装 [Node.js](https://nodejs.org/en/) 推荐版本 v20.10.0

```bash
$ node -v
v20.10.0
```

## 开发

```bash
# install dependencies
$ npm install

# develop library by docs demo
$ npm start

# build library source code
$ npm run build

# build library source code in watch mode
$ npm run build:watch

# build docs
$ npm run docs:build

# check your project for potential problems
$ npm run doctor
```

## 发布

允许覆盖版本

发布前请修改 `package.json` 的 `version`

```bash
$ npm publish --registry=https://linglu-npm.pkg.coding.net/linglupin/smartdeer-ui/
```
