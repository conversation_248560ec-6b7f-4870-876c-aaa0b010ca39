module.exports = {
  content: [
    './src/**/*.tsx',
  ],
  theme: {
    extend: {
      fontSize: {
        '10': ['10px', '12px'],
        '12': ['12px', '14px'],
        '14': ['14px', '18px'],
        '16': ['16px', '20px'],
        '18': ['18px', '21px'],
        '20': ['20px', '24px'],
        '24': ['24px', '28px'],
        '26': ['26px', '32px'],
        '32': ['32px', '36px'],
        '44': ['44px', '56px'],
      },

      colors: {
        '#22': '#222222',
        '#22-05': '#rgba(34, 34, 34, 0.05)',
        '#22-3': '#rgba(34, 34, 34, 0.3)',

        '#f7': '#F7F7F7',
        '#eb': '#EBEBEB',
        '#dd': '#DDDDDD',
        '#d3': '#D3D3D3',
        '#c2': '#C2C2C2',
        '#b0': '#B0B0B0',
        '#71': '#717171',
        '#5e': '#5E5E5E',
        '#47': '#474747',
        '#30': '#303036',

        '#p1': '#FF814F',
        '#p2': '#FF812A',
        '#p3': '#FFC0A7',
        '#p4': '#FFF9F6',

        '#g1': '#FF9111',

        '#err1': '#F91B42',
        '#err2': '#C13515',

        '#t1': '#FF814F',
        '#t2': '#FF812A',
        '#t3': '#008A05',
        '#t4': '#BC6526',
      },

      borderRadius: {
        '4': '4px',
        '6': '6px',
        '8': '8px',
        '12': '12px',
        '16': '16px',
        '18': '18px',
        '20': '20px',
        '24': '24px',
        '32': '32px',
        '64': '64px',
      },

      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
      },

      animation: {
        fadeIn: 'fadeIn 0.5s ease-in-out',
      }
    },
  },
  plugins: [],
}

