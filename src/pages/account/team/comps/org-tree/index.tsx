import api from '@/services';
import { Loading } from '@smartdeer-ui/pc';
import { useParams } from '@umijs/max';
import { Empty } from 'antd';
import { FC, useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import Panel from './panel';
import Sidebar from './sidebar';
import { TreeData } from './types';

const updateTreeData = (
  list: TreeData[],
  kid: React.Key,
  children: TreeData[],
): TreeData[] => {
  const data = list.map((node) => {
    if (node.kid === kid) {
      return {
        ...node,
        children: children.map((item, index) => {
          let childPos = kid.split('-').slice(0, -1).join('-');
          return {
            ...item,
            kid: `${childPos}-${index}-${item.id}`,
          };
        }),
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, kid, children),
      };
    }
    return node;
  });
  return data;
};

const findDataById = (data: TreeData[], kid: string): TreeData | null => {
  for (let i = 0; i < data.length; i++) {
    if (data[i].kid === kid) {
      return data[i];
    }
    if (data[i].children) {
      const result = findDataById(data[i].children, kid);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

const OrgTree: FC = () => {
  const [currentItem, setCurrentItem] = useState<TreeData>();
  const [columnsData, setColumnsData] = useState<TreeData[]>([]);
  const [treeData, setTreeData] = useState<TreeData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  const params = useParams();
  const entityId = params.entityId as string;

  const fetchRoot = async () => {
    const res = await api.account.getTeamRootNode(entityId, {});
    if (res?.rs?.id) {
      setSelectedKeys([`0-${res.rs.id}`]);
      setExpandedKeys([`0-${res.rs.id}`]);
    }

    return res?.rs ? [res?.rs] : [];
  };

  const fetchNode = async (uuid: string) => {
    if (!uuid) return;
    const res = await api.account.getTeamChildNode(entityId, { uuid });
    return res?.rs || [];
  };

  const fetchOrgStructure = async ({
    uuid = '',
    kid,
  }: {
    uuid?: string;
    kid?: string;
  }) => {
    try {
      setIsLoading(true);
      let data;
      if (!uuid) {
        data = await fetchRoot();
      } else {
        data = await fetchNode(uuid);
      }

      data.forEach((node: TreeData) => {
        const childNum = node.childrenCount || 0;
        node.isLeaf = childNum > 0 ? false : true;
        node.kid = kid ? kid : `0-${node.id}`;
        node.isRoot = uuid ? false : true;
      });
      const newTreeData = updateTreeData(treeData, kid || '0', data);
      if (treeData.length) {
        setTreeData(newTreeData);
      } else {
        setTreeData(data);
      }
      return newTreeData;
    } catch (error: any) {
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrgStructure({});
  }, []);

  /**
   * 设置列数据
   * @param {TreeData} item - 要设置的列数据
   */
  const handleSetColumnsData = async (item: TreeData) => {
    setColumnsData([item]);
  };

  /**
   * 处理侧边栏点击事件，点击同时触发展开收起
   * @param {TreeData} item - 点击的侧边栏项
   */
  const handleSidebarItemClick = (item: TreeData[]) => {
    const id = item[0].kid;
    setSelectedKeys([id]);
    if (!expandedKeys.includes(id)) {
      setExpandedKeys([...expandedKeys, id]);
    }
    handleSetColumnsData(item[0] || {});
  };

  /**
   * 根据不同的displayMode获取panel组件需要的数据
   * @returns {panelData}
   */
  const panelData = useMemo<TreeData>(() => {
    if (columnsData.length === 0) return {} as TreeData;
    return columnsData[0];
  }, [columnsData]);

  useEffect(() => {
    if (!treeData.length) return;
    if (!currentItem) {
      setCurrentItem(treeData[0] || {});
    }
  }, [treeData]);

  useEffect(() => {
    let data = findDataById(treeData, selectedKeys[0]);

    if (data) {
      setColumnsData([data]);
    } else {
      setColumnsData([]);
    }
  }, [treeData, selectedKeys, currentItem]);

  if (!treeData) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;
  }

  if (isLoading && !treeData?.length) {
    return <Loading />;
  }

  if (!treeData?.length) {
    return null;
  }

  return (
    <div className={styles.orgTreeContainer}>
      <div className={styles.contentBox}>
        <div className="flex gap-[20px] bg-white mx-5 flex-col md:flex-row">
          <Sidebar
            data={treeData}
            handleSidebarItemClick={handleSidebarItemClick}
            fetchOrgStructure={fetchOrgStructure}
            setTreeData={setTreeData}
            selectedKeys={selectedKeys}
            expandedKeys={expandedKeys}
            setExpandedKeys={setExpandedKeys}
            setSelectedKeys={setSelectedKeys}
          />
          <div className="flex-1 rounded-8 border-[#ddd]">
            <Panel data={panelData} treeLoading={isLoading} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrgTree;
