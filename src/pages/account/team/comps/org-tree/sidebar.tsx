import { Tree } from 'antd';
import { AntTreeNodeProps } from 'antd/es/tree';
import { FC } from 'react';
import { TreeData } from './types';

interface SidebarProps {
  data?: TreeData[];
  selectedKeys: string[];
  expandedKeys: string[];
  setTreeData: React.Dispatch<React.SetStateAction<TreeData[]>>;
  fetchOrgStructure: ({
    uuid,
    kid,
  }: {
    uuid?: string;
    kid?: string;
  }) => Promise<TreeData[] | undefined>;
  handleSidebarItemClick: (item: TreeData[]) => void;
  setExpandedKeys: React.Dispatch<React.SetStateAction<string[]>>;
  setSelectedKeys: React.Dispatch<React.SetStateAction<string[]>>;
}

const expandedIcon = (
  <svg
    width="10"
    height="9"
    viewBox="0 0 10 9"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5 5.141L2.41451 2.55551C2.02399 2.16499 1.46997 2.08584 1.17707 2.37873C0.88418 2.67163 0.963325 3.22565 1.35385 3.61617L4.18228 6.4446C4.42557 6.68789 4.73231 6.81033 4.99985 6.79726C5.26744 6.81043 5.57432 6.68799 5.8177 6.44461L8.64613 3.61618C9.03665 3.22566 9.1158 2.67164 8.82291 2.37875C8.53001 2.08586 7.97599 2.165 7.58547 2.55552L5 5.141Z"
      fill="black"
      fillOpacity="0.85"
    />
  </svg>
);

const unexpandIcon = (
  <svg
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5 4.359L2.41451 6.94449C2.02399 7.33501 1.46997 7.41416 1.17707 7.12127C0.88418 6.82837 0.963325 6.27435 1.35385 5.88383L4.18228 3.0554C4.42557 2.81211 4.73231 2.68967 4.99985 2.70274C5.26744 2.68957 5.57432 2.81201 5.8177 3.05539L8.64613 5.88382C9.03665 6.27434 9.1158 6.82836 8.82291 7.12125C8.53001 7.41414 7.97599 7.335 7.58547 6.94448L5 4.359Z"
      fill="black"
      fillOpacity="0.85"
    />
  </svg>
);

const lineIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="10"
    height="10"
    viewBox="0 0 10 10"
    fill="none"
  >
    <path d="M1 0.5V9.5H10" stroke="#DDDDDD" />
  </svg>
);

const Sidebar: FC<SidebarProps> = (props) => {
  const {
    data,
    handleSidebarItemClick,
    fetchOrgStructure,
    selectedKeys,
    expandedKeys,
    setExpandedKeys,
    setSelectedKeys,
  } = props;

  if (!data) return null;

  const onLoadData = async ({
    id,
    uuid,
    children,
    childrenCount,
    pos = '0',
    kid,
  }: any) => {
    if (children || childrenCount === 0) {
      return;
    }
    await fetchOrgStructure({
      uuid,
      kid: kid ? kid : `${pos}-${id}`,
    });
  };

  const handleSelectedTreeItem = (_: React.Key[], { node }: any) => {
    handleSidebarItemClick([node]);
  };

  const handleExpand = (_: React.Key[], { expanded, node }: any) => {
    if (expanded) {
      setSelectedKeys([node.kid]);
    }
    if (expandedKeys.includes(node.kid)) {
      setExpandedKeys(expandedKeys.filter((key) => key !== node.kid));
    } else {
      setExpandedKeys([...expandedKeys, node.kid]);
    }
  };

  const titleRender = (nodeData: TreeData) => {
    return (
      <span className="block py-[7px] leading-[18px]">
        {nodeData.objectName}
      </span>
    );
  };

  const switcherIcon = (props: AntTreeNodeProps) => {
    const expanded = props.expanded;
    if (expanded) {
      return expandedIcon;
    } else {
      return unexpandIcon;
    }
  };

  const renderLine = () => {
    const showLeafIcon = (props: AntTreeNodeProps) => {
      const isLeaf = props.isLeaf;
      if (isLeaf) {
        return (
          <div className="w-full h-full flex items-center justify-center">
            {lineIcon}
          </div>
        );
      }
    };

    return {
      showLeafIcon,
    };
  };

  return (
    <div className="flex flex-col md:w-1/3 w-full border rounded-8  border-[#ddd] py-6 text-[16px]">
      <Tree
        blockNode
        showLine={renderLine()}
        switcherIcon={switcherIcon}
        onSelect={handleSelectedTreeItem}
        treeData={data as unknown as TreeData[]}
        fieldNames={{
          title: 'objectName',
          key: 'kid',
        }}
        loadData={onLoadData}
        selectedKeys={selectedKeys}
        onExpand={handleExpand}
        expandedKeys={expandedKeys}
        titleRender={titleRender}
        rootStyle={{
          wordBreak: 'break-all',
          fontSize: '16px',
          color: '#222',
          fontWeight: '500',
          lineHeight: '18px',
        }}
      />
    </div>
  );
};

export default Sidebar;
