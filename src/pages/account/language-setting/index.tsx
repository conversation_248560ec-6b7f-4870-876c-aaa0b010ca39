import { FormattedMessage, setLocale, useModel, getLocale, useIntl, useParams } from '@umijs/max';
import { message, Select } from 'antd';
import React from 'react';
import { ACCEPT_LANGUAGE_OPTIONS, STORE } from '@/consts'
import api from '@/services';
import app from '@/utils/app';
import { Loading, PageHeader } from '@smartdeer-ui/pc'
import system from '@/utils/system';
import S from '@/utils/storage'

const Page: React.FC = () => {
  const { config } = useModel('useConfig')
  const params = useParams();

  const entityUUID = params.entityUUID as string

  const intl = useIntl()

  const [loading, setLoading] = React.useState(true)

  const [systemLang, setSystemLang] = React.useState('')

  const [options, setOptions] = React.useState<any[]>([])

  // const options = React.useMemo(() => {
  //   return ACCEPT_LANGUAGE_OPTIONS.filter(item => {
  //     return item.key !== 'ja-JP'
  //   }).map(item => {
  //     return { value: item.key, label: item.label }
  //   })
  // }, [])

  const fetch = async () => {
    setLoading(true)
    try {

      const languageStr = await api.login.getEntityConfPermission({
        uName: entityUUID,
        confKey: 'LANGUAGE'
      })

      const languageList = languageStr.split(',')

      const options: any[] = ACCEPT_LANGUAGE_OPTIONS.filter(item => {
        return languageList.includes(item.mapping)
      }).map(item => {
        return { value: item.key, label: item.label }
      })

      setOptions(options)

      const groupKey = S.get(STORE.LOGIN_GROUP_KEY, true)
      const { confValue } = await api.login.getCorePeopleConf({
        groupKey
      })
      const curInfo = ACCEPT_LANGUAGE_OPTIONS.find(item => item.mapping === confValue)

      const key = curInfo?.key || getLocale()

      setSystemLang(key)
    } catch (err: any) {
      message.error(err.message)
    }

    setLoading(false)
  }

  React.useEffect(() => {
    fetch()
  }, [])

  const handleChangeLang = async (value: string) => {
    try {
      const curInfo = ACCEPT_LANGUAGE_OPTIONS.find(item => item.key === value)

      const groupKey = S.get(STORE.LOGIN_GROUP_KEY, true)

      const res = await api.login.addCorePeopleConf({
        confValue: curInfo?.mapping,
        confKey: 'DEFAULT_LANGUAGE',
        groupKey: groupKey
      })

      message.success(intl.formatMessage({ id: 'common.theSettingsHaveTakenEffect' }))

      setTimeout(() => {
        setLocale(curInfo?.key)
        system.removeEntityMenu()
      }, 1000)
    } catch (err: any) {
      message.error(err.message)
    }

  }

  const languageSettingText = intl.formatMessage({ id: 'common.languageSetting' })
  const selectSystemLanguageText = intl.formatMessage({ id: 'account.selectSystemLanguage' })

  if (loading) return <Loading />

  return (
    <>
      <PageHeader title={languageSettingText} />

      <div className={`mt-[24px]`}>{selectSystemLanguageText}</div>

      <div className={`mt-[8px] w-[300px]`}>
        <Select
          value={systemLang}
          size='large'
          style={{ height: '48px' }}
          className={`w-full`}
          options={options}
          onChange={handleChangeLang}
        />
      </div>
    </>
  );
};

export default Page;
