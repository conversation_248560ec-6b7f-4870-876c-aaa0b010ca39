import { Container } from '@/pages/bm/comps/container';
import { history, useLocation, useParams, useAccess } from '@umijs/max';
import type { TabsProps } from 'antd';
import { Tabs } from 'antd';
import React, { useMemo } from 'react';

const Layout: React.FC = (props: React.PropsWithChildren<any>) => {
  const { entityUUID } = useParams();
  const { canAccessModules } = useAccess();
  const { pathname } = useLocation();

  const activeKey = useMemo(() => {
    return pathname.includes('member') ? 'member' : 'role';
  }, []);

  const items: TabsProps['items'] = useMemo(() => [
    {
      key: 'member',
      label: '成员',
      children: props.children,
      disabled: !canAccessModules('gs.permission.member.list'),
    },
    {
      key: 'role',
      label: '角色',
      children: props.children,
      disabled: !canAccessModules('gs.permission.role.list'),
    },
  ], []); 

  const handleTabChange = (key: string) => {
    history.replace(`/bm/${entityUUID}/permission/${key}`);
  };
  
  return (
    <Container>
      <Tabs items={items} activeKey={activeKey} onChange={handleTabChange} />
    </Container>
  );
};

export default Layout;
