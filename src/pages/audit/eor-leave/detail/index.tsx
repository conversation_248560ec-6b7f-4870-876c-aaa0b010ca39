import { PageContainer } from '@ant-design/pro-components';
import { useIntl, useSearchParams } from '@umijs/max';
import CanceLeaveDetail from '../comps/cance-leave-detail';
import LeaveDetail from '../comps/leave-detail';

const Page = () => {
  const { formatMessage } = useIntl();
  const [searchparams] = useSearchParams();
  const flowKey = searchparams.get('key');
  const flowUuid = searchparams.get('id');
  const titleStr =
    flowKey === 'create_eor_leave'
      ? formatMessage({ id: 'leave.audit.application.detail' })
      : flowKey === 'create_eor_cancel_leave'
        ? formatMessage({ id: 'leave.audit.cancellation.detail' })
        : '';

  return (
    <PageContainer ghost title={titleStr} breadcrumb={{}}>
      <div className="max-w-[525px]">
        {flowKey === 'create_eor_leave' && <LeaveDetail flowUuid={flowUuid} />}
        {flowKey === 'create_eor_cancel_leave' && (
          <CanceLeaveDetail flowUuid={flowUuid} />
        )}
      </div>
    </PageContainer>
  );
};

export default Page;
