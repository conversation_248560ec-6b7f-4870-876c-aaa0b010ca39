import React from 'react';
import { Select, Tabs } from 'antd';
import type { TabsProps } from 'antd';
import { useIntl, useSearchParams } from '@umijs/max';

import Content from '../comps/content'

const EXPRESSIONS = {
  pending: 'system_status:0',
  processed: 'system_status:1 OR system_status:2'
}

type EXPRESSIONS_TYPE = keyof typeof EXPRESSIONS

const Page: React.FC = () => {
  const [searchParams] = useSearchParams();

  const { formatMessage } = useIntl();

  const defaultActiveKey = searchParams.get('activeKey') || 'pending';

  const [activeKey, setActiveKey] = React.useState(defaultActiveKey)

  const [expression, setExpression] = React.useState(EXPRESSIONS[defaultActiveKey as EXPRESSIONS_TYPE])

  const [flowKey, setFlowKey] = React.useState('variables.flowKey:create_eor_leave OR variables.flowKey:create_eor_cancel_leave')

  const items: TabsProps['items'] = [
    {
      key: 'pending',
      label: formatMessage({ id: 'account.leave.pending' }),
    },
    {
      key: 'processed',
      label: formatMessage({ id: 'account.leave.processed' }),
    }
  ];

  const flowKeyOptions = [
    {
      label: formatMessage({ id: 'common.all' }),
      value: 'variables.flowKey:create_eor_leave OR variables.flowKey:create_eor_cancel_leave',
    },
    {
      label: formatMessage({ id: 'account.leave' }),
      value: 'variables.flowKey:create_eor_leave',
    },
    {
      label: formatMessage({ id: 'account.rescind' }),
      value: 'variables.flowKey:create_eor_cancel_leave',
    },
  ]

  const handleChange = (key: string) => {
    setActiveKey(key)

    setExpression(EXPRESSIONS[key as EXPRESSIONS_TYPE])

    setFlowKey('variables.flowKey:create_eor_leave OR variables.flowKey:create_eor_cancel_leave')
  }

  const handleChangeFlowKey = (key: string) => {
    setFlowKey(key)
  }

  return (
    <>
      <Tabs activeKey={activeKey} items={items} onChange={handleChange} />

      <div className='absolute right-[24px]'>
        <span className='hidden md:inline-block'>{formatMessage({ id: 'leave.applicationType' })}：</span>
        <Select
          style={{ width: '160px', textAlign: 'left' }}
          value={flowKey}
          options={flowKeyOptions}
          onChange={handleChangeFlowKey}
        ></Select>
      </div>

      <Content expression={expression} flowKey={flowKey} />
    </>
  )
}

export default Page;