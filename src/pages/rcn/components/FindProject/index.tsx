// 寻找项目主要内容区域组件
import React from 'react';
import FiltersPanel from './FiltersPanel';
import { Skeleton, Pagination, Button, Drawer, message } from 'antd';
import { Search, Filter, Menu } from 'lucide-react';
import ProjectCard from '../RecommendProjects/ProjectCard';
import MainPage from './MainPage';
import { useModel, useParams, useSearchParams } from '@umijs/max';
import api from '@/services';
import JobDetailPageLayout from '../ProjectDetail/JobDetailPageLayout';


const MainProjectsArea: React.FC = ({
  
}) => {

  const params = useParams();
  const { initialState } = useModel('@@initialState');
  const { entityList, entity, accountInfo, projectDict } = initialState;

  const [isLoad, setIsLoad] = React.useState(false);
  const [allProjectList, setAllProjectList] = React.useState<any[]>([]);
  const [pagination, setPagination] = React.useState({
    current: 1,
    size: 20,
    total: 0,
  });
  const [currentQueries, setCurrentQueries] = React.useState<any[]>([]);

  const fetchProjects = async (queries: any[] = [], page: number = 1, pageSize: number = 20) => {
    try {
      const res = await api.rcn.project.searchProject(params?.entityUUID, {
        queries,
        current: page,
        size: pageSize,
      });
      console.log(`res:`, res);

      setAllProjectList(res?.jobRequirements || []);
      setPagination({
        current: page,
        size: pageSize,
        total: res?.total || 0,
      });
    } catch (err: any) {
      message.error(err.message);
    }
  };

  // 处理筛选条件变化
  const handleFiltersChange = (queries: any[]) => {
    setCurrentQueries(queries);
    fetchProjects(queries, 1, pagination.size); // 筛选时重置到第一页
  };

  // 处理分页变化
  const handlePageChange = (page: number, pageSize?: number) => {
    const newPageSize = pageSize || pagination.size;
    fetchProjects(currentQueries, page, newPageSize);

    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  React.useEffect(() => {
    if (params?.entityUUID) {
      fetchProjects();
    }
  }, []);

  // 监控 projectDict 状态，用于调试
  React.useEffect(() => {
    console.log('find-projects page - projectDict changed:', projectDict);
  }, [projectDict]);


  return (
    <>
      <MainPage
        allProjectList={allProjectList}
        pagination={pagination}
        isLoading={isLoad}
        onFiltersChange={handleFiltersChange}
        onPageChange={handlePageChange}
      />
    </>
  );
};

export default MainProjectsArea;