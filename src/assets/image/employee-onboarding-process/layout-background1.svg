<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440px" height="960px" viewBox="0 0 1440 960" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 3</title>
    <defs>
        <circle id="path-1" cx="29" cy="29" r="29"></circle>
        <filter x="-31.0%" y="-20.7%" width="162.1%" height="162.1%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="6" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.862834101   0 0 0 0 0.457026282   0 0 0 0 0  0 0 0 0.100802762 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M59.4600996,98.4659971 C85.1537954,94.8549836 103.090366,71.348001 99.5225516,45.9616818 C95.9547371,20.5753626 72.2335962,2.92298949 46.5399004,6.53400294 C20.8462046,10.1450164 2.90963393,33.651999 6.47744842,59.0383182 C10.0452629,84.4246374 33.7664038,102.077011 59.4600996,98.4659971 Z" id="path-3"></path>
        <filter x="-30.9%" y="-20.5%" width="161.7%" height="162.5%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="10" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="8" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 0.525490196   0 0 0 0 0  0 0 0 0.210727505 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <circle id="path-5" cx="22" cy="22" r="22"></circle>
        <filter x="-33.0%" y="-21.6%" width="165.9%" height="165.9%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.862834101   0 0 0 0 0.457026282   0 0 0 0 0  0 0 0 0.100802762 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <circle id="path-7" cx="43.5" cy="43.5" r="43.5"></circle>
        <filter x="-28.7%" y="-19.5%" width="157.5%" height="157.5%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="0" dy="8" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.862834101   0 0 0 0 0.457026282   0 0 0 0 0  0 0 0 0.100802762 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <circle id="path-9" cx="50.5" cy="50.5" r="50.5"></circle>
        <filter x="-24.8%" y="-16.8%" width="149.5%" height="149.5%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="0" dy="8" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.862834101   0 0 0 0 0.457026282   0 0 0 0 0  0 0 0 0.100802762 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-11" points="194.000534 270.70424 184.112194 275.99921 186.000306 264.783523 178 256.840156 189.055665 255.20424 193.999544 245 198.944209 255.204635 210 256.841435 202.000306 264.784162 203.889281 276"></polygon>
        <filter x="-78.1%" y="-54.8%" width="256.2%" height="261.3%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="0" dy="8" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.802323936   0 0 0 0 0.421734376   0 0 0 0 0  0 0 0 0.356818246 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-13" points="1094.43164 613.239545 1087.38089 616.945798 1088.72718 609.095295 1083.02267 603.535277 1090.90577 602.390206 1094.43094 595.247673 1097.95666 602.390482 1105.83985 603.536172 1100.13577 609.095742 1101.48268 616.946351"></polygon>
        <filter x="-78.9%" y="-55.3%" width="257.8%" height="265.9%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="0" dy="6" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.802323936   0 0 0 0 0.421734376   0 0 0 0 0  0 0 0 0.356818246 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <ellipse id="path-15" cx="325.238061" cy="641.950124" rx="6.39779926" ry="6.39701871"></ellipse>
        <filter x="-140.7%" y="-93.8%" width="381.3%" height="381.4%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="0" dy="6" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.802323936   0 0 0 0 0.421734376   0 0 0 0 0  0 0 0 0.356818246 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <ellipse id="path-17" cx="1132.23806" cy="315.950124" rx="6.39779926" ry="6.39701871"></ellipse>
        <filter x="-140.7%" y="-93.8%" width="381.3%" height="381.4%" filterUnits="objectBoundingBox" id="filter-18">
            <feOffset dx="0" dy="6" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.716985721   0 0 0 0 0.802323936  0 0 0 0.2 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-3">
            <g id="签约" transform="translate(1274.000000, 252.000000)">
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
                </g>
                <g id="申请" transform="translate(18.000000, 17.000000)" fill-rule="nonzero">
                    <path d="M11.7814498,24 L3.13415638,24 L3.13415638,24 C1.41037037,24 0,22.569778 0,20.821729 L0,3.178271 C0,1.43022195 1.41037037,0 3.13415638,0 L18.7706055,0 C20.4943915,0 21.9047619,1.43022195 21.9047619,3.178271 L21.9047619,12.9335802 C21.9047619,13.4743953 21.7055586,13.9962624 21.3451935,14.3995216 L13.4218814,23.2659414 C13.0045037,23.7329993 12.407826,24 11.7814498,24 Z" id="形状" stroke="#544022" stroke-width="1.76" fill="#FFDC3F"></path>
                    <path d="M9.9476122,13.2235118 L4.84468212,13.2235118 C4.36268816,13.2235118 3.97195485,13.6142451 3.97195485,14.096239 C3.97195485,14.578233 4.36268816,14.9689663 4.84468212,14.9689663 L9.93628243,14.9689663 C10.4192002,14.9689663 10.8115584,14.5791466 10.814693,14.096239 C10.8178014,13.6173635 10.4321159,13.2266384 9.95324044,13.22353 C9.95136439,13.2235178 9.9494883,13.2235118 9.9476122,13.2235118 Z M16.7462714,7.68 L4.84468212,7.68 C4.36268816,7.68 3.97195485,8.07073331 3.97195485,8.55272727 C3.97195485,9.03472124 4.36268816,9.42545455 4.84468212,9.42545455 L16.7349416,9.42545455 C17.2178594,9.42545455 17.6102176,9.03563486 17.6133522,8.55272727 C17.6164606,8.07385179 17.2307751,7.68312665 16.7518997,7.68001827 C16.7500236,7.68000609 16.7481475,7.68 16.7462714,7.68 Z" id="形状" fill="#544022"></path>
                    <path d="M23,17.4545455 L17.2343488,24" id="路径" stroke="#544022" stroke-width="1.951488" stroke-linecap="round"></path>
                </g>
            </g>
            <g id="编组-6" transform="translate(80.000000, 699.000000)">
                <g id="椭圆形" transform="translate(53.000000, 52.500000) rotate(8.000000) translate(-53.000000, -52.500000) ">
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-3"></use>
                </g>
                <g id="发起响应" transform="translate(30.105975, 30.084554)" stroke="#544022" stroke-width="2.56">
                    <path d="M36.6749571,1.28 C37.8424538,1.28 38.8994238,1.75322156 39.6645195,2.5183173 C40.4296152,3.28341305 40.9028368,4.34038298 40.9028368,5.50787973 L40.9028368,31.2872856 C40.9028368,32.4547823 40.4296152,33.5117522 39.6645195,34.276848 C38.8994238,35.0419437 37.8424538,35.5151653 36.6749571,35.5151653 L11.0358201,35.5151653 L3.57133296,40.4897175 C3.23263701,40.7154344 2.83451555,40.782499 2.46463885,40.7084604 C2.09476216,40.6344218 1.75313023,40.4192801 1.52741336,40.0805841 C1.36608532,39.8385058 1.28,39.554101 1.28,39.263191 L1.28,5.50787973 C1.28,4.34038298 1.75322156,3.28341305 2.5183173,2.5183173 C3.28341305,1.75322156 4.34038298,1.28 5.50787973,1.28 Z" id="矩形" fill="#FFC4ED" fill-rule="nonzero"></path>
                    <ellipse id="椭圆形" fill="#FFFBF7" cx="35.3809843" cy="35.3350397" rx="9.8997706" ry="9.81695461"></ellipse>
                    <polyline id="路径-2" stroke-linecap="round" stroke-linejoin="round" points="34.8790951 32.0601684 34.8790951 35.8867045 40.277432 35.8867045"></polyline>
                    <line x1="10.9721745" y1="14.3877756" x2="31.0231402" y2="14.3877756" id="路径-4" stroke-linecap="round"></line>
                    <line x1="10.9721745" y1="23.687982" x2="24.0005054" y2="23.687982" id="路径-15" stroke-linecap="round"></line>
                </g>
            </g>
            <g id="编组-44" transform="translate(410.000000, 804.000000)">
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-5"></use>
                </g>
                <g id="编组-32" transform="translate(12.692258, 6.769231)" fill-rule="nonzero">
                    <g id="入职">
                        <path d="M4.09209016,17.7243317 C6.14569638,16.2016342 8.63987962,15.6088472 11.1546386,16.081343 L11.1538919,17.4007892 L11.152862,19.891897 L11.1527915,22.289595 L11.1537335,23.2893281 C10.6578515,23.8457297 9.93454924,23.843191 8.9706283,23.8398116 C8.21994258,23.8371797 7.32285477,23.8340466 6.40017958,23.8304744 L5.78259787,23.8280285 C3.5139549,23.8188268 1.25284276,23.8070837 0.763918461,23.793707 C0.975192753,21.2012522 2.19298633,19.1324696 4.09209016,17.7243317 Z M9.30769231,0.6912 C10.9867483,0.6912 12.5062975,1.37110962 13.6060555,2.47086759 C14.7058135,3.57062556 15.3857231,5.09017477 15.3857231,6.76923077 C15.3857231,8.44828677 14.7058135,9.96783598 13.6060555,11.0675939 C12.5062975,12.1673519 10.9867483,12.8472615 9.30769231,12.8472615 C7.62863631,12.8472615 6.1090871,12.1673519 5.00932913,11.0675939 C3.90957116,9.96783598 3.22966154,8.44828677 3.22966154,6.76923077 C3.22966154,5.09017477 3.90957116,3.57062556 5.00932913,2.47086759 C6.1090871,1.37110962 7.62863631,0.6912 9.30769231,0.6912 Z" id="形状" stroke="#503C1F" stroke-width="1.3824" fill="#C68FFF"></path>
                        <path d="M16.9217942,16.3141423 C17.2408738,15.993671 17.7478371,15.9985036 18.0612241,16.325004 C18.3746111,16.6515045 18.378776,17.1791878 18.070585,17.5110033 L16.2065093,19.4501212 L20.3410725,19.4501212 C20.7899826,19.4501212 21.1538962,19.8289051 21.1538962,20.2961586 C21.1538962,20.7634122 20.7899826,21.1421961 20.3410725,21.1421961 L16.1967555,21.1421961 L18.0716688,23.0937225 C18.3891978,23.4242266 18.3891978,23.9600795 18.0716687,24.2905835 C17.7541397,24.6210875 17.2393233,24.6210876 16.9217942,24.2905835 L13.8558232,21.1004583 C13.432741,20.6599549 13.432741,19.945899 13.8558232,19.5053956 L16.9217942,16.3141423 Z" id="路径" fill="#503C1F"></path>
                    </g>
                </g>
            </g>
            <g id="编组-43备份" transform="translate(1171.000000, 475.000000)">
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-7"></use>
                </g>
                <g id="编组" transform="translate(40.116899, 43.207151) rotate(-35.000000) translate(-40.116899, -43.207151) translate(16.101468, 19.352312)">
                    <path d="M29.5998325,36.7150762 L32.7316922,32.1435389 L44.0238844,40.3551385 C45.157235,41.1793029 45.4672806,42.7358816 44.7362197,43.9314068 L44.5860343,44.1770095 C43.8486873,45.3828146 42.2734514,45.7625749 41.0676463,45.025228 C41.0102844,44.9901513 40.954327,44.9528276 40.8999046,44.9133438 L29.5998325,36.7150762 L29.5998325,36.7150762 Z" id="Fill-4" stroke="#544022" stroke-width="2.24" fill="#FFFAE5" transform="translate(37.815272, 39.341535) rotate(8.000000) translate(-37.815272, -39.341535) "></path>
                    <path d="M21.2359779,4.07800664 C26.7520732,3.30277001 32.0576343,4.81530889 36.1760254,7.88892832 C40.2901595,10.9593706 43.2193299,15.5880066 43.9866906,21.0480611 C44.7540512,26.5081157 43.2141768,31.7648349 40.1057459,35.8503425 C36.9940986,39.9400776 32.3109782,42.8564341 26.7948829,43.6316708 C21.2787876,44.4069074 15.9732266,42.8943685 11.8548354,39.8207491 C7.74070131,36.7503068 4.8115309,32.1216709 4.04417027,26.6616163 C3.27680964,21.2015617 4.816684,15.9448425 7.9251149,11.8593349 C11.0367622,7.76959979 15.7198827,4.85324327 21.2359779,4.07800664 Z" id="椭圆形" stroke="#544022" stroke-width="2.24" fill="#FF9719" transform="translate(24.015430, 23.854839) rotate(8.000000) translate(-24.015430, -23.854839) "></path>
                    <path d="M31.3485905,26.6182997 C31.9663298,26.6182997 32.5201402,26.9971838 32.5201402,27.5445699 C32.5201402,28.0076805 31.966305,28.4287392 31.3485905,28.4287392 L24.808876,28.4287392 L24.808876,33.7336075 C24.808876,34.1967181 24.3614979,34.7440306 23.6373264,34.7440306 C22.9769595,34.7440306 22.4230747,34.1967181 22.4230747,33.7336075 L22.4230747,28.4287392 L16.1603027,28.4287392 C15.5425634,28.4287392 15.0312812,28.0076559 15.0312812,27.5024689 C15.0312812,26.9972819 15.5425137,26.6182997 16.1603027,26.6182997 L22.444376,26.6182997 L22.444376,23.8605885 L16.1603027,23.8605885 C15.5425634,23.8605885 15.0312812,23.5027304 15.0312812,22.9343183 C15.0312812,22.4712077 15.5425137,22.0712486 16.1603027,22.0712486 L21.5709957,22.0712486 L17.2041435,16.2822192 C16.9911548,15.8191086 17.1402891,15.1244673 17.6727733,14.871837 C18.2479844,14.5981562 18.844323,14.850811 19.1425916,15.2296951 L23.6799042,21.2713549 L28.3024471,15.6717798 C28.6007158,15.2506965 29.2397564,14.7034086 30.00663,15.1454932 C30.8161063,15.6296297 30.3048986,16.5769995 30.3048986,16.5769995 L25.767586,22.0503208 L31.0930736,22.0503208 C31.7108128,22.0503208 32.2646232,22.4292049 32.2646232,22.9555405 C32.2646232,23.4818762 31.710788,23.8397098 31.0930736,23.8397098 L24.8090002,23.8397098 L24.8090002,26.5974209 L31.3487146,26.6184469 L31.3485905,26.6182997 Z" id="路径" fill="#544022" fill-rule="nonzero" transform="translate(23.775711, 24.750397) rotate(37.000000) translate(-23.775711, -24.750397) "></path>
                </g>
            </g>
            <g id="编组-42" transform="translate(230.000000, 342.000000)">
                <g id="椭圆形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-9"></use>
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-9"></use>
                </g>
                <g id="编组" transform="translate(21.649471, 18.757976)">
                    <g fill="#8EFFFC" id="Fill-1" stroke="#544022" stroke-width="2.88">
                        <path d="M54.8285572,13.5057673 C54.7873615,30.3543435 52.7003544,51.2192267 27.4142801,60.5991673 C2.13208199,51.3237251 0.119780149,30.3684362 1.10183715e-05,13.6090644 C-0.00483058052,11.5054754 1.5871313,11.4535962 2.37805123,11.3646825 C7.53843069,10.7845623 15.3498899,7.15226652 25.812429,0.467795104 L25.8121297,0.467325227 C26.7918803,-0.156806987 28.0441609,-0.158142118 29.0252402,0.463899539 C39.6509601,7.21614138 47.6365332,10.8360622 52.9801308,11.3265457 C53.5960739,11.3830825 54.8334036,11.503949 54.8285572,13.5057673 Z"></path>
                    </g>
                    <path d="M36.1610053,37.513453 C36.901672,37.513453 37.507672,36.907453 37.507672,36.1667863 C37.507672,32.059453 35.083672,28.5581197 31.5823387,26.9421197 C32.5250053,25.9321197 33.1310053,24.5181197 33.1310053,23.0367863 C33.1310053,19.8721197 30.5723387,17.313453 27.407672,17.313453 C24.2430053,17.313453 21.6843387,19.8721197 21.6843387,23.0367863 C21.6843387,24.585453 22.2903387,25.9321197 23.2330053,26.9421197 C19.731672,28.5581197 17.307672,32.059453 17.307672,36.1667863 C17.307672,36.907453 17.913672,37.513453 18.6543387,37.513453" id="Fill-3" fill="#544022"></path>
                    <g transform="translate(24.521958, 28.394666)">
                        <g transform="translate(1.442857, 0.461644)" fill="#74D9E5" id="形状结合">
                            <path d="M0.133186485,4.77599036 C0.0444097017,4.7042407 0,4.59662483 0,4.50695068 L0.421764307,0.358713836 C0.443969158,0.16140659 0.665932427,0 0.910121856,0 L1.97561374,0 C2.21978186,0 2.41956159,0.16140659 2.46394998,0.358713836 L2.88571429,4.50695068 C2.88571429,4.61456655 2.84132589,4.7042407 2.7525278,4.77599036 L1.79801755,5.63691734 C1.59823782,5.81626565 1.28747646,5.81626565 1.08769673,5.63691734 L0.133186485,4.77599036 Z"></path>
                        </g>
                        <g></g>
                    </g>
                    <path d="M18.7505291,40.3991673 L36.0648149,40.3991673 C36.8616828,40.3991673 37.507672,41.0451564 37.507672,41.8420244 C37.507672,42.6388924 36.8616828,43.2848816 36.0648149,43.2848816 L18.7505291,43.2848816 C17.9536611,43.2848816 17.307672,42.6388924 17.307672,41.8420244 C17.307672,41.0451564 17.9536611,40.3991673 18.7505291,40.3991673 Z" id="矩形" fill="#544022"></path>
                </g>
            </g>
            <g id="星形" transform="translate(194.000000, 260.500000) rotate(-20.000000) translate(-194.000000, -260.500000) ">
                <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-11"></use>
                <path stroke="#23272C" stroke-width="2.304" d="M193.999606,247.641987 L198.173061,256.255024 L207.556427,257.644223 L200.765205,264.387084 L202.363334,273.876008 L194.000563,269.397461 L185.637929,273.875452 L187.235368,264.386408 L180.443723,257.6431 L189.826823,256.254675 L193.999606,247.641987 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <path d="M1053.28007,161.280073 L1073.71875,161.281245 L1073.71993,181.719927 L1053.28125,181.718755 L1053.28007,161.280073 Z" id="矩形" stroke="#544022" stroke-width="2.56" fill="#BE80FF" transform="translate(1063.500000, 171.500000) rotate(-35.000000) translate(-1063.500000, -171.500000) "></path>
            <path d="M43.0821688,613.363911 L55.8766393,613.364644 L55.8773728,626.158514 L43.0829022,626.157781 L43.0821688,613.363911 Z" id="矩形备份" stroke="#544022" stroke-width="1.6" fill="#BE80FF" transform="translate(49.479771, 619.761213) rotate(-35.000000) translate(-49.479771, -619.761213) "></path>
            <g id="星形" transform="translate(1094.431261, 606.097012) rotate(-20.000000) translate(-1094.431261, -606.097012) ">
                <use fill="black" fill-opacity="1" filter="url(#filter-14)" xlink:href="#path-13"></use>
                <path stroke="#23272C" stroke-width="1.6" d="M1094.43098,597.055178 L1097.42544,603.121682 L1104.12062,604.094715 L1099.27617,608.816441 L1100.42009,615.483921 L1094.43166,612.335742 L1088.44334,615.483528 L1089.58676,608.815969 L1084.74201,604.093928 L1091.437,603.121436 L1094.43098,597.055178 Z" stroke-linejoin="square" fill="#FFFFFF" fill-rule="evenodd"></path>
            </g>
            <g id="椭圆形">
                <use fill="black" fill-opacity="1" filter="url(#filter-16)" xlink:href="#path-15"></use>
                <use fill="#EE6808" fill-rule="evenodd" xlink:href="#path-15"></use>
            </g>
            <g id="椭圆形备份">
                <use fill="black" fill-opacity="1" filter="url(#filter-18)" xlink:href="#path-17"></use>
                <use fill="#8EFFFC" fill-rule="evenodd" xlink:href="#path-17"></use>
            </g>
        </g>
    </g>
</svg>