export const entityListMock = [
  {
    id: 33,
    uuid: '02231e19800a488fa91048de8ef2b916',
    parentId: 0,
    organizationId: 0,
    entityKey: 'ICB Limited',
    entityName: 'ICB Limited',
    uName: 'icb_test',
    uniqueName: 'ICB Limited',
    groupKey: 'ICB_test',
    logo: 'https://global-image.smartdeer.work/test/images/0xd1f16a1380cb443496dbeb136cfe60ed.png',
    shortName: 'ICB CoreHr 01',
    description: 'ICB CoreHr 01',
    verifyStatus: 0,
    children: [
      {
        id: 66,
        uuid: 'b9c3ea866ff64391',
        parentId: 33,
        organizationId: 0,
        entityKey: 'icb_limited_bj',
        entityName: 'ICB limited Beijing',
        uName: 'icb_limited_bj',
        uniqueName: 'icb_limited_bj',
        groupKey: 'icb_limited_bj',
        logo: '',
        shortName: 'ICB limited Beijing',
        description: '',
        verifyStatus: 0,
        children: [],
      },
    ],
  },
  {
    id: 51,
    uuid: '02231e19800a488fa91048de8ef2b922',
    parentId: 0,
    organizationId: 0,
    entityKey: 'HashKey',
    entityName: 'HashKey',
    uName: 'Hashkey',
    uniqueName: 'hashkey',
    groupKey: 'hashkey',
    logo: 'https://global-image.smartdeer.work/test/images/0xd1235112048249e28d252f8eafa1bfee.jpeg',
    shortName: 'HashKey',
    description: 'HashKey',
    verifyStatus: 0,
    children: [],
  },
];
