import React, { useEffect, useState } from 'react';
import { Spin, message, Popover, Space } from 'antd';
import { useSearchParams, useIntl } from '@umijs/max';
import dayjs from 'dayjs';
import api from '@/services'

const D = dayjs()
const YMD = D.format('YYYY-MM-DD')

interface EntityPopoverProps {
  entity: Record<string, any>
  entityList: any[]
}


const EntityPopover: React.FC<EntityPopoverProps> = (props) => {
  const { entity, entityList } = props

  const intl = useIntl();

  const [searchParams] = useSearchParams()

  const [isLoad, setIsLoad] = useState<boolean>(false)
  const [isEntityPopoverLoad, setIsEntityPopoverLoad] = useState<boolean>(false)

  // const [entity, setEntity] = useState<Record<string, any>>({})

  // const [entityList, setEntityList] = useState<any[]>([])

  // const fetch = async () => {
  //   setIsLoad(true)

  //   try {
  //     // let list: any[] = entityListMock

  //     const list = await api.bm.entity.getEntityGroup()

  //     setEntity(list[0])

  //     setEntityList(list)

  //   } catch (err: any) {
  //     message.error(err.message)
  //   }

  //   setIsLoad(false)
  // }

  // useEffect(() => {
  //   fetch()
  // }, [])

  const handleClickEntityItem = async (entity: object) => {
    // setIsEntityPopoverLoad(true)

    // await getEncryptType(entity.uName)

    // setIsEntityPopoverLoad(false)

    // setEntity(entity)
  }

  const entityPopoverContent = React.useMemo(() => {
    return (
      <div>
        {entityList.map(item => {
          return (
            <p
              key={item.id}
              // className={`${styles['item']} ${entity.id === item.id ? styles['active'] : ''} cursor-pointer`}
              onClick={() => handleClickEntityItem(item)}
            >
              {item?.json?.organizationName}
            </p>
          )
        })}
      </div>
    );
  }, [entityList, entity.id])

  return (
    <Spin spinning={isLoad}>
      <div className={`flex justify-between items-center`}>
        <Popover content={entityPopoverContent} title={intl.formatMessage({ id: 'home.xuanzeLegalEntity' })} placement="bottomLeft" trigger="click">
          <Space className={`cursor-pointer text-#22 hover:text-#p1 transition-colors select-none`}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M6.90401 13.4181C7.43425 14.1834 8.56575 14.1834 9.09599 13.4181L13.4784 7.09266C14.091 6.20839 13.4581 5 12.3824 5H3.61763C2.54187 5 1.90899 6.20839 2.52163 7.09266L6.90401 13.4181Z" />
            </svg>

            <span className={`text-20 font-semibold leading-[32px]`}>
              {entity?.json?.organizationName}
            </span>
          </Space>
        </Popover>

        <div className={`text-16 text-#47 hidden md:block`}>{YMD}</div>
      </div>
    </Spin>
  );
};

export default EntityPopover;
