import React, { memo, useEffect, useState } from 'react';
import type { MenuProps } from 'antd';
import { Dropdown } from 'antd';
import { setLocale, getLocale, useParams } from '@umijs/max';
import system from '@/utils/system';
import { ACCEPT_LANGUAGE_OPTIONS } from '@/consts'

import langImage from '@/assets/image/icon/lang.svg';

interface SelectLangProps {
  isRefreshPage?: boolean
}

const SelectLang: React.FC<SelectLangProps> = memo((props) => {

  const { isRefreshPage = true } = props

  const params = useParams()
  const [lang, setLang] = useState(getLocale())
  const [items, setItems] = useState<any[]>(ACCEPT_LANGUAGE_OPTIONS)

  const getLocaleLabel = (key: string) => {
    const item: any = items.find(item => item?.key === key)

    return item?.label
  }

  const onClick: MenuProps['onClick'] = ({ key }) => {
    system.removeEntityMenu()

    setLocale(key, isRefreshPage)

    if (!isRefreshPage) {
      setLang(key)
    }
  };

  return (
    <Dropdown menu={{ items, onClick }} placement="bottom">
      <div className={`text-[#474747] cursor-pointer flex items-center whitespace-nowrap`}>
        <img src={langImage} className={`w-[18px] h-[18px]`} />
        <span className={`ml-[4px] text-[14px]`}>
          {getLocaleLabel(lang)}
        </span>
      </div>
    </Dropdown>
  );
});

export default SelectLang;
