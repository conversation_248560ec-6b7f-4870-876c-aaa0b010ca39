import React, { useEffect, memo, useState } from 'react';
import { useParams, history, useIntl, useModel } from '@umijs/max';
import api from '@/services';
import { Dropdown, Avatar } from 'antd';
import type { MenuProps } from 'antd';
import { LogoutOutlined, EditOutlined, SettingOutlined } from '@ant-design/icons';
import app from '@/utils/app';
import { setUserAccountId } from '@smartdeer-ui/pc'
import S from '@/utils/storage';
import { STORE } from '@/consts';
import system from '@/utils/system';


interface AccountInfoProps {
  entityUUID?: string | number;
}

const AccountInfo: React.FC<AccountInfoProps> = memo((props) => {

  const params = useParams()

  const entityUUID = props.entityUUID || params.entityUUID

  const { initialState, setInitialState } = useModel('@@initialState');


  const intl = useIntl()

  const items: MenuProps['items'] = React.useMemo(() => {
    const list = [
      {
        key: 'languageSetting',
        icon: <SettingOutlined />,
        label: intl.formatMessage({ id: 'common.languageSetting' })
      },
      {
        key: 'editPassword',
        icon: <EditOutlined />,
        label: intl.formatMessage({ id: 'common.xiugaimima' })
      },
      {
        key: 'logout',
        icon: <LogoutOutlined />,
        label: intl.formatMessage({ id: 'common.dengchu' })
      },
    ]

    return list;
  }, []);

  const [info, setInfo] = useState({
    name: '',
    formalPhoto: ''
  })

  const handleLogout = async () => {
    try {
      await api.account.logout(entityUUID)
    } catch (error) { } finally {
    }
  }


  const fetch = async () => {
    if (!entityUUID) return

    const res = await api.account.getEmployeeInfo(entityUUID as string)

    setUserAccountId(res.accountId.toString())

    setInfo({
      name: res.realName,
      formalPhoto: res.formalPhoto
    })
    setInitialState({
      ...initialState,
      accountInfo: res
    })
  }

  useEffect(() => {
    fetch()
  }, [entityUUID])

  const onClick: MenuProps['onClick'] = async ({ key }) => {
    switch (key) {
      case 'languageSetting': // 语言设置
        history.push(`/n/${entityUUID}/account/language-setting`);
        break;
      case 'editPassword': // 修改密码
        history.push(`/n/${entityUUID}/forgot-password?source=my`);
        break;
      case 'logout': // 退出登录
        await handleLogout()
        app.logout()
        break;
      default:
        break;
    }
  };

  return (
    <Dropdown menu={{ items, onClick }}>
      <div className={`text-[#474747] cursor-pointer whitespace-nowrap flex items-center`}>
        <Avatar src={info.formalPhoto} size={26} style={{ backgroundColor: '#ffaa3b', verticalAlign: 'middle' }}>
          <div className={`text-[12px]`}>{info.name && info.name[0]}</div>
        </Avatar>
        <div className={`hidden lg:block ml-[6px] text-[14px] overflow-hidden text-ellipsis`}>
          {info.name}
        </div>
      </div>
    </Dropdown>
  );
});

export default AccountInfo;
