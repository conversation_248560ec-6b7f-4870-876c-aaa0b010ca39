import { memo } from 'react';
import { Space } from 'antd';
import AccountInfo from './account-info'
import SelectLang from './select-lang'

import styles from './index.less';

interface HeaderActionsProps {
  entityUUID?: ICB.EntityId
}

const HeaderActions: React.FC<HeaderActionsProps> = memo((props) => {

  const { entityUUID } = props

  return (
    <div className={`${styles.actions} px-[16px]`}>
      <Space size={20}>
        {/* <SelectLang /> */}

        <AccountInfo entityUUID={entityUUID!} />
      </Space>
    </div>
  );
});

export default HeaderActions;
