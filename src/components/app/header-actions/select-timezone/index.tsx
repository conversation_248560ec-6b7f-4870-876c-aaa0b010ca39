import React from 'react';
import type { MenuProps } from 'antd';
import { Dropdown } from 'antd';
import { useParams, history } from '@umijs/max';
import S from '@/utils/storage';
import { STORE } from '@/consts';

import timezoneImage from '@/assets/image/icon/timezone.svg';

const SelectTimezone: React.FC = () => {
  const params = useParams()

  const [timezone, setTimezone] = React.useState(
    S.get(STORE.GS_TIMEZONE, true),
  )

  const [items, setItems] = React.useState<any[]>([
    {
      key: 'Asia/Shanghai', // zh
      label: '北京时间 (+08:00)',
    },
    // {
    //   key: 'America/New_York', // zh
    //   label: '美国时间 (+08:00)',
    // }
  ])

  const getTimezoneLabel = (key: string) => {
    const item: any = items.find(item => item?.key === key)

    return item?.label
  }

  const onClick: MenuProps['onClick'] = ({ key }) => {
    if (timezone === key) return

    S.set(STORE.GS_TIMEZONE, key, true);

    setTimezone(key)

    history.go(0)
  };

  return (
    <Dropdown menu={{ items, onClick }} placement="bottom">
      <div className={`text-[#474747] cursor-pointer flex items-center whitespace-nowrap`}>
        <img src={timezoneImage} className={`w-[20px] h-[20px]`} />

        <span className={`ml-[4px] text-[14px]`}>
          {getTimezoneLabel(timezone)}
        </span>
      </div>
    </Dropdown>
  );
};

export default React.memo(SelectTimezone);
