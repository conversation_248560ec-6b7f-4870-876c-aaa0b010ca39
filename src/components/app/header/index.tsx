import React from 'react';

import <PERSON><PERSON><PERSON><PERSON> from '../header-logo'
import HeaderActions from '../header-actions'

interface HeaderProps {
  entityUUID?: ICB.EntityId
}

const Header: React.FC<HeaderProps> = (props) => {
  const { entityUUID } = props

  return (
    <header className={`fixed top-0 left-0 z-50 w-full h-[56px] px-[16px] flex justify-between items-center`}>
      <HeaderLogo />

      <HeaderActions entityUUID={entityUUID!} />
    </header>
  );
};

export default React.memo(Header);
