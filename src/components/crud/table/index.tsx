import { ProTable } from '@ant-design/pro-components';
import { Button, message, Drawer } from 'antd';
import api from '@/services'
import React, { useState } from 'react';

import { isEmptyObject, asyncComponent } from '@/utils';

interface TProps {
  conf: ICB.Conf
}

const getSearchItems = (params: { [key: string]: string }): any[] => {
  let action = 'eq';
  const searchItems: any[] = [];
  // 判断是不是空对象
  if (isEmptyObject(params)) return searchItems;

  Object.keys(params).forEach((key) => {
    const value: any = params[key];
    let nKey = key;
    // if (key.startsWith('%')) {
    //   action = 'like';
    //   nKey = key.replace(/%/g, '');
    // }
    searchItems.push({
      action: action,
      key: nKey,
      value: value,
    });
  });

  return searchItems;
};

const getPostData = (searchItems: any[]) => {
  return {
    orders: [
      {
        asc: 'desc',
        key: 'updateTime',
        order: 0,
      },
      {
        asc: 'asc',
        key: 'createTime',
        order: 1,
      },
    ],
    searchItems: searchItems,
  };
};

const treeEmptyNode = (list: any[]): any[] => {
  if (!list.length) return [];

  return list.map((item) => {
    let children: any[] | null = treeEmptyNode(item.children);

    if (!children.length) {
      children = null;
    }

    return {
      ...item,
      children,
    };
  });
};

const columnsMap = (item: any) => {
  let render: any;
        
  if (item.render && Array.isArray(item.render)) {
    render = () => {
      return item.render.map((r: any) => {
        return <a key={r.name}>{ r.name }</a>
      })
    }
  }

  return {
    ...item,
    render
  }
}

const fetch = async (
  params: ICB.Object, 
  sort: ICB.Object, 
  filter: ICB.Object,
  conf: ICB.Conf
  ): Promise<ICB.ProTable> => {
  // console.log('params',params)
  // console.log('sort',sort)
  // console.log('filter',filter)
  try {

    // const searchItems = getSearchItems(params);
    // const postData = getPostData(searchItems);

    const { dataInfo, total } = await api.conf.getTable(conf, params)

    return {
      data: dataInfo,
      success: true,
      total
    }
  } catch (err: any) {
    message.error(err.message)

    return {
      data: [],
      success: false,
    }
  }
}

const CrudTable: React.FC<TProps & ICB.ConfTable & ICB.ConfList> = ({ conf, columns, search, newButton }) => {
  const searchConfig = {
    defaultCollapsed: true,
  }
  
  const toolBarRender: any = []

  if (newButton) {
    toolBarRender.push(<Button
      key="button"
      type="primary"
    >
      新建
    </Button>)
  }


  const [open, setOpen] = useState<any>(false);
  const [TempComponent, setTempComponent] = useState<any>(null)

  const handleClickItem = ({type, component}: ICB.Object) => {
    if (type === 'open') {
      setTempComponent(asyncComponent(component))
      setOpen(true)
    }
  }

  return (
    <>
      <ProTable<ICB.Object>
        columns={columns.map((item: any) => {
          let render: any;
                
          if (item.render && Array.isArray(item.render)) {
            render = () => {
              return item.render.map((r: any) => {
                return <a key={r.name} onClick={() => handleClickItem(r)}>{ r.name }</a>
              })
            }
          }
        
          return {
            ...item,
            render
          }
        })}
        request={(...args) => fetch(...args, conf )}
        rowKey="id"
        pagination={{
          showQuickJumper: true,
        }}
        search={search ? searchConfig : false}
        dateFormatter="string"
        toolbar={{
          title: '查询列表',
          // tooltip: '这是一个标题提示',
        }}
        toolBarRender={() => toolBarRender}
      />

      <Drawer
        width={`80%`}
        open={!!open}
        onClose={() => {
          setOpen(undefined);
        }}
        destroyOnClose
      >
        <> 
          {/* 渲染异步组件 */}
          { TempComponent ? (
             <TempComponent component={TempComponent} />
          ) : '' }
        </>
      </Drawer>
    </>
   
  );
};

export default CrudTable;