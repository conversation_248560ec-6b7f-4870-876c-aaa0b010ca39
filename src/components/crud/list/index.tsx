import React, { useState } from 'react';
import { useFetchTableConf } from '@/hooks';
import { CrudTable } from '@/components/crud'

import styles from './index.less';

const CrudList: React.FC<ICB.ConfList> = (props) => {
  const conf: ICB.Conf = {
    key: props.confKey,
    language: 'gl',
    version: '1'
  }

  const { info, isLoad, retry } = useFetchTableConf(conf)

  return (
    <>
      { isLoad ? '' : (
        <CrudTable { ...props } { ...info } conf={conf}  />
      ) }
    </>
  );
};

export default CrudList;
