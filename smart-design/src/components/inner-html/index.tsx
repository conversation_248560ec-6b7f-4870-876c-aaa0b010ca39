import React from 'react';
import classNames from 'classnames'
import DOMPurify from 'dompurify';

DOMPurify.addHook('afterSanitizeAttributes', function (node) {
  // set all elements owning target to target=_blank
  if ('target' in node) {
    node.setAttribute('target', '_blank');
    node.setAttribute('rel', 'noopener noreferrer');
  }
});

const classPrefix = 's-inner-html'

export interface InnerHtmlProps {
  className?: string;
  style?: React.CSSProperties;
  content?: string;
}

const InnerHtml: React.FC<InnerHtmlProps> = (props) => {
  const {
    className,
    style,
    content,
  } = props

  const contentHtml = React.useMemo(() => {
    let html = content

    if (html?.includes('<p><br></p>')) {
      html = content?.replaceAll('<p><br></p>', '')
    }

    if (html?.includes('<p><br/></p>')) {
      html = content?.replaceAll('<p><br/></p>', '')
    }

    return DOMPurify.sanitize(html || '')
  }, [content])


  return (
    <div
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      dangerouslySetInnerHTML={{ __html: contentHtml || '' }}
    />
  )
}

export default InnerHtml
