import React from 'react'
import classNames from 'classnames';
import { Spin } from 'antd';
import type { SpinProps } from 'antd';

import './index.less'

export interface LoadingProps extends SpinProps {
  className?: string;
}

const classPrefix = `s-loading`

const Loading: React.FC<LoadingProps> = (props) => {
  const { className, ...restProps } = props

  return (
    <div className={classNames(classPrefix, className)}>
      <Spin size='large' {...restProps}>
        <div className={`${classPrefix}-content`}>
        </div>
      </Spin>
    </div>
  )
}

export default Loading
