---
toc: content
group: 
  title: 事件中心
  order: 0
---

# EventHub 事件中心

用于管理组件内部的事件的系统。

<!-- ## 示例

<code src="./demos/index.tsx"></code> -->

## Page 事件

| 事件名称 | 说明 | 参数 |  版本 |
| --- | --- | --- | --- |
| `field:page-retry` | 刷新	|  | |

### Steps 事件

| 事件名称 | 说明 | 参数 |  版本 |
| --- | --- | --- | --- |
| `field:page-steps-to` | 跳转到指定步骤	 | `(index: string)`  | |
| `field:page-steps-back` |  回退	|  | | 
| `field:page-steps-next` |  下一步	|  | |
| `field:page-steps-submit` |  提交	|  | |

### Form 事件

| 事件名称 | 说明 | 参数 |  版本 |
| --- | --- | --- | --- |
| `field:form-submit` |  提交 |  | |
| `field:form-steps-submit` | `Steps` 提交	|  | |

### Effect 事件

| 事件名称 | 说明 | 参数 |  版本 |
| --- | --- | --- | --- |
| `field:effect-retry` |  刷新	|  | |

### Drawer 事件

| 事件名称 | 说明 | 参数 |  版本 |
| --- | --- | --- | --- |
| `field:drawer-show` | 显示	|  | |
| `field:drawer-hide` | 隐藏	|  | |

## 使用方法

### 订阅事件

```typescript
import pageEventBus from '../../bus/page-event-hub';

pageEventBus.subscribe('field:page-retry', () => {
  console.log('页面刷新事件触发');
});
```

### 发布事件

```typescript
import pageEventBus from '../../bus/page-event-hub';

pageEventBus.publish('field:page-retry');
```

### 取消订阅事件

```typescript
import pageEventBus from '../../bus/page-event-hub';

const callback = () => {
  console.log('页面刷新事件触发');
};

pageEventBus.subscribe('field:page-retry', callback);
pageEventBus.unsubscribe('field:page-retry', callback);
```

### 获取当前订阅的所有事件名称

```typescript
import pageEventBus from '../../bus/page-event-hub';

const events = pageEventBus.getSubscribedEvents();
console.log(events);
```

## FAQ
### field 是什么？
field 是组件的唯一标识，在组件内部的事件中会用到。
