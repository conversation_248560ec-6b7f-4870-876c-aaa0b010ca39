import React from 'react';
import { Steps, message } from 'antd';
import SchemaPage from '../schema-page'
import Loading from '../loading'
import swrFetcher from '../../utils/swrFetcher'
import { ConfigContext } from '../config-provider'
import S from '../../utils/storage'
import { getSearchParams, setSearchParams } from '../../utils/searchParams'

const classPrefix = `x-dynamic-page-river`

import './river.less'

interface RiverProps {
  conf: {
    key: string
    version: string
    token?: string
  }
  bodyClassName?: string
  bodyStyle?: React.CSSProperties

  isStep?: boolean
  isInitialValues?: boolean
}

const River: React.FC<RiverProps> = (props) => {
  const { conf, bodyStyle, bodyClassName, isStep = false, isInitialValues = false } = props

  const { dynamicPage, api, useUrlParams } = React.useContext(ConfigContext)

  const params = useUrlParams();

  const { step: queryStep = '0' } = getSearchParams()

  const [step, setStep] = React.useState<number | string>(isStep ? queryStep : '0')

  const [isLoading, setIsLoading] = React.useState(true)

  const [initialValues, setInitialValues] = React.useState<object>({})
  const [items, setItems] = React.useState<any[]>([])

  const functionUri = React.useMemo(() => {
    const t = S.getAuthToken()

    const uri = t ? params?.entityUUID ? api.appRiverEntityFunction?.replace('*{entityUUID}', params?.entityUUID || '') : api.appRiverAuthFunction : api?.appRiverFunction

    return uri
  }, [api])

  const processUri = React.useMemo(() => {
    const t = S.getAuthToken()

    const uri = t ? params?.entityUUID ? api.appRiverEntityProcess?.replace('*{entityUUID}', params?.entityUUID || '') : api.appRiverAuthProcess : api?.appRiverProcess

    return uri
  }, [api])

  const getItems = async () => {
    setIsLoading(true)

    const { data } = await swrFetcher(functionUri as string, 'POST', {
      functionKey: dynamicPage?.river?.getStepwiseDetail,
      params: {
        key: conf.key,
        version: conf.version,
        token: conf.token
      }
    })

    const list = data.rs.sort((a: any, b: any) => a.step - b.step)

    let initialValues = {}

    list.forEach((item: any) => {
      const dataJson = item.dataJson ? JSON.parse(item.dataJson) : {}

      initialValues = {
        ...initialValues,
        ...dataJson
      }
    })

    setInitialValues(initialValues)

    setItems(list)

    setIsLoading(false)
  }

  React.useEffect(() => {
    getItems()
  }, [])

  const addStepwiseTask = async (index: number, values: any) => {
    const item: any = items[index]

    if (!item) {
      message.error('没有找到对应的步骤配置信息')
      return false
    }

    // const status = items.length === index + 1 ? '1' : '0'

    await swrFetcher(processUri as string, 'POST', {
      processKey: dynamicPage?.river?.addStepwiseTask,
      params: {
        token: conf.token,
        taskUuid: item.taskUuid,
        stepwiseUuid: item.uuid,
        stepwiseKey: item.stepwiseKey,
        version: item.version,
        step: item.step,
        status: '1',
        dataJson: {
          ...values
        }
      }
    })

    if (items.length !== index + 1) {
      setStep(index + 1)

      if (isStep) {
        setSearchParams('step', index + 1)
      }
    }
  }

  const handleStepFinish = async (index: number, values: any) => {

    await addStepwiseTask(index, values)

    return true
  }

  const handleSkip = async (index: number) => {

    await addStepwiseTask(index, {})
  }

  const handleBack = (index: number) => {
    setStep(index)

    if (!isStep) return
    setSearchParams('step', index)
  }

  if (isLoading) {
    return <Loading />
  }

  return (
    <div className={classPrefix}>
      <Steps
        current={Number(step)}
        items={items.map((item: any) => ({
          title: item.titleName,
        }))}
      />

      <div
        className={`${classPrefix}-main ${bodyClassName}`}
        style={{ ...bodyStyle }}
      >
        <SchemaPage.PageSteps
          defaultActiveIndex={Number(step)}
          initialValues={isInitialValues ? initialValues : {}}
          field='signupSteps'
          items={items.map((item: any) => JSON.parse(item.json).page)}
          // onFinish={handleFinish}
          onStepFinish={handleStepFinish}
          onSkip={handleSkip}
          onBack={handleBack}
        />
      </div>
    </div>
  )
};

export default River;
