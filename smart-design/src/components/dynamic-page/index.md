---
toc: content
group: 
  title: 建设
  order: 19
---

# DynamicPage 动态页面

可以配置的页面

## 示例

### Form

<code src="./demos/form/basic.tsx">基本使用</code>


<!-- <code src="./demos/page/drawer.tsx"></code> -->

<!-- <code src="./demos/page/crud-table.tsx"></code> -->

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| confKey | 配置文件的唯一标识 key | `string` | `必填` | |
