import type { SchemaPageProps } from 'smart-design';

const columns: SchemaPageProps['columns'] = [
  {
    type: 'input',
    field: 'username',
    labelProps: {
      label: '用户名',
      rules: [{ required: true, message: 'Please input your username!' }],
    },
  },
  {
    type: 'password',
    field: 'password',
    labelProps: {
      label: '密码',
      rules: [{ required: true, message: 'Please input your password!' }],
    },
    effect: {},
  },
  {
    type: 'button',
    field: 'button',
    props: {
      type: 'primary',
    },
    children: '提交',
    effect: {
      // show: '*{buttonType}===primary',
      // show: `*{a} === *{b} && *{b} !== *{c} || *{d} === *{e}`,
      show: `(*{a} === '1' || *{c} === *{d}) && *{e} > *{f}`,
      // show: `*{a} === '1' || *{c} === *{d} && *{e} > *{f}`,
      // show: '1 === 1 || 2 === 4 && 6 > 10',
    },
    events: {
      click: {
        target: 'form-basic:form-submit',
      },
    },
  },
];

export const mock = {
  'form.basic': {
    schema: {
      field: 'form-basic',
      columns,
      variables: {
        a: '1',
        b: '2',
        c: '3',
        d: '4',
        e: '5',
        f: '6',
        g: '7',
        h: '8',
        i: '9',
        j: '10',
        k: null,
        l: null,
        m: undefined,
        n: { a: 'a' },
        o: { b: 'b' },
        p: ['a'],
        q: ['b'],
        r: 'a',
        s: 'b',
        t: true,
        u: false,
        v: 123,
      },
    },
  },
};
