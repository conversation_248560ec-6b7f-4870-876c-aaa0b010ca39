import type { DynamicPageProps } from './page';
import InternalPage from './page';

import River from './river';
import RiverSteps from './river-steps';

export type { DynamicPageProps } from './page';

type CompoundedComponent = React.FC<DynamicPageProps> & {
  River: typeof River;
  RiverSteps: typeof RiverSteps;
};

const DynamicPage = InternalPage as CompoundedComponent;

DynamicPage.River = River;
DynamicPage.RiverSteps = RiverSteps;

export default DynamicPage;
