import React from 'react'
import classNames from 'classnames'
import swrFetcher from '../../utils/swrFetcher'
import { ConfigContext } from '../config-provider'
import { devError, prodError } from '../../utils/log'
import Loading from '../loading'
import { useParamsDict } from '../../utils/hooks'
import { replaceVariablesInTemplate } from '../../utils/replaceVariablesInTemplate';
import SchemaPage from '../schema-page'
import type { SchemaPageProps } from '../schema-page'
import { replaceReferences } from '../../utils/replaceReferences'
import { delay } from '../../utils'
import { isEmpty } from 'lodash'

const classPrefix = `x-dynamic-page`

export interface DynamicPageProps {
  className?: string;
  style?: React.CSSProperties;

  conf?: {
    version?: string;
    confKey: string;
    language?: string;
  };

  mock?: SchemaPageProps['mock'];
}

const DynamicPage: React.FC<DynamicPageProps> = (props) => {
  const {
    className,
    style,

    conf,
    mock,
  } = props

  const configContext = React.useContext(ConfigContext);
  const paramsDict = useParamsDict()

  if (!conf) {
    devError('DynamicPagePro', '缺少必要参数 conf ！')
  }

  const [isLoading, setIsLoading] = React.useState(true)
  const [schemaProps, setSchemaProps] = React.useState<Omit<SchemaPageProps, 'children'>>({})

  const getConf = async () => {
    if (!conf) {
      setIsLoading(false)
      return
    }

    try {

      if (mock && mock[conf.confKey]) {

        await delay(1000)

        const { schema, ...rest } = mock[conf.confKey]

        const newSchema = replaceReferences(schema, rest)

        setSchemaProps(newSchema as SchemaPageProps)

        setIsLoading(false)

        return
      }

      const api = replaceVariablesInTemplate(paramsDict, configContext.appFunctionApi!)

      const { data } = await swrFetcher(api!, 'POST', {
        functionKey: configContext.formatterUiByKey!,
        params: {
          confKey: conf?.confKey,
          language: conf?.language || 'gl',
          version: conf?.version || '1'
        }
      })

      const { schema, ...rest } = JSON.parse(data.rs)

      const newSchema = replaceReferences(schema, rest)

      setSchemaProps(newSchema as SchemaPageProps)

    } catch (err) {
      prodError('DynamicPagePro', '获取配置失败！')
    }

    setIsLoading(false)
  }

  React.useEffect(() => {
    setIsLoading(true)
    setSchemaProps({})
    getConf()
  }, [conf])

  if (isLoading) {
    return <Loading />
  }

  if (isEmpty(schemaProps)) {
    return null
  }

  return (
    <div className={classNames(classPrefix, className)} style={{ ...style }}>
      <SchemaPage
        {...schemaProps}
        mock={mock}
      />
    </div>
  );
};

export default DynamicPage;
