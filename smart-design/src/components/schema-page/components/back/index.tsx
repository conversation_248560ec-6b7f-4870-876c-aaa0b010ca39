import React from 'react';
import classNames from 'classnames';
import { ProFieldFC } from '../../typing'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { SchemaPageContext } from '../../context'

const classPrefix = `x-back`

const FieldBack: ProFieldFC = (props) => {
  const {
    fieldProps,
  } = props

  const {
    className,
    style,
  } = fieldProps

  const { onBack } = React.useContext(SchemaPageContext)

  return (
    <span
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      onClick={() => onBack?.()}
    >
      <ArrowLeftOutlined />
      <span>返回</span>
    </span>
  );
};

export default FieldBack
