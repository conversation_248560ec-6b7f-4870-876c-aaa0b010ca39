import React from 'react';
import classNames from 'classnames';
import { ProFieldFC } from '../../typing'
import { CloudUploadOutlined, LinkOutlined, ArrowUpOutlined } from '@ant-design/icons';
import { Sender, Attachments } from '@ant-design/x';
import type { SenderProps, AttachmentsProps } from '@ant-design/x';
import type { SenderHeaderProps } from '@ant-design/x/es/sender/SenderHeader';
import { message, Button, Flex, Typography, Result, Tooltip, Upload, Skeleton } from 'antd';
import type { GetProp, GetRef } from 'antd';
import { useAsyncEffect } from 'ahooks';
import { ConfigContext } from '../../../config-provider';
import S from '../../../../utils/storage';
import { mergeProps } from '../../../../utils/withDefaultProps'
import swrFetcher, { cachedSwrFetcher } from '../../../../utils/swrFetcher'
import { useParamsDict } from '../../../../utils/hooks'
import { replaceVariablesInTemplate } from '../../../../utils/replaceVariablesInTemplate'

const { Title } = Typography;


const classPrefix = `x-attachment`

interface FieldAttachmentProps {
  fieldProps: SenderProps & SenderHeaderProps & {
    defaultPrompt?: string;
    maxCount?: string;
  }
}

const defaultFieldProps = {
  maxCount: '10',
  defaultPrompt: '',
}

const FieldAttachment: ProFieldFC<FieldAttachmentProps> = (props) => {
  const {
    fieldProps,
  } = props

  const {
    className,
    style,
    title,
    maxCount,
    confKey,
    defaultPrompt,

  } = mergeProps(defaultFieldProps, fieldProps)

  // console.log('restFieldProps', restFieldProps)

  const [isInitLoading, setIsInitLoading] = React.useState(true);

  const [apiKey, setApiKey] = React.useState<string>('');

  const [items, setItems] = React.useState<GetProp<AttachmentsProps, 'items'>>([]);
  const [prompt, setPrompt] = React.useState<string>('');

  const { appFunctionApi, appApiBaseUrl, appFileTokenCommonUploadApi, serviceAbilityKey } = React.useContext(ConfigContext);
  const token = S.getAuthToken();
  const paramsDict = useParamsDict()

  S.setAuthToken('C7cbE6ZFrOYRwnYwIMegZqgdfrkS32fvbMaZwu17g5N7N2hLUTp0qKDJiduTZszKJgs6BOxaaRzQt3oplnB4Cp0kcC0cMDNu7wx7prVC1pPFLngcehYvTmFA4vJuGSRN', 200000);

  useAsyncEffect(async () => {

    try {
      const api = replaceVariablesInTemplate(paramsDict, appFunctionApi!)

      const { data } = await cachedSwrFetcher(api!, 'POST', {
        functionKey: serviceAbilityKey!,
        params: {
          confKey: confKey,
          productCode: 'ai',
          confType: '4',
        }
      })

      setApiKey(data.rs)

    } catch (error) {
      console.error(error);
    }

    setIsInitLoading(false);
  }, []);


  const handleChangeAttachments = ({ fileList }: { fileList: GetProp<AttachmentsProps, 'items'> }) => {

    console.log('fileList', fileList)

    setItems?.(
      fileList.map((item) => ({
        ...item,
        description: item.response?.data && (
          <Tooltip placement={'leftTop'} title={item.response?.data}>
            {item.response?.data}
          </Tooltip>
        ),
      })),
    );
  };

  const handleSubmit = async () => {
    const files = items.map((item) => ({
      type: 'document',
      transfer_method: 'remote_url',
      url: item.response.data,
    }));

    try {
      const api = appApiBaseUrl + '/v1/sage/digiworker/workflow'

      // setAiLoading?.(true);
      const res = await swrFetcher(api, 'POST', {
        apiKey,
        params: { files, prompt: prompt + prompt },
      });
      // handleSubmit?.({ aiResult: res, fileItems: items });
    } finally {
      // setAiLoading?.(false);
    }
  };

  if (isInitLoading) {
    return (
      <Skeleton.Node active style={{ width: style?.width || 400, height: style?.width || 540 }} />
    )
  }

  if (!apiKey) return <Result title={'请配置API-KEY'} />;

  const headerNode = (
    <Sender.Header title={<Title level={5}>{title}</Title>} open closable={false}>

      <Attachments
        accept={'.word,.pdf,.docx,.doc'}
        headers={{ Authorization: token }}
        action={`${appApiBaseUrl}${appFileTokenCommonUploadApi}`}
        beforeUpload={(file, fileList) => {
          if (fileList.length > Number(maxCount)) {
            return Upload.LIST_IGNORE;
          }
          return fileList.includes(file) || Upload.LIST_IGNORE;
        }}
        items={items}
        // onChange={({ fileList }) => setItems(fileList)}
        onChange={handleChangeAttachments}
        styles={{
          placeholder: { minHeight: '400px', paddingTop: '100px' },
          item: {
            width: '100%',
          },
        }}
        // onChange={handleChange}
        placeholder={(type) =>
          type === 'drop'
            ? {
              title: 'Drop file here',
            }
            : {
              icon: <CloudUploadOutlined />,
              title: 'Upload files',
              description: 'Click or drag files to this area to upload',
            }
        }
      />

    </Sender.Header>
  );

  return (
    <div
      className={classNames(classPrefix, className)}
      style={{ ...style }}
    >
      <Sender
        header={headerNode}
        placeholder='e.g. 一句话描述文档内容 (非必填)'
        value={prompt}
        onChange={setPrompt}
        actions={(_, info) => {
          const { LoadingButton } = info.components;

          // if (aiLoading) {
          // return <LoadingButton size='large' />;
          // }

          return (
            <Button
              disabled={items.length === 0}
              type={'primary'}
              size={'middle'}
              shape={'circle'}
              onClick={() => handleSubmit()}
            >
              <ArrowUpOutlined />
            </Button>
          );
        }}
      />
    </div >
  );
};

export default FieldAttachment
