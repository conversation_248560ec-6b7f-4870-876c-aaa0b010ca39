import React from 'react';
import * as Icons from '@ant-design/icons';

import { ProFieldFC } from '../../typing'

interface FieldHtmlProps {
  fieldProps: {
    type?: string;
    style?: React.CSSProperties;
  }
}

const FieldAntdIcon: ProFieldFC<FieldHtmlProps> = (props) => {
  const {
    fieldProps,
  } = props

  const {
    type,
    style,
  } = fieldProps

  const Component: React.ComponentType<{ style?: React.CSSProperties }>
    = (Icons as any)[type as any] as React.ComponentType;

  return (
    <Component
      style={{ ...style }}
    />
  );
};

export default FieldAntdIcon
