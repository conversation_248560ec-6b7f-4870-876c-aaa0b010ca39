import React from 'react';
import classNames from 'classnames';
import InnerHtml from '../../../inner-html'
import { ProFieldFC } from '../../typing'
import { } from '../../context'

const classPrefix = `s-schema-page-html`

interface FieldHtmlProps {
  fieldProps: {
    content?: string;
  }
}

const FieldHtml: ProFieldFC<FieldHtmlProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restFieldProps
  } = fieldProps

  return (
    <InnerHtml
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      content={restFieldProps?.content || children as string}
    >
    </InnerHtml>
  );
};

export default FieldHtml
