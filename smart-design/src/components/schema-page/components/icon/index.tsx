import React from 'react';
import Icon from '../../../icon'
import { ProFieldFC } from '../../typing'

interface FieldIconProps {
  fieldProps: {
    type?: string;
    style?: React.CSSProperties;
  }
}

const FieldIcon: ProFieldFC<FieldIconProps> = (props) => {
  const {
    fieldProps,
  } = props

  const {
    type,
    style,
  } = fieldProps

  const Component: React.ComponentType<{ style?: React.CSSProperties }>
    = (Icon as any)[type as any] as React.ComponentType;

  return (
    <Component
      style={{ ...style }}
    />
  );
};

export default FieldIcon
