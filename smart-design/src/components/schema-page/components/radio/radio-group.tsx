import React from 'react';
import { Radio, Skeleton } from 'antd';
import type { RadioProps } from 'antd';
import { isString } from 'lodash';
import type { ProFieldFC } from '../../typing'

interface FieldRadioProps {
  fieldProps: RadioProps
}

const FieldRadioGroup: ProFieldFC<FieldRadioProps> = (props) => {

  const { value, onChange, fieldProps } = props

  const {
    responseLoading,
    responseData,
    ...restProps
  } = fieldProps

  const { fieldNames } = restProps

  const options: any[] = React.useMemo(() => {
    const newOptions = responseData || restProps?.options || []

    return newOptions.map((item: any) => {
      return {
        ...item,
        label: item[fieldNames?.label || 'label'],
        value: item[fieldNames?.value || 'value']
      }
    })

  }, [restProps?.options, responseData])

  const handleChange = (e: any) => {
    onChange?.(e.target.value)
  }

  if (responseLoading) {
    return <Skeleton active paragraph={{ width: '100%', rows: 0 }} title={{ width: '80%' }} />
  }

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Radio.Group
      {...restProps}
      value={value}
      options={options}
      disabled={isDisabled}
      onChange={handleChange}
    />
  );
};

export default FieldRadioGroup
