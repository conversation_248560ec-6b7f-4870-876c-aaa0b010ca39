import React from 'react';
import { Radio, Skeleton } from 'antd';
import type { RadioProps } from 'antd';
import { isString } from 'lodash';
import type { ProFieldFC } from '../../typing'
import InnerHtml from '../../../inner-html'

interface FieldRadioProps {
  fieldProps: RadioProps
}

const FieldRadio: ProFieldFC<FieldRadioProps> = (props) => {

  const { value, fieldProps, events, children } = props

  const {
    responseLoading,
    responseData,
    ...restProps
  } = fieldProps

  const { fieldNames } = restProps

  const options: any[] = React.useMemo(() => {
    const newOptions = responseData || restProps?.options || []

    return newOptions.map((item: any) => {
      return {
        ...item,
        label: item[fieldNames?.label || 'label'],
        value: item[fieldNames?.value || 'value']
      }
    })

  }, [restProps?.options, responseData])

  const handleChange = (e: any) => {
    events.onChange?.(e.target.value)
  }

  if (responseLoading) {
    return <Skeleton active paragraph={{ width: '100%', rows: 0 }} title={{ width: '80%' }} />
  }

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Radio
      {...restProps}
      value={value}
      disabled={isDisabled}
      onChange={handleChange}
    >
      <InnerHtml content={children as string} />
    </Radio>
  );
};

export default FieldRadio
