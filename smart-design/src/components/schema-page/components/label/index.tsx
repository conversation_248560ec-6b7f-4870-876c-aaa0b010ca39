import React from 'react';
import classNames from 'classnames';
import { ProFieldFC } from '../../typing'

const classPrefix = `x-page-pro-field-label`

const FieldLabel: ProFieldFC = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
  } = fieldProps

  return (
    <label className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </label>
  );
};

export default FieldLabel
