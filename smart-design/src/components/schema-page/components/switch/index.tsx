import React from 'react';
import { Switch } from 'antd';
import type { ProFieldFC } from '../../typing'
import { isString } from 'lodash';

const FieldSwitch: ProFieldFC = (props) => {
  const { value, fieldProps, events } = props

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Switch
      {...fieldProps}
      disabled={isDisabled}
      checked={value}
      onChange={events.onChange}
    />
  );
};

export default FieldSwitch
