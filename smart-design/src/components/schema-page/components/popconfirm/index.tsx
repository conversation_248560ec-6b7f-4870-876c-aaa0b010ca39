import React from 'react';
import { But<PERSON>, Popconfirm } from 'antd';
import type { ButtonProps, PopconfirmProps } from 'antd';
import type { ProFieldFC } from '../../typing'
import { isString } from 'lodash';
import { mergeProps } from '../../../../utils/withDefaultProps'

interface FieldPopconfirmProps {
  fieldProps?: Omit<PopconfirmProps, 'title'> & {
    type?: ButtonProps['type'];
    title?: string | React.ReactNode;
  };
}

const defaultFieldProps = {
  title: 'Are you sure delete this task?'
}

const FieldPopconfirm: ProFieldFC<FieldPopconfirmProps> = (props) => {
  const { children, fieldProps } = props

  const {
    isButtonLoading,
    type,
    onClick,
    disabled,
    ...restFieldProps
  } = mergeProps(defaultFieldProps, fieldProps)

  const isDisabled = isString(disabled) ? disabled === 'true' : disabled

  return (

    <Popconfirm
      {...restFieldProps}
      onConfirm={onClick}
    >
      <Button
        type={type}
        loading={isButtonLoading}
        disabled={isDisabled}
      >
        {children}
      </Button>
    </Popconfirm >
  );
};

export default FieldPopconfirm
