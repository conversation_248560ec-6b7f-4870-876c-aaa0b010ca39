import React from 'react';
import { Checkbox, Divider } from 'antd';
import { isString } from 'lodash'
import omit from '../../../../utils/omit'
import type { ProFieldFC } from '../../typing'
import InnerHtml from '../../../inner-html'

const FieldCheckboxCard: ProFieldFC = (props) => {

  const { value, fieldProps, events } = props

  const {
    disabled,
    options,
    responseLoading,
    responseData,
    extendedFields = [],

    ...restFieldProps
  } = fieldProps

  const isDisabled = isString(disabled) ? disabled === 'true' : disabled

  const checkboxProps = omit(restFieldProps, ['responseLoading', 'responseData', 'extendedFields', 'isButtonLoading'])

  console.log('checkboxProps', props)

  return (
    <Checkbox.Group
      {...checkboxProps}
      value={value}
      onChange={events.onChange}
      disabled={isDisabled}
    >
      {options.map((item: any, index: number) => {
        return (
          <React.Fragment key={item.value}>
            <Checkbox value={item.value} >
              <InnerHtml content={item.label} />
            </Checkbox>
            {index < options.length - 1 && <Divider />}
          </React.Fragment>
        )
      })}
    </Checkbox.Group>
  );
};

export default FieldCheckboxCard
