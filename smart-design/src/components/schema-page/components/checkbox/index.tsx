import React from 'react';
import { Checkbox } from 'antd';
import { isString } from 'lodash'
import omit from '../../../../utils/omit'
import type { ProFieldFC } from '../../typing'
import InnerHtml from '../../../inner-html'

const FieldCheckbox: ProFieldFC = (props) => {

  const { value, fieldProps, events, children } = props

  const {
    disabled,
    options,
    responseLoading,
    responseData,
    extendedFields = [],

    ...restFieldProps
  } = fieldProps

  console.log('fieldProps', props)
  console.log('fieldProps', events)

  const handleChange = (e: any) => {
    console.log('e', e.target.checked)
    events.onChange?.(e.target.checked)
  }

  const isDisabled = isString(disabled) ? disabled === 'true' : disabled

  const checkboxProps = omit(restFieldProps, ['responseLoading', 'responseData', 'extendedFields', 'isButtonLoading'])

  return (
    <Checkbox
      {...checkboxProps}
      checked={value}
      onChange={handleChange}
      disabled={isDisabled}
    >
      <InnerHtml content={children as string} />
    </Checkbox>
  );
};

export default FieldCheckbox
