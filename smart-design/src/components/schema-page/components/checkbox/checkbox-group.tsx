import React from 'react';
import { Checkbox } from 'antd';
import { isString } from 'lodash'
import omit from '../../../../utils/omit'
import type { ProFieldFC } from '../../typing'

const FieldCheckboxGroup: ProFieldFC = (props) => {

  const { value, fieldProps, events } = props

  const {
    disabled,
    options,
    responseLoading,
    responseData,
    extendedFields = [],

    ...restFieldProps
  } = fieldProps


  const handleChange = (checkedValues: string[]) => {
    events.onChange?.(checkedValues)
  }

  const isDisabled = isString(disabled) ? disabled === 'true' : disabled

  const checkboxProps = omit(restFieldProps, ['responseLoading', 'responseData', 'extendedFields', 'isButtonLoading'])

  return (
    <Checkbox.Group
      {...checkboxProps}
      value={value}
      onChange={handleChange}
      disabled={isDisabled}
      options={options}
    />
  );
};

export default FieldCheckboxGroup
