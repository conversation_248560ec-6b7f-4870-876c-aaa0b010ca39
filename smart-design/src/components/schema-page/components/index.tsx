import React from 'react';
import type { <PERSON>hemaField, SchemaColumn, SchemaEventHandlers } from '../typing'
import { omitUndefined } from '../../../utils/omitUndefined'

// 通用组件
import FieldButton from './button'
import FieldIcon from './icon'
import FieldAntdIcon from './antd-icon'

// 内置方法
import FieldBack from './back'

// 布局组件
import FieldContainer from './container'
import FieldRow from './row'
import FieldCol from './col'
import FieldDivider from './divider'
import FieldFlex from './flex'
import FieldSpace from './space'

// 展示组件
import FieldHtml from './html'
import FieldBadge from './badge'
import FieldItem from './item'
import FieldFiles from './files'
import FieldTable from './table'

// 导航组件
import FieldPageHeader from './page-header'

// html 元素
import FieldDiv from './div'
import FieldTitle from './title'
import FieldText from './text'
import FieldStrong from './strong'
import FieldSpan from './span'
import FieldLabel from './label'
import FieldParagraph from './paragraph'

// 反馈组件
import FieldPopconfirm from './popconfirm'
import FieldConfirm from './confirm'

// 表单组件
import FieldInput from './input'
import FieldPassword from './password'
import FieldTextArea from './text-area'
import FieldSelect from './select'
import FieldRadio from './radio'
import FieldCheckbox from './checkbox'
import FieldCheckboxCard from './checkbox/checkbox-card'
import FieldCheckboxGroup from './checkbox/checkbox-group'
import FieldDatePicker from './date-picker'
import FieldSwitch from './switch'

// AI 组件
import FieldAttachment from './attachment'

// 高级组件

// 临时组件

type DefaultRenderComponentProps = {
  children: React.ReactNode;
  fieldProps: SchemaField['fieldProps']
  field: string | string[];
  events: SchemaEventHandlers;
  value?: any;
}

const defaultRenderComponent = (
  valueType: SchemaColumn['type'],
  props: DefaultRenderComponentProps,
) => {
  const { children, ...restProps } = props

  let childNode: React.ReactNode = null;

  switch (valueType) {
    // ---------------------------- 布局组件 ----------------------------
    case 'container':
      childNode = <FieldContainer {...restProps}>{children}</FieldContainer>;
      break;

    case 'row':
      childNode = <FieldRow {...restProps}>{children}</FieldRow>;
      break;

    case 'col':
      childNode = <FieldCol {...restProps}>{children}</FieldCol>;
      break;

    case 'divider':
      childNode = <FieldDivider {...restProps}>{children}</FieldDivider>;
      break;

    case 'flex':
      childNode = <FieldFlex {...restProps}>{children}</FieldFlex>;
      break;

    case 'space':
      childNode = <FieldSpace {...restProps}>{children}</FieldSpace>;
      break;

    // ---------------------------- 展示组件 ----------------------------

    case 'table':
      childNode = <FieldTable {...restProps}>{children}</FieldTable>;
      break;

    case 'html':
      childNode = <FieldHtml {...restProps}>{children}</FieldHtml>;
      break;

    case 'badge':
      childNode = <FieldBadge {...restProps}>{children}</FieldBadge>;
      break;

    case 'item':
      childNode = <FieldItem {...restProps}>{children}</FieldItem>;
      break;

    case 'files':
      childNode = <FieldFiles {...restProps}>{children}</FieldFiles>;
      break;

    // ---------------------------- 导航组件 ----------------------------

    case 'pageHeader':
      childNode = <FieldPageHeader {...restProps}>{children}</FieldPageHeader>;
      break;

    // ---------------------------- 元素组件 ----------------------------

    case 'div':
      childNode = <FieldDiv {...restProps}>{children}</FieldDiv>;
      break;

    case 'title':
      childNode = <FieldTitle {...restProps}>{children}</FieldTitle>;
      break;

    case 'h1':
      childNode = <FieldTitle {...restProps} fieldProps={{ ...restProps.fieldProps, level: '1' }}>{children}</FieldTitle>;
      break;

    case 'h2':
      childNode = <FieldTitle {...restProps} fieldProps={{ ...restProps.fieldProps, level: '2' }}>{children}</FieldTitle>;
      break;

    case 'h3':
      childNode = <FieldTitle {...restProps} fieldProps={{ ...restProps.fieldProps, level: '3' }}>{children}</FieldTitle>;
      break;

    case 'h4':
      childNode = <FieldTitle {...restProps} fieldProps={{ ...restProps.fieldProps, level: '4' }}>{children}</FieldTitle>;
      break;

    case 'h5':
      childNode = <FieldTitle {...restProps} fieldProps={{ ...restProps.fieldProps, level: '5' }}>{children}</FieldTitle>;
      break;

    case 'text':
      childNode = <FieldText {...restProps}>{children}</FieldText>;
      break;

    case 'strong':
      childNode = <FieldStrong {...restProps}>{children}</FieldStrong>;
      break;

    case 'span':
      childNode = <FieldSpan {...restProps}>{children}</FieldSpan>;
      break;

    case 'label':
      childNode = <FieldLabel {...restProps}>{children}</FieldLabel>;
      break;

    case 'paragraph':
      childNode = <FieldParagraph {...restProps}>{children}</FieldParagraph>;
      break;

    // ---------------------------- 通用组件 ----------------------------

    case 'button':
      childNode = <FieldButton {...restProps}>{children}</FieldButton>;
      break;

    case 'icon':
      childNode = <FieldIcon {...restProps}>{children}</FieldIcon>;
      break;

    case 'antdIcon':
      childNode = <FieldAntdIcon {...restProps}>{children}</FieldAntdIcon>;
      break;

    // ---------------------------- 内置方法 ----------------------------
    case 'back':
      childNode = <FieldBack {...restProps}>{children}</FieldBack>;
      break;


    // ---------------------------- 反馈组件 ----------------------------

    case 'popconfirm':
      childNode = <FieldPopconfirm {...restProps}>{children}</FieldPopconfirm>;
      break;

    case 'confirm':
      childNode = <FieldConfirm {...restProps}>{children}</FieldConfirm>;
      break;


    // ---------------------------- 表单组件 ----------------------------

    case 'input':
      childNode = <FieldInput {...restProps}>{children}</FieldInput>;
      break;

    case 'password':
      childNode = <FieldPassword {...restProps}>{children}</FieldPassword>;
      break;

    case 'textArea':
      childNode = <FieldTextArea {...restProps}>{children}</FieldTextArea>;
      break;

    case 'select':
      childNode = <FieldSelect {...restProps}>{children}</FieldSelect>;
      break;

    case 'radio':
      childNode = <FieldRadio {...restProps}>{children}</FieldRadio>;
      break;

    case 'checkbox':
      childNode = <FieldCheckbox {...restProps}>{children}</FieldCheckbox>;
      break;

    case 'checkbox.card':
      childNode = <FieldCheckboxCard {...restProps}>{children}</FieldCheckboxCard>;
      break;

    case 'checkbox.group':
      childNode = <FieldCheckboxGroup {...restProps}>{children}</FieldCheckboxGroup>;
      break;

    case 'datePicker':
      childNode = <FieldDatePicker {...restProps}>{children}</FieldDatePicker>;
      break;

    case 'switch':
      childNode = <FieldSwitch {...restProps}>{children}</FieldSwitch>;
      break;

    // ---------------------------- AI 组件 ----------------------------

    case 'attachment':
      childNode = <FieldAttachment {...restProps}>{children}</FieldAttachment>;
      break;

    // ---------------------------- 高级组件 ----------------------------


    // ---------------------------- 临时组件 ----------------------------

    default:
      childNode = <div className={`bg-red-500 text-white rounded px-2 py-1 mb-[20px]`}>没有实现的组件: {valueType}</div>
  }

  return childNode
}

interface FieldComponentProps {
  type: SchemaColumn['type'];
  fieldProps: SchemaField['fieldProps'];
  children: React.ReactNode;
  field: string | string[];
  events: SchemaEventHandlers;
  value?: any;
  responseLoading?: boolean;
  responseData?: any;
}

const FieldRenderers: React.FC<FieldComponentProps> = (props) => {
  const {
    type,
    ...restProps
  } = props;

  const renderedDom = defaultRenderComponent(type, omitUndefined({
    ...restProps
  }))

  return (
    <React.Fragment>
      {renderedDom}
    </React.Fragment>
  )
}

export default FieldRenderers
