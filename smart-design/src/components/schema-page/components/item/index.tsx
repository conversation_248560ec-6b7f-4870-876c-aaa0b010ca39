import classNames from 'classnames';
import React from 'react';
import { ProFieldFC } from '../../typing'

const classPrefix = `x-page-pro-field-item`

export interface FieldItemProps {
  fieldProps: {
    label?: string;
    value?: string;
    layout?: 'horizontal' | 'vertical';
    emoji?: string;
  }
}

const FieldItem: ProFieldFC<FieldItemProps> = (props) => {
  const { fieldProps } = props;

  const {
    className,
    style,
    label,
    value,
    children,
    layout = 'vertical',
    emoji,
  } = fieldProps;

  const containerClass = `${classPrefix}-${layout}`;

  return (
    <div className={classNames(containerClass, className)} style={{ ...style }}>
      <div className={`${containerClass}-label`}>{label}</div>

      {value && (
        <div className={`${containerClass}-value`}>
          {emoji && (
            <span role="img" aria-label={value} className={`${containerClass}-emoji`}>
              {emoji}
            </span>
          )}

          {value}
        </div>
      )}
      {children && (
        <div className={`${containerClass}-content`}>{children}</div>
      )}
    </div>
  );
};

export default FieldItem;
