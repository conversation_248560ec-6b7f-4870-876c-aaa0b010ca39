import React from 'react';
import { Typography } from 'antd';
import type { TypographyProps } from 'antd';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const { Text } = Typography;

const classPrefix = `x-page-pro-field-text`

const defaultFieldProps = {
}

const FieldText: ProFieldFC = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)


  return (
    <Text
      {...restProps}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
    >
      {children}
    </Text>
  );
};

export default FieldText
