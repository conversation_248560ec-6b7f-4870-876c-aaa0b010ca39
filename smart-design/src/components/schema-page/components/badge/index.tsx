import React from 'react';
import classNames from 'classnames';
import { Badge } from 'antd';
import type { BadgeProps } from 'antd';
import { ProFieldFC } from '../../typing'

const classPrefix = `x-page-pro-field-badge`

interface FieldBadgeProps {
  fieldProps: BadgeProps
}

const FieldBadge: ProFieldFC<FieldBadgeProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restFieldProps
  } = fieldProps

  return (
    <Badge
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restFieldProps}
    >
      {children}
    </Badge >
  );
};

export default FieldBadge
