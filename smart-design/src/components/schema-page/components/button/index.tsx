import React from 'react';
import { Button } from 'antd';
import type { ProFieldFC } from '../../typing'
import { isString } from 'lodash';

const FieldButton: ProFieldFC = (props) => {
  const { children, fieldProps, events } = props

  const {
    ...restFieldProps
  } = fieldProps || {}

  const [isLoading, setIsLoading] = React.useState(false)

  const onClick = async (e: React.MouseEvent) => {
    setIsLoading(true)
    await events?.onClick?.(e)

    setIsLoading(false)
  }

  const isDisabled = isString(fieldProps?.disabled) ? fieldProps?.disabled === 'true' : fieldProps?.disabled

  return (
    <Button
      {...restFieldProps}
      loading={isLoading}
      disabled={isDisabled}
      onClick={onClick}
    >
      {children}
    </Button>
  );
};

export default FieldButton
