import React from 'react';
import classNames from 'classnames';
import { ProFieldFC } from '../../typing'

const classPrefix = `x-page-pro-field-span`

const FieldSpan: ProFieldFC = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
  } = fieldProps

  return (
    <span className={classNames(classPrefix, className)} style={{ ...style }}>
      {children}
    </span>
  );
};

export default FieldSpan
