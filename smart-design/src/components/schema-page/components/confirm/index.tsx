import React from 'react';
import { But<PERSON>, Modal } from 'antd';
import type { ModalFuncProps, ButtonProps } from 'antd';
import type { ProFieldFC } from '../../typing'
import { isString } from 'lodash';
import { mergeProps } from '../../../../utils/withDefaultProps'

interface FieldPopconfirmProps {
  fieldProps?: Omit<ModalFuncProps, 'title'> & {
    type?: ButtonProps['type'];
    title?: string | React.ReactNode;
    description?: string | React.ReactNode;
  };
}

const defaultFieldProps = {
  title: 'Are you sure delete this task?'
}

const FieldPopconfirm: ProFieldFC<FieldPopconfirmProps> = (props) => {
  const { children, fieldProps } = props
  const [modal, contextHolder] = Modal.useModal();

  const {
    isButtonLoading,
    type,
    onClick,
    disabled,
    ...restFieldProps
  } = mergeProps(defaultFieldProps, fieldProps)

  const handleClick = async () => {
    await modal.confirm({
      ...restFieldProps,
      content: restFieldProps.description || restFieldProps.content,
      onOk: onClick,
    });
  };

  const isDisabled = isString(disabled) ? disabled === 'true' : disabled

  return (
    <>
      <Button
        type={type}
        loading={isButtonLoading}
        disabled={isDisabled}
        onClick={handleClick}
      >
        {children}
      </Button>

      <div style={{ position: 'absolute' }}>
        {contextHolder}
      </div>
    </>
  );
};

export default FieldPopconfirm
