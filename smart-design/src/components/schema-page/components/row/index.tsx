import React from 'react';
import { Row } from 'antd';
import type { RowProps } from 'antd';
import type { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const classPrefix = `x-page-pro-field-row`

const defaultFieldProps = {
  gutter: '24',
  align: 'top',
  justify: 'start',
  wrap: true
}

interface FieldRowProps {
  fieldProps?: RowProps
}

const FieldRow: ProFieldFC<FieldRowProps> = (props) => {
  const { children, fieldProps } = props

  const {
    className,
    style,
    align,
    gutter,
    justify,
    wrap
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <Row
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
      align={align}
      gutter={Number(gutter)}
      justify={justify}
      wrap={wrap}
    >
      {children}
    </Row>
  );
};

export default FieldRow
