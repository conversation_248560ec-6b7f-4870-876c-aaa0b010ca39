import React from 'react';
import type { TemplateActionType } from '../../typing'
import { useParamsDict } from '../../../../../../utils/hooks'
import { ConfigContext } from '../../../../../config-provider'
import { replaceTemplateWithPipes } from '../../../../../../utils/replaceTemplateWithPipes'

interface FieldLinkProps {
  item: TemplateActionType;
  record: Record<string, any>
}

const FieldLink: React.FC<FieldLinkProps> = ({ item, record = {} }) => {
  const { name, props } = item

  const { router } = React.useContext(ConfigContext)
  const paramsDict = useParamsDict(record)

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!props?.url) {
      console.warn('url is not defined')
      return
    }

    if (!router) {
      console.error('router is not defined')
    }

    const path = replaceTemplateWithPipes(props?.url, { ...paramsDict, ...record })

    router?.push(path)
  }

  return (
    <a onClick={handleClick}>
      {name}
    </a>
  );
};

export default React.memo(FieldLink)
