import React from 'react'
import type { TemplateActionType } from '../../typing'
import type { SchemaEvent } from '../../../../typing'
import pageEventBus from '../../../../../../bus/page-event-hub'

interface FieldPopconfirmProps {
  item: TemplateActionType;
  record: Record<string, any>
}

const Button: React.FC<FieldPopconfirmProps> = (props) => {
  const { item, record } = props

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const { click } = item.events || {}

    if (click) {
      pageEventBus.publish(click.target, { ...record, ...click.payload })
    }
  }

  return (
    <a onClick={handleClick}>{item.name}</a>
  )
}


export default React.memo(Button)
