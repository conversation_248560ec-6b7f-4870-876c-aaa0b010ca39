import React from 'react';
import { Table } from 'antd';
import CurdTableColumnContent from './table-column-content'
import { mergeProps } from '../../../../utils/withDefaultProps'
import { parseJson } from '../../../../utils'
import { getDataSourceKey } from './utils'
import type { TablePaginationConfig } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import type { ProFieldFC } from '../../typing'
import type { CurdTableColumnType } from './typing'
import InnerHtml from '../../../inner-html';
import { isString } from 'lodash';
import './index.less'

const classPrefix = `x-table`

type RecordType = Record<string, any>;

interface CrudTableProps {
  fieldProps: {
    className?: string;
    style?: React.CSSProperties;

    size?: 'large' | 'middle' | 'small';
    scroll?: {
      scrollToFirstRowOnChange?: boolean;
      x?: string | number | true;
      y?: string | number;
    };

    rowKey?: string;
    columns?: CurdTableColumnType[];
    dataSource?: RecordType[];
    pagination?: TablePaginationConfig;
    rowSelection?: TableRowSelection<Record<string, any>>;
    onChange?: (...args: any) => void;
    onRefresh?: () => void;
    onRowClick?: (record: RecordType, index: number | undefined) => void;
    onRowDoubleClick?: (record: RecordType, index: number | undefined) => void;
    onRowContextMenu?: (record: RecordType, index: number | undefined) => void;
    onRowMouseEnter?: (record: RecordType, index: number | undefined) => void;
    onRowMouseLeave?: (record: RecordType, index: number | undefined) => void;
  }
}

const defaultProps = {
  columns: [],
  dataSource: [],
  size: 'large',
  pagination: false,
  rowKey: 'id',
  scroll: { x: 768 }
}

const CrudTable: ProFieldFC<CrudTableProps> = (props) => {
  const { fieldProps } = props

  const {
    size,
    scroll,
    rowKey,

    columns,
    dataSource,
    pagination,
    rowSelection,
    onRefresh,

    onChange,
    onRowClick,
    onRowDoubleClick,
    onRowContextMenu,
    onRowMouseEnter,
    onRowMouseLeave,
  } = mergeProps(defaultProps, fieldProps)

  const handleRefresh = () => {
    onRefresh?.()
  }

  const handleChange = (...args: any) => {
    onChange?.(...args)
  }

  const newColumns: CurdTableColumnType[] = React.useMemo(() => {
    const list = parseJson(dataSource)

    return columns.length !== 0 ? columns : getDataSourceKey(list as RecordType[])
  }, [columns, dataSource])


  return (
    <div className={classPrefix}>
      <Table
        rowKey={rowKey}
        dataSource={parseJson(dataSource) as RecordType[] || []}
        size={size}
        scroll={scroll}
        pagination={pagination}
        rowSelection={rowSelection}
        onChange={handleChange}
        onRow={(record: Record<string, any>, index: number | undefined) => {
          return {
            onClick: (e: React.MouseEvent) => {
              e.stopPropagation()
              onRowClick?.(record, index)
            },
            onDoubleClick: (e: React.MouseEvent) => {
              e.stopPropagation()
              onRowDoubleClick?.(record, index)
            },
            onContextMenu: (e: React.MouseEvent) => {
              e.stopPropagation()
              console.log(12)
              onRowContextMenu?.(record, index)
            },
            onMouseEnter: (e: React.MouseEvent) => {
              e.stopPropagation()
              onRowMouseEnter?.(record, index)
            },
            onMouseLeave: (e: React.MouseEvent) => {
              e.stopPropagation()
              onRowMouseLeave?.(record, index)
            },
          };
        }}
      >
        {newColumns.map((column) => {
          let dataIndex = column.dataIndex || column.key
          dataIndex = isString(dataIndex) ?
            dataIndex.includes(',') ? dataIndex.split(',') :
              dataIndex : dataIndex.join(',');

          let key = column.key
          key = isString(key) ? key : key.join(',');

          const title = <InnerHtml content={column.title} />
          // const title = column.title

          return (
            <Table.Column
              title={title}
              key={key}
              dataIndex={dataIndex}
              fixed={column?.fixed ? column?.fixed : false}
              width={column.width}
              sorter={column.sorter}
              defaultSortOrder={column.defaultSortOrder}
              render={(text: any, record: any, index: number) => {
                return <CurdTableColumnContent
                  column={column}
                  text={text}
                  index={index}
                  record={record}
                />
              }}
            />
          )
        })}
      </Table>
    </div>
  )
}

export default CrudTable
