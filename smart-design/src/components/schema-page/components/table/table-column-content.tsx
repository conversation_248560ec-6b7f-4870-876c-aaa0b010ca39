import React from 'react'
import { Space, Avatar, Tag, Flex } from 'antd';
import { isString } from 'lodash'
import { ConfigContext, ConfigContextProps } from '../../../config-provider'
import { renderTemplateJson, renderTemplateStatus, renderTemplateAction, renderTemplateSwitch } from './render-column-template'
import type { CurdTableColumnProps } from './typing'
import InnerHtml from '../../../inner-html'
// import Image from '../../../image'
import isDeepEqualReact from '../../../../utils/isDeepEqualReact'
import { replaceTemplateWithPipes } from '../../../../utils/replaceTemplateWithPipes'

const renderChildNode = (props: CurdTableColumnProps, configContext: ConfigContextProps) => {
  const { column, text, record, index } = props

  let childNode: React.ReactNode = null;

  switch (column.type) {
    case 'avatar':
      childNode = (
        text ? <Avatar shape='square' size={38} src={<img src={text} alt='avatar' />} /> : <Avatar shape='square' size={38} />
      )
      break;

    // case 'image':
    //   childNode = (
    //     text ? <Image src={text} width={38} height={38} {...column.props} /> : <Avatar shape='square' size={38} />
    //   )
    //   break;

    case 'template':
      childNode = (
        <InnerHtml content={replaceTemplateWithPipes(column.template, record)} />
      )
      break;

    case 'templateJson':
      childNode = (
        <>{renderTemplateJson(column.template, record, configContext.router)}</>
      )
      break;

    case 'templateSwitch':
      childNode = (
        <>{renderTemplateSwitch(props, configContext, renderChildNode)}</>
      );
      break;

    case 'status':
      childNode = (
        <Space>
          {renderTemplateStatus(column.template, record, configContext.timezone!)}
        </Space>
      )
      break;

    case 'action':
      childNode = (
        <Space>
          {renderTemplateAction(column.template, record, configContext.timezone!)}
        </Space>
      )
      break;

    default:
      if (isString(text)) {
        childNode = (
          <>{text}</>
        )
      } else {
        childNode = (
          <>{JSON.stringify(text)}</>
        )
      }

      break;
  }
  return childNode;
}

const CurdTableColumn: React.FC<CurdTableColumnProps> = (props) => {
  const configContext = React.useContext(ConfigContext)

  const childNode = renderChildNode(props, configContext);

  return (<>{childNode}</>)
}

export default React.memo(CurdTableColumn, (prevProps, nextProps) => {
  return isDeepEqualReact(nextProps, prevProps, []);
});
