import { CompareFn, SortOrder } from 'antd/es/table/interface';
import React from 'react';
import type { FetchType } from '../../../../typing';
import type { CloseType, SchemaEvent, SuccessType } from '../../typing';

export type TemplateJsonChildrenType = {
  type: string;
  props?: {
    url?: string;
    style?: React.CSSProperties;
  };
  children?: string | TemplateJsonChildrenType;
};

export type TemplateJsonType = {
  type: string;
  props?: {
    url?: string;
    style?: React.CSSProperties;
  };
  children: TemplateJsonChildrenType[];
};

export type TemplateStatusType = {
  vif?: string;
  name: string;
  color?: string;
};

export type TemplateActionType = {
  vif?: string;
  name: string;
  type?: string;
  version?: string;
  props?: {
    title?: string;
    description?: string;
    okText?: string;
    cancelText?: string;
    url?: string;
    color?: string;
    fileKey?: string;
    fileName?: string;
  };
  effect?: {
    publish?: string | string[];
    submit: FetchType;
    success?: SuccessType;
    close?: CloseType;
  };
  events?: SchemaEvent;
  children?: string;
};

export type CurdTableColumnType = {
  title: string;
  key: string | string[];
  dataIndex?: string | string[];
  type?:
    | 'avatar'
    | 'image'
    | 'template'
    | 'templateJson'
    | 'templateSwitch'
    | 'status'
    | 'action'
    | 'text';
  fixed?: boolean | 'left' | 'right';
  width?: string | number;
  template?:
    | string
    | TemplateJsonType
    | TemplateStatusType[]
    | Array<TemplateActionType>;
  props?: Record<string, any>;
  sorter?:
    | boolean
    | CompareFn<any>
    | {
        compare?: CompareFn<any>;
        multiple?: number;
      };
  defaultSortOrder?: SortOrder;
};
