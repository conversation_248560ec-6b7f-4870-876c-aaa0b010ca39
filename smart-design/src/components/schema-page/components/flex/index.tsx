import React from 'react';
import classNames from 'classnames';
import { Flex } from 'antd'
import type { FlexProps } from 'antd'
import { ProFieldFC } from '../../typing'
import { mergeProps } from '../../../../utils/withDefaultProps'

const classPrefix = `x-page-pro-field-flex`

interface FieldFlexProps {
  fieldProps: Omit<FlexProps, 'children'>
}

const defaultFieldProps = {
  gap: 'middle',
}

const FieldFlex: ProFieldFC<FieldFlexProps> = (props) => {
  const {
    fieldProps,
    children,
  } = props

  const {
    className,
    style,
    ...restFieldProps
  } = mergeProps(defaultFieldProps, fieldProps)

  return (
    <Flex
      className={classNames(classPrefix, className)}
      style={{ ...style }}
      {...restFieldProps}
    >
      {children}
    </Flex>
  );
};

export default FieldFlex
