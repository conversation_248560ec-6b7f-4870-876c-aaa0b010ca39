import classNames from 'classnames';
import React from 'react';
import FileView from '../../../file-view'
import type { FileViewProps } from '../../../file-view'
import { ProFieldFC } from '../../typing'
import { isArray, isString } from 'lodash';

const classPrefix = `x-page-pro-field-files`

export interface FieldFilesProps {
  fieldProps: {
    label?: string;
    value?: string | string[];
    children?: React.ReactNode;
    layout?: 'horizontal' | 'vertical';
    isPrivate?: boolean;
    type?: FileViewProps['layout'];
  }
}

const FieldFiles: ProFieldFC<FieldFilesProps> = (props) => {

  const { fieldProps } = props

  const {
    className,
    style,
    label,
    value,
    children,
    layout = 'vertical',
    isPrivate = false,
    type = 'default',
  } = fieldProps;

  const list = React.useMemo<string[]>(() => {
    let arr = []

    if (isArray(value)) {
      arr = value;
    } else if (isString(value)) {
      try {
        arr = value ? JSON.parse(value) : []
        arr = Array.isArray(arr) ? arr : [arr];
      } catch (error) {
      }
    }

    return arr
  }, [value])

  const containerClass = `${classPrefix}-${layout}`;

  return (
    <div className={classNames(classPrefix, `${classPrefix}-${layout}`, className)} style={{ ...style }}>
      <div className={`${containerClass}-label`}>{label}</div>

      <div className={`${containerClass}-value`}>
        {list.length === 0 ? '-' : (
          <FileView.Group
            list={list}
            isPrivate={isPrivate}
            layout={type}
          />
        )}
      </div>

      {!!children && (
        <div className={`${containerClass}-content`}>{children}</div>
      )}
    </div>
  );
};

export default FieldFiles;
