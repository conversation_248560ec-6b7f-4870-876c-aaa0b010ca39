import { Skeleton } from 'antd';
import { get, isArray, isFunction, isObject } from 'lodash';
import React, { useMemo, useState } from 'react';
import { ConfigContext } from '../../config-provider';
import { getEffectFetchConfig } from '../../../utils/getEffectFetchConfig';
import { useParamsDict } from '../../../utils/hooks';
import { replaceKeysWithRegex } from '../../../utils/replaceKeysWithRegex';
import swrFetcher, { cachedSwrFetcher } from '../../../utils/swrFetcher';
import { transformDataBasedOnRules } from '../../../utils/transformDataBasedOnRules';
import { SchemaPageContext } from '../context';
import type { SchemaField, RenderValueTypeHelpers } from '../typing';
import { delay } from '../../../utils'
import { prodError } from '../../../utils/log'
import pageEventBus from '../../../bus/page-event-hub'
import { PAGE_EVENT_NAME } from '../../../consts'

export interface EffectProps extends SchemaField {
  generateFields: RenderValueTypeHelpers['generateFields']
}

type RecordType = Record<string, any>;

const Effect: React.FC<EffectProps> = (params) => {
  const { effect, children, generateFields, field } = params;

  if (!field) {
    prodError('SchemaPagePro', 'Effect 组件必须配置 field 属性')
  }

  const [data, setData] = useState<any>(null);

  const [isLoading, setIsLoading] = useState(false);

  const paramsDict = useParamsDict();
  const configContext = React.useContext(ConfigContext);
  const { mock } = React.useContext(SchemaPageContext);

  const effectFetch = effect?.fetch;

  const { transformResponseData } = effectFetch || {};

  const fetch = async (fetchParams = {}, needLoading = true) => {
    setIsLoading(needLoading);

    try {
      let data: Record<string, any> = {}

      const mockKey = effectFetch?.functionKey || effectFetch?.processKey

      if (mock && mockKey && mock[mockKey]) {

        if (isFunction(mock[mockKey])) {
          const defaultParams = effectFetch?.defaultParams || {};

          data = mock[mockKey](defaultParams);
        } else {
          data = mock[mockKey];
        }

        await delay(1000)

      } else {
        const { api, method, params, dataIndex, isCached } = getEffectFetchConfig(
          effectFetch!,
          configContext,
          { ...paramsDict },
        );

        let res: any = {}

        if (isCached) {
          res = await cachedSwrFetcher(api, method, {
            ...params,
            params: {
              ...(params?.params || {}),
              ...fetchParams,
            },
          });
        } else {
          res = await swrFetcher(api, method, {
            ...params,
            params: {
              ...(params?.params || {}),
              ...fetchParams,
            },
          });
        }

        data = dataIndex ? get(res.data, dataIndex) : res.data;
      }

      if (transformResponseData) {
        data = transformDataBasedOnRules(data, transformResponseData);
      }

      console.log('effect', data)

      setData({
        [field.toString()]: data
      });
    } catch (err: any) {
      console.error('fetch error', err);
    }

    setIsLoading(false);
  };

  React.useEffect(() => {
    if (!effectFetch) return;
    setIsLoading(true)
    fetch();
  }, []);

  const handleRetry = async (params = {}, loading = true) => {
    await fetch(params, loading);
  };

  React.useEffect(() => {
    if (!field) return
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.EFFECT_RETRY}`, handleRetry)

    return () => {
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.EFFECT_RETRY}`, handleRetry)
    }
  }, [])

  const columns = useMemo(() => {
    if (!data || (!isArray(children) && !isObject(children))) return null;

    const result = replaceKeysWithRegex(
      children as RecordType | RecordType[],
      { ...paramsDict, ...data },
    ) as any;

    return isArray(result) ? result : [result];
  }, [data, paramsDict]);

  if (isLoading) {
    return (
      <div style={{ marginTop: '20px' }}>
        <Skeleton active paragraph={{ rows: 3 }} />
      </div>
    );
  }

  return (
    <React.Fragment>
      {columns && <>{generateFields(columns)}</>}
    </React.Fragment>
  );
};

export default Effect;
