import React from 'react';
import { isArray, isObject } from 'lodash';
import { Drawer as AntdDrawer } from 'antd';
import { mergeProps } from '../../../utils/withDefaultProps'
import { PAGE_EVENT_NAME } from '../../../consts'
import pageEventBus from '../../../bus/page-event-hub'
import type { DrawerStyles } from 'antd/es/drawer/DrawerPanel';
import { replaceKeysWithRegex } from '../../../utils/replaceKeysWithRegex';
import type { RenderValueTypeHelpers, SchemaField } from '../typing';

const classPrefix = `x-drawer`

type RecordType = Record<string, any>;

export interface DrawerProps extends SchemaField {
  generateFields: RenderValueTypeHelpers['generateFields']
}

const defaultFieldProps = {

}

const Drawer: React.FC<DrawerProps> = (props) => {
  const { children, fieldProps, field, generateFields } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  const [open, setOpen] = React.useState(false);

  const [data, setData] = React.useState<any>(null);


  const showDrawer = (values = {}) => {
    setData({ [field.toString()]: values });

    setOpen(true);
  };

  const hideDrawer = () => {
    setData(null);

    setOpen(false);
  };

  React.useEffect(() => {
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.DRAWER_SHOW}`, showDrawer)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.DRAWER_HIDE}`, hideDrawer)
    return () => {
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.DRAWER_SHOW}`, showDrawer)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.DRAWER_HIDE}`, hideDrawer)
    }
  }, [])

  const columns = React.useMemo(() => {
    if (!data || (!isArray(children) && !isObject(children))) return null;

    const result = replaceKeysWithRegex(
      children as RecordType | RecordType[],
      { ...data },
    ) as any;

    return isArray(result) ? result : [result];
  }, [data]);

  const drawerStyles: DrawerStyles = {
    body: {
      padding: '16px',
      background: '#f6f6f6',
    }
  };

  return (
    <AntdDrawer
      width={640}
      {...restProps}
      open={open}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
      onClose={() => setOpen(false)}
      styles={drawerStyles}
      destroyOnClose
    >
      {columns && <>{generateFields(columns)}</>}
    </AntdDrawer>
  );
};

export default Drawer
