import React from 'react';
import { isArray, isObject } from 'lodash';
import { Modal as AntdModal } from 'antd';
// import type { DrawerProps as AntdDrawerProps } from 'antd';
import { mergeProps } from '../../../utils/withDefaultProps'
import { PAGE_EVENT_NAME } from '../../../consts'
import pageEventBus from '../../../bus/page-event-hub'
import type { DrawerStyles } from 'antd/es/drawer/DrawerPanel';
import { replaceKeysWithRegex } from '../../../utils/replaceKeysWithRegex';
import { RenderValueTypeHelpers, SchemaField } from '../typing';

const classPrefix = `x-modal`

type RecordType = Record<string, any>;

interface ModalProps extends SchemaField {
  // fieldProps?: AntdDrawerProps,
  generateFields: RenderValueTypeHelpers['generateFields']
}

const defaultFieldProps = {

}

const Modal: React.FC<ModalProps> = (props) => {
  const { children, fieldProps, field, generateFields } = props

  const {
    className,
    style,
    ...restProps
  } = mergeProps(defaultFieldProps, fieldProps)

  const [open, setOpen] = React.useState(false);

  const [data, setData] = React.useState<any>(null);


  const showModal = (values = {}) => {
    setData({ [field.toString()]: values });

    setOpen(true);
  };

  const hideModal = () => {
    setData(null);

    setOpen(false);
  };

  React.useEffect(() => {
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.MODAL_SHOW}`, showModal)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.MODAL_HIDE}`, hideModal)
    return () => {
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.MODAL_SHOW}`, showModal)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.MODAL_HIDE}`, hideModal)
    }
  }, [])

  const columns = React.useMemo(() => {
    if (!data || (!isArray(children) && !isObject(children))) return null;

    const result = replaceKeysWithRegex(
      children as RecordType | RecordType[],
      { ...data },
    ) as any;

    return isArray(result) ? result : [result];
  }, [data]);

  return (
    <AntdModal
      width={640}
      {...restProps}
      open={open}
      className={`${classPrefix} ${className}`}
      style={{ ...style }}
      onCancel={() => setOpen(false)}
      onOk={() => setOpen(false)}
      destroyOnClose
      footer={null}
    >
      {columns && <>{generateFields(columns)}</>}
    </AntdModal>
  );
};

export default Modal
