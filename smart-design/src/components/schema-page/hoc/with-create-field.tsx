import React from 'react'
import { message } from 'antd'
import { isEmpty, get } from 'lodash';
import { useDeepCompareMemo, useParamsDict } from '../../../utils/hooks';
import FormItem from '../form-item';
import { FORM_FIELD_TYPES } from '../consts'
import type { SchemaFieldWrapperProps, RecordType, EventActionType, EffectType } from '../typing'
import pageEventBus from '../../../bus/page-event-hub'
import { omitUndefined } from '../../../utils/omitUndefined'
import { ConfigContext } from '../../config-provider'
import { getEffectFetchConfig } from '../../../utils/getEffectFetchConfig'
import { cachedSwrFetcher } from '../../../utils/swrFetcher'
import { replaceTemplateWithPipes } from '../../../utils/replaceTemplateWithPipes'

type ProFormComponent<P, Extends> = React.ComponentType<P & Extends>;

type FunctionFieldProps = {
  getFormItemProps?: () => Record<string, any>;
  getFieldProps?: () => Record<string, any>;
};

const CreateField = <P extends SchemaFieldWrapperProps = any>(
  Field: React.ComponentType<P> | React.ForwardRefExoticComponent<P>,
): ProFormComponent<P, FunctionFieldProps> => {

  const EvevtWrapper = (props: P) => {
    const { router } = React.useContext(ConfigContext);

    const { events, onChange } = props;

    const { click, change, blur, focus, input, clear, close, submit, confirm, next, back } = events || {};

    // 统一的事件处理函数
    const handleEvent = React.useCallback(async (eventConfig: EventActionType, values: RecordType) => {
      const { target, payload = {}, stopPropagation, expression } = eventConfig;
      console.log('eventConfig', eventConfig);
      console.log('values', values);
      const { event } = values;

      // 处理条件表达式
      // if (expression && !eval(expression)) {
      //   return;
      // }

      // 处理阻止冒泡
      if (stopPropagation && event) {
        event?.stopPropagation();
      }

      try {
        // 发布事件
        const resList: RecordType[] | undefined = await pageEventBus.publish(target, {
          ...payload,
          ...values
        });

        const res = {}

        resList?.forEach((item) => {
          Object.assign(res, item || {})
        })

        // console.log('redirect', redirect);
        // console.log('res', res);

        // 处理成功回调
        if (eventConfig.success) {
          const { message: msg, delay, redirect, callback } = eventConfig.success;
          // 处理成功逻辑...

          if (msg) {
            message.success(msg);
          }

          if (redirect) {

            const redirectUrl = replaceTemplateWithPipes(redirect, res)

            router?.replace(redirectUrl)
          }
        }
      } catch (error: any) {
        console.error(error);
        if(error?.message) message.error(error?.message || '操作失败');

        // 处理失败回调
        if (eventConfig.fail) {
          const { message, delay, retryTimes, callback, showError } = eventConfig.fail;
          // 处理失败逻辑...
        }
      }
    }, []);

    // 生成所有事件处理函数
    const eventHandlers = omitUndefined({
      onClick: click ? (e: any) => handleEvent(click, { event: e }) : undefined,
      onChange: change ? (e: any) => handleEvent(change, { value: e }) : onChange,
      onBlur: blur ? (e: any) => handleEvent(blur, { event: e }) : undefined,
      onFocus: focus ? (e: any) => handleEvent(focus, { event: e }) : undefined,
      onInput: input ? (e: any) => handleEvent(input, { event: e }) : undefined,
      onClear: clear ? () => handleEvent(clear, {}) : undefined,
      onClose: close ? () => handleEvent(close, {}) : undefined,
      onSubmit: submit ? (e: any) => handleEvent(submit, { event: e }) : undefined,
      onConfirm: confirm ? () => handleEvent(confirm, {}) : undefined,
      onNext: next ? () => handleEvent(next, {}) : undefined,
      onBack: back ? () => handleEvent(back, {}) : undefined,
    })

    return (
      <Field {...props} events={eventHandlers} />
    )
  }

  const EffectWrapper = (props: P) => {
    const { effect, dependenciesValues } = props;

    const configContext = React.useContext(ConfigContext)
    const paramsDict = useParamsDict(dependenciesValues || {})

    const { fetch: fetchConfig } = effect as EffectType;

    const [responseLoading, setResponseLoading] = React.useState<boolean>(false)
    const [responseData, setResponseData] = React.useState<RecordType | RecordType[]>()


    const fetch = async () => {
      setResponseLoading(true)

      try {
        const { api, method, params, dataIndex } = getEffectFetchConfig(fetchConfig!, configContext, paramsDict)

        const { data } = await cachedSwrFetcher(api, method, params)

        const result = dataIndex ? get(data, dataIndex) : data

        setResponseData(result)
      } catch (error) {
        console.error('fetch error', error)
      }

      setResponseLoading(false)
    }

    React.useEffect(() => {
      if (!fetchConfig) return
      fetch()
    }, [])

    return <EvevtWrapper {...props} responseLoading={responseLoading} responseData={responseData} />
  }

  const FromItemWrapper = (props: P) => {

    const { effect, type, labelProps, ...restProps } = props;

    const childNode = React.useMemo(() => {
      return isEmpty(effect) ? <EvevtWrapper {...props} /> : <EffectWrapper {...props} />;
    }, [effect]);

    const renderFieldNode = useDeepCompareMemo(() => {
      if (!type) {
        console.warn('Field: type is required');
        return null;
      }

      if (FORM_FIELD_TYPES.includes(type as string)) {
        return (
          <FormItem labelProps={labelProps} childrenType={props.type}>
            {childNode}
          </FormItem>
        )
      }

      return <>{childNode}</>
    }, [type, labelProps, restProps]);

    return renderFieldNode
  };

  return FromItemWrapper;
}

export { CreateField }
