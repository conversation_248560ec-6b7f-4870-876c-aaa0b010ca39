import React from 'react';
import type { FetchType, OnSuccessType } from '../../typing';
import { FORM_FIELD_TYPES } from './consts';

// ==================== 基础类型 ====================
// 通用记录类型
export type RecordType = Record<string, any>;

// ==================== 组件类型分类 ====================
// 视图组件类型
export type ViewComponentType =
  | 'container'
  | 'row'
  | 'col'
  | 'divider'
  | 'flex'
  | 'space'
  | 'table'
  | 'html'
  | 'badge'
  | 'div'
  | 'title'
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'text'
  | 'strong'
  | 'paragraph'
  | 'button'
  | 'popconfirm'
  | 'confirm'
  | 'icon';

// Pro 组件类型
export type ProComponentType = 'drawer' | 'modal' | 'effect' | 'group';

// 表单组件类型
export type FormComponentType = (typeof FORM_FIELD_TYPES)[number];

// AI 组件类型
export type AiComponentType = 'attachment';

// 自定义类型
export type CustomizeComponentType =
  | 'contractView'
  | 'tableFilter'
  | 'crudTable'
  | 'pageHeader'
  | 'item'
  | 'files';

// 所有组件类型汇总
export type AllComponentType =
  | ViewComponentType
  | ProComponentType
  | FormComponentType
  | AiComponentType
  | CustomizeComponentType;

// ==================== 事件相关类型 ====================
export type EventActionType = {
  /** 事件目标，指定事件触发后要执行的动作或方法 */
  target: string;
  /** 事件携带的数据负载，可以传递任意类型的数据 */
  payload?: RecordType;
  /** 是否阻止事件冒泡，设置为 true 时将阻止事件继续传播 */
  stopPropagation?: boolean;
  /** 条件表达式，用于控制事件是否执行 */
  expression?: string;
  /** 是否忽略错误，设置为 true 时即使发生错误也会继续执行 */
  ignoreError?: boolean;

  /** 事件执行成功后的回调配置 */
  success?: {
    /** 成功提示消息 */
    message?: string;
    /** 成功后的延迟时间(ms) */
    delay?: number;
    /** 成功后的跳转链接 */
    redirect?: string;
    /** 成功后执行的回调函数名 */
    callback?: string;
  };
  /** 事件执行失败后的回调配置 */
  fail?: {
    /** 失败提示消息 */
    message?: string;
    /** 失败后的延迟时间(ms) */
    delay?: number;
    /** 失败后的重试次数 */
    retryTimes?: number;
    /** 失败后执行的回调函数名 */
    callback?: string;
    /** 是否显示错误详情 */
    showError?: boolean;
  };
};

/**
 * 事件类型定义
 * 用于定义组件可以响应的各种事件
 */
export type SchemaEvent = {
  /** 点击事件 */
  click?: EventActionType;
  /** 值变化事件 */
  change?: EventActionType;
  /** 失去焦点事件 */
  blur?: EventActionType;
  /** 获得焦点事件 */
  focus?: EventActionType;
  /** 输入事件 */
  input?: EventActionType;
  /** 清除事件 */
  clear?: EventActionType;
  /** 关闭事件 */
  close?: EventActionType;
  /** 提交事件 */
  submit?: EventActionType;
  /** 确认事件 */
  confirm?: EventActionType;
  /** 下一步事件 */
  next?: EventActionType;
  /** 返回事件 */
  back?: EventActionType;
};

/**
 * Schema事件处理器类型定义
 * 统一定义所有事件的处理函数类型
 */
export type SchemaEventHandlers = {
  /** 点击事件 */
  onClick?: (...args: any) => any;
  /** 值变化事件 */
  onChange?: (...args: any) => any;
  /** 失去焦点事件 */
  onBlur?: (...args: any) => any;
  /** 获得焦点事件 */
  onFocus?: (...args: any) => any;
  /** 输入事件 */
  onInput?: (...args: any) => any;
  /** 清除事件 */
  onClear?: (...args: any) => any;
  /** 关闭事件 */
  onClose?: (...args: any) => any;
  /** 提交事件 */
  onSubmit?: (...args: any) => any;
  /** 确认事件 */
  onConfirm?: (...args: any) => any;
  /** 下一步事件 */
  onNext?: (...args: any) => any;
  /** 返回事件 */
  onBack?: (...args: any) => any;
};

// ==================== 副作用相关类型 ====================
export type SuccessType = {
  type:
    | 'link'
    | 'replace'
    | 'retry'
    | 'message'
    | 'modal'
    | 'reset'
    | 'effectRetry';
  url?: string;
  message?: string;
  delay?: string;
};

export type FailType = {
  type:
    | 'link'
    | 'replace'
    | 'retry'
    | 'message'
    | 'modal'
    | 'reset'
    | 'effectRetry';
  url?: string;
  message?: string;
  modal?: {
    title?: string;
    description?: string;
    ok?: OnSuccessType & {
      name?: string;
    };
    cancel?: OnSuccessType & {
      name?: string;
    };
    width?: string;
  };
  delay?: string;
};

export type CloseType = {
  type: 'link' | 'replace' | 'retry';
  url?: string;
  message?: string;
};

// 副作用配置类型
export type EffectType = {
  show?: string;
  fetch?: FetchType;
};

// ==================== Schema结构相关类型 ====================
export type SchemaColumn = {
  /** 作用域标识 */
  scope?: true;
  /** 字段名 */
  field: string | string[];
  /** 组件类型 */
  type: AllComponentType;
  /** 是否隐藏 */
  hidden?: boolean;
  /** 组件属性 */
  props?: {
    /** 样式对象 */
    style?: React.CSSProperties;
    /** 类名 */
    className?: string;
    /** 支持其他任意属性 */
    [key: string]: any;
  };
  /** 标签属性 */
  labelProps?: {
    /** 样式对象 */
    style?: React.CSSProperties;
    /** 类名 */
    className?: string;
    /** 标签文本 */
    label?: string;
    /** 提示信息 */
    tooltip?: string;
    /** 是否必填 */
    required?: boolean;
    /** 校验规则数组 */
    rules?: any[];
    /** 初始值 */
    initialValue?: any;
    /** 支持其他任意属性 */
    [key: string]: any;
  };
  /** 依赖字段 */
  dependencies?: Array<string> | Array<string[]>;
  /** 副作用配置 */
  effect?: EffectType;
  /** 子节点 */
  children?: string | SchemaColumn | SchemaColumn[];
  /** 事件配置 */
  events?: SchemaEvent;
};

export type AppType = {
  components?: SchemaColumn[]
  events?: SchemaEvent
  columns: SchemaColumn[]
}

/**
 * 表单项类型定义
 * 继承自 SchemaColumn 'hidden' 和 'props' 属性
 */
export type SchemaField = Omit<SchemaColumn, 'hidden' | 'props'> & {
  /** 字段属性配置，包含样式、类名等，确保不为空 */
  fieldProps: NonNullable<SchemaColumn['props']>;
  /** 标签属性配置，包含标签文本、提示、样式等，确保不为空 */
  labelProps: NonNullable<SchemaColumn['labelProps']>;
};

// ==================== 渲染辅助类型 ====================
/**
 * 渲染值类型的辅助工具接口
 */
export type RenderValueTypeHelpers = {
  /** 原始的列配置项 */
  originItem: SchemaColumn;
  /** 当前项的索引 */
  index: number;
  /** 生成子项的渲染函数 */
  generateFields: (items: SchemaColumn[]) => React.ReactNode[];
  /** 可用的变量集合 */
  variables: RecordType;
};

/**
 * Schema渲染值类型函数接口定义
 * @template InjectSchemaField - 注入的额外项类型，默认为空对象
 * @template HelpersType - 注入的额外辅助工具类型，默认为空对象
 * @param item - 包含注入类型的表单项配置
 * @param helpers - 包含注入类型的辅助工具对象
 * @returns React节点
 */
export type SchemaRenderValueTypeFunction<
  InjectSchemaField = object,
  HelpersType = object,
> = (
  item: SchemaField & InjectSchemaField,
  helpers: RenderValueTypeHelpers & HelpersType,
) => React.ReactNode;

export type SchemaFieldWrapperProps = SchemaField & {
  renderChildren: () => React.ReactNode;
  onChange?: (...args: any) => any;
  dependenciesValues?: RecordType;
};

/**
 * Pro字段组件的类型定义
 * @template T - 扩展属性的类型参数，默认为空对象
 * @type {React.ForwardRefRenderFunction} - React的转发引用渲染函数类型
 * @property {any} value - 字段的值
 * @property {Function} onChange - 值变化时的回调函数
 * @property {Function} onClick - 点击事件的回调函数
 * @property {string|React.ReactNode|React.ReactNode[]} children - 子元素
 * @property {SchemaField['fieldProps']} fieldProps - 字段属性配置
 * @property {string|string[]} field - 字段名或字段路径数组
 */
export type ProFieldFC<T = object> = React.ForwardRefRenderFunction<
  any,
  {
    value?: any;
    events: SchemaEventHandlers;
    children?: string | React.ReactNode | React.ReactNode[];
    fieldProps: SchemaField['fieldProps'];
    field: string | string[];
    responseData?: any;
    responseLoading?: boolean;
  } & T
>;
