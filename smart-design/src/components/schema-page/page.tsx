import React from "react"
import classNames from "classnames"
import { Form, Empty, Typography } from 'antd'
import type { FormProps, FormInstance } from 'antd';
import { mergeProps } from '../../utils/withDefaultProps'
import renderValueType from './value-type'
import { omitUndefined } from '../../utils/omitUndefined'
import type { SchemaColumn, RenderValueTypeHelpers, RecordType } from './typing'
import { replaceKeysWithRegex } from '../../utils/replaceKeysWithRegex'
import { SchemaPageContext } from './context'
import type { SchemaPageContextType } from './context'
import { isEmpty } from 'lodash'
import { useDeepCompareMemo, useParamsDict } from '../../utils/hooks'
import { delay } from '../../utils'
import { PAGE_EVENT_NAME } from '../../consts'
import { usePageEventBus } from './hooks'

import './page.less'

const classPrefix = `x-schema-page`

export {
  SchemaPageContext,
  type SchemaPageContextType,
};

export interface SchemaFormProRef extends FormInstance {
  submit: () => void;
}

export interface SchemaPageProps extends FormProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;

  field?: SchemaPageContextType['field'];

  columns?: SchemaColumn[];
  initialValues?: SchemaPageContextType['initialValues'];
  variables?: SchemaPageContextType['variables'];

  forceUpdate?: boolean;
  onRetry?: () => void
  onReset?: () => void;
  onBack?: () => void;
  mock?: RecordType
  components?: SchemaPageContextType['components'];
}

const defaultProps = {
  forceUpdate: false,

  columns: [],
  size: 'large',
  layout: 'vertical',
  labelAlign: 'right',
  labelWrap: false,
  variables: {},
  initialValues: {},
}

/**
 * 错误边界组件，捕获渲染过程中的错误
 */
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-500 rounded">
          <Typography.Title level={5} className="text-red-500">渲染错误</Typography.Title>
          <Typography.Text className="text-red-500">
            {this.state.error?.message || '未知错误'}
          </Typography.Text>
        </div>
      );
    }

    return this.props.children;
  }
}

interface SchemaPageContentProps {
  columns: SchemaColumn[];
  initialValues?: SchemaPageContextType['initialValues'];
  variables?: SchemaPageContextType['variables'];
  paramsDict: RecordType;
  children?: React.ReactNode;
}

const SchemaPageContent: React.FC<SchemaPageContentProps> = React.memo((props) => {
  const { columns, initialValues, variables, paramsDict } = props

  /**
   * 使用正则表达式替换键名
   *
   * @param columns 要处理的列数组
   * @returns 替换后的列数组
   */
  const replaceTemplateVariables = (columns: any[]) => {
    if (isEmpty(initialValues)) {
      return columns
    }

    return replaceKeysWithRegex(columns, { ...paramsDict, ...initialValues }) as any[]
  }

  /**
   * 生成渲染项的函数。
   *
   * @param items 页面列数组
   * @returns 过滤和映射后的渲染项数组
   */
  const generateFields: RenderValueTypeHelpers['generateFields'] = (items: SchemaColumn[]) => {
    return items.filter(item => {
      return !item.hidden
    }).map((originItem, index) => {
      const item = omitUndefined({
        scope: originItem.scope,
        field: originItem.field,
        type: originItem.type,
        hidden: originItem.hidden,
        fieldProps: omitUndefined({
          ...originItem.props,
        }) || {},
        labelProps: omitUndefined({
          name: originItem.field,
          ...originItem.labelProps
        }) || {},
        effect: originItem.effect,
        children: originItem.children,
        dependencies: originItem.dependencies,
        events: originItem.events,
      })

      return (
        renderValueType(item, {
          originItem,
          index,
          generateFields,
          variables: { ...paramsDict, ...variables, ...initialValues }
        })
      );
    }).filter(Boolean);
  }
  const childNode = useDeepCompareMemo(() => {
    if (columns.length === 0) return null

    const newColumns = replaceTemplateVariables(columns)

    return generateFields(newColumns);
  }, [columns, initialValues]);

  return (
    <ErrorBoundary>
      {childNode}
    </ErrorBoundary>
  )
});

SchemaPageContent.displayName = 'SchemaPageContent';

/**
 * SchemaPage 组件
 * 基于 JSON schema 配置的页面生成器
 */
const SchemaPage = React.forwardRef<FormInstance, React.PropsWithChildren<SchemaPageProps>>((p, ref) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,

    field,
    forceUpdate,

    initialValues,
    columns,
    variables,

    onRetry,
    onReset,
    onBack,
    mock,
    components,
    ...restProps
  } = props

  const paramsDict = useParamsDict()

  const [form] = Form.useForm();

  // 将 form 暴露给父组件
  React.useImperativeHandle(ref, () => form);

  const [update, setUpdate] = React.useState(false);

  /**
   * 处理重试操作的函数
   */
  const handleRetry = React.useCallback(() => {
    if (forceUpdate) {
      setUpdate(true)

      setTimeout(() => {
        setUpdate(false)
      }, 10)
    }

    onRetry?.()
  }, [forceUpdate, onRetry]);

  /**
   * 处理表单提交
   */
  const handleFinish = React.useCallback(async (): Promise<boolean> => {
    try {
      await form.validateFields()
      const values = form.getFieldsValue()
      console.log('values', values)
      await delay(1000)
      return true
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  }, [form]);

  /**
   * 处理表单重置
   */
  const handleReset = React.useCallback(async () => {
    form.resetFields();
    onReset?.();
  }, [form, onReset]);

  // 注册事件监听
  usePageEventBus(field, React.useMemo(() => ({
    [PAGE_EVENT_NAME.PAGE_RETRY]: handleRetry,
    [PAGE_EVENT_NAME.FORM_SUBMIT]: handleFinish,
    [PAGE_EVENT_NAME.FORM_RESET]: handleReset,
  }), [handleRetry, handleFinish, handleReset]));

  /**
   * 阻止表单中按钮的回车提交行为
   */
  const preventEnterPress = React.useCallback((e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === 'Enter' && (e.target as HTMLElement)?.tagName === 'BUTTON') {
      e.preventDefault();
    }
  }, []);

  // 创建 Context 值
  const contextValue = useDeepCompareMemo(() => ({
    field,
    form,
    initialValues,
    variables: {
      ...initialValues,
      ...paramsDict,
      ...variables
    },
    onBack,
    mock,
    components
  }), [field, variables, initialValues, form, paramsDict, onBack, mock, components]);

  // 如果没有列定义，显示空状态
  if (isEmpty(columns) || columns.length === 0) {
    return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
  }

  return (
    <div className={classNames(classPrefix, className)}>
      <Form
        form={form}
        autoComplete='off'
        initialValues={initialValues}
        style={{ ...style }}
        onKeyDown={preventEnterPress}
        {...restProps}
      >
        <SchemaPageContext.Provider value={contextValue}>
          {!update && (
            <SchemaPageContent
              columns={columns}
              initialValues={initialValues}
              paramsDict={paramsDict}
              variables={variables}
            />
          )}
        </SchemaPageContext.Provider>
      </Form>
    </div>
  )
});

export default SchemaPage
