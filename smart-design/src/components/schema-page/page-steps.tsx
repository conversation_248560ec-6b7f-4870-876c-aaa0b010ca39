
import React from 'react';
import { Form } from 'antd'
import CSSMotion from 'rc-motion';
import classNames from 'classnames';
import type { SchemaColumn } from './typing';
import type { SchemaPageProps, SchemaFormProRef } from './page'
import SchemaPagePro from './page'
import { mergeProps } from '../../utils/withDefaultProps'
import pageEventBus from '../../bus/page-event-hub'
import { PAGE_EVENT_NAME } from '../../consts'
import { isUndefined, get } from 'lodash';
import { prodError, prodWarning } from '../../utils/log'
import { SchemaPageContext } from './context'
import type { FetchType } from '../../typing';
import { ConfigContext } from '../config-provider';
import { useParamsDict } from '../../utils/hooks';
import { cachedSwrFetcher } from '../../utils/swrFetcher'
import { getEffectFetchConfig } from '../../utils/getEffectFetchConfig'

import './page-steps.less';

const classPrefix = `x-steps`;

type ItemType = {
  title?: string;
  description?: string;

  destroyInactiveTabPane?: boolean;
  forceRender?: boolean;

  columns?: SchemaColumn[];
  pageProps?: Omit<SchemaPageProps, 'columns'>
}

export interface SchemaPageStepsProps {
  className?: string;
  style?: React.CSSProperties;
  field?: string;
  destroyInactiveTabPane?: boolean;
  forceRender?: boolean;
  defaultActiveIndex?: number;
  items: ItemType[];
  fetchItems?: FetchType;

  variables?: Record<string, any>;

  pageProps?: Omit<SchemaPageProps, 'columns'>;
  initialValues?: Record<string, any>;

  mock?: Record<string, any>;

  onStepFinish?: (step: number, values: Record<string, any>, allValues: Record<string, any>) => Promise<boolean>;
  onFinish?: (values: Record<string, any>) => Promise<void>;
  onSkip?: (step: number) => Promise<boolean>;
  onBack?: (step: number) => void;
}

const defaultProps = {
  forceRender: false,
  destroyInactiveTabPane: false,
  defaultActiveIndex: 0,
  initialValues: {},
  variables: {}
}

const SchemaPageProSteps: React.FC<SchemaPageStepsProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    className,
    style,
    field,
    forceRender,
    destroyInactiveTabPane,
    defaultActiveIndex,
    initialValues,
    variables,

    fetchItems,
    pageProps,

    mock,

    onStepFinish,
    onFinish,
    onSkip,
    onBack,

    ...restProps
  } = props

  const configContext = React.useContext(ConfigContext);

  const paramsDict = useParamsDict({ ...variables, ...initialValues })


  const [activeIndex, setActiveIndex] = React.useState(defaultActiveIndex)
  const [items, setItems] = React.useState<any[]>(restProps.items)

  const [formValues, setFormValues] = React.useState<Record<string, any>>(initialValues)

  // useRef 可以提供一个在组件的整个生命周期内都保持不变的引用对象。
  const activeIndexRef = React.useRef(defaultActiveIndex)
  const formValuesRef = React.useRef(initialValues)
  const itemsRef = React.useRef<any[]>(restProps.items)

  const formsRef = React.useRef([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20].map(() => React.createRef<SchemaFormProRef>())).current;

  React.useEffect(() => {
    if (!restProps.items) return
    setItems(JSON.parse(JSON.stringify(restProps.items)))
  }, [restProps.items])

  /**
   * 根据索引设置活动步骤
   *
   * @param index 索引值，可以是字符串或数字
   */
  const handleTo = (index: string | number) => {
    if (isUndefined(index) || index === '') {
      prodError('SchemaPagePro Steps', 'index is undefined')
      return
    }

    setActiveIndex(Number(index))
  }

  // const isNextNo = React.useRef(false)

  const handleSkip = async () => {
    // 获取下一个索引
    const nextIndex = activeIndexRef.current + 1

    // 判断是否超出索引
    if (nextIndex >= itemsRef.current.length) {
      const check = await onSkip?.(activeIndexRef.current)
      return check
    }

    activeIndexRef.current = nextIndex
    setActiveIndex(nextIndex)
  }

  /**
   * 处理下一步操作
   */
  const handleNext = async () => {
    // 获取下一个索引
    const nextIndex = activeIndexRef.current + 1

    // 判断是否超出索引
    if (nextIndex >= itemsRef.current.length) {
      prodWarning('SchemaPagePro Steps', 'index is out of range')
      return
    }

    // 校验当前表单
    await formsRef[activeIndexRef.current].current?.validateFields()


    // 获取当前表单数据
    const curFormValues = await formsRef[activeIndexRef.current].current?.getFieldsValue()

    console.log('curFormValues', curFormValues)

    formValuesRef.current = {
      ...formValuesRef.current,
      ...curFormValues
    }

    setFormValues(formValuesRef.current)

    activeIndexRef.current = nextIndex
    setActiveIndex(nextIndex)

  }

  const handleNextAndSubmit = async () => {
    // 校验当前表单
    await formsRef[activeIndexRef.current].current?.validateFields()

    // 获取当前表单数据
    const curFormValues = await formsRef[activeIndexRef.current].current?.getFieldsValue()

    formValuesRef.current = {
      ...formValuesRef.current,
      ...curFormValues
    }

    setFormValues(formValuesRef.current)

    const check = await onStepFinish?.(activeIndexRef.current, curFormValues, formValuesRef.current)

    if (!check) return

    // 获取下一个索引
    const nextIndex = activeIndexRef.current + 1

    // 判断是否超出索引
    if (nextIndex >= itemsRef.current.length) {
      return check
    }

    activeIndexRef.current = nextIndex
    setActiveIndex(nextIndex)

    return check
  }

  /**
   * 异步函数，用于处理步骤的下一页获取逻辑
   */
  const handleFetchNext = async () => {

    if (!fetchItems) {
      prodError('SchemaPagePro Steps', 'fetchItems is undefined or null')
      return
    }

    // 校验当前表单
    await formsRef[activeIndexRef.current].current?.validateFields()

    const curFormValues = await formsRef[activeIndexRef.current].current?.getFieldsValue()

    formValuesRef.current = {
      ...formValuesRef.current,
      ...curFormValues
    }

    setFormValues(formValuesRef.current)

    const { api, method, params, dataIndex } = getEffectFetchConfig(fetchItems, configContext, { ...paramsDict, ...formValuesRef.current })

    const { data } = await cachedSwrFetcher(api, method, params)

    let result: any = dataIndex ? get(data, dataIndex) : data

    try {
      result = JSON.parse(result)
    } catch (err) {
    }

    itemsRef.current = [
      ...itemsRef.current,
      ...result
    ]

    setItems(JSON.parse(JSON.stringify(itemsRef.current)))

    handleNext()
  }

  /**
   * 处理后退按钮点击事件
   *
   * 当点击后退按钮时，将当前活动索引减一，并检查新索引是否越界。
   * 如果新索引小于0，则显示警告信息并返回。
   * 否则，更新活动索引和组件状态。
   */
  const handleBack = () => {
    const backIndex = activeIndexRef.current - 1

    if (backIndex < 0) {
      prodWarning('SchemaPagePro Steps', 'index is out of range')
      return
    }

    onBack?.(backIndex)

    activeIndexRef.current = backIndex
    setActiveIndex(backIndex)
  }

  /**
   * 异步提交表单处理函数
   */
  const handleSubmit = async () => {
    console.log('handleSubmit')

    if (activeIndexRef.current !== itemsRef.current.length - 1) {
      prodWarning('SchemaPagePro Steps', 'is not last step')
      return
    }

    // 校验当前表单
    await formsRef[activeIndexRef.current].current?.validateFields()

    // 获取当前表单数据
    const curFormValues = await formsRef[activeIndexRef.current].current?.getFieldsValue()

    formValuesRef.current = {
      ...formValuesRef.current,
      ...curFormValues
    }

    setFormValues(formValuesRef.current)

    await onFinish?.(formValuesRef.current)
  }

  React.useEffect(() => {
    if (!field) return

    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_TO}`, handleTo)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_SKIP}`, handleSkip)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_NEXT}`, handleNext)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_NEXT_AND_SUBMIT}`, handleNextAndSubmit)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_FETCH_NEXT}`, handleFetchNext)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_BACK}`, handleBack)
    pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.FORM_STEPS_SUBMIT}`, handleSubmit)

    return () => {
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_TO}`, handleTo)
      pageEventBus.subscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_SKIP}`, handleSkip)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_NEXT}`, handleNext)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_NEXT_AND_SUBMIT}`, handleNextAndSubmit)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_FETCH_NEXT}`, handleFetchNext)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.PAGE_STEPS_BACK}`, handleBack)
      pageEventBus.unsubscribe(`${field}:${PAGE_EVENT_NAME.FORM_STEPS_SUBMIT}`, handleSubmit)
    }
  }, [])

  return (
    <div
      className={classNames(classPrefix, className)}
      style={{ ...style }}
    >
      <Form.Provider
      // onFormFinish={(formName: string, { forms, values }) => { }}
      // onFormChange={(formName: string, info) => { }}
      >
        {items?.map((item, index) => {
          const {
            title,
            description,
            forceRender: itemForceRender,
            destroyInactiveTabPane: itemDestroyInactiveTabPane,
            // ...rest
          } = item;

          const active = index === activeIndex;

          return (
            <CSSMotion
              key={title || index}
              visible={active}
              forceRender={!!(forceRender || itemForceRender)}
              removeOnLeave={!!(destroyInactiveTabPane || itemDestroyInactiveTabPane)}
              leavedClassName={`${classPrefix}-hidden`}
            >
              {({ style: motionStyle, className: motionClassName }, ref) => (
                <div
                  ref={ref}
                  className={classNames(`${classPrefix}-pane`, motionClassName)}
                  style={{ ...motionStyle }}
                  data-pane-title={title || index}
                  data-pane-description={description || index}
                >
                  <SchemaPagePro
                    key={index}
                    variables={variables}
                    mock={mock}
                    {...pageProps}
                    {...item.pageProps}
                    columns={item?.columns || []}
                    ref={formsRef[index]}
                    initialValues={{ ...initialValues, ...formValues }}
                    onBack={handleBack}
                  />
                </div>
              )}
            </CSSMotion>
          )
        })}
      </Form.Provider>
    </div>
  )
}

export default SchemaPageProSteps;


