import React from 'react';
import pageEventBus from '../../bus/page-event-hub';

/**
 * 事件处理器类型
 */
type EventHandler<T = any> = (...args: any[]) => T;

/**
 * 事件处理器映射类型
 */
export type EventHandlersMap = Record<string, EventHandler>;

/**
 * 使用页面事件总线
 *
 * 该钩子用于连接组件与全局事件总线，在组件内处理特定字段的事件
 *
 * @param field 字段名，用于标识事件的命名空间
 * @param handlers 事件处理器对象，键为事件名，值为处理函数
 * @returns 一个包含触发事件的方法的对象
 */
export const usePageEventBus = <T extends EventHandlersMap>(
  field: string | undefined,
  handlers: T,
) => {
  // 使用 React.useRef 保存 handlers，避免因 handlers 变化导致的不必要重新订阅
  const savedHandlers = React.useRef(handlers);

  // 创建一个方法用于触发事件
  const trigger = React.useCallback((event: string, payload?: any) => {
    if (field) {
      pageEventBus.publish(`${field}:${event}`, payload);
    }
  }, [field]);

  // 当 handlers 发生变化时，更新 savedHandlers.current
  React.useEffect(() => {
    savedHandlers.current = handlers;
  }, [handlers]);

  // 当 field 发生变化时，进行订阅和清理
  React.useEffect(() => {
    const subscriptions: Array<() => void> = [];

    // 如果 field 为空，则直接返回，不进行订阅
    if (!field) return;

    // 遍历 savedHandlers.current 的键值对，进行事件订阅
    Object.entries(savedHandlers.current).forEach(([event, handler]) => {
      // 订阅事件，并获取取消订阅的函数
      const unsubscribe = pageEventBus.subscribe(`${field}:${event}`, handler);
      // 如果 unsubscribe 是函数类型，则将其添加到 subscriptions 数组中
      if (typeof unsubscribe === 'function') {
        subscriptions.push(unsubscribe);
      }
    });

    // 组件卸载时取消所有订阅
    return () => {
      subscriptions.forEach((unsubscribe) => unsubscribe());
    };
  }, [field]);

  // 返回触发事件的方法
  return {
    trigger
  };
};

/**
 * 在指定条件下重新渲染组件
 *
 * @param effect 需要执行的副作用函数
 * @param deps 依赖项数组
 * @param conditions 额外的条件检查函数，返回 true 时才执行 effect
 */
export const useConditionalEffect = (
  effect: React.EffectCallback, 
  deps: React.DependencyList, 
  conditions: () => boolean
) => {
  React.useEffect(() => {
    if (conditions()) {
      return effect();
    }
    return undefined;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);
};
