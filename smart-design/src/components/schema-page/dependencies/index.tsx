import React from 'react';
import { Form } from 'antd';
import { get, isArray } from 'lodash'
import { isDeepEqualReact } from '../../../utils'

const classPrefix = `x-dependencies`

export interface DependenciesProps {
  names: Array<string> | Array<string[]>;
  children: (values: { [key: string]: any }) => React.ReactNode;
};

const Dependencies: React.FC<DependenciesProps> = (props) => {

  const { children, names: nameList } = props

  return (
    <div className={classPrefix} data-dependencies={nameList}>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, nextValues) => {
          return nameList.some((name) => {
            return !isDeepEqualReact(
              get(prevValues, name),
              get(nextValues, name),
            );
          });
        }}
      >
        {(form) => {
          const values: Record<string, any> = {} as Record<string, any>;

          for (let i = 0; i < nameList.length; i++) {
            const itemName = nameList[i]

            const value = form?.getFieldValue?.(itemName);
            if (isArray(itemName)) {
              values[itemName.join('-')] = value;
            } else {
              values[itemName] = value
            }
          }

          return <>{children(values)}</>
        }}
      </Form.Item>
    </div>
  )
};

export default Dependencies
