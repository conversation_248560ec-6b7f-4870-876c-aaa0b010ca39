import classNames from 'classnames';
import React from 'react';
import { Flex } from 'antd';
import type { FlexProps } from 'antd';

const classPrefix = `x-group`;

export interface GroupProps extends FlexProps {
  title?: string;
}

const Group: React.FC<GroupProps> = (props) => {
  const {
    className,
    style,
    children,
    vertical = true,
    gap = 'large',
  } = props;

  return (
    <Flex
      vertical={vertical}
      gap={gap}
      className={classNames(classPrefix, className)}
      style={{ ...style }}>
      {children}
    </Flex>
  );
};

export default Group;
