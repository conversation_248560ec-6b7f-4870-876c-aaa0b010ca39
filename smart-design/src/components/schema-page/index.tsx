import InternalSchemaPage from './page';
import PageSteps from './page-steps';
import { withPerformanceMonitoring } from './performance-monitor';

export type { SchemaPageProps } from './page';
export type { SchemaPageStepsProps } from './page-steps';
export { useSchemaPageContext } from './context';
export { usePageEventBus } from './hooks';

// 添加性能监控的组件版本
const MonitoredSchemaPage = withPerformanceMonitoring(InternalSchemaPage, {
  componentName: 'SchemaPage',
  enabled: process.env.NODE_ENV === 'development'
});

const MonitoredPageSteps = withPerformanceMonitoring(PageSteps, {
  componentName: 'SchemaPageSteps',
  enabled: process.env.NODE_ENV === 'development'
});

// 使用原始类型定义复合组件
type InternalSchemaPageType = typeof InternalSchemaPage;

type CompoundedComponent = InternalSchemaPageType & {
  PageSteps: typeof PageSteps;
};

// 在开发环境中使用监控版本，在生产环境中使用原始版本
const SchemaPage = (process.env.NODE_ENV === 'development'
  ? MonitoredSchemaPage
  : InternalSchemaPage) as CompoundedComponent;

// 附加 PageSteps 子组件
SchemaPage.PageSteps = process.env.NODE_ENV === 'development'
  ? MonitoredPageSteps
  : PageSteps;

export default SchemaPage;
