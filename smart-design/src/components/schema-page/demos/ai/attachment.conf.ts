import type { SchemaPageProProps } from 'smart-design';

export const mock = {
  get_basic: {
    title: '这是一个标题',
    col: 'col-12',
    flex: 'flex',
    space: 'space',
    badge: 'badge',
    container: '这是一个container元素内容',
    div: '这是一个div元素内容',
    html: '用于将HTML字符串直接插入到DOM中‌。',
    text: '这是一个文本内容',
    strong: '这是一个加粗的文本内容',
    table: [
      {
        headers: [],
        data: [
          [
            {
              text: '姓名',
              className: 'bolder',
            },
            {
              text: 'zhangying',
            },
          ],
          [
            {
              text: '公司名称',
              className: 'bolder',
            },
            {
              text: 'ICB Limited',
            },
          ],
          [
            {
              text: '所属部门',
              className: 'bolder',
            },
            {
              text: '人力资源',
            },
          ],
          [
            {
              text: '入职日期',
              className: 'bolder',
            },
            {
              text: '2023-11',
            },
          ],
          [
            {
              text: '邮箱',
              className: 'bolder',
            },
            {
              text: '<EMAIL>',
            },
          ],
          [
            {
              text: '手机号',
              className: 'bolder',
            },
            {
              text: '+86,15210818572',
            },
          ],
          [
            {
              text: '职级',
              className: 'bolder',
            },
            {
              text: '3-1',
            },
          ],
          [
            {
              text: '职位',
              className: 'bolder',
            },
            {
              text: '人力资源经理',
            },
          ],
          [
            {
              text: '直属经理',
              className: 'bolder',
            },
            {
              text: 'Elaine',
            },
          ],
        ],
      },
    ],
    group: [
      { address: 'New York No. 1 Lake Park' },
      { address: 'London No. 1 Lake Park' },
      { address: 'Sydney No. 1 Lake Park' },
    ],
  },
};

export const columns: SchemaPageProProps['columns'] = [
  {
    type: 'attachment',
    field: 'attachment',
    props: {
      confKey: 'system.EXTRACT-FILE-INFO-API-KEY',
      title: '上传',
      defaultPrompt:
        '提炼内容为json，包含合同名称 contractName, 雇员名称 employeeName, 开始时间 startTime(格式为 yyyy-MM-dd)。无法解析时对应字段为空字符串。',
      style: {
        width: '400px',
      },
    },
  },
  {
    type: 'modal',
    field: 'modal',
    props: {
      title: '请确认合同文件',
    },
  },
];
