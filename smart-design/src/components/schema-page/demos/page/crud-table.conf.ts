import type { SchemaPageProProps } from 'smart-design';

export const mock = {
  get_crud_table: [
    {
      id: '1',
      key: '1',
      user: {
        name: 'sx',
        email: '<EMAIL>',
      },
      age: '18',
      address: '上海',
      birthday: '1740478763606',
      system_status: '1',
    },
    {
      id: '2',
      key: '2',
      user: {
        name: 'test',
        email: '<EMAIL>',
      },
      age: '20',
      address: '北京',
      birthday: '1743573763606',
      system_status: '0',
    },
    {
      id: '3',
      key: '3',
      user: {
        name: '小明',
        email: '<EMAIL>',
      },
      age: '20',
      address: '深圳',
      birthday: '1742108463606',
      system_status: '2',
    },
  ],
  get_user_info: ({ id }: { id: string }) => {
    let result = {};

    switch (id) {
      case '1':
        result = {
          username: 'sx',
          telephone: '10000',
        };
        break;
      case '2':
        result = {
          username: 'test',
          telephone: '10086',
        };
        break;
      case '3':
        result = {
          username: '小明',
          telephone: '10010',
        };
        break;
      default:
        break;
    }

    return result;
  },
};

const curdTableColumns: any[] = [
  {
    title: '用户',
    key: 'user',
    type: 'templateJson',
    template: {
      type: 'div',
      children: [
        {
          type: 'button',
          props: {
            // "to": "/components/table"
          },
          children: '*{user.name}',
        },
        {
          type: 'div',
          props: {
            style: {
              fontSize: '12px',
              color: '#9ca3af',
              marginTop: '5px',
            },
          },
          children: {
            type: 'span',
            children: '*{user.email}',
          },
        },
      ],
    },
  },
  {
    title: 'Age',
    key: 'age',
  },
  {
    title: 'Address',
    key: 'address',
  },
  {
    title: '合同开始时间',
    key: 'expectedStartDate',
    type: 'template',
    template: '<div>*{birthday | formatTime(YYYY-MM-DD)}</div>',
  },
  {
    title: '合同状态',
    key: 'system.status',
    type: 'status',
    template: [
      {
        vif: '*{variables.expectedStartDate} < now',
        name: '已失效',
        color: '#9ca3af',
      },
      {
        vif: '*{system_status}===0',
        name: '草稿',
        color: '#9ca3af',
      },
      {
        vif: '*{system_status}===1',
        name: '待邀请',
        color: '#fbbf24',
      },
      {
        vif: '*{system_status}===2',
        name: '信息审核中',
        color: '#3b82f6',
      },
      {
        vif: '*{system_status}===3',
        name: '信息已审核',
        color: '#10b981',
      },
      {
        vif: '*{system_status}===4',
        name: '待开始',
        color: '#ea580c',
      },
      {
        vif: '*{system_status}===5',
        name: '生效中',
        color: '#16a34a',
      },
      {
        vif: '*{system_status}===6',
        name: '已取消',
        color: '#f43f5e',
      },
      {
        vif: '*{system_status}===7',
        name: '已邀请',
        color: '#7c3aed',
      },
    ],
  },
  // {
  //   title: '业务操作',
  //   key: 'action',
  //   type: 'action',
  //   fixed: 'right',
  //   width: '200px',
  //   template: [
  //     {
  //       vif: '*{system_status}===0',
  //       name: '发送邀请',
  //       type: 'pop',
  //       props: {
  //         title: '发送邀请入职邮件',
  //         description:
  //           '<div><div>确定要给预入职员工发送邀请邮件吗？</div><div>发出后将无法撤回！</div><div>邮件将发送给 <a>*{variables.email}</a></div></div>',
  //         okText: '是',
  //         cancelText: '否',
  //       },
  //       // "onSubmit": {
  //       //   "type": "function",
  //       //   "functionKey": "flow_start",
  //       //   "defaultParams": {
  //       //     "flowKey": "global_gs_c_flow",
  //       //     "version": "41808f76f6384287",
  //       //     "system.parentFlowId": "*{id}",
  //       //     "uniqueKey": "*{id}"
  //       //   }
  //       // }
  //     },
  //     {
  //       vif: '*{system_status}===0',
  //       name: '编辑',
  //       type: 'link',
  //       props: {
  //         url: '/agencyoperations/entry/edit/*{id}',
  //       },
  //     },
  //     {
  //       vif: '*{system_status} === 2 || *{system_status} === 3 || *{system_status} === 4 || *{system_status} === 5',
  //       name: '上传 SOW',
  //       type: 'upload',
  //     },
  //     {
  //       vif: '*{system_status} === 2 || *{system_status} === 3 || *{system_status} === 4 || *{system_status} === 5',
  //       name: '上传押金凭证',
  //       type: 'upload',
  //     },
  //     {
  //       vif: '*{system_status} === 3 || *{system_status} === 4 || *{system_status} === 5',
  //       name: '上传员工合同',
  //       type: 'upload',
  //     },
  //     {
  //       vif: '*{system_status}===4',
  //       name: '确认入职',
  //       type: 'pop',
  //     },
  //     {
  //       vif: '*{system_status}===0',
  //       name: '继续填写',
  //       type: 'link',
  //     },
  //     {
  //       vif: '*{system_status}===7',
  //       name: '再次发送邀请',
  //       type: 'pop',
  //     },
  //   ],
  // },
  {
    title: '系统操作',
    key: 'action',
    type: 'action',
    width: '270px',
    fixed: 'right',
    template: [
      {
        name: 'Drawer 详情',
        type: 'button',
        props: {},
        effect: {
          publish: 'drawer:drawer-show',
        },
        children: 'Drawer 详情',
      },
      {
        vif: '*{id}===1',
        name: '跳转到 GS',
        type: 'link',
        props: {
          url: 'https://service-test.smartdeer.tech/login?id=*{id}',
        },
        children: '跳转到 GS',
      },
      {
        vif: '*{isRelease}===0',
        name: '发布',
        type: 'pop',
        children: '发布',
        effect: {
          submit: {
            type: 'function',
            functionKey: 'x_sage_corehr_release_payroll',
            method: 'post',
            defaultParams: {
              id: '*{id}',
              __is_admin__: 'true',
            },
          },
          success: {
            type: 'retry',
          },
        },
      },
      {
        vif: '*{isRelease}!==0',
        name: '撤销发布',
        type: 'pop',
        children: '撤销发布',
        effect: {
          submit: {
            type: 'function',
            functionKey: 'x_sage_corehr_release_payroll',
            method: 'post',
            defaultParams: {
              id: '*{id}',
              __is_admin__: 'true',
            },
          },
          success: {
            type: 'retry',
          },
        },
      },
      {
        vif: '*{system_status}===0',
        name: '删除',
        type: 'del',
        props: {
          title: '删除',
          description:
            '<div>您确定要删除此薪资项吗？</div><div style="margin-top: 5px; font-weight: bolder; color: #e52c2c">删除之后无法反悔！可能会影响其他数据！</div>',
          okText: '是',
          cancelText: '否',
        },
        children: '删除',
        effect: {
          fetch: {
            type: 'function',
            functionKey: 'x_sage_corehr_remove_payroll',
            defaultParams: {
              id: '*{id}',
            },
          },
        },
      },
    ],
  },
];

export const columns: SchemaPageProProps['columns'] = [
  {
    type: 'effect',
    field: 'crudTable',
    props: {},
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'get_crud_table',
        defaultParams: {},
        data: 'rs',
      },
    },
    children: [
      {
        type: 'crudTable',
        field: 'crudTable',
        props: {
          columns: curdTableColumns,
          dataSource: '*{crudTable}',
        },
      },
    ],
  },

  
  {
    type: 'drawer',
    field: 'drawer',
    props: {
      title: '详情',
    },
    children: {
      scope: true,
      type: 'effect',
      field: 'userInfo',
      props: {},
      effect: {
        fetch: {
          type: 'function',
          functionKey: 'get_user_info',
          defaultParams: {
            id: '*{drawer.id}',
          },
          data: 'rs',
        },
      },
      children: {
        type: 'container',
        field: 'container',
        children: [
          {
            type: 'h5',
            field: 'h5',
            props: {},
            children: '用户基本信息',
          },
          {
            type: 'item',
            field: 'username',
            props: {
              label: 'Username',
              value: '*{userInfo.username}',
              style: {
                marginTop: '16px',
              },
            },
          },
          {
            type: 'item',
            field: 'telephone',
            props: {
              label: 'Telephone',
              value: '*{userInfo.telephone}',
              style: {
                marginTop: '16px',
              },
            },
          },
        ],
      },
    },
  },
];
