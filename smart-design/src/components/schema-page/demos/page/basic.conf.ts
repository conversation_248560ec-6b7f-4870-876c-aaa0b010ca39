import type { SchemaPageProProps } from 'smart-design';

export const mock = {
  get_basic: {
    title: '这是一个标题',
    col: 'col-12',
    flex: 'flex',
    space: 'space',
    badge: 'badge',
    container: '这是一个container元素内容',
    div: '这是一个div元素内容',
    html: '用于将HTML字符串直接插入到DOM中‌。',
    text: '这是一个文本内容',
    strong: '这是一个加粗的文本内容',
    table: [
      {
        headers: [],
        data: [
          [
            {
              text: '姓名',
              className: 'bolder',
            },
            {
              text: 'zhangying',
            },
          ],
          [
            {
              text: '公司名称',
              className: 'bolder',
            },
            {
              text: 'ICB Limited',
            },
          ],
          [
            {
              text: '所属部门',
              className: 'bolder',
            },
            {
              text: '人力资源',
            },
          ],
          [
            {
              text: '入职日期',
              className: 'bolder',
            },
            {
              text: '2023-11',
            },
          ],
          [
            {
              text: '邮箱',
              className: 'bolder',
            },
            {
              text: '<EMAIL>',
            },
          ],
          [
            {
              text: '手机号',
              className: 'bolder',
            },
            {
              text: '+86,15210818572',
            },
          ],
          [
            {
              text: '职级',
              className: 'bolder',
            },
            {
              text: '3-1',
            },
          ],
          [
            {
              text: '职位',
              className: 'bolder',
            },
            {
              text: '人力资源经理',
            },
          ],
          [
            {
              text: '直属经理',
              className: 'bolder',
            },
            {
              text: 'Elaine',
            },
          ],
        ],
      },
    ],
    group: [
      { address: 'New York No. 1 Lake Park' },
      { address: 'London No. 1 Lake Park' },
      { address: 'Sydney No. 1 Lake Park' },
    ],
  },
};

export const columns: SchemaPageProProps['columns'] = [
  {
    type: 'effect',
    field: 'basic',
    props: {},
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'get_basic',
        defaultParams: {},
        data: 'rs',
      },
    },
    children: [
      {
        type: 'divider',
        field: 'divider-page-header',
        children: 'page-header',
      },
      {
        type: 'pageHeader',
        field: 'pageHeader',
        props: {
          title: ['组件', 'SchemaPagePro'],
        },
      },

      {
        type: 'divider',
        field: 'divider-title',
        children: 'title',
      },
      {
        type: 'title',
        field: 'title',
        children: '*{basic.title}',
      },

      {
        type: 'divider',
        field: 'divider-icon',
        children: 'icon',
      },
      {
        type: 'icon',
        field: 'icon',
        props: {
          type: 'SettingOutline',
          style: {
            fontSize: '40px',
            color: '#555',
          },
        },
      },

      {
        type: 'divider',
        field: 'divider-row',
        children: 'row',
      },
      {
        type: 'row',
        field: 'row',
        children: [
          {
            type: 'col',
            field: 'row-col-1',
            children: {
              type: 'div',
              field: 'row-col-1-div',
              props: {
                style: {
                  height: '50px',
                  background: '#1677ffbf',
                  textAlign: 'center',
                  lineHeight: '50px',
                  color: '#fff',
                },
              },
              children: '*{basic.col}',
            },
          },
          {
            type: 'col',
            field: 'row-col-2',
            children: {
              type: 'div',
              field: 'row-col-2-div',
              props: {
                style: {
                  height: '50px',
                  background: '#1677ff',
                  textAlign: 'center',
                  lineHeight: '50px',
                  color: '#fff',
                },
              },
              children: '*{basic.col}',
            },
          },
        ],
      },

      {
        type: 'divider',
        field: 'divider-flex',
        children: 'flex',
      },
      {
        type: 'flex',
        field: 'flex',
        children: [
          {
            type: 'div',
            field: 'flex-div-1',
            props: {
              style: {
                width: '50%',
                height: '50px',
                background: '#1677ffbf',
                textAlign: 'center',
                lineHeight: '50px',
                color: '#fff',
              },
            },
            children: '*{basic.flex}',
          },
          {
            type: 'div',
            field: 'flex-div-2',
            props: {
              style: {
                width: '50%',
                height: '50px',
                background: '#1677ff',
                textAlign: 'center',
                lineHeight: '50px',
                color: '#fff',
              },
            },
            children: '*{basic.flex}',
          },
        ],
      },

      {
        type: 'divider',
        field: 'divider-space',
        children: 'space',
      },
      {
        type: 'space',
        field: 'space',
        children: [
          {
            type: 'div',
            field: 'space-div-1',
            props: {
              style: {
                width: '100px',
                height: '50px',
                background: '#1677ffbf',
                textAlign: 'center',
                lineHeight: '50px',
                color: '#fff',
              },
            },
            children: '*{basic.space}',
          },
          {
            type: 'div',
            field: 'space-div-2',
            props: {
              style: {
                width: '100px',
                height: '50px',
                background: '#1677ff',
                textAlign: 'center',
                lineHeight: '50px',
                color: '#fff',
              },
            },
            children: '*{basic.space}',
          },
        ],
      },

      {
        type: 'divider',
        field: 'divider-badge',
        children: 'badge',
      },
      {
        type: 'badge',
        field: 'badge',
        props: {
          count: '10',
        },
        children: {
          type: 'div',
          field: 'badge-div',
          props: {
            style: {
              width: '50px',
              height: '50px',
              background: '#1677ff',
              textAlign: 'center',
              lineHeight: '50px',
              color: '#fff',
            },
          },
          children: '*{basic.badge}',
        },
      },

      {
        type: 'divider',
        field: 'divider-group',
        children: 'group',
      },
      {
        type: 'group',
        field: 'group',
        props: {
          ttile: 'group title',
          dataSource: '*{basic.group}',
          style: {
            background: '#F8FAFC',
            padding: '12px',
            borderRadius: '8px',
          },
        },
        children: [
          {
            type: 'item',
            field: 'group-item',
            props: {
              label: 'Address',
              value: '*{address}',
            },
          },
        ],
      },

      {
        type: 'divider',
        field: 'divider-item',
        children: 'item-vertical',
      },
      {
        type: 'item',
        field: 'item-tsername_vertical',
        props: {
          label: 'Username',
          value: 'sx',
        },
      },
      {
        type: 'item',
        field: 'item-telephone_vertical',
        props: {
          label: 'Telephone',
          value: '13123456789',
          style: {
            marginTop: '16px',
          },
        },
      },

      {
        type: 'divider',
        field: 'divider-item',
        children: 'item-horizontal',
      },
      {
        type: 'item',
        field: 'item-tsername_horizontal',
        props: {
          layout: 'horizontal',
          label: 'Username',
          value: 'sx',
        },
      },
      {
        type: 'item',
        field: 'item-telephone_horizontal',
        props: {
          layout: 'horizontal',
          label: 'Telephone',
          value: '13123456789',
          style: {
            marginTop: '16px',
          },
        },
      },

      {
        type: 'divider',
        field: 'divider-files',
        children: 'files-vertical',
      },
      {
        type: 'files',
        field: 'files-vertical',
        props: {
          label: 'logo',
          value: [
            'https://global-image.smartdeer.work/p/images/0xaeeb4111e99f41d09b20c6d712693d77.png',
          ],
          style: {
            marginTop: '16px',
          },
        },
      },

      {
        type: 'divider',
        field: 'divider-files',
        children: 'files-horizontal',
      },
      {
        type: 'files',
        field: 'files-horizontal',
        props: {
          layout: 'horizontal',
          label: 'logo',
          value: [
            'https://global-image.smartdeer.work/p/images/0xaeeb4111e99f41d09b20c6d712693d77.png',
          ],
          style: {
            marginTop: '16px',
          },
        },
      },

      {
        type: 'divider',
        field: 'divider-files',
        children: 'files-horizontal-text',
      },
      {
        type: 'files',
        field: 'files-horizontal-text',
        props: {
          type: 'text',
          label: 'logo',
          value: [
            'https://global-image.smartdeer.work/p/images/0xaeeb4111e99f41d09b20c6d712693d77.png',
          ],
          style: {
            marginTop: '16px',
          },
        },
      },

      {
        type: 'divider',
        field: 'container-container',
        children: 'container',
      },
      {
        type: 'container',
        field: 'container',
        props: {
          style: {
            width: '100%',
            border: '1px solid #1677ff',
          },
        },
        children: {
          type: 'div',
          field: 'container-div',
          props: {
            style: {
              width: '100%',
              height: '50px',
              background: '#1677ff',
              textAlign: 'center',
              lineHeight: '50px',
              color: '#fff',
            },
          },
          children: '*{basic.container}',
        },
      },

      {
        type: 'divider',
        field: 'divider-div',
        children: 'div',
      },
      {
        type: 'div',
        field: 'div',
        props: {
          style: {
            width: '100%',
            height: '50px',
            background: '#1677ff',
            textAlign: 'center',
            lineHeight: '50px',
            color: '#fff',
          },
        },
        children: '*{basic.div}',
      },

      {
        type: 'divider',
        field: 'divider-html',
        children: 'html',
      },
      {
        type: 'html',
        field: 'html',
        children: '*{basic.html}',
      },

      {
        type: 'divider',
        field: 'divider-text',
        children: 'text',
      },
      {
        type: 'text',
        field: 'text',
        children: '*{basic.text}',
      },

      {
        type: 'divider',
        field: 'divider-strong',
        children: 'strong',
      },
      {
        type: 'strong',
        field: 'strong',
        children: '*{basic.strong}',
      },

      {
        type: 'divider',
        field: 'divider-table',
        children: 'table',
      },
      {
        type: 'table',
        field: 'table',
        props: {
          dataSource: '*{basic.table}',
        },
      },

      {
        type: 'divider',
        field: 'divider-button',
        children: 'button',
      },
      {
        type: 'flex',
        field: 'flex-button-wrapper',
        props: {
          justify: 'center',
        },
        children: [
          {
            type: 'button',
            field: 'retry',
            props: {
              type: 'primary',
            },
            children: 'Button 刷新',
            effect: {
              publish: 'basic:effect-retry',
            },
          },
          {
            type: 'popconfirm',
            field: 'popconfirm',
            props: {
              type: 'primary',
              title: '你确定要刷新吗？',
              description: '刷新后将无法恢复数据，请谨慎操作。',
              okText: '刷新',
              cancelText: '取消',
            },
            children: 'Popconfirm 刷新',
            effect: {
              publish: 'basic:effect-retry',
            },
          },
          {
            type: 'confirm',
            field: 'confirm',
            props: {
              type: 'primary',
              title: '你确定要刷新吗？',
              description: '刷新后将无法恢复数据，请谨慎操作。',
              okText: '刷新',
              cancelText: '取消',
            },
            children: 'Confirm 刷新',
            effect: {
              publish: 'basic:effect-retry',
            },
          },
        ],
      },
    ],
  },
];
