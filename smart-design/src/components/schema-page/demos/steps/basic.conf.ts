import type { SchemaPageProStepsProps } from 'smart-design';

export const items: SchemaPageProStepsProps['items'] = [
  {
    title: 'k1',
    columns: [
      {
        type: 'title',
        field: 'k1.title',
        props: {
          level: '2',
        },
        children: 'Your organization details',
      },
      {
        type: 'div',
        field: 'k1.div.description',
        props: {},
        children:
          'Please provide your Organization information accurately, it will be used in all your communications on the platform.',
      },
      {
        type: 'input',
        field: 'organizationName',
        props: {
          placeholder: '请输入',
        },
        labelProps: {
          label: 'Organization name',
          rules: [
            {
              required: true,
              message: 'Please input your organization name',
            },
          ],
        },
      },
      {
        type: 'input',
        field: 'organizationSize',
        labelProps: {
          label: 'Organization size',
        },
        props: {
          placeholder: '请输入',
        },
      },
      {
        type: 'input',
        field: 'headquartersCountry',
        labelProps: {
          label: 'Headquarters country',
        },
        props: {
          placeholder: '请输入',
        },
      },

      {
        type: 'flex',
        field: 'button-wrapper',
        props: {
          justify: 'center',
        },
        children: [
          {
            type: 'button',
            field: 'k1.button',
            props: {
              type: 'primary',
            },
            children: 'Next',
            effect: {
              publish: 'steps:page-steps-next',
            },
          },
        ],
      },
    ],
  },
  {
    title: 'k2',
    columns: [
      {
        type: 'title',
        field: 'k2.title',
        props: {
          level: '2',
        },
        children: 'Personal Details',
      },
      {
        type: 'div',
        field: 'k2.div.description',
        props: {},
        children:
          'Please provide your personal details, they will be used to complete your profile on ICB.',
      },
      {
        type: 'input',
        field: 'legalFirstName',
        labelProps: {
          label: 'Legal first name',
        },
        props: {},
      },
      {
        type: 'input',
        field: 'legalLastName',
        labelProps: {
          label: 'Legal last name',
        },
        props: {},
      },
      {
        type: 'input',
        field: 'preferredName',
        labelProps: {
          label: 'Preferred name (optional)',
        },
        props: {},
      },
      {
        type: 'input',
        field: 'citizenOf',
        labelProps: {
          label: 'Citizen of',
        },
        props: {},
      },
      {
        type: 'input',
        field: 'dateOfBirth',
        labelProps: {
          label: 'Date of birth (MM/DD/YYYY)',
        },
        props: {},
      },
      {
        type: 'row',
        field: 'k2.row_1',
        children: [
          {
            type: 'col',
            field: 'k2.col_1',
            props: {
              span: '8',
            },
            children: [
              {
                type: 'input',
                field: 'dialCode',
                labelProps: {
                  label: 'Dial Code',
                },
                props: {
                  placeholder: '请输入内容',
                },
              },
            ],
          },
          {
            type: 'col',
            field: 'k2.col_2',
            props: {
              span: '16',
            },
            children: [
              {
                type: 'input',
                field: 'phoneNumber',
                labelProps: {
                  label: 'Phone number',
                },
                props: {
                  placeholder: '请输入',
                },
              },
            ],
          },
        ],
      },
      {
        type: 'input',
        field: 'department',
        labelProps: {
          label: 'Department',
        },
        props: {},
      },
      {
        type: 'input',
        field: 'jobTitle',
        labelProps: {
          label: 'Job title',
        },
        props: {},
      },
      {
        type: 'input',
        field: 'seniorityLevel',
        labelProps: {
          label: 'Seniority level',
        },
        props: {},
      },

      {
        type: 'flex',
        field: 'button-wrapper',
        props: {
          justify: 'center',
        },
        children: [
          {
            type: 'button',
            field: 'step2.back1',
            props: {},
            children: '返回',
            effect: {
              publish: 'steps:page-steps-back',
            },
          },
          {
            type: 'button',
            field: 'k2.submit',
            props: {
              type: 'primary',
            },
            children: 'Submit',
            effect: {
              publish: 'steps:form-steps-submit',
            },
          },
        ],
      },
    ],
  },
];
