import React from 'react';
import { SchemaPage } from 'smart-design'
import { items } from './basic.conf'

const { PageSteps } = SchemaPage

export default () => {

  const onFinish = (values: any) => {
    console.log('onFinish --- steps --- > ', values)
    console.log('onFinish --- steps --- > ', JSON.stringify(values))
  }

  return (
    <>
      <PageSteps
        field='steps'
        items={items}
        pageProps={{
          size: 'large',
          layout: 'vertical'
        }}
        onFinish={onFinish}
      />
    </>
  )
}
