import type { SchemaPageProProps } from 'smart-design';

export const mock = {
  get_select_options: [
    {
      id: '1',
      name: 'month',
    },
    {
      id: '2',
      name: 'weekly',
    },
  ],
  get_radio_options: [
    {
      value: 'a',
      label: 'A',
    },
    {
      value: 'b',
      label: 'B',
    },
    {
      value: 'c',
      label: 'C',
    },
    {
      value: 'd',
      label: 'D',
    },
  ],
};

export const columns: SchemaPageProProps['columns'] = [
  {
    type: 'input',
    field: 'input',
    labelProps: {
      label: 'input',
    },
    props: {},
  },
  {
    type: 'password',
    field: 'password',
    labelProps: {
      label: 'password',
    },
    props: {},
  },
  {
    type: 'textArea',
    field: 'textArea',
    labelProps: {
      label: 'textArea',
    },
    props: {},
  },
  {
    type: 'select',
    field: 'select',
    labelProps: {
      label: 'select',
    },
    props: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'get_select_options',
        defaultParams: {},
      },
    },
  },
  {
    type: 'radio',
    field: 'radio',
    labelProps: {
      label: 'radio',
    },
    props: {},
    effect: {
      fetch: {
        type: 'function',
        functionKey: 'get_radio_options',
        defaultParams: {},
      },
    },
  },
  {
    type: 'datePicker',
    field: 'datePicker',
    labelProps: {
      label: 'datePicker',
    },
    props: {},
  },
  {
    type: 'flex',
    field: 'button-wrapper',
    props: {
      justify: 'center',
    },
    children: [
      {
        type: 'button',
        field: 'reset',
        props: {},
        children: 'Reset',
        effect: {
          publish: 'formBasic:form-reset',
        },
      },
      {
        type: 'button',
        field: 'submit',
        props: {
          type: 'primary',
        },
        children: 'Submit',
        effect: {
          publish: 'formBasic:form-submit',
        },
      },
    ],
  },
];
