import React from 'react';
import { SchemaPage } from 'smart-design'
import { mock, columns } from './basic.conf'

export default () => {
  const onFinish = (values: any) => {
    console.log('onFinish --- basic --- > ', values)
    console.log('onFinish --- basic --- > ', JSON.stringify(values))
  }

  return (
    <SchemaPage
      mock={mock}
      field='formBasic'
      columns={columns}
      size='large'
      variables={{
        entityId: 2,
      }}
      onFinish={onFinish}
    />
  )
}
