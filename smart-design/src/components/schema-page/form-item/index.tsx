import React from 'react';
import { Form } from 'antd';
import type { ColumnsType } from '../typing'
import { mergeProps } from '../../../utils/withDefaultProps'
import { isDeepEqualReact } from '../../../utils'
import { isArray } from 'lodash'

interface FormItemProps {
  labelProps: ColumnsType['labelProps'];
  children?: React.ReactNode
  childrenType: string
}

const defaultProps = {
  labelProps: {},
  dependenciesValues: {}
}

const normFile = (e: any) => {
  if (isArray(e)) {
    return e;
  }
  return e?.fileList;
};

const FormItem: React.FC<FormItemProps> = (p) => {
  const props = mergeProps(defaultProps, p)

  const {
    children,
    labelProps,
  } = props

  console.log('labelProps', labelProps)

  return (
    <Form.Item
      {...labelProps}
    >
      {children}
    </Form.Item>
  )
};

export default FormItem
