import React, { useEffect, useRef } from 'react';

interface PerformanceMonitorProps {
  componentName: string;
  enabled?: boolean;
  children: React.ReactNode;
}

/**
 * 性能监控组件
 * 
 * 用于测量子组件的渲染时间并在开发环境中输出性能数据
 * 
 * @param props 组件属性
 * @returns 包含子组件的 React Fragment
 */
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  componentName,
  enabled = process.env.NODE_ENV === 'development',
  children
}) => {
  const renderStart = useRef<number>(0);
  const renderCount = useRef<number>(0);
  const totalRenderTime = useRef<number>(0);

  useEffect(() => {
    if (!enabled) return;

    const endTime = performance.now();
    const renderTime = endTime - renderStart.current;

    renderCount.current += 1;
    totalRenderTime.current += renderTime;

    const avgRenderTime = totalRenderTime.current / renderCount.current;

    console.log(
      `%c[Performance] ${componentName}`,
      'color: #2196F3; font-weight: bold',
      `\nRender #${renderCount.current}`,
      `\nRender time: ${renderTime.toFixed(2)}ms`,
      `\nAverage time: ${avgRenderTime.toFixed(2)}ms`
    );

    // 对较慢的渲染发出警告
    if (renderTime > 100) {
      console.warn(
        `%c[Performance Warning] ${componentName} render took ${renderTime.toFixed(2)}ms`,
        'color: #FF9800; font-weight: bold'
      );
    }
  });

  // 记录渲染开始时间
  if (enabled) {
    renderStart.current = performance.now();
  }

  return <>{children}</>;
};

/**
 * 使用性能监控的高阶组件
 * 
 * @param WrappedComponent 要监控的组件
 * @param options 配置选项
 * @returns 包装后的组件
 */
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: { componentName?: string; enabled?: boolean } = {}
) {
  const componentName = options.componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';

  const MonitoredComponent = (props: P) => {
    return (
      <PerformanceMonitor
        componentName={componentName}
        enabled={options.enabled}
      >
        <WrappedComponent {...props} />
      </PerformanceMonitor>
    );
  };

  MonitoredComponent.displayName = `WithPerformanceMonitoring(${componentName})`;

  return MonitoredComponent;
}

export default PerformanceMonitor; 