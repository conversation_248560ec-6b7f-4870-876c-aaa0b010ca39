import React from 'react'
import type { FormInstance } from 'antd';

/**
 * 通用记录类型
 */
export type RecordType = Record<string, any>

export type ComponentsType = {
  type: string;
  render: ((props: any) => React.ReactNode);
};

/**
 * SchemaPage 上下文类型
 */
export type SchemaPageContextType = {
  /** 字段名称，用于事件通信的命名空间 */
  field?: string;
  /** 表单初始值 */
  initialValues: RecordType;
  /** 环境变量 */
  variables?: RecordType;
  /** 表单实例引用 */
  form?: FormInstance<any>;
  /** 是否为步骤表单 */
  isSteps?: boolean;
  /** 模拟数据，用于开发阶段 */
  mock?: RecordType;
  /** 返回上一步或上一页的回调函数 */
  onBack?: () => void;

  /** 组件配置 */
  components?: Array<ComponentsType>;
}

/**
 * SchemaPage 上下文默认值
 */
export const defaultSchemaPageContext: SchemaPageContextType = {
  initialValues: {},
  isSteps: false,
}

/**
 * SchemaPage 上下文对象
 * 用于在组件树中共享 SchemaPage 的状态和方法
 */
export const SchemaPageContext =
  React.createContext<SchemaPageContextType>(defaultSchemaPageContext)

/**
 * 使用 SchemaPage 上下文的自定义 Hook
 * 
 * @returns SchemaPage 上下文值
 */
export const useSchemaPageContext = (): SchemaPageContextType => {
  const context = React.useContext(SchemaPageContext);
  return context;
}
