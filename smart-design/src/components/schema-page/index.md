---
toc: content
group:
  title: SchemaPage 高级页面组件
  order: 9
---

# SchemaPage 高级页面组件

SchemaPage 是一个基于 JSON schema 配置的高级页面生成组件，能够根据配置渲染复杂的表单和页面布局。

## 特性

- ✨ **声明式配置**：通过 JSON schema 配置页面结构，无需编写大量 JSX
- 🚀 **高性能**：采用 React.memo 和 useMemo 等优化手段，确保高效渲染
- 🛡️ **类型安全**：完善的 TypeScript 类型定义，提供更好的开发体验
- 🧩 **丰富的组件**：支持多种组件类型，包括表单组件、布局组件、展示组件等
- 🔗 **依赖关系**：支持字段间的依赖关系配置，实现条件渲染和联动效果
- 🔄 **事件处理**：强大的事件处理系统，支持多种事件类型
- 🐞 **错误处理**：内置错误边界，防止渲染错误导致整个应用崩溃
- 📊 **性能监控**：开发环境中自动启用性能监控，帮助发现性能问题

## 优化亮点

最新版本的 SchemaPage 进行了多项性能和结构优化：

1. **代码分离**：将复杂功能拆分为独立的函数和模块，提高可维护性
2. **记忆化处理**：使用 React.memo 和 useMemo 减少不必要的重新渲染
3. **错误边界**：增加错误边界捕获渲染过程中的错误，防止应用崩溃
4. **性能监控**：开发环境中自动监控渲染性能，及时发现性能问题
5. **类型增强**：完善的 TypeScript 类型定义，提供更好的开发体验
6. **自定义 Hook**：封装常用逻辑为自定义 Hook，方便在组件间复用

## API

### SchemaPage Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| field | 字段名，用于事件通信 | `string` | - |
| columns | 页面列配置 | `SchemaColumn[]` | `[]` |
| initialValues | 初始值 | `Record<string, any>` | `{}` |
| variables | 环境变量 | `Record<string, any>` | `{}` |
| forceUpdate | 是否强制更新 | `boolean` | `false` |
| onRetry | 重试回调 | `() => void` | - |
| onReset | 重置回调 | `() => void` | - |
| onBack | 返回回调 | `() => void` | - |
| mock | 模拟数据 | `Record<string, any>` | - |

此外，SchemaPage 支持 antd Form 组件的所有属性。

## 更多示例

请查看 [docs/guide/schema-page](../../guide/schema-page.md) 获取更多示例和高级用法。

## 示例 Page

<code id='page-basic' src="./demos/page/basic.tsx">基础组件类型</code>
<!-- <code id='crud-table' src="./demos/page/crud-table.tsx">CrudTable</code> -->
<!-- <code id='form-basic' src="./demos/form/basic.tsx">基础 Form 类型</code> -->
<!-- <code id='steps-basic' src="./demos/steps/basic.tsx">基础 Steps</code> -->
<!-- <code id='crud-table' src="./demos/ai/attachment.tsx">AI 组件</code> -->

## API

属性说明如下：

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| forceUpdate | 强制刷新 | `boolean` | `--` | |
| initialValues | 默认值，只有初始化以及重置时生效 | `object` | `--` | |
| columns | 配置描述，具体项见下表 | `ColumnsType[]` | `--` | |
| size |	设置字段组件的尺寸（仅限 antd 组件） | `small` \| `middle` \| `large	` | `large` | |
| layout | 表单布局	 | `horizontal` \| `vertical` \| `inline` | `horizontal` | |
| onFinish | 提交表单且数据验证成功后回调事件		| `function(values)	` | `--` | |

支持原生 [antd form](https://ant-design.antgroup.com/components/form-cn) 的所有属性。

## Steps

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| className | 类名 | `string` | `--` |  |
| style | `style` 样式 | `CSSProperties` | `--` | |
| items | 全局变量 | `itemType[]` | `--` | |
| forceRender | 被隐藏时是否渲染 DOM 结构	 | `boolean` | `false` | |
| destroyInactiveTabPane | 被隐藏时是否销毁 DOM 结构 | `boolean` | `false` | |
| form | 参数 from 配置	| `Form` | `--` | |
| onFinish | 提交表单且数据验证成功后回调事件		| `function(values)	` | `--` | |

### ItemType

| 属性 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| key | 唯一标识 | `string` | `--` |  |
| forceRender | 被隐藏时是否渲染 DOM 结构	 | `boolean` | `false` | |
| destroyInactiveTabPane | 被隐藏时是否销毁 DOM 结构 | `boolean` | `false` | |
| form | 参数 from 配置	| `Form` | `--` | |

