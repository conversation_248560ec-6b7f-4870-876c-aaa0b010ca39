.deer-field-confirm-modal {
  .deer-form-item-title {
    color: rgba(0, 0, 0, 0.88);
  }
}

.deer-field-group-item {
  &-title {
    font-size: 12px;
    line-height: 12px;
    color: #757575;
  }

  &-content {
    margin-top: 10px;
  }
}


.x-page-pro-field-container {
  background-color: #fff;
  border-radius: 12px;
  padding: 12px;
}

.x-page-pro-field-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ddd;
  background: white;

  &-internal {
    width: 100%;
    white-space: normal;
    word-break: break-all;
    font-size: 13px;

    td {
      padding: 12px;
      width: 200px;
      border-right: 1px solid rgba(0, 0, 0, 0.06);

      &:last-child {
        border-right: 0 none;
      }
    }

    th {
      padding: 12px;
      position: relative;
    }

    tr {
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);

      &:last-child {
        border-bottom: 0 none;
      }
    }


    .important {
      font-weight: bolder;
      text-align: left;
      background: #f0f0f0;
    }

    .highlight {
      font-weight: 700;
      font-size: 14px;
      text-align: left;
      background: #bdbbbb;
    }

    .bolder {
      font-weight: bolder;
    }
  }
}

.x-page-pro-field-item {
  &-vertical {
    &-label {
      font-size: 12px;
      line-height: 12px;
      color: #757575;
    }

    &-value {
      font-size: 14px;
      line-height: 18px;
      margin-top: 10px;
      color: #474747;
      white-space: normal;
      /* 确保空白字符正常处理 */
      overflow-wrap: break-word;
      /* 允许长单词或URL断行 */
      word-break: break-word;
      /* 对于非CJK文本，确保长单词正确断行 */
    }

    &-emoji {
      margin-right: 4px;
      display: inline-block;
      font-size: 20px;
      vertical-align: middle;
    }
  }

  &-horizontal {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-label {
      font-size: 14px;
      line-height: 1px;
      color: #323233;
    }

    &-emoji {
      margin-right: 4px;
      display: inline-block;
      font-size: 20px;
      vertical-align: middle;
    }

    &-value {
      font-size: 14px;
      line-height: 14px;
      color: #969799;
      white-space: normal;
      /* 确保空白字符正常处理 */
      overflow-wrap: break-word;
      /* 允许长单词或URL断行 */
      word-break: break-word;
      /* 对于非CJK文本，确保长单词正确断行 */
    }
  }
}

.x-page-pro-field-files {
  &-vertical {
    &-label {
      font-size: 12px;
      line-height: 12px;
      color: #757575;
    }

    &-value {
      margin-top: 10px;
      font-size: 14px;
      color: #474747;
    }

    &-item {
      color: #fb923c;
      cursor: pointer;
      transition: color 0.3s;
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        // color: #fb923c;
        opacity: 0.75;
      }
    }
  }

  &-horizontal {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-label {
      font-size: 14px;
      line-height: 1px;
      color: #323233;
    }

    &-value {
      font-size: 14px;
      line-height: 14px;
      color: #969799;
    }

    &-content {
      display: none;
    }
  }
}

.x-attachment {

  .ant-upload-wrapper,
  .ant-upload,
  .ant-upload,
  .ant-btn {
    width: 100% !important;
  }
}


.x-page-pro-field-label {
  position: relative;
  display: inline-flex;
  align-items: center;
  max-width: 100%;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
}