import React from 'react';
import FieldComponent from '../components';
import isDeepEqualReact from '../../../utils/isDeepEqualReact';
import type { SchemaField } from '../typing'
import { CreateField } from '../hoc/with-create-field';
import { SchemaPageContext } from '../context';

export type BaseFieldProps<
  T = any,
  FiledProps = Record<string, any>,
> = Pick<SchemaField, 'scope' | 'field' | 'type' | 'fieldProps' | 'labelProps' | 'events'> & {
  renderChildren: () => React.ReactNode;
  fieldProps: FiledProps;
} & T


const BaseField: React.FC<BaseFieldProps> = (props) => {
  const {
    type,
    field,
    fieldProps,
    events = {},
    renderChildren,
    value,
    responseLoading,
    responseData,
  } = props;
  // 缓存 renderChildren 的结果，避免重复渲染
  const children = React.useMemo(() => renderChildren(), [renderChildren]);

  const { components } = React.useContext(SchemaPageContext);

  const component = components?.find(item => item.type === type);

  if (component) {
    return component.render(props);
  }

  return (
    <FieldComponent
      type={type}
      field={field}
      fieldProps={fieldProps}
      events={events}
      value={value}
      responseLoading={responseLoading}
      responseData={responseData}
    >
      {children}
    </FieldComponent>
  )
};

const Field = CreateField<BaseFieldProps>(
  React.memo(BaseField, (prevProps, nextProps) => {
    return isDeepEqualReact(nextProps, prevProps, ['onChange', 'onBlur']);
  })
) as <FiledProps, DataType = Record<string, any>>(
  props: BaseFieldProps<DataType, FiledProps>,
) => React.ReactElement;

export default Field;
