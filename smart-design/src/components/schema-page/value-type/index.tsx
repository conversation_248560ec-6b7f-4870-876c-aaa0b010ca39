import type { SchemaField, RenderValueTypeHelpers } from '../typing';
import fieldRenderer from './field';

/**
 * 值类型渲染器
 * 
 * 根据 Schema 配置的类型，将 JSON 配置转换为相应的 React 组件
 * 
 * @param item Schema 字段配置
 * @param helpers 渲染辅助函数和数据
 * @returns 渲染后的 React 组件
 */

const renderValueType = (
  item: SchemaField,
  helpers: RenderValueTypeHelpers,
): React.ReactNode => {
  // 使用 field 处理器渲染所有类型的组件
  return fieldRenderer(item, helpers);
};

export default renderValueType
