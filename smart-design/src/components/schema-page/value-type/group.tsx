import React from 'react';
import ProGroup from '../group';
import type { SchemaRenderValueTypeFunction } from '../typing';
import { replaceKeysWithRegex } from '../../../utils/replaceKeysWithRegex'

const Group: SchemaRenderValueTypeFunction = (
  item,
  { generateFields, index }
) => {

  if (item.type === 'group') {

    if (!Array.isArray(item.children)) return null;

    const {
      children,
      fieldProps
    } = item

    const {
      dataSource,
      separator,
      empty,
      ...restProps
    } = fieldProps

    if (!dataSource) {
      return empty ? <div dangerouslySetInnerHTML={{ __html: empty }} /> : '-'
    }

    let list: any[] = [];

    try {
      list = JSON.parse(dataSource as string);
    } catch (err) {
      console.error(`[smartdeer-ui: Group] dataSource is not a json string`);
    }

    return (
      <ProGroup
        key={index}
        {...restProps}
      >
        {list.map((row: any, index: number) => {
          const columns = replaceKeysWithRegex(children, { index: index + 1, ...row }) as any[];

          return (
            <React.Fragment key={index} >
              {generateFields(columns)}
              {index !== list.length - 1 && separator}
            </React.Fragment>
          );
        })}
      </ProGroup >
    );
  }

  return true;
};

export default Group;
