import React from 'react';
import { Alert } from 'antd';
import ProModal from '../modal';
import type { SchemaRenderValueTypeFunction } from '../typing';
import { isObject } from 'lodash';

const Drawer: SchemaRenderValueTypeFunction = (item, helpers) => {

  if (item.type === 'modal') {

    const { index, generateFields } = helpers

    if (!isObject(item.children) && !Array.isArray(item.children)) return null;

    if (!item.field) {
      return <Alert message='Modal 组件必须配置 field 属性' type='error' />
    }

    return (
      <React.Fragment key={index}>
        <ProModal {...item} generateFields={generateFields} />
      </React.Fragment>
    );
  }

  return true;
};

export default Drawer;
