import React from 'react';
import type { SchemaField, SchemaColumn, SchemaRenderValueTypeFunction, RenderValueTypeHelpers } from '../typing';
import Field from '../field'
import { isString, isArray, isObject, isEmpty } from 'lodash';
import Dependencies from '../dependencies'
import { omit } from '../../../utils'
import { evaluateLogicalExpression } from '../../../utils/evaluateLogicalExpression'

// 引入特殊的值类型组件处理器
import group from './group'
import effect from './effect'
import drawer from './drawer'
import modal from './modal'

/**
 * 特殊的值类型处理器集合
 * 这些处理器优先于普通 Field 组件处理特殊类型
 */
const specialTypeHandlers: SchemaRenderValueTypeFunction[] = [
  group,
  effect,
  drawer,
  modal
];

/**
 * 渲染子组件
 * 
 * @param columns 列配置，可以是字符串、对象数组或单个对象
 * @param generateFields 字段生成函数
 * @returns 生成的子组件或字符串
 */
const renderChildren = (
  columns: SchemaColumn['children'],
  generateFields: RenderValueTypeHelpers['generateFields']
): React.ReactNode => {
  if (isArray(columns)) return generateFields(columns);
  if (isObject(columns)) return generateFields([columns]);
  if (isString(columns)) return columns;
  return '';
};

/**
 * 检查条件表达式是否允许显示字段
 * 
 * @param item 字段配置
 * @param variables 变量列表
 * @param dependenciesValues 依赖值
 * @returns 是否允许显示
 */
const checkVisibilityCondition = (
  item: SchemaField,
  variables: Record<string, any>,
  dependenciesValues: Record<string, any> = {}
): boolean => {
  const { effect = {} } = item;
  const { show } = effect;

  if (!show) return true;

  if (isEmpty(dependenciesValues) && isEmpty(variables)) return true;

  const data = { ...variables, ...dependenciesValues };
  return evaluateLogicalExpression(data, show);
};

/**
 * 字段渲染函数
 * 根据字段配置生成对应的表单组件
 * 
 * @param item 字段配置对象
 * @param helpers 渲染辅助函数和数据
 * @returns 渲染的字段组件
 */
const fieldRenderer = (
  item: SchemaField,
  helpers: RenderValueTypeHelpers,
): React.ReactNode => {
  const { index, generateFields, variables } = helpers;

  const fieldProps = {
    ...omit(item, [
      'dependencies',
      'children'
    ]),
  };

  /**
   * 获取表单字段
   * 
   * @param dependenciesValues 依赖值对象，默认为空对象
   * @returns 表单字段组件或无效组件占位符
   */
  const getField = (dependenciesValues = {}): React.ReactNode => {
    // 检查显示条件
    if (!checkVisibilityCondition(item, variables, dependenciesValues)) {
      return <React.Fragment key={[item.field, index || 0].join('_')} />;
    }

    // 处理特殊类型
    for (const handler of specialTypeHandlers) {
      const result = handler(item, helpers);
      if (result !== true) return result;
    }

    // 渲染标准字段
    return (
      <Field
        {...fieldProps}
        key={[item.field, index || 0].join('_')}
        renderChildren={() => renderChildren(item.children, generateFields)}
      />
    );
  };

  // 处理依赖关系
  if (item.dependencies && Array.isArray(item.dependencies) && item.dependencies.length > 0) {
    return (
      <Dependencies
        names={item.dependencies}
        key={[item.field, helpers.index || 0].join('-')}
      >
        {getField}
      </Dependencies>
    );
  }

  return getField();
};

export default fieldRenderer;
