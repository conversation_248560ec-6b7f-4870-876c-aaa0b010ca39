import React from 'react';
import { Alert } from 'antd';
import ProDrawer from '../drawer';
import type { SchemaRenderValueTypeFunction } from '../typing';
import { isObject } from 'lodash';

const Drawer: SchemaRenderValueTypeFunction = (item, helpers) => {

  if (item.type === 'drawer') {

    const { index, generateFields } = helpers

    if (!isObject(item.children) && !Array.isArray(item.children)) return null;

    if (!item.field) {
      return <Alert message='Drawer 组件必须配置 field 属性' type='error' />
    }

    return (
      <React.Fragment key={index}>
        <ProDrawer {...item} generateFields={generateFields} />
      </React.Fragment>
    );
  }

  return true;
};

export default Drawer;
