import React from 'react';
import { Alert } from 'antd';
import ProEffect from '../effect';
import type { SchemaRenderValueTypeFunction } from '../typing';
import { isObject } from 'lodash';

const Effect: SchemaRenderValueTypeFunction = (item, helpers) => {

  if (item.type === 'effect') {

    const { index, generateFields } = helpers

    if (!isObject(item.children) && !Array.isArray(item.children)) return null;

    if (!item.field) {
      return <Alert message='effect 组件必须配置 field 属性' type='error' />
    }

    return (
      <React.Fragment key={index}>
        <ProEffect {...item} generateFields={generateFields} />
      </React.Fragment>
    );
  }

  return true;
};

export default Effect;
