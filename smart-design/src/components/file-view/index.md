---
toc: content
group: 
  title: 数据展示
  order: 9
---

# FileView 文件预览

文件预览

## 示例

<!-- <code src="./demos/index.tsx"></code> -->

## API

### Image

| 参数 | 说明 | 类型 | 默认值 | 版本 |
| --- | --- | --- | --- | --- |
| alt | 图像描述 | `string` | - |  |
| fallback | 加载失败容错地址 | `string` | - |  |
| height | 图像高度 | `string` \| `number` | - |  |
| placeholder | 加载占位，为 `true` 时使用默认占位 | `ReactNode` | - |  |
| preview | 预览参数，为 `false` 时禁用 | `boolean` \| [PreviewType](#previewtype) | `false` |  |
| src | 图片地址 | `string` | - |  |
| width | 图像宽度 | `string` \| `number` | - |  |
| onError | 加载错误回调 | `(event: Event) => void `| - |  |
