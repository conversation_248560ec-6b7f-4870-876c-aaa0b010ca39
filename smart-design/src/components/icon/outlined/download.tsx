import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const DownloadOutlined: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 14 14" fill="currentColor">
        <path d="M6.90156 9.3252C6.91326 9.34014 6.92819 9.35222 6.94525 9.36053C6.96231 9.36884 6.98103 9.37316 7 9.37316C7.01897 9.37316 7.03769 9.36884 7.05475 9.36053C7.0718 9.35222 7.08674 9.34014 7.09844 9.3252L8.84844 7.11113C8.9125 7.02988 8.85469 6.90957 8.75 6.90957H7.59219V1.62207C7.59219 1.55332 7.53594 1.49707 7.46719 1.49707H6.52969C6.46094 1.49707 6.40469 1.55332 6.40469 1.62207V6.90801H5.25C5.14531 6.90801 5.0875 7.02832 5.15156 7.10957L6.90156 9.3252ZM12.7188 8.77832H11.7812C11.7125 8.77832 11.6562 8.83457 11.6562 8.90332V11.3096H2.34375V8.90332C2.34375 8.83457 2.2875 8.77832 2.21875 8.77832H1.28125C1.2125 8.77832 1.15625 8.83457 1.15625 8.90332V11.9971C1.15625 12.2736 1.37969 12.4971 1.65625 12.4971H12.3438C12.6203 12.4971 12.8438 12.2736 12.8438 11.9971V8.90332C12.8438 8.83457 12.7875 8.77832 12.7188 8.77832Z" />
      </svg>
    </span>
  )
}

export default DownloadOutlined;
