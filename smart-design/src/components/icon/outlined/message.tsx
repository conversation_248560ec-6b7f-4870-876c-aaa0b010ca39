import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const MessageOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 18 18" fill="currentColor">
        <path d="M9.0609 4.46277C9.83052 4.56072 10.5302 4.89655 11.0829 5.44228C11.7476 6.09295 12.1114 6.95352 12.1114 7.87007V12.7956C12.1114 12.8446 12.1114 12.9005 12.1184 12.9495H5.07989C5.08689 12.9005 5.08689 12.8516 5.08689 12.7956V7.94003C5.08689 6.18391 6.40223 4.68666 8.14436 4.46277C8.30528 4.44178 8.89298 4.44178 9.0609 4.46277ZM8.59913 2C8.2563 2 7.97644 2.27286 7.97644 2.6087V3.25937C5.63261 3.56022 3.84151 5.56822 3.84151 7.94003V12.7956C3.84151 12.8726 3.81352 12.9425 3.75755 12.9915C3.70858 13.0405 3.63861 13.0685 3.56165 13.0685C3.40773 13.0685 3.2678 13.1314 3.16285 13.2294C3.05091 13.3343 2.98794 13.4883 3.00193 13.6562C3.02292 13.95 3.28179 14.1669 3.58264 14.1669H13.6366C13.7905 14.1669 13.9305 14.1039 14.0284 14.006C14.1334 13.908 14.1963 13.7681 14.1963 13.6212C14.1963 13.4673 14.1334 13.3343 14.0354 13.2364C13.9375 13.1384 13.7975 13.0755 13.6366 13.0755C13.4827 13.0755 13.3568 12.9495 13.3568 12.8026V7.87007C13.3568 6.58271 12.825 5.42129 11.9644 4.58171C11.2438 3.87506 10.2853 3.3923 9.22182 3.25937V2.6087C9.22182 2.44078 9.15185 2.28686 9.03991 2.17491C8.92797 2.06997 8.77404 2 8.59913 2ZM9.71857 15.993H7.47969C7.17184 15.993 6.91997 15.7411 6.91997 15.4333C6.91997 15.1254 7.17184 14.8736 7.47969 14.8736H9.71857C10.0264 14.8736 10.2783 15.1254 10.2783 15.4333C10.2783 15.7411 10.0264 15.993 9.71857 15.993Z" />
      </svg>
    </span>
  )
}

export default MessageOutline;
