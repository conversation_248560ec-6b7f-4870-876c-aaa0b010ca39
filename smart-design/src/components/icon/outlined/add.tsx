import React, { type FC } from 'react';
import { OutlineProps } from '../typing';

const PassOutline: FC<OutlineProps> = (props) => {
  const { className, style } = props;

  return (
    <span
      className={className}
      style={{
        ...style,
        verticalAlign: 'text-bottom',
      }}
    >
      <svg
        width="1em"
        height="1em"
        viewBox="0 0 14 14"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_3864_3394)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.60003 2.36367C7.60003 2.0323 7.3314 1.76367 7.00003 1.76367C6.66866 1.76367 6.40003 2.0323 6.40003 2.36367V6.49965H2.36377C2.08763 6.49965 1.86377 6.72351 1.86377 6.99965C1.86377 7.27579 2.08763 7.49965 2.36377 7.49965H6.40003V11.6359C6.40003 11.9673 6.66866 12.2359 7.00003 12.2359C7.3314 12.2359 7.60003 11.9673 7.60003 11.6359V7.49965H11.636C11.9122 7.49965 12.136 7.27579 12.136 6.99965C12.136 6.72351 11.9122 6.49965 11.636 6.49965H7.60003V2.36367Z"
            fill="currentColor"
          />
        </g>
        <defs>
          <clipPath id="clip0_3864_3394">
            <rect width="1em" height="1em" fill="currentColor" />
          </clipPath>
        </defs>
      </svg>
    </span>
  );
};

export default PassOutline;
