import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const LangOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg width="1em" height="1em" viewBox="0 0 18 18" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M9.35352 14.79L9.40438 14.7898C9.45634 14.7723 9.54768 14.7247 9.67833 14.5958C9.89151 14.3854 10.1385 14.0215 10.3719 13.477C10.8367 12.3924 11.1535 10.8104 11.1535 9H7.55352C7.55352 10.8104 7.87033 12.3924 8.33517 13.477C8.5685 14.0215 8.81553 14.3854 9.0287 14.5958C9.15935 14.7247 9.25069 14.7723 9.30265 14.7898L9.35352 14.79ZM7.60096 7.8C7.70575 6.49378 7.97636 5.36018 8.33517 4.52296C8.5685 3.97851 8.81553 3.61458 9.0287 3.40419C9.15935 3.27525 9.25069 3.22773 9.30266 3.21022L9.35352 3.21L9.40438 3.21022C9.45634 3.22773 9.54768 3.27525 9.67833 3.40419C9.89151 3.61458 10.1385 3.97851 10.3719 4.52296C10.7307 5.36018 11.0013 6.49378 11.1061 7.8H7.60096ZM12.3535 9C12.3535 11.2211 11.9102 13.2007 11.2187 14.483C13.5013 13.7068 15.1435 11.5452 15.1435 9H12.3535ZM15.019 7.8H12.3096C12.1818 6.07324 11.7828 4.56313 11.2187 3.51698C13.1323 4.16774 14.5958 5.79218 15.019 7.8ZM6.39743 7.8H3.68803C4.11119 5.79218 5.57472 4.16775 7.48835 3.51698C6.92425 4.56313 6.52527 6.07324 6.39743 7.8ZM3.56352 9C3.56352 11.5452 5.20573 13.7068 7.48836 14.483C6.79687 13.2007 6.35352 11.2211 6.35352 9H3.56352ZM16.3535 9C16.3535 5.13401 13.2195 2 9.35352 2C5.48752 2 2.35352 5.13401 2.35352 9C2.35352 12.866 5.48752 16 9.35352 16C13.2195 16 16.3535 12.866 16.3535 9Z" />
      </svg>
    </span>
  )
}

export default LangOutline;
