import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const ClockOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" version="1.1" width="1em" height="1em" viewBox="0 0 14 14">
        <defs><clipPath id="master_svg0_334_6071"><rect x="0" y="0" width="14" height="14" rx="0" /></clipPath></defs><g clipPath="url(#master_svg0_334_6071)"><g><path d="M12.4831,7C12.4831,10.0282,10.0282,12.4831,7,12.4831C3.97177,12.4831,1.5169,10.0282,1.5169,7C1.5169,3.97177,3.97177,1.5169,7,1.5169C10.0282,1.5169,12.4831,3.97177,12.4831,7ZM7,14C10.8661,14,14,10.8661,14,7C14,3.1339,10.8661,0,7,0C3.1339,0,0,3.1339,0,7C0,10.8661,3.1339,14,7,14ZM7.7581,4.0831C7.7581,3.66441,7.41869,3.325,7,3.325C6.58131,3.325,6.2419,3.66441,6.2419,4.0831L6.2419,7C6.2419,7.4186,6.5814,7.7581,7,7.7581L9.9169,7.7581C10.3356,7.7581,10.675,7.41869,10.675,7C10.675,6.58131,10.3356,6.2419,9.9169,6.2419L7.7581,6.2419L7.7581,4.0831Z" fillOpacity="1" /></g></g>
      </svg>
    </span>
  )
}

export default ClockOutline;
