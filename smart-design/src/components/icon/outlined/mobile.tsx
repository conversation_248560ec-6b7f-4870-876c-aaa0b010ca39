import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const MobileOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 12 16" fill="currentColor">
        <path fillRule="evenodd" clipRule="evenodd" d="M0.105469 1.68421C0.105469 0.754047 0.859515 0 1.78968 0H10.2107C11.1409 0 11.8949 0.754047 11.8949 1.68421V14.3158C11.8949 15.246 11.1409 16 10.2107 16H1.78968C0.859518 16 0.105469 15.246 0.105469 14.3158V1.68421ZM1.79004 2.10514C1.79004 1.87259 1.97855 1.68408 2.21109 1.68408H9.79004C10.0226 1.68408 10.2111 1.87259 10.2111 2.10513V10.5262C10.2111 10.7587 10.0226 10.9472 9.79004 10.9472H2.21109C1.97855 10.9472 1.79004 10.7587 1.79004 10.5262V2.10514ZM6.00031 12.6318C5.53523 12.6318 5.1582 13.0089 5.1582 13.4739C5.1582 13.939 5.53523 14.316 6.00031 14.316C6.46539 14.316 6.84241 13.939 6.84241 13.4739C6.84241 13.0089 6.46539 12.6318 6.00031 12.6318Z" />
      </svg>
    </span>
  )
}

export default MobileOutline;
