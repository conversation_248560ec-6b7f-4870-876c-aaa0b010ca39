import React, { type FC } from 'react';
import { OutlineProps } from '../typing'

const RejectOutline: FC<OutlineProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 14 14" fill="currentColor">
      <path
      d="M7.8095 6.99927L11.9111 2.11021C11.9798 2.02896 11.922 1.90552 11.8158 1.90552H10.5689C10.4954 1.90552 10.4251 1.93833 10.3767 1.99458L6.99388 6.02739L3.61106 1.99458C3.56419 1.93833 3.49388 1.90552 3.41888 1.90552H2.172C2.06575 1.90552 2.00794 2.02896 2.07669 2.11021L6.17825 6.99927L2.07669 11.8883C2.06129 11.9064 2.05141 11.9286 2.04822 11.9521C2.04503 11.9757 2.04867 11.9997 2.05871 12.0212C2.06874 12.0428 2.08475 12.061 2.10483 12.0737C2.12492 12.0865 2.14823 12.0931 2.172 12.093H3.41888C3.49231 12.093 3.56263 12.0602 3.61106 12.004L6.99388 7.97114L10.3767 12.004C10.4236 12.0602 10.4939 12.093 10.5689 12.093H11.8158C11.922 12.093 11.9798 11.9696 11.9111 11.8883L7.8095 6.99927Z"
    />
      </svg>
    </span>
  )
}

export default RejectOutline;
