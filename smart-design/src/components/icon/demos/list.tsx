import React from 'react'
import { Icon } from 'smart-design';

type IconItem = {
  name: string
  component: React.ComponentType<{ style?: React.CSSProperties }>
}

const items: IconItem[] = [];
for (let key in Icon) {
  if (Icon.hasOwnProperty(key)) {
    const component = (Icon as any)[key] as React.ComponentType;

    console.log(key);

    items.push({
      name: key,
      component,
    });
  }
}

export default () => {

  return (
    <div className={`flex flex-wrap`}>
      {items.map(item => {
        return (
          <div key={item.name} className={`w-[120px] h-[120px] flex items-center justify-center`}>
            <div>
              <div className={`flex justify-center text-[40px]`}>
                <item.component style={{ fontSize: '40px', color: '#555' }} />
              </div>
              <div className={`text-[12px] mt-[20px] text-[#55]`}> {item.name} </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
