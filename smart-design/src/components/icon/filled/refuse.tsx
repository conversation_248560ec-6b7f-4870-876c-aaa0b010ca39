import React, { type FC } from 'react';
import { FilledProps } from '../typing'

const RefuseFilled: FC<FilledProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" fill="#fff" version="1.1" width="1em" height="1em" viewBox="0 0 14 14">
        <g><g><ellipse cx="7" cy="7" rx="7" ry="7" fill="currentColor" fillOpacity="1" /></g><g><path d="M6.99865,6.00882L5.19499,4.205166L5.19493,4.205099Q5.09647,4.106642,4.96783,4.0533576Q4.83919,4.0000732541,4.699951,4.0000732541Q4.631007,4.0000732541,4.563388,4.0135236Q4.495769,4.0269739,4.432073,4.0533576Q4.368377,4.0797413,4.311052,4.118045Q4.253727,4.156348,4.204976,4.205098Q4.156226,4.253849,4.117922,4.311174Q4.0796192,4.368499,4.0532355,4.432195Q4.0268518,4.495891,4.0134015,4.56351Q3.9999511838,4.631129,3.9999511838,4.700073Q3.9999511838,4.839312,4.0532355,4.967952Q4.10652,5.09659,4.204976,5.19505L6.0087,6.99877L4.205267,8.802209999999999L4.204976,8.8025Q4.10652,8.90095,4.0532355,9.029589999999999Q3.9999511838,9.15823,3.9999511838,9.29747Q3.9999511838,9.36642,4.0134015,9.43404Q4.0268518,9.50165,4.0532355,9.565349999999999Q4.0796192,9.62905,4.117922,9.68637Q4.156226,9.7437,4.204976,9.792449999999999Q4.253727,9.8412,4.311052,9.8795Q4.368377,9.9178,4.432073,9.944189999999999Q4.495769,9.97057,4.563388,9.984020000000001Q4.631007,9.99747,4.699951,9.99747Q4.83919,9.99747,4.96783,9.944189999999999Q5.09647,9.8909,5.19493,9.792449999999999L5.19496,9.79242L6.99865,7.98872L8.80238,9.792449999999999Q8.90083,9.8909,9.02947,9.944189999999999Q9.15811,9.99747,9.29735,9.99747Q9.36629,9.99747,9.433910000000001,9.984020000000001Q9.501529999999999,9.97057,9.56523,9.944189999999999Q9.62892,9.9178,9.686250000000001,9.8795Q9.74357,9.8412,9.79233,9.792449999999999Q9.84107,9.7437,9.879380000000001,9.68637Q9.91768,9.62905,9.94407,9.565349999999999Q9.97045,9.50165,9.9839,9.43404Q9.99735,9.36642,9.99735,9.29747Q9.99735,9.15823,9.94407,9.029589999999999Q9.89078,8.90095,9.79233,8.8025L7.9886,6.99877L9.792290000000001,5.19508L9.79233,5.19505Q9.89078,5.09659,9.94407,4.967952Q9.99735,4.839312,9.99735,4.700073Q9.99735,4.631129,9.9839,4.56351Q9.97045,4.495891,9.94407,4.432195Q9.91768,4.368499,9.879380000000001,4.311174Q9.84107,4.253849,9.79232,4.205098Q9.74357,4.156348,9.686250000000001,4.118045Q9.62892,4.0797413,9.56523,4.0533576Q9.501529999999999,4.0269739,9.433910000000001,4.0135236Q9.36629,4.0000732541,9.29735,4.0000732541Q9.15811,4.0000732541,9.02947,4.0533576Q8.90083,4.106642,8.80238,4.205098L8.80209,4.205389L6.99865,6.00882Z" fillRule="evenodd" fillOpacity="1" /></g></g>
      </svg>
    </span>
  )
}

export default RefuseFilled;
