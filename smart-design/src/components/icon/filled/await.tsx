import React, { type FC } from 'react';
import { FilledProps } from '../typing'

const AwaitFilled: FC<FilledProps> = (props) => {
  const {
    className,
    style
  } = props

  return (
    <span className={className} style={{ ...style }}>
      <svg xmlns="http://www.w3.org/2000/svg" fill="#fff" version="1.1" width="1em" height="1em" viewBox="0 0 14 14">
        <g><g>
          <ellipse cx="7" cy="7" rx="7" ry="7" fill="currentColor" fillOpacity="1" /></g><g><path d="M5,7Q5,7.09849,4.98079,7.19509Q4.96157,7.29169,4.9238800000000005,7.38268Q4.88619,7.47368,4.8314699999999995,7.5555699999999995Q4.77675,7.63746,4.70711,7.70711Q4.63746,7.77675,4.5555699999999995,7.8314699999999995Q4.47368,7.88619,4.38268,7.9238800000000005Q4.29169,7.96157,4.19509,7.98079Q4.09849,8,4,8Q3.901509,8,3.80491,7.98079Q3.708311,7.96157,3.6173159999999998,7.9238800000000005Q3.526322,7.88619,3.44443,7.8314699999999995Q3.362537,7.77675,3.292893,7.70711Q3.223249,7.63746,3.16853,7.5555699999999995Q3.113811,7.47368,3.0761205,7.38268Q3.0384294,7.29169,3.0192147,7.19509Q3,7.09849,3,7Q3,6.901509,3.0192147,6.80491Q3.0384294,6.708311,3.0761205,6.617316Q3.113811,6.526322,3.16853,6.44443Q3.223249,6.362537,3.292893,6.292893Q3.362537,6.223249,3.44443,6.16853Q3.526322,6.113811,3.6173159999999998,6.0761205Q3.708311,6.0384294,3.80491,6.0192147Q3.901509,6,4,6Q4.09849,6,4.19509,6.0192147Q4.29169,6.0384294,4.38268,6.0761205Q4.47368,6.113811,4.5555699999999995,6.16853Q4.63746,6.223249,4.70711,6.292893Q4.77675,6.362537,4.8314699999999995,6.44443Q4.88619,6.526322,4.9238800000000005,6.617316Q4.96157,6.708311,4.98079,6.80491Q5,6.901509,5,7ZM8,7Q8,7.09849,7.98078,7.19509Q7.96157,7.29169,7.92388,7.38268Q7.88619,7.47368,7.83147,7.5555699999999995Q7.77675,7.63746,7.70711,7.70711Q7.63746,7.77675,7.55557,7.8314699999999995Q7.47368,7.88619,7.38268,7.9238800000000005Q7.29169,7.96157,7.19509,7.98079Q7.09849,8,7,8Q6.90151,8,6.80491,7.98079Q6.70831,7.96157,6.617319999999999,7.9238800000000005Q6.52632,7.88619,6.4444300000000005,7.8314699999999995Q6.36254,7.77675,6.29289,7.70711Q6.22325,7.63746,6.1685300000000005,7.5555699999999995Q6.11381,7.47368,6.0761199999999995,7.38268Q6.03843,7.29169,6.01921,7.19509Q6,7.09849,6,7Q6,6.901509,6.01921,6.80491Q6.03843,6.708311,6.0761199999999995,6.617316Q6.11381,6.526322,6.1685300000000005,6.44443Q6.22325,6.362537,6.29289,6.292893Q6.36254,6.223249,6.4444300000000005,6.16853Q6.52632,6.113811,6.617319999999999,6.0761205Q6.70831,6.0384294,6.80491,6.0192147Q6.90151,6,7,6Q7.09849,6,7.19509,6.0192147Q7.29169,6.0384294,7.38268,6.0761205Q7.47368,6.113811,7.55557,6.16853Q7.63746,6.223249,7.70711,6.292893Q7.77675,6.362537,7.83147,6.44443Q7.88619,6.526322,7.92388,6.617316Q7.96157,6.708311,7.98078,6.80491Q8,6.901509,8,7ZM11,7Q11,7.09849,10.98078,7.19509Q10.96157,7.29169,10.92388,7.38268Q10.88619,7.47368,10.83147,7.5555699999999995Q10.77675,7.63746,10.70711,7.70711Q10.63746,7.77675,10.55557,7.8314699999999995Q10.47368,7.88619,10.38268,7.9238800000000005Q10.29169,7.96157,10.19509,7.98079Q10.09849,8,10,8Q9.90151,8,9.80491,7.98079Q9.70831,7.96157,9.61732,7.9238800000000005Q9.52632,7.88619,9.44443,7.8314699999999995Q9.36254,7.77675,9.29289,7.70711Q9.22325,7.63746,9.16853,7.5555699999999995Q9.11381,7.47368,9.07612,7.38268Q9.03843,7.29169,9.019210000000001,7.19509Q9,7.09849,9,7Q9,6.901509,9.019210000000001,6.80491Q9.03843,6.708311,9.07612,6.617316Q9.11381,6.526322,9.16853,6.44443Q9.22325,6.362537,9.29289,6.292893Q9.36254,6.223249,9.44443,6.16853Q9.52632,6.113811,9.61732,6.0761205Q9.70831,6.0384294,9.80491,6.0192147Q9.90151,6,10,6Q10.09849,6,10.19509,6.0192147Q10.29169,6.0384294,10.38268,6.0761205Q10.47368,6.113811,10.55557,6.16853Q10.63746,6.223249,10.70711,6.292893Q10.77675,6.362537,10.83147,6.44443Q10.88619,6.526322,10.92388,6.617316Q10.96157,6.708311,10.98078,6.80491Q11,6.901509,11,7Z" fill="#FFFFFF" fillOpacity="1" /></g></g>
      </svg>
    </span>
  )
}

export default AwaitFilled;
