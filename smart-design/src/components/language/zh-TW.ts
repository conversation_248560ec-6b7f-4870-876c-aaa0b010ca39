import type { LanguageType } from '.';

const languageValues: LanguageType = {
  locale: 'zh-TW',

  common: {
    day: '天',
  },

  message: {
    success: '操作成功',
    error: '操作失敗',
  },

  form: {
    submitText: '提交',
    resetText: '重置',
    deleteText: '删除',
    addText: '添加',

    popconfirm: {
      title: '確定要刪除嗎？',
      okText: '删除',
      cancelText: '取消',
    },

    defaultItemLabel: {
      startTime: '開始時間',
      endTime: '結束時間',
    },

    defaultValidateMessages: {
      upload: {
        default: '請上傳文件！',
      },
      hashkeyGsLeave: {
        blockLeave: '強制性休假期需要最少5個工作天的連續休假。',
      },
    },
  },

  approvalTimeline: {
    action: {
      approving: '待審批',
      sending: '提交',
      approved: '審批已通過',
      rejected: '已拒絕審批',
      abolished: '已取消',
      approvalFailed: '審批失敗',
    },
  },

  approvalProcess: {
    cc: '抄送',
    people: '人',
  },

  auditTimeline: {
    process: {
      submit: '提交',
      approve: '審批',
      cc: '抄送',
      end: '結束',
      cancel: '取消',
    },
    status: {
      submit: '已提交',
      pending: '待審批',
      approved: '已通過',
      rejected: '已拒絕',
      cancelled: '已取消',
      ended: '已結束',
      notStarted: '未開始',
    },
    title: {
      system: '系統',
    },
    description: {
      autoCC: '自動抄送 {names} {num}人',
    },
    reasonForRejection: '駁回原因：',
  },

  modal: {
    okText: '確 定',
    cancelText: '取 消',
  },

  jsonEditor: {
    fullScreen: '全屏',
    import: '導入',
    export: '導出',
    clear: '清空',
    retract: '收起',
  },

  codeEditor: {
    programmingLanguage: '編程語言',
  },

  dynamicForm: {
    messageCreateSuccess: '創建成功',
    messageUpdateSuccess: '更新成功',
  },

  tableFilter: {
    filterCriteria: '篩選條件：',
    addCriteria: '添加條件 +',
    actions: {
      eq: '等于',
      like: '包含',
      gt: '大于',
      lt: '小于',
    },
  },

  TableSearch: {
    actions: {
      clear: '清空',
      search: '搜索',
      downloadExcel: '下載Excel',
      expand: '展開',
      collapse: '收起',
      download: {
        executing: '文件生成中... 請不要關閉頁面',
        downloading: '正在下載中... 請不要關閉頁面',
        downloadingTip: '提前關閉頁面將會造成下載失敗',
        gotIt: '我知道了',
        successfully: '下載成功',
        failed: '下載失敗',
        generated: '文件已生成，請下載！',
        downloadNow: '立即下載',
      },
    },
  },

  dynamicTable: {
    newButtonText: '新增',
  },

  button: {
    defaultValidateMessages: {
      fileTypeError: '文件類型錯誤',
      jsonIsNotDefined: 'JSON 未定義',
      jsonTypeError: 'JSON 類型錯誤',
    },
    exportJson: {
      fileName: '文件名',
    },
    sendCode: {
      sendCode: '發送驗證碼',
      sending: '發送中',
      seconds: '秒',
    },
  },

  commentEditor: {
    submit: '提交',
  },

  upload: {
    defaultValidateMessages: {
      fileTypeError: '文件類型錯誤',
      imageUploadFailed: '圖片上傳失敗',
      cannotExceed3: '一次上傳圖片數量不能超過3個',
      isNotSupported: '不支持此檔案類型！',
      fileSizeError: '檔案大小必須小於 {size}MB。',
    },
    imageUploading: '圖片上傳中',
    onClickText: '點擊上傳',
  },

  quill: {
    toolbar: {
      size: '字號',
      h1: '標題 h1',
      h2: '標題 h2',
      h3: '標題 h3',
      h4: '標題 h4',
      h5: '標題 h5',
      align: '對齊方式',
      direction: '文本方向',
      bold: '加粗',
      italic: '斜體',
      underline: '下劃線',
      strike: '刪除線',
      color: '顏色',
      background: '背景顏色',
      sub: '上標',
      super: '下標',
      blockquote: '引用',
      formula: '公式',
      codeBlock: '代碼塊',
      ordered: '有序列表',
      bullet: '無序列表',
      increaseIndent: '增加縮進',
      decreaseIndent: '減少縮進',
      table: '表格',
      link: '添加鏈接',
      image: '插入圖片',
      clean: '清除字體樣式',
    },

    fileExpire: {
      tip: '為了安全和隱私保密起見，所有的圖片和文件都需要設置過期時間。',
      placeholder: '文件過期時間',
      day10: '10天',
      day30: '30天',
      day60: '60天',
      day90: '90天',
      day180: '180天',
      day360: '360天',
    },
  },

  table: {
    details: '詳情',
  },

  popDownload: {
    successfully: '下載成功',
    selectLanguage: '選擇薪資單語言，默認為英文',
    setPasswordType: '設置薪資單密碼類型',
    systemGenerated: '系統生成',
    custom: '自定義',
    setPassword: '設置密碼',
    enterPassword: '請輸入密碼',
    passwordRules:
      '密碼必須包含大寫字母、小寫字母、數字和特殊字符（如 !@#$%^&*）',
    systemGeneratedPassword: '系統生成密碼',
    saveTip: '點擊“下載”按鈕後，密碼將會消失，請務必在下載前保存好。密碼為：',
    pleaseInput: '請輸入密碼',
    atLeast: '請輸入至少6位字符',
  },

  fileView: {
    download: '下載',
  },

  f2a: {
    securityVerification: '安全驗證',
    verifyTip: '為了保護資訊隱私和安全，我們需要驗證您的身分。',
    emailVerification: '透過電子郵件驗證',
    mobileVerification: '透過手機驗證',
    sendVerificationCode: '立即發送驗證碼',
    enterVerificationCode: '輸入驗證碼',
    codeTip: '安全校驗碼已傳送至{userEmail}，驗證碼5分鐘內有效',
    incorrectCode: '驗證碼錯誤，請重試',
    enterCode: '請輸入驗證碼',
    sendCode: '發送驗證碼',
    seconds: '秒',
    previousStep: '上一步',
    verificationSuccessful: '安全驗證成功！',
    timeLimitTip: '您已取得 {timeStr}的免驗證權限，請在時限內完成您的操作',
    gotIt: '我知道了',
    confirm: '確認',
    mobile: '手機',
    email: '電子郵件',
    minutes: '分鐘',
    sending: '發送中',
  },

  businessComponent: {
    securityCheckModal: {
      securityVerification: '安全驗證',
      verifyTip: '為了保護資訊隱私和安全，我們需要驗證您的身分。',
      emailVerification: '透過電子郵件驗證',
      mobileVerification: '透過手機驗證',
      sendVerificationCode: '立即發送驗證碼',
      enterVerificationCode: '輸入驗證碼',
      codeTip: '安全校驗碼已傳送至{userEmail}，驗證碼5分鐘內有效',
      incorrectCode: '驗證碼錯誤，請重試',
      enterCode: '請輸入驗證碼',
      sendCode: '發送驗證碼',
      seconds: '秒',
      previousStep: '上一步',
      verificationSuccessful: '安全驗證成功！',
      timeLimitTip: '您已取得 {timeStr}的免驗證權限，請在時限內完成您的操作',
      gotIt: '我知道了',
      confirm: '確認',
      mobile: '手機',
      email: '電子郵件',
      minutes: '分鐘',
      sending: '發送中',
    },
  },

  error: {
    title: '系統提示：數據加載失敗',
    description:
      '抱歉，數據加載出現錯誤，可能是網絡問題或系統異常。請嘗試刷新頁面後重新操作。如果問題仍未解決，請聯系我們的客服團隊，我們將盡快為您提供幫助！',
    button: '刷新頁面',
  },
};

export default languageValues;
