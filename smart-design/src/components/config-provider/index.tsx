import React from 'react';
import { mergeProps } from '../../utils/withDefaultProps'
import { ConfigContext } from './context';
import type { ConfigContextProps, LocaleType, TimezoneType, EncryptType } from './context';
import S from '../../utils/storage'
import { STORE } from '../../consts'
import LanguageDict from '../language'
import { useFileAccessToken } from '../../utils/hooks'
import { clearFetchPromisesCache } from '../../utils/swrFetcher';
import { event, LANG_CHANGE_EVENT, RESET_STORAGE_EVENT, SECURITY_CHECK_EVENT, ENTITY_ID_CHANGE_EVENT, LOGIN_EVENT, setModule2FA, SAGE_ENTITY_AUTH, TO_LOGIN } from './utils';
import pageEventBus from '../../bus/page-event-hub';

export {
  ConfigContext,
  type ConfigContextProps,
  type LocaleType,
  type TimezoneType,
  type EncryptType,
};

export type ConfigProviderProps = {
  children?: React.ReactNode;
  toLogin?: () => void;
} & Omit<ConfigContextProps, 'language'>

const defaultProps = {
  locale: 'zh-CN',
  isRequesId: false,
  isEncrypt: false,
  encryptType: 'BASE64',
  rsaPublicKey: '',
  rsaPrivateKey: '',
  isFileToken: false
}
const ConfigProvider: React.FC<ConfigProviderProps> = (p) => {
  const props = mergeProps(defaultProps, p)
  const {
    prefixCls,

    locale,
    isRequesId,
    isEncrypt,
    encryptType,

    rsaPublicKey,
    rsaPrivateKey,

    timezone,

    appDefaultEntityId,

    appApiBaseHost,

    authorization,
    authorizationExpired,
    router,
    useUrlParams,
    theme,
    systemName,

    toLogin,

    api,
    dynamicPage,

    isStorageEncrypt,

    children
  } = props

  const { reset: fileAccessTokenReset } = useFileAccessToken()

  const [lang, setLang] = React.useState(locale);
  const [entityId, setEntityId] = React.useState(appDefaultEntityId);

  const handleSetStorage = () => {
    S.set(STORE.IS_REQUES_ID, isRequesId)

    S.set(STORE.IS_ENCRYPT, isEncrypt)

    S.set(STORE.ENCRYPT_TYPE, encryptType)

    S.set(STORE.RSA_PUBLIC_KEY, rsaPublicKey)
    S.set(STORE.RSA_PRIVATE_KEY, rsaPrivateKey)


    if (authorization && authorizationExpired) {
      S.setAuthToken(authorization, authorizationExpired)
    }

    if (locale) {
      S.set(STORE.LOCALE, locale, true)
    }

    if (timezone) {
      S.set(STORE.TIMEZONE, timezone, true)
    }

    if (appApiBaseHost) {
      S.set(STORE.APP_API_BASE_URL, appApiBaseHost, true)
    }
  }

  React.useEffect(() => {
    handleSetStorage()
  }, [])

  React.useEffect(() => {
    if (!router) return
    fileAccessTokenReset()

    router.listen(() => {
      pageEventBus.unsubscribeAll();

      fileAccessTokenReset()

      clearFetchPromisesCache()
    })
  }, [])

  const handleResetStorage = () => {
    handleSetStorage()
  }

  const handleLangChange = (lang: LocaleType) => {
    setLang(lang)
  }


  const handleChangeEntityId = (id: string) => {
    setEntityId(id);
  }


  const handleToLogin = () => {
    toLogin?.()
  }

  React.useEffect(() => {
    event.on(RESET_STORAGE_EVENT, handleResetStorage);
    event.on(LANG_CHANGE_EVENT, handleLangChange);
    event.on(ENTITY_ID_CHANGE_EVENT, handleChangeEntityId);

    event.on(TO_LOGIN, handleToLogin);

    return () => {
      event.off(RESET_STORAGE_EVENT, handleResetStorage);
      event.off(LANG_CHANGE_EVENT, handleLangChange);
      event.off(ENTITY_ID_CHANGE_EVENT, handleChangeEntityId);

      event.off(TO_LOGIN, handleToLogin);
    };
  }, []);

  return (
    <ConfigContext.Provider
      value={{
        prefixCls,

        isRequesId,
        isEncrypt,
        encryptType,
        rsaPublicKey,
        rsaPrivateKey,
        locale: lang,
        timezone,
        appDefaultEntityId: entityId,
        appApiBaseHost,
        authorization,

        router,
        useUrlParams,
        theme,
        language: LanguageDict[lang],
        systemName,

        api,

        dynamicPage,
      }}
    >
      {children}
    </ConfigContext.Provider>
  );
};

export default ConfigProvider;
