import React from 'react';
import ZhTW from '../language/zh-CN';

import type { LanguageType } from '../language';

export type LocaleType = 'zh-CN' | 'en-US' | 'zh-TW' | 'ja-JP';
export type TimezoneType = 'Asia/Shanghai' | 'America/New_York' | string;
export type EncryptType = 'DISABLE' | 'BASE64' | 'RSA';

type ListenType = ({
  location,
  action,
}: {
  location: Location;
  action: 'PUSH' | 'POP' | 'REPLACE';
}) => void;

export interface ConfigContextProps {
  prefixCls?: string; // 设置统一样式前缀

  isRequesId?: boolean; // url 加密
  isEncrypt?: boolean; // 加密
  encryptType?: EncryptType; // 加密类型
  rsaPublicKey?: string; // rsa 加密 key
  rsaPrivateKey?: string; // rea 解密 key

  locale?: LocaleType; // 语言
  timezone?: TimezoneType; // 时区
  appDefaultEntityId?: string; // 默认实体 id

  appApiBaseHost?: string; // api 地址

  authorization?: string; // token
  authorizationExpired?: number; // token 过期时间

  isStorageEncrypt?: boolean; // 是否加密存储

  file?: {
    isFileToken?: boolean; // 文件是否加 token
    appFileTokenUploadApi?: string; // 文件上传地址
    appFileTokenCommonUploadApi?: string; // 公共文件上传地址
    appFileTokenApi?: string; // 文件 token api 地址
    appFileTokenExpireTime?: string; // 文件 token 过期时间
    appFileDownloadApi?: string; // 私密文件下载
    appFlowTokenFileUploadApi?: string; // 流程文件上传
  }

  api: {
    appFunctionApi?: string; // 函数 api 地址
    appProcessApi?: string; // 过程 api 地址

    appRiverFunction?: string;
    appRiverProcess?: string;
    appRiverAuthFunction?: string;
    appRiverAuthProcess?: string;
    appRiverEntityFunction?: string;
    appRiverEntityProcess?: string;
  };

  dynamicPage?: {
    river?: {
      getStepwise: string;
      getStepwiseDetail: string;
      addStepwiseTask: string;
    };
  };

  router?: {
    push: (to: string, state?: any) => void;
    replace: (to: string, state?: any) => void;
    back: () => void;
    listen: (fn: ListenType) => void;
    go: (delta: number) => void;
  };
  useUrlParams: () => Record<string, string | undefined>;
  theme?: {
    // 主题
  };
  language: LanguageType;

  systemName?: 'GS' | 'SAGE'; // 系统名称
}

export const ConfigContext = React.createContext<ConfigContextProps>({
  timezone: 'Asia/Shanghai',
  useUrlParams: () => {
    return {};
  },
  language: ZhTW,
  api: {},
  isStorageEncrypt: true,
});
