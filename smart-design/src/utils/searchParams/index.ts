/**
 * 从给定的字符串或当前窗口的URL中提取查询参数，并返回一个对象，其中键为参数名，值为参数值。
 *
 * @param str 可选的字符串，默认为当前窗口的URL。
 * @returns 一个包含查询参数的对象，其中键为参数名，值为参数值。
 */
export const getSearchParams = (str?: string): Record<string, string> => {
  const url = str || window.location.href;

  const urlObj = new URL(url);
  const paramsObj: Record<string, string> = {};
  const params = urlObj.searchParams;

  for (const [key, value] of params.entries()) {
    paramsObj[key] = value;
  }

  return paramsObj;
};

/**
 * 设置 URL 查询参数
 *
 * @param key 要设置的查询参数的键
 * @param value 要设置的查询参数的值
 */
export const setSearchParams = (key: string, value: string | number) => {
  // 创建当前 URL 的副本
  const url = new URL(window.location.href);

  // 操作查询参数
  // url.searchParams.append(key, value); // 追加参数（允许重复）
  url.searchParams.set(key, value); // 替换已有参数（不重复）

  // 更新浏览器地址栏（无刷新）
  window.history.pushState({}, '', url.toString());
};
