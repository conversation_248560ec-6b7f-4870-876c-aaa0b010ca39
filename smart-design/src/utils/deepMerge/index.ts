import { isArray, isObject } from 'lodash';

type RecordType = Record<string, any>;

const seen = new WeakMap<object, boolean>();

/**
 * 深度合并两个对象
 *
 * @param target 目标对象，将被修改以包含源对象的属性
 * @param source 源对象，其属性将被合并到目标对象中
 * @returns 合并后的目标对象
 * @throws 如果任一参数不是对象，则抛出 TypeError
 */
export const deepMerge = (
  target: RecordType,
  source: RecordType,
): RecordType => {
  if (!isObject(target) || !isObject(source)) {
    throw new TypeError('两个参数都必须是对象！');
  }

  if (seen.has(target)) {
    return target;
  }

  seen.set(target, true);

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const targetValue = target[key];
      const sourceValue = source[key];

      if (isArray(sourceValue) && isArray(targetValue)) {
        target[key] = [...new Set([...targetValue, ...sourceValue])];
      } else if (isObject(sourceValue) && isObject(targetValue)) {
        target[key] = deepMerge(Object.assign({}, targetValue), sourceValue);
      } else {
        target[key] = source[key];
      }
    }
  }

  seen.delete(target);

  return target;
};

/**
 * 深拷贝合并两个不可变对象，并返回一个新的对象。
 *
 * @param target 目标对象，将作为合并的基础对象。
 * @param source 源对象，其属性将覆盖目标对象中的同名属性。
 * @returns 返回一个新的对象，该对象包含了目标对象和源对象合并后的所有属性。
 * @throws 如果任一参数不是对象，则抛出 TypeError 异常。
 */
export const deepMergeImmutable = (
  target: RecordType,
  source: RecordType,
): RecordType => {
  if (!isObject(target) || !isObject(source)) {
    throw new TypeError('两个参数都必须是对象！');
  }

  const result = JSON.parse(JSON.stringify(target));

  return deepMerge(result, source);
};
