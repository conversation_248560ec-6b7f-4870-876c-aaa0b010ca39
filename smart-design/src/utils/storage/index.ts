import { isString } from 'lodash';
import { STORE } from '../../consts';
import base64EncoderDecoder from '../base64EncoderDecoder';

/**
 * sessionStorage 和 localStorage 的2此封装
 * @param type 存储方式：sessionStorage、localStorage
 * @returns storage
 */

interface Item {
  key: any;
  value: any;
  expired?: number;
}

class Storage {
  memory: any;
  constructor(type: string) {
    if (type === 'localStorage') {
      this.memory = window.localStorage;
    } else {
      this.memory = window.sessionStorage;
    }
  }

  /**
   * 设置缓存
   * @param params
   * key: 键、value: 值、expired: 过期时间
   */
  setItem(params: Item) {
    // 默认的参数对象
    const defaultParams = {
      key: '',
      value: undefined,
      expired: undefined, // 默认为 undefined，而不是空字符串
      startTime: undefined, // 记录何时将值存入缓存，毫秒级
    };

    // 将obj和传进来的params合并
    const options = { ...defaultParams, ...params };

    // 对 options.key 进行加密
    const encryptKey = base64EncoderDecoder.encrypt(options.key);

    // 加密 value
    let value: any = '';

    // 如果options.expired存在，则将整个options对象转为JSON字符串
    if (options.expired) {
      value = JSON.stringify({
        ...options,
        startTime: new Date().getTime(),
      });
    } else {
      // 如果options.value不是字符串，则尝试转换为JSON字符串
      if (!isString(options.value)) {
        value = JSON.stringify(options.value);
      } else {
        // 否则直接使用options.value的值
        value = options.value;
      }
    }

    value = base64EncoderDecoder.encrypt(value);
    // 将加密后的key和value存入内存缓存中
    this.memory.setItem(encryptKey, value);
  }

  /**
   * 取值
   * @param key 键
   */
  getItem(key: string) {
    const encryptKey = base64EncoderDecoder.encrypt(key);

    let item = this.memory.getItem(encryptKey);

    if (!item || item == 'null' || item == 'undefined') {
      return null;
    }

    item = base64EncoderDecoder.decrypt(item);

    //先将拿到的试着进行json转为对象的形式
    try {
      item = JSON.parse(item);
    } catch (error) {
      //如果不行就不是json的字符串，就直接返回
      item = item;
    }

    //如果有 expired 的值，说明设置了失效时间
    if (item.startTime) {
      const now = new Date().getTime();
      //何时将值取出减去刚存入的时间，与item.expires比较，如果大于就是过期了，如果小于或等于就还没过期
      if (now - item.startTime > item.expired) {
        //缓存过期，清除缓存，返回空字符串
        this.memory.removeItem(key);
        return null;
      } else {
        //缓存未过期，返回值
        return item.value;
      }
    } else {
      //如果没有设置失效时间，直接返回值
      return item;
    }
  }

  /**
   * 移除缓存
   * @param key 键
   */
  removeItem(key: string) {
    const encryptKey = base64EncoderDecoder.encrypt(key);

    this.memory.removeItem(encryptKey);
  }

  /**
   * 移除全部缓存
   */
  clear() {
    this.memory.clear();
  }
}

/**
 * 存储和读取缓存
 *
 * @class Sx
 */
class Sx {
  sessionMemory: Storage;
  localMemory: Storage;
  constructor() {
    // super()

    this.sessionMemory = new Storage('sessionStorage');
    this.localMemory = new Storage('localStorage');
  }

  /**
   * 读取 AuthToken
   *
   * @returns string
   * @memberof Sx
   */
  getAuthToken(): string {
    return this.localMemory.getItem(STORE.TOKEN_IDENTIFIER);
  }

  /**
   * 存储 AuthToken
   *
   * @param token
   * @param expired
   * @memberof Sx
   */
  setAuthToken(token: string, expired: number) {
    this.localMemory.setItem({
      key: STORE.TOKEN_IDENTIFIER,
      value: token,
      expired: expired,
    });
  }

  /**
   * 存储 AuthToken
   * @memberof Sx
   */
  removeAuthToken() {
    this.remove(STORE.TOKEN_IDENTIFIER, true);
  }

  /**
   * 读取缓存
   *
   * @param key 键
   * @param forceLocal 是否读取 localStorage 中的缓存
   */
  get(key: string, forceLocal: boolean = false) {
    let result: any;
    if (forceLocal) {
      result = this.localMemory.getItem(key);
    } else {
      result = this.sessionMemory.getItem(key);
    }
    return result;
  }

  /**
   * 存储缓存
   *
   * @param key 键
   * @param value 值
   * @param forceLocal 是否存入 localStorage
   */
  set(key: string, value: any, forceLocal: boolean = false) {
    if (forceLocal) {
      this.localMemory.setItem({
        key,
        value,
      });
      return;
    }
    this.sessionMemory.setItem({
      key,
      value,
    });
  }

  /**
   * 清楚单个缓存
   *
   * @param key 键
   * @param forceLocal 是否删除 localStorage 中的缓存
   */
  remove(key: string, forceLocal: boolean = false) {
    this.sessionMemory.removeItem(key);
    if (forceLocal) {
      this.localMemory.removeItem(key);
    }
  }

  /**
   * 清楚所有缓存
   *
   * @param forceLocal 是否删除 localStorage 中的缓存
   */
  clear(forceLocal: boolean = false) {
    this.sessionMemory.clear();
    if (forceLocal) {
      this.localMemory.clear();
    }
  }
}

const S = new Sx();

export default S;

export { Storage };
