import { isEmpty } from 'lodash';
import type { ConfigContextProps } from '../../components/config-provider';
import type { FetchMethodType, FetchType } from '../../typing';
import { mergeNestedObjectValues } from '../mergeNestedObjectValues';
import { omitSpecialValue } from '../omitSpecialValue';
import { replaceTemplateWithPipes } from '../replaceTemplateWithPipes';
import { replaceVariablesInTemplate } from '../replaceVariablesInTemplate';

export const getEffectFetchConfig = (
  config: FetchType,
  context: ConfigContextProps,
  paramsDict: Record<string, any>,
  paramsToMerge?: Record<string, any>,
) => {
  const { api: apiMap, systemName } = context;

  const { appFunctionApi, appProcessApi, appObjectApi } = apiMap;

  let fetchUrl = '';
  let fetchMethod: FetchMethodType = 'post';
  let fetchParams = {};

  const effectFetch = config;
  const effectFetchType = config?.type || 'api';
  const paramsInjectionLocation = config?.paramsInjectionLocation || [];

  const dataIndex = config.data;

  const defaultParams = config?.defaultParams || {};
  const newDefaultParams = isEmpty(defaultParams)
    ? defaultParams
    : omitSpecialValue(
      JSON.parse(
        replaceTemplateWithPipes(JSON.stringify(defaultParams), {
          ...paramsToMerge,
          ...paramsDict,
        }),
      ),
    );

  switch (effectFetchType) {
    case 'api':
      fetchParams = newDefaultParams;
      fetchUrl =
        (effectFetch?.api as string) || (effectFetch?.action as string);
      fetchMethod = effectFetch?.method || 'post';
      break;

    case 'object':
      fetchParams = {
        object: effectFetch.objectKey,
      };
      fetchMethod = (
        effectFetch?.method || 'get'
      ).toLocaleLowerCase() as FetchMethodType;

      fetchUrl =
        fetchMethod === 'post'
          ? (appObjectApi?.replace('/*{id}', '') as string)
          : appObjectApi!;
      break;

    case 'function':
      let functionKey = effectFetch?.functionKey;
      const isMultipleFunctionKey = functionKey?.includes('|');

      if (isMultipleFunctionKey && functionKey) {
        functionKey = functionKey
          .split('|')
          .map((item) => item.trim())
          .find((key) => key.includes(`_${systemName?.toLocaleLowerCase()}_`));
      }

      fetchParams = {
        functionKey: functionKey,
        params: newDefaultParams,
      };

      fetchUrl = appFunctionApi!;
      fetchMethod = 'post';
      break;

    case 'process':
      let processKey = effectFetch?.processKey;
      const isMultipleProcessKey = processKey?.includes('|');

      if (isMultipleProcessKey && processKey) {
        processKey = processKey
          .split('|')
          .map((item) => item.trim())
          .find((key) => key.includes(`_${systemName?.toLocaleLowerCase()}_`));
      }

      fetchParams = {
        processKey: processKey,
        params: newDefaultParams,
      };

      fetchUrl = appProcessApi!;
      fetchMethod = 'post';
      break;

    default:
      break;
  }

  const api = replaceVariablesInTemplate(paramsDict, fetchUrl);

  const newFetchParams = paramsToMerge
    ? mergeNestedObjectValues(
      fetchParams,
      paramsToMerge,
      paramsInjectionLocation,
    )
    : fetchParams;

  return {
    api,
    method: fetchMethod,
    params: newFetchParams,
    type: effectFetchType,
    dataIndex,
    isCached: !!effectFetch?.isCached,
  };
};
