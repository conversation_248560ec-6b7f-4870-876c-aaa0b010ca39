import { evaluate } from 'mathjs';
import type { TimezoneType } from '../../components/config-provider';
import { evaluateCondition } from '../evaluateCondition';
import { containsMathSymbols } from '../index';
import { isSpecialValue } from '../isSpecialValue';
import { replaceTemplateWithPipes } from '../replaceTemplateWithPipes';
import { getTimeInTimeZone } from '../time';

/**
 * 评估一个逻辑表达式是否成立。
 *
 * @param data 包含变量名称和值的对象。
 * @param expression 要评估的逻辑表达式字符串。
 * @param timezone 时区字符串
 * @returns 如果表达式成立，返回 true；否则返回 false。
 * @throws 如果表达式中存在未定义的变量或操作符，则抛出错误。
 */
export function evaluateLogicalExpression(
  data: Record<string, string | number>,
  expression: string,
  timezone?: TimezoneType,
): boolean {
  // 替换表达式中的变量
  const replacedExpression = replaceTemplateWithPipes(expression, data);

  if (isSpecialValue(replacedExpression)) return false;

  // 分割表达式为子句（由 '||' 分隔）
  const clauses = replacedExpression.split('||');

  // 评估所有子句，至少有一个为真则整个表达式为真
  for (const clause of clauses) {
    // 分割子句为条件（由 '&&' 分隔）
    const conditions = clause.split('&&');

    // 检查所有条件是否都为真
    let allConditionsTrue = true;
    for (const condition of conditions) {
      // 解析条件为操作符和左右值
      const opMatch = condition.match(/(===|==|!==|!=|>=|>|<=|<)/);
      if (!opMatch) {
        throw new Error(`表达式中存在未知的操作符: ${condition}`);
      }
      const op = opMatch[0];

      let [leftValue, rightValue] = condition.split(op);

      // 判断是否存在数学符号
      if (containsMathSymbols(leftValue)) {
        try {
          leftValue = evaluate(leftValue);
          leftValue = leftValue.toString();
        } catch (err: any) {
          // throw new Error(`无法计算的数据公式: ${leftValue}`);
        }
      }

      // 判断是否存在数学符号
      if (containsMathSymbols(rightValue)) {
        try {
          rightValue = evaluate(rightValue);
          rightValue = rightValue.toString();
        } catch (err: any) {
          // throw new Error(`无法计算的数据公式: ${rightValue}`);
        }
      }

      // 获取当前时间
      if (leftValue.trim() === 'now' && !!timezone) {
        leftValue = getTimeInTimeZone(timezone as TimezoneType, 'x') + '';
      }

      // 获取当前时间
      if (rightValue.trim() === 'now' && !!timezone) {
        rightValue = getTimeInTimeZone(timezone as TimezoneType, 'x') + '';
      }

      // 根据操作符评估条件
      const result = evaluateCondition(op, leftValue.trim(), rightValue.trim());

      // 如果条件为假，则整个子句为假，跳出循环
      if (!result) {
        allConditionsTrue = false;
        break;
      }
    }

    // 如果至少有一个子句的所有条件都为真，则整个表达式为真
    if (allConditionsTrue) {
      return true;
    }
  }

  // 所有子句的条件都没有全部为真，整个表达式为假
  return false;
}
