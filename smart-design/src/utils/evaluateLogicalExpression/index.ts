const operators = [
  '===',
  '!==',
  '==',
  '!=',
  '>=',
  '<=',
  '&&',
  '||',
  '>',
  '<',
  '(',
  ')',
];

const precedence: Record<string, number> = {
  '||': 1,
  '&&': 2,
  '===': 3,
  '!==': 3,
  '==': 3,
  '!=': 3,
  '>': 3,
  '<': 3,
  '>=': 3,
  '<=': 3,
  '(': 0,
  ')': 0,
};

/**
 * 将给定的表达式字符串拆分成一个包含字符串、数字、布尔值、null、undefined的数组。
 *
 * @param expr 要拆分的表达式字符串。
 * @returns 返回一个包含字符串、数字、布尔值、null、undefined的数组。
 */
export const tokenize = (
  expr: string,
): (string | number | boolean | null | undefined)[] => {
  // 创建一个新的变量来存储处理后的表达式
  const cleanExpr = expr.replace(/\s/g, '');
  const tokens: (string | number | boolean | null | undefined)[] = [];
  let pos = 0;
  const len = cleanExpr.length;

  while (pos < len) {
    // 处理字符串
    if (cleanExpr[pos] === '"' || cleanExpr[pos] === "'") {
      const quote = cleanExpr[pos];
      let str = '';
      pos++;
      while (pos < len && cleanExpr[pos] !== quote) {
        str += cleanExpr[pos];
        pos++;
      }
      pos++;
      tokens.push(str);
    }
    // 处理数字
    else if (/\d/.test(cleanExpr[pos]) || cleanExpr[pos] === '.') {
      let numStr = '';
      while (
        pos < len &&
        (/\d/.test(cleanExpr[pos]) || cleanExpr[pos] === '.')
      ) {
        numStr += cleanExpr[pos];
        pos++;
      }
      tokens.push(Number(numStr));
    }
    // 处理标识符和关键字
    else if (/[a-zA-Z_]/.test(cleanExpr[pos])) {
      let word = '';
      while (pos < len && /[a-zA-Z0-9_]/.test(cleanExpr[pos])) {
        word += cleanExpr[pos];
        pos++;
      }
      // 处理布尔值
      if (word === 'true') tokens.push(true);
      else if (word === 'false') tokens.push(false);
      // 处理 null 和 undefined
      else if (word === 'null') tokens.push(null);
      else if (word === 'undefined') tokens.push(undefined);
      // 处理其他标识符
      else tokens.push(word);
    }
    // 处理操作符
    else {
      let matched = false;
      for (const op of operators) {
        if (cleanExpr.startsWith(op, pos)) {
          tokens.push(op);
          pos += op.length;
          matched = true;
          break;
        }
      }
      // 如果没有匹配到操作符，则移动指针
      if (!matched) pos++;
    }
  }

  return tokens;
};

/**
 * 将中缀表达式转换为后缀表达式（逆波兰表达式）
 *
 * @param tokens 中缀表达式的分词数组
 * @returns 后缀表达式（逆波兰表达式）的分词数组
 */
export const infixToPostfix = (tokens: any[]): any[] => {
  // 存储输出结果的数组
  const output: any[] = [];
  // 存储操作符的栈
  const stack: any[] = [];

  // 遍历输入的标记数组
  for (const token of tokens) {
    // 如果标记是字符串且存在于优先级对象中
    if (typeof token === 'string' && precedence.hasOwnProperty(token)) {
      // 如果是左括号，直接压入栈中
      if (token === '(') {
        stack.push(token);
      }
      // 如果是右括号，弹出栈顶元素直到遇到左括号
      else if (token === ')') {
        while (stack.length > 0 && stack[stack.length - 1] !== '(') {
          output.push(stack.pop());
        }
        stack.pop();
      }
      // 如果是其他操作符，弹出栈顶元素直到栈顶操作符的优先级低于当前操作符的优先级
      else {
        while (
          stack.length > 0 &&
          precedence[stack[stack.length - 1]] >= precedence[token]
        ) {
          output.push(stack.pop());
        }
        stack.push(token);
      }
    }
    // 如果标记不是操作符，直接添加到输出结果数组中
    else {
      output.push(token);
    }
  }

  // 将栈中剩余的操作符依次弹出并添加到输出结果数组中
  while (stack.length > 0) {
    output.push(stack.pop());
  }

  return output;
};

/**
 * 评估后缀表达式
 *
 * @param postfix 后缀表达式数组
 * @returns 表达式计算结果，可以是布尔值、数字、字符串、null 或 undefined
 * @throws 如果遇到未知的运算符，则抛出错误
 */
export const evaluatePostfix = (
  postfix: any[],
): boolean | number | string | null | undefined => {
  const stack: any[] = [];

  for (const token of postfix) {
    // 如果 token 是字符串并且是运算符之一
    if (
      typeof token === 'string' &&
      ['===', '!==', '==', '!=', '>', '<', '>=', '<=', '&&', '||'].includes(
        token,
      )
    ) {
      const b = stack.pop(); // 从栈中弹出第二个操作数
      const a = stack.pop(); // 从栈中弹出第一个操作数
      let result: boolean | number;

      // 根据运算符执行相应的操作
      switch (token) {
        case '===':
          result = JSON.stringify(a) === JSON.stringify(b); // 严格等于运算符
          break;
        case '!==':
          result = JSON.stringify(a) !== JSON.stringify(b); // 不严格等于运算符
          break;
        case '==':
          // eslint-disable-next-line eqeqeq
          result = a == b; // 添加宽松等于
          break;
        case '!=':
          // eslint-disable-next-line eqeqeq
          result = a != b; // 添加宽松不等于
          break;
        case '>':
          result = a > b; // 大于运算符
          break;
        case '<':
          result = a < b; // 小于运算符
          break;
        case '>=':
          result = a >= b; // 大于等于运算符
          break;
        case '<=':
          result = a <= b; // 小于等于运算符
          break;
        case '&&':
          result = Boolean(a) && Boolean(b); // 逻辑与运算符
          break;
        case '||':
          result = Boolean(a) || Boolean(b); // 逻辑或运算符
          break;
        default:
          throw new Error(`Unknown operator: ${token}`); // 抛出未知运算符错误
      }

      stack.push(result); // 将运算结果压入栈中
    } else {
      stack.push(token); // 如果 token 不是运算符，则直接压入栈中
    }
  }

  return stack[0]; // 返回栈顶元素作为最终结果
};

/**
 * 根据给定的数据和逻辑表达式评估布尔值。
 *
 * @param data 包含用于替换表达式中变量的值的对象。
 * @param expr 要评估的逻辑表达式，其中可以使用 `*{key}` 语法来引用 `data` 中的变量。
 * @returns 如果表达式评估为 true，则返回 true；否则返回 false。
 * @throws 如果在表达式中引用的变量在 `data` 中不存在，则抛出错误。
 */
export const evaluateLogicalExpression = (
  data: Record<string, any>,
  expr: string,
): boolean => {
  // 替换表达式中的变量为它们的值
  const replaced = expr.replace(/\*\{([^}]+)\}/g, (_, key) => {
    if (data.hasOwnProperty(key)) {
      return JSON.stringify(data[key]);
    }

    return key;
  });
  // console.log('Replaced expression:', replaced);

  // 对表达式进行分词
  const tokens = tokenize(replaced);
  // console.log('Tokens:', tokens);

  // 将中缀表达式转换为后缀表达式
  const postfix = infixToPostfix(tokens);
  // console.log('Postfix expression:', postfix);

  // 计算后缀表达式的布尔结果
  const result = evaluatePostfix(postfix);

  // console.log('Result:', result);
  return Boolean(result);
};
