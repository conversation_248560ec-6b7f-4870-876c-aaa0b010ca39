/**
 * 将字符串按照指定长度分割成多个子字符串数组
 *
 * @param str 要分割的字符串
 * @param length 每个子字符串的最大长度
 * @returns 分割后的子字符串数组
 */
export const splitStringByLength: (str: string, length: number) => string[] = (
  str,
  length,
) => {
  const result: string[] = [];
  let startIndex = 0;

  while (startIndex < str.length) {
    // 截取子字符串，注意第二个参数是结束索引（不包含）
    result.push(str.substring(startIndex, startIndex + length));
    startIndex += length;

    // 如果字符串的剩余部分不足length，则结束循环
    if (startIndex >= str.length) {
      break;
    }
  }

  return result;
};
