import { evaluate } from 'mathjs';
import {
  containsMathSymbols,
  isValidNumberString,
  removeAllSpaces,
} from '../index';
import { replaceVariablesInTemplate } from '../replaceVariablesInTemplate';

/**
 * 评估表达式并返回结果
 *
 * @param expression 待评估的表达式，格式为 "条件 ? 真值 : 假值"
 * @returns 返回表达式的评估结果
 */
export const evaluateExpression = (
  data: Record<string, string | number>,
  expression: string,
): any => {
  // 替换表达式中的变量
  const replacedExpression = replaceVariablesInTemplate(data, expression);

  // 假设表达式格式正确且遵循三元操作符的语法
  // 表达式格式为 "条件 ? 真值 : 假值"
  const parts = removeAllSpaces(replacedExpression).split(/(\?|:)/);
  if (parts.length !== 5) {
    return expression;
  }

  // 简单的表达式解析器，仅支持基础类型和简单的比较
  // 注意：这个实现非常基础，仅用于演示目的
  const [condition, _, trueValue, __, falseValue] = parts;

  const opMatch = condition.match(/(===|==|!==|!=|>=|>|<=|<)/);
  if (!opMatch) {
    throw new Error(`表达式中存在未知的操作符: ${condition}`);
  }

  const op = opMatch[0];

  let [leftValue, rightValue] = condition.split(op) as any[];

  // 判断是否存在数学符号
  if (containsMathSymbols(leftValue)) {
    try {
      leftValue = evaluate(leftValue);
      leftValue = leftValue.toString();
    } catch (err: any) {
      // throw new Error(`无法计算的数据公式: ${leftValue}`);
    }
  }

  // 判断是否存在数学符号
  if (containsMathSymbols(rightValue)) {
    try {
      rightValue = evaluate(rightValue);
      rightValue = rightValue.toString();
    } catch (err: any) {
      // throw new Error(`无法计算的数据公式: ${rightValue}`);
    }
  }

  if (isValidNumberString(leftValue)) {
    leftValue = Number(leftValue);
  }

  if (isValidNumberString(rightValue)) {
    rightValue = Number(rightValue);
  }

  let result: any;

  switch (op) {
    case '==':
      result = leftValue == rightValue ? trueValue : falseValue;
      break;
    case '===':
      result = leftValue === rightValue ? trueValue : falseValue;
      break;

    case '!=':
      result = leftValue != rightValue ? trueValue : falseValue;
      break;

    case '!==':
      result = leftValue !== rightValue ? trueValue : falseValue;
      break;

    case '>=':
      result = leftValue >= rightValue ? trueValue : falseValue;
      break;

    case '>':
      result = leftValue > rightValue ? trueValue : falseValue;
      break;

    case '<=':
      result = leftValue <= rightValue ? trueValue : falseValue;
      break;

    case '<':
      result = leftValue < rightValue ? trueValue : falseValue;
      break;
    default:
      throw new Error(`Unsupported operator: ${op}`);
  }

  return result;
};
