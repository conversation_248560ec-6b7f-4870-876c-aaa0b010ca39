import { decode, encode } from 'js-base64';

export { decode as base64Decode, encode as base64Encode };

class Base64EncoderDecoder {
  private blockSize: number;
  private symbol: string;

  constructor() {
    this.blockSize = 8;
    this.symbol = '*';
  }

  public setSymbol(symbol: string) {
    this.symbol = symbol;
  }

  public setBlockSize(size: number) {
    this.blockSize = size;
  }

  /**
   * 对输入字符串进行块化处理，并在长度不足的块后填充特定符号
   *
   * @param input 输入的字符串
   * @returns 处理后的字符串
   */
  transform(input: string): string {
    let inputList: string[] = [];
    let tmp: string[] = [];

    for (let i = 0; i < input.length; i++) {
      let c = input.charAt(i);
      tmp.push(c);

      if (tmp.length >= this.blockSize) {
        inputList.push(tmp.join(''));
        tmp = []; // 清空数组
      }
    }

    if (tmp.length > 0) {
      inputList.push(tmp.join(''));
    }

    let str = '';
    for (let i = inputList.length - 1; i >= 0; i--) {
      let t = inputList[i];
      if (t.length < this.blockSize) {
        let len = this.blockSize - t.length;
        for (let j = 0; j < len; j++) {
          t += this.symbol;
        }
      }

      str = str + t;
    }

    return str;
  }

  public encrypt(input: string): string {
    if (!input) return input;

    const newInput = encode(input);

    return this.transform(newInput);
  }

  decrypt = (input: string): string => {
    let newInput = this.transform(input);

    newInput = newInput.replaceAll(this.symbol, '');

    return decode(newInput);
  };
}

export default new Base64EncoderDecoder();
