/**
 * 更新 HTML 内容中的图片链接，添加文件令牌。
 *
 * @param htmlContent 包含图片的 HTML 内容
 * @param fileToken 文件令牌，用于添加到图片链接中
 * @returns 更新后的 HTML 内容
 */
export const updateImageLinks = (htmlContent: string | null | undefined, fileToken: string | null | undefined): string | null | undefined => {
  if (!htmlContent || !fileToken) {
    return htmlContent; // 如果任一参数不存在，则返回原始内容（或 null）
  }

  // 使用正则表达式匹配图片标签的 src 属性
  return htmlContent.replace(/<img[^>]+src="([^"]+)"[^>]*>/g, (match, capture) => {
    let captureStr: string = capture

    if (captureStr.includes('?')) {
      captureStr = captureStr.split('?')[0]
    }

    const fileUrl = `${captureStr}?${fileToken}`;

    console.log(fileUrl, 'fileUrl')

    // 注意：这里我们直接替换整个 src 属性而不是整个 img 标签，以避免不必要的解析
    return match.replace(`src="${capture}"`, `src="${fileUrl}"`);
  });
};

export const updateImageLink = (url: string, fileToken: string) => {
  let urlStr = url

  if (urlStr.includes('?')) {
    urlStr = urlStr.split('?')[0]
  }

  return `${urlStr}?${fileToken}`
}
