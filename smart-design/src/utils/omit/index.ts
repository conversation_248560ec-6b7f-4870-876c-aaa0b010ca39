/**
 * 去除对象中指定的字段，返回新对象
 *
 * @param obj 要操作的对象
 * @param fields 要去除的字段数组
 * @returns 返回一个新对象，其中不包含指定的字段
 */
const omit = <T extends { [key: string]: any }, K extends keyof T>(
  obj: T,
  fields: K[],
): Pick<T, Exclude<keyof T, K>> => {
  // 创建一个浅拷贝的对象
  const shallowCopy = Object.assign({}, obj);
  // 遍历要去除的字段数组
  for (let i = 0; i < fields.length; i += 1) {
    const key = fields[i];
    // 从浅拷贝的对象中删除指定字段
    delete shallowCopy[key];
  }
  // 返回处理后的新对象
  return shallowCopy as any;
};

export default omit;
