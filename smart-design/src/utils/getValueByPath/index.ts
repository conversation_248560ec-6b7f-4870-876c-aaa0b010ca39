/**
 * 根据给定的路径获取对象中的属性值。
 *
 * @param obj 要从中获取属性的对象。
 * @param path 以点分隔的属性路径字符串，如 "nestedObj.property"。
 * @returns 返回找到的属性值，如果路径不存在则返回 undefined。
 */
export const getValueByPath = (obj: Record<string, any>, path: string): any => {
  // 将路径字符串按点分割成数组
  const parts = path.split('.');
  // 当前处理的对象，初始化为传入的 obj
  let currentObj: any = obj;

  // 遍历路径的每一部分
  for (let i = 0; i < parts.length; i++) {
    // 检查当前对象是否包含当前路径部分作为属性
    if (currentObj && currentObj.hasOwnProperty(parts[i])) {
      // 将当前对象更新为当前路径部分对应的属性值
      currentObj = currentObj[parts[i]];
    } else {
      // 如果路径中的某部分不存在，返回 undefined
      return undefined;
    }
  }
  // 返回最终的属性值
  return currentObj;
};
