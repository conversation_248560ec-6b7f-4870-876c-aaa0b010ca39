type OmitUndefined<T> = {
  [P in keyof T]: NonNullable<T[P]>;
};

/**
 * 从对象中移除所有值为 undefined 的属性
 *
 * @param obj 要处理的原始对象
 * @returns 返回一个新的对象，其中所有值为 undefined 的属性都被移除
 */
export const omitUndefined = <T extends Record<string, any>>(
  obj: T,
): OmitUndefined<T> => {
  // 创建一个新对象来存储非 undefined 的属性
  const newObj = {} as Record<string, any> as T;

  Object.keys(obj || {}).forEach((key) => {
    // 遍历对象的所有键
    if (obj[key] !== undefined) {
      // 如果当前属性值不是 undefined
      // 将该属性复制到新对象中
      (newObj as any)[key] = obj[key];
    }
  });

  if (Object.keys(newObj as Record<string, any>).length < 1) {
    // 如果新对象为空（没有任何属性）
    // 返回 undefined
    return undefined as any;
  }

  // 返回新对象，并断言其类型为 OmitUndefined<T>
  return newObj as OmitUndefined<T>;
};

export default omitUndefined;
