import { isArray, isObject } from 'lodash';
import { isSpecialValue } from '../isSpecialValue';
import pipe from '../pipe';

// 模板编译缓存
const templateCache = new Map<string, CompiledTemplate>();

// 编译后的模板结构
interface CompiledTemplate {
  segments: TemplateSegment[];
  regex: RegExp;
}

// 模板片段
type TemplateSegment = TextSegment | ExpressionSegment;

// 纯文本片段
interface TextSegment {
  type: 'text';
  content: string;
}

// 表达式片段
interface ExpressionSegment {
  type: 'expression';
  rawExpression: string;
  key: string;
  pipeFuncs: string[];
}

/**
 * 解析模板字符串为片段
 * 
 * @param template 模板字符串
 * @param regex 用于匹配表达式的正则表达式
 * @returns 解析后的模板片段数组
 */
function parseTemplate(
  template: string,
  regex = /\*\{([^}]+)\}/g
): TemplateSegment[] {
  // 获取缓存键
  const cacheKey = `${template}:${regex.source}`;

  // 检查缓存
  if (templateCache.has(cacheKey)) {
    return templateCache.get(cacheKey)!.segments;
  }

  const segments: TemplateSegment[] = [];
  let lastIndex = 0;
  let match: RegExpExecArray | null;

  // 创建新的正则表达式实例（因为exec会修改lastIndex）
  const expressionRegex = new RegExp(regex.source, regex.flags);

  // 逐个匹配表达式
  while ((match = expressionRegex.exec(template)) !== null) {
    const [fullMatch, expression] = match;
    const startIndex = match.index;

    // 添加表达式前的文本片段
    if (startIndex > lastIndex) {
      segments.push({
        type: 'text',
        content: template.substring(lastIndex, startIndex)
      });
    }

    // 处理表达式片段
    const hasPipe = expression.includes('|');
    const [key, ...pipeFuncs] = hasPipe ? expression.split('|').map(s => s.trim()) : [expression, ''];

    segments.push({
      type: 'expression',
      rawExpression: expression,
      key,
      pipeFuncs: pipeFuncs.filter(Boolean)
    });

    lastIndex = startIndex + fullMatch.length;
  }

  // 添加最后一段文本
  if (lastIndex < template.length) {
    segments.push({
      type: 'text',
      content: template.substring(lastIndex)
    });
  }

  // 缓存解析结果
  templateCache.set(cacheKey, {
    segments,
    regex
  });

  return segments;
}

/**
 * 获取对象中指定路径的值
 * 
 * @param data 数据对象
 * @param path 属性路径，如 'a.b.c'
 * @returns 路径对应的值
 */
function getValueByPath(data: Record<string, any>, path: string): any {
  if (!path) return data;

  const keys = path.split('.').map(k => k.trim());
  let value = data;

  for (const key of keys) {
    if (value === undefined || value === null) {
      return undefined;
    }

    // 检查键是否包含数组索引模式[n]
    const arrayIndexMatch = key.match(/(.+)\[(\d+)\]$/);
    if (arrayIndexMatch) {
      const [, baseKey, index] = arrayIndexMatch;
      const arrayValue = value[baseKey];

      // 如果值是数组并且索引有效
      if (Array.isArray(arrayValue) && arrayValue.length > parseInt(index)) {
        value = arrayValue[parseInt(index)];
      } else {
        return undefined;
      }
    } else {
      value = value[key];
    }
  }

  return value;
}

/**
 * 使用管道符号替换模板中的占位符 - 优化版
 *
 * @param template 模板字符串，其中包含形如 `*{key}` 的占位符
 * @param data 记录数据对象，用于替换模板中的占位符
 * @param regex 正则表达式, 使用正则表达式查找字符串中的参数占位符，例如：`*{key}`
 * @returns 生成的字符串
 */
export const replaceTemplateWithPipes = (
  template: string,
  data: Record<string, any>,
  regex = /\*\{([^}]+)\}/g,
): string => {
  // 空模板检查
  if (!template) return '';

  // 特殊值检查
  if (!isSpecialValue(template, regex)) return template;

  try {
    // 解析模板
    const segments = parseTemplate(template, regex);

    // 如果没有需要处理的片段，直接返回原模板
    if (segments.length === 0 || (segments.length === 1 && segments[0].type === 'text')) {
      return template;
    }

    // 渲染各个片段
    return segments.map(segment => {
      // 文本片段直接返回内容
      if (segment.type === 'text') {
        return segment.content;
      }

      // 处理表达式片段
      const { key, pipeFuncs } = segment;

      // 获取键对应的值
      let value = getValueByPath(data, key);

      // 找不到值时返回原始表达式
      // if (value === undefined) {
      //   return `*{${segment.rawExpression}}`;
      // }

      // 对象或数组转JSON字符串
      if (isObject(value) || isArray(value)) {
        value = JSON.stringify(value);
      } else {
        // 其他类型转字符串
        value = String(value ?? '');
      }

      // 没有管道函数直接返回值
      if (!pipeFuncs.length) {
        return value;
      }

      // 使用管道处理
      return pipe.render(value, pipeFuncs, data);
    }).join('');
  } catch (error) {
    console.error('模板处理错误:', error, { template });
    return template; // 出错时返回原模板
  }
};

/**
 * 递归处理对象中所有可能的模板字符串
 * 
 * @param obj 要处理的对象
 * @param data 用于替换模板的数据
 * @param regex 模板匹配正则表达式
 * @returns 处理后的对象
 */
export const processTemplatesInObject = (
  obj: any,
  data: Record<string, any>,
  regex = /\*\{([^}]+)\}/g
): any => {
  // 非对象直接返回
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  // 如果是数组，递归处理每个元素
  if (Array.isArray(obj)) {
    return obj.map(item => processTemplatesInObject(item, data, regex));
  }

  // 处理对象
  const result: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      // 处理字符串模板
      result[key] = replaceTemplateWithPipes(value, data, regex);
    } else if (typeof value === 'object' && value !== null) {
      // 递归处理嵌套对象
      result[key] = processTemplatesInObject(value, data, regex);
    } else {
      // 其他类型直接复制
      result[key] = value;
    }
  }

  return result;
};

export default replaceTemplateWithPipes;