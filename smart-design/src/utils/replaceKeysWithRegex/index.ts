import { isArray, isEmpty, isObject } from 'lodash';
import { isSpecialValue } from '../isSpecialValue';
import { replaceTemplateWithPipes } from '../replaceTemplateWithPipes';

// 定义更精确的类型
type RecordType = Record<string, any>;

// 定义配置项接口
interface ReplaceOptions {
  /** 最大递归深度，防止栈溢出 */
  maxDepth?: number;
  /** 跳过替换的字段名列表 */
  skipFields?: string[];
  /** 对于有scope标记的对象，不处理的特定属性路径 */
  scopeSkipPaths?: string[][];
}

// 定义默认配置
const DEFAULT_OPTIONS: ReplaceOptions = {
  maxDepth: 100,
  skipFields: [],
  scopeSkipPaths: [['children'], ['props', 'columns']]
};

/**
 * 使用正则表达式替换对象或对象数组中的键。
 *
 * @param input 要处理的输入对象或对象数组。
 * @param record 包含替换模板中变量的记录对象。
 * @param options 替换选项配置。
 * @returns 替换后的对象或对象数组。
 */
export const replaceKeysWithRegex = <T extends RecordType[] | RecordType>(
  input: T,
  record: RecordType,
  options?: Partial<ReplaceOptions>
): T => {
  // 合并选项与默认值
  const mergedOptions: ReplaceOptions = {
    ...DEFAULT_OPTIONS,
    ...options
  };

  // 如果输入不是数组也不是对象，直接返回输入（因为它们是基本类型，不需要进一步处理）
  if (!isObject(input) && !isArray(input)) {
    return input;
  }

  if (isEmpty(record)) return input;

  // 用于检查对象是否已处理过，避免循环引用问题
  const processedObjects = new WeakMap<object, any>();

  /**
   * 处理有scope标记的对象
   * @param obj 带scope标记的对象
   * @param key 当前处理的键
   * @param value 当前处理的值
   * @returns 如果需要特殊处理则返回处理结果，否则返回undefined
   */
  const handleScopedObject = (
    obj: RecordType,
    key: string,
    value: any
  ): { handled: boolean; result?: any } => {
    if (obj.scope !== true) {
      return { handled: false };
    }

    // 检查是否是需要跳过的直接字段
    if (mergedOptions.scopeSkipPaths?.some(path => path.length === 1 && path[0] === key)) {
      return { handled: true, result: value };
    }

    // 检查是否是需要特殊处理的嵌套路径
    const nestedPaths = mergedOptions.scopeSkipPaths?.filter(
      path => path.length > 1 && path[0] === key
    );

    if (nestedPaths?.length && isObject(value)) {
      const remainingPaths = nestedPaths.map(path => path.slice(1));
      const processedValue = processObjectWithSkipPaths(value as RecordType, remainingPaths);
      return { handled: true, result: processedValue };
    }

    return { handled: false };
  };

  /**
   * 处理对象时跳过特定路径
   * @param obj 要处理的对象
   * @param skipPaths 要跳过的路径列表
   * @returns 处理后的对象
   */
  const processObjectWithSkipPaths = (
    obj: RecordType,
    skipPaths: string[][]
  ): RecordType => {
    const result: RecordType = {};

    Object.keys(obj).forEach(key => {
      const value = obj[key];

      // 检查是否是需要跳过处理的字段
      if (skipPaths.some(path => path.length === 1 && path[0] === key)) {
        result[key] = value;
        return;
      }

      // 处理嵌套路径
      const nestedPaths = skipPaths.filter(
        path => path.length > 1 && path[0] === key
      );

      if (nestedPaths.length && isObject(value)) {
        const remainingPaths = nestedPaths.map(path => path.slice(1));
        result[key] = processObjectWithSkipPaths(value as RecordType, remainingPaths);
      } else {
        // 正常处理
        result[key] = processValue(value, 0);
      }
    });

    return result;
  };

  /**
   * 递归处理值
   * @param value 要处理的值
   * @param depth 当前递归深度
   * @returns 处理后的值
   */
  const processValue = (
    value: any,
    depth: number
  ): any => {
    // 防止无限递归
    if (depth > (mergedOptions.maxDepth || DEFAULT_OPTIONS.maxDepth!)) {
      console.warn('超出最大递归深度，replaceKeysWithRegex 将返回原始值');
      return value;
    }

    // 处理基本类型
    if (!isObject(value) && !isArray(value)) {
      return isSpecialValue(value)
        ? replaceTemplateWithPipes(value, record)
        : value;
    }

    // 检查是否已处理过该对象（避免循环引用）
    if (typeof value === 'object' && value !== null) {
      if (processedObjects.has(value)) {
        return processedObjects.get(value);
      }
    }

    // 处理数组
    if (isArray(value)) {
      const result = value.map(item => processValue(item, depth + 1));
      if (typeof value === 'object' && value !== null) {
        processedObjects.set(value, result);
      }
      return result;
    }

    // 处理对象
    if (isObject(value)) {
      const result: RecordType = {};
      
      // 预先存储空结果，防止循环引用
      if (typeof value === 'object' && value !== null) {
        processedObjects.set(value, result);
      }

      Object.keys(value as RecordType).forEach(key => {
        // 跳过配置中指定的字段
        if (mergedOptions.skipFields?.includes(key)) {
          result[key] = (value as RecordType)[key];
          return;
        }

        const currentValue = (value as RecordType)[key];
        
        // 处理scope标记的特殊对象
        const scopeResult = handleScopedObject(value as RecordType, key, currentValue);
        if (scopeResult.handled) {
          result[key] = scopeResult.result;
          return;
        }

        // 处理特殊值
        if (isSpecialValue(currentValue)) {
          try {
            result[key] = replaceTemplateWithPipes(currentValue, record);
          } catch (error) {
            console.error(`替换键 "${key}" 的模板时出错:`, error);
            result[key] = currentValue; // 出错时保留原值
          }
        } else {
          // 递归处理嵌套值
          result[key] = processValue(currentValue, depth + 1);
        }
      });

      return result;
    }

    return value;
  };

  // 启动处理逻辑
  const result = processValue(input, 0);
  
  // 确保返回类型与输入类型一致
  return result as T;
};
