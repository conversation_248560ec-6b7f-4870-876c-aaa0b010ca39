import { isString } from 'lodash';
import queryString from 'query-string';
import isDeepEqualReact from './isDeepEqualReact';
import omit from './omit';
import omitUndefined from './omitUndefined';

export { isDeepEqualReact, omit, omitUndefined };

/**
 * 将文件转换为 Base64 编码的字符串
 *
 * @param file 文件对象
 * @returns 返回 Promise，resolve 时携带 Base64 编码的字符串，reject 时携带错误对象
 */
export const getBase64 = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

/**
 * 移除字符串中的所有空格。
 *
 * @param str 要处理的字符串。
 * @returns 返回没有空格的新字符串。
 */
export const removeAllSpaces = (str: string): string => {
  return str.replace(/\s+/g, '');
};

/**
 * 获取fetchAction的依赖项
 *
 * @param inputString 输入的字符串
 * @param regex 正则表达式，用于匹配依赖项，默认为/\*{([^}]+)\}/g
 * @returns 返回匹配到的依赖项数组
 */
export const getFetchActionDependencys = (
  inputString: string,
  regex = /\*\{([^}]+)\}/g,
) => {
  const result = [];

  // 使用正则表达式的 exec 方法循环查找匹配项
  let match = regex.exec(inputString);
  while (match) {
    // 获取参数名，例如：name
    const paramName = match[1];

    if (paramName) {
      result.push(paramName);
    }

    // 继续查找下一个匹配项
    match = regex.exec(inputString);
  }

  return result;
};

/**
 * 格式化依赖项的值
 *
 * @param values 依赖项的值，类型为Record<string, any>
 * @returns 格式化后的依赖项的值，类型为Record<string, any>
 */
export const formatDependencysValues = (values: Record<string, any>) => {
  const result: Record<string, any> = {};

  for (const key in values) {
    if (Object.prototype.hasOwnProperty.call(values, key)) {
      const val = values[key];

      if (isString(val) && val.includes('&') && val.includes('=')) {
        const obj = queryString.parse(val);
        result[key] = obj.nameId;
      } else {
        result[key] = val;
      }
    }
  }

  return omitUndefined(result);
};

/**
 * 下载 json 文件
 *
 * @param fileName 文件名
 * @param data Object 对象
 *
 * */
export const downloadJson = (fileName: string, data: Record<string, any>) => {
  const a: any = document.createElement('a');

  const str = JSON.stringify(data, null, 2);

  a.download = `${fileName}.json`;

  a.href = `data:,${str}`;

  a.click();
};

/**
 * 检查给定的HTML字符串中是否包含<img>标签。
 *
 * @param htmlString 要检查的HTML字符串。
 * @returns 如果字符串中包含<img>标签，则返回true；否则返回false。
 */
export const hasImageTag = (htmlString: string) => {
  // 使用正则表达式来查找<img>标签（简化版，可能无法处理所有情况）
  const regex = /<img[^>]*>/g;
  // 使用test方法检查字符串是否匹配正则表达式
  return regex.test(htmlString);
};

export const formatQueryString = (obj: Record<string, string>): string => {
  return Object.entries(obj)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');
};

/**
 * 判断字符串中是否存在数学符号
 *
 * @param str 要检查的字符串
 * @returns 如果字符串中存在数学符号，则返回 true；否则返回 false
 */
export const containsMathSymbols = (str: string): boolean => {
  // 定义一个正则表达式，匹配常见的数学符号
  const regex = /[+\-*/()%]/g;
  // 使用test方法检查字符串中是否存在匹配的符号
  return regex.test(str);
};

/**
 * 判断给定的字符串是否为有效的数字字符串
 *
 * @param str 待判断的字符串
 * @returns 如果字符串是有效的数字字符串，则返回 true；否则返回 false
 */
export const isValidNumberString = (str: string) => {
  // 使用 parseFloat 或 Number.parseFloat 来尝试转换
  // 如果转换后的结果与原始字符串的字符串表示形式相同，那么它就是一个有效的数字字符串
  const num = parseFloat(str);
  return !isNaN(num) && String(num) === str.trim();
};

/**
 * 延迟函数，返回一个 Promise 对象，在指定的毫秒数后 resolve。
 *
 * @param ms 延迟时间，单位为毫秒，默认为 1000 毫秒。
 * @returns 返回一个 Promise 对象，在指定的毫秒数后 resolve。
 */
export const delay = (ms: number = 1000) =>
  new Promise((resolve) => {
    setTimeout(resolve, ms);
  });

/**
 * 将输入解析为 JSON 对象或数组，如果输入是字符串则尝试解析，否则直接返回输入。
 *
 * @param input 要解析的输入，可以是字符串、对象或数组
 * @returns 解析后的 JSON 对象或数组，或者错误信息字符串
 */
export const parseJson = <T>(input: string | T): T | Error => {
  // 如果输入类型为字符串
  if (typeof input === 'string') {
    try {
      // 尝试解析JSON字符串
      return JSON.parse(input) as T;
    } catch (error) {
      // 如果解析失败，则捕获错误并返回错误信息
      return new Error(`Invalid JSON: ${(error as Error).message}`);
    }
  } else {
    // 如果输入类型不是字符串，则直接返回输入
    return input;
  }
};
