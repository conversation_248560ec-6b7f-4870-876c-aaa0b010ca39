import * as React from 'react';
import { ConfigContext } from '../../../components/config-provider';
import { STORE } from '../../../consts';
import { getSearchParams } from '../../searchParams';
import S from '../../storage';

const useParamsDict = (dataSource = {}): Record<string, any> => {
  const context = React.useContext(ConfigContext);

  const query = getSearchParams();

  const params = context.useUrlParams();
  const appDefaultEntityId = context.appDefaultEntityId;

  const userAccountInfo = S.get(STORE.USER_ACCOUNT_INFO);

  const paramsDict: Record<string, any> = {
    ...params,
    ...query,
    userAccountId: userAccountInfo?.userAccountId || '',
    userAccountName: userAccountInfo?.userName || '',
    now: Date.now(),
    systemName: context.systemName,
    ...dataSource,
  };

  if (!paramsDict.entityId && appDefaultEntityId) {
    paramsDict['entityId'] = appDefaultEntityId;
  }

  return paramsDict;
};

export default useParamsDict;
