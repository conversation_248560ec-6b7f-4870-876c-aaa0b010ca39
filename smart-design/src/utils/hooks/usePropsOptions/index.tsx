import * as React from 'react';
import { cachedSwrFetcher } from '../../../utils/swrFetcher'
import { getEffectFetchConfig } from '../../../utils/getEffectFetchConfig'
import { extractPatternsFromInput } from '../../extract'
import { checkKeysExistInObject } from '../../../utils/checkKeysExistInObject'
import { deepMergeImmutable } from '../../../utils/deepMerge'
import { ConfigContext } from '../../../components/config-provider'
import useParamsDict from '../useParamsDict'
import { isArray } from 'lodash';
import { useDeepCompareEffect } from 'ahooks';

type PropsOptionsType = {
  [key: string]: any
}

const transformListWithNestedChildren = <T extends { [key: string]: any }>(
  list: T[],
  fieldNames: {
    children: keyof T;
    label: keyof T;
    value: keyof T;
    description: keyof T;
    key: keyof T;
    extra: keyof T;
    templateKey: keyof T;
  },
  parentSelectable: boolean = true,
): any[] => {
  return list.map(item => {
    const { ...rest } = item;


    // 使用解构和模板字符串来避免重复
    const {
      [fieldNames.label]: label,
      [fieldNames.value]: value,
      [fieldNames.description]: description,
      [fieldNames.key]: key,
      [fieldNames.extra]: extra,
      [fieldNames.templateKey]: templateKey,
      [fieldNames.children || 'children']: rawChildren,
    } = item;

    const children = isArray(rawChildren) ? transformListWithNestedChildren(rawChildren, fieldNames, parentSelectable) : [];


    // 返回一个新的对象，只包含需要的字段，并处理 value 为字符串
    return {
      label: label,
      value: value?.toString(), // 使用可选链操作符来安全地调用 toString
      description: description,
      enumKey: key,
      extra: extra,
      children: children,
      default: rest.default,
      templateKey,
      selectable: parentSelectable ? true : children.length === 0,
      // 如果不想在最终对象中保留原始对象的所有属性，可以去掉 ...rest
    };
  });
};


const usePropsOptions = (props: PropsOptionsType) => {
  const { value, dependenciesValues, effect, onChange, fieldProps } = props

  const { fieldNames, parentSelectable, defaultOptions } = fieldProps

  const configContext = React.useContext(ConfigContext)
  const paramsDict = useParamsDict(dependenciesValues || {})


  const [options, setOptions] = React.useState<any[]>([])
  const fetchInitRef = React.useRef(true)
  const [isLoading, setIsLoading] = React.useState(false)

  const fetch = async (remoteSearchParams?: Record<string, any>) => {
    let fetchConfig = JSON.parse(JSON.stringify(effect.fetch))

    const fetchDependentValue = extractPatternsFromInput(JSON.stringify((fetchConfig)))

    if (fetchDependentValue.length > 0) {
      const has = checkKeysExistInObject(paramsDict, fetchDependentValue)

      if (!has) return
    }

    setIsLoading(true)

    if (remoteSearchParams) {
      fetchConfig.defaultParams = deepMergeImmutable(fetchConfig.defaultParams || {}, remoteSearchParams)
    }

    const { api, method, params, dataIndex } = getEffectFetchConfig(fetchConfig, configContext, paramsDict)

    const newDataIndex = dataIndex ? dataIndex : 'root'

    const { data } = await cachedSwrFetcher(api, method, params)

    let list: any[] | string = []

    if (newDataIndex === 'root') {
      list = data
    } else if (isArray(newDataIndex)) {
      list = newDataIndex.reduce((acc, key) => acc[key], data);
    } else {
      list = data[newDataIndex]
    }

    if (typeof list === 'string' && /^\[.*\]$/.test(list)) {
      list = JSON.parse(list)
    }

    if (typeof list === 'string') {
      list = list.split(',').map(item => ({
        label: item,
        value: item
      }))
    }

    if (isArray(list) && list.every(item => typeof item === 'string')) {
      list = list.map(item => ({
        label: item,
        value: item
      }))
    }

    if (isArray(defaultOptions)) {
      list = [...defaultOptions, ...list]
    }

    if (fieldProps && fieldNames && fieldNames.value) {
      list = transformListWithNestedChildren(list, fieldNames, parentSelectable)
    }

    setOptions(list)

    setIsLoading(false)

    if (!fetchInitRef.current) {
      onChange('')
    }

    fetchInitRef.current = false
  }

  useDeepCompareEffect(() => {
    if (effect?.fetch) {
      fetch()
    } else {
      let options = (fieldProps && isArray(fieldProps?.options)) ? fieldProps?.options : []

      if (fieldProps && fieldNames && fieldNames.value) {
        options = transformListWithNestedChildren(options, fieldNames, parentSelectable)
      }
      setOptions(options)
    }
  }, [dependenciesValues])

  return {
    options,
    isLoading,
    fetch
  }
}

export default usePropsOptions
