import { Button, message, Modal } from 'antd';
import React from 'react';
import { ConfigContext } from '../../../components/config-provider';
import type { OnSuccessType } from '../../../typing';
import { delay } from '../../index';
import { replaceVariablesInTemplate } from '../../replaceVariablesInTemplate';
import useParamsDict from '../useParamsDict';

type Options = {
  onRetry?: () => void;
  onReset?: () => void;
  onEffectRetry?: () => void;
};

const useSuccess = (options?: Options) => {

  const { router } = React.useContext(ConfigContext);
  const paramsDict = useParamsDict();
  const { onRetry, onReset, onEffectRetry } = options || {};

  const onSuccess = async (confSuccess: OnSuccessType, extra: Record<string, any> = {}) => {

    switch (confSuccess.type) {
      case 'link':
        router?.push(replaceVariablesInTemplate({...paramsDict, ...extra}, confSuccess.url!));
        break;
      case 'replace':
        router?.push(replaceVariablesInTemplate({...paramsDict, ...extra}, confSuccess.url!));
        break;
      case 'retry':
        if (confSuccess.message) {
          message.success(confSuccess?.message);
        }

        if (confSuccess.delay) {
          setTimeout(() => {
            onRetry?.();
          }, Number(confSuccess.delay));

          await delay(Number(confSuccess.delay));
        } else {
          onRetry?.();
        }

        break;
      case 'effectRetry':
        if (confSuccess.message) {
          message.success(confSuccess?.message);
        }

        if (confSuccess.delay) {
          setTimeout(() => {
            onEffectRetry?.();
          }, Number(confSuccess.delay));

          await delay(Number(confSuccess.delay));
        } else {
          onEffectRetry?.();
        }

        break;
      case 'reset':
        onReset?.();
        break;
      case 'message':
        message.success(confSuccess.message!);
        break;
      case 'modal':
      {
        const confirmed = Modal.confirm({
          icon: null,
          width: confSuccess.modal?.width || 350,
          title: confSuccess.modal?.title,
          content: confSuccess.modal?.description,
          okText: confSuccess.modal?.ok?.name,
          cancelText: confSuccess.modal?.cancel?.name,
          footer: (_, {OkBtn, CancelBtn}) => {
            let domList: React.ReactNode[] = [];
            if(confSuccess.modal?.footer) {
              domList = confSuccess.modal?.footer.map(item =>
                <Button key={item.name} onClick={() => {
                  onSuccess(item as any, extra);
                  confirmed.destroy();
                }}>{item.name}</Button>)
            }
            return <>{domList}<OkBtn /><CancelBtn/></>
          },
          onOk() {
            if (!confSuccess.modal?.ok) return;
            onSuccess(confSuccess.modal?.ok as OnSuccessType, extra);
          },
          onCancel() {
            if (!confSuccess.modal?.cancel) return;
            onSuccess(confSuccess.modal?.cancel as OnSuccessType, extra);
          },
        });
      }
        break;
      default:
        message.success(confSuccess.message!);
        break;
    }
  };

  return onSuccess;
};

export default useSuccess;
