import EventEmitter from 'eventemitter3';

/**
 * 创建一个事件总线（Event Hub）
 *
 * @returns 包含订阅、取消订阅和发布事件功能的对象
 */
const useEventHub = () => {
  // 创建一个新的EventEmitter实例
  const emitter = new EventEmitter();

  return {
    // 订阅事件
    subscribe: (eventName: string, callback: (...args: any[]) => void) => {
      emitter.on(eventName, callback);
    },
    // 取消订阅事件
    unsubscribe: (eventName: string, callback: (...args: any[]) => void) => {
      emitter.removeListener(eventName, callback);
    },
    // 发布事件
    publish: (eventName: string, ...args: any[]) => {
      emitter.emit(eventName, ...args);
    },
  };
};
export default useEventHub;
