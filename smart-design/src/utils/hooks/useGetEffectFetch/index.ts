import { message } from 'antd';
import React from 'react';
import { ConfigContext } from '../../../components/config-provider';
import type { FetchType } from '../../../typing';
import { getEffectFetchConfig } from '../../getEffectFetchConfig';
import swrFetcher from '../../swrFetcher';
import useParamsDict from '../useParamsDict';

const useGetEffectFetch = (config?: FetchType) => {
  const configContext = React.useContext(ConfigContext);

  const paramsDict = useParamsDict();

  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [data, setData] = React.useState<any>(null);

  const fetch = async () => {
    setIsLoading(true);

    try {
      const dataIndex = config?.data;

      const { api, method, params } = getEffectFetchConfig(
        config!,
        configContext,
        paramsDict,
      );

      const { data } = await swrFetcher(api, method, params);

      let newData = dataIndex ? data[dataIndex] : data;

      setData(newData);
    } catch (err: any) {
      message.error(err.message);
    }

    setIsLoading(false);
  };

  React.useEffect(() => {
    if (!config) return;
    fetch();

    // console.log('config', config);
  }, [config]);

  return {
    data,
    isLoading,
  };
};

export default useGetEffectFetch;
