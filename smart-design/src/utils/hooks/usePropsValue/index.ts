import { useMemoizedFn, useUpdate } from 'ahooks';
import { SetStateAction, useRef } from 'react';

type Options<T> = {
  value?: T;
  defaultValue: T;
  onChange?: (v: T) => void;
};

/**
 * 自定义Hooks函数，用于管理传入的props值，并支持通过回调函数或直接设置值的方式更新状态
 *
 * @param options 选项对象，包含值、默认值、以及值变化时的回调函数
 * @param options.value 当前的值，若传入则使用传入的值作为初始状态
 * @param options.defaultValue 默认值，若未传入值，则使用默认值作为初始状态
 * @param options.onChange 值变化时的回调函数
 * @returns 返回一个元组，包含当前状态和更新状态的函数
 * - 第一个元素为当前状态值
 * - 第二个元素为更新状态的函数，支持传入回调函数或直接设置值的方式更新状态
 */
export default function usePropsValue<T>(options: Options<T>) {
  const { value, defaultValue, onChange } = options;

  const update = useUpdate();

  const stateRef = useRef<T>(value !== undefined ? value : defaultValue);
  if (value !== undefined) {
    stateRef.current = value;
  }

  const setState = useMemoizedFn(
    (v: SetStateAction<T>, forceTrigger: boolean = false) => {
      // `forceTrigger意味着触发器“onChange”，即使“v”与“stateRef.current”相同`
      const nextValue =
        typeof v === 'function'
          ? (v as (prevState: T) => T)(stateRef.current)
          : v;
      if (!forceTrigger && nextValue === stateRef.current) return;
      stateRef.current = nextValue;
      update();

      return onChange?.(nextValue);
    },
  );
  return [stateRef.current, setState] as const;
}
