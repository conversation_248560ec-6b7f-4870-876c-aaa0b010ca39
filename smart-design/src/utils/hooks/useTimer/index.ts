import { useEffect, useRef, useState } from 'react';

const useTimer = (duration: number, onTimeout?: () => void) => {
  const [isTimerActive, setIsTimerActive] = useState<boolean>(false);
  const timerRef = useRef<number | null>(null);

  const resetTimer = () => {
    if (!duration) return;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    timerRef.current = window.setTimeout(() => {
      onTimeout?.();
      setIsTimerActive(false);
    }, duration);
  };

  const startTimer = () => {
    if (!duration || !onTimeout) return;
    setIsTimerActive(true);
    resetTimer();
  };

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return { startTimer, resetTimer, isTimerActive };
};

export default useTimer;
