import { message } from 'antd';
import React from 'react';
import { ConfigContext } from '../../../components/config-provider';
import type { FetchMethodType, FetchType } from '../../../typing';
import { replaceVariablesInTemplate } from '../../replaceVariablesInTemplate';
import useParamsDict from '../useParamsDict';

type FetchConfigType = {
  api: string;
  method: FetchMethodType;
  params: Record<string, any>;
};

const useGetEffectFetchConfig = (
  config?: FetchType,
  dataSource: Record<string, any> = {},
) => {
  const { appProcessApi, appFunctionApi } = React.useContext(ConfigContext);

  const paramsDict = useParamsDict(dataSource);

  const [data, setData] = React.useState<FetchConfigType>({
    api: '',
    method: 'post',
    params: {},
  });

  const handleEffectFetchConfig = async () => {
    try {
      const effectFetch = config;
      const effectFetchType = config?.type;

      const defaultParams = config?.defaultParams || {};
      const newDefaultParams = JSON.parse(
        replaceVariablesInTemplate(paramsDict, JSON.stringify(defaultParams)),
      );

      let fetchUrl = '';
      let fetchMethod: FetchMethodType = 'post';
      let fetchParams = {};

      switch (effectFetchType) {
        case 'api':
          fetchParams = newDefaultParams;
          fetchUrl = effectFetch?.api as string;
          fetchMethod = effectFetch?.method || 'post';
          break;

        case 'function':
          fetchParams = {
            functionKey: effectFetch?.functionKey,
            params: newDefaultParams,
          };

          fetchUrl = appFunctionApi!;
          fetchMethod = 'post';
          break;

        case 'process':
          fetchParams = {
            processKey: effectFetch?.processKey,
            params: newDefaultParams,
          };

          fetchUrl = appProcessApi!;
          fetchMethod = 'post';
          break;

        default:
          break;
      }

      const api = replaceVariablesInTemplate(paramsDict, fetchUrl);

      setData({ api, method: fetchMethod, params: fetchParams });
    } catch (err: any) {
      message.error(err.message);
    }
  };

  React.useEffect(() => {
    if (!config) return;
    handleEffectFetchConfig();
  }, [config]);

  return {
    ...data,
  };
};

export default useGetEffectFetchConfig;
