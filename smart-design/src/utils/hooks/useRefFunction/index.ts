import { useCallback, useRef } from 'react';

/**
 * 使用 `useRef` 包裹一个函数，确保函数在组件的重新渲染过程中保持引用不变。
 *
 * @param reFunction 需要被包裹的函数
 * @returns 返回一个被 `useCallback` 包裹的、引用不变的函数
 * @template T 函数的类型，接受任意参数并返回任意类型
 */
const useRefFunction = <T extends (...args: any) => any>(reFunction: T) => {
  const ref = useRef<any>(null);
  ref.current = reFunction;
  return useCallback((...rest: Parameters<T>): ReturnType<T> => {
    return ref.current?.(...(rest as any));
  }, []);
};

export default useRefFunction;
