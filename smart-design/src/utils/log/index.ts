import { isEmpty } from 'lodash';
import { isDev } from '../is-dev';

type LogLevel = 'log' | 'info' | 'warn' | 'error' | 'debug';

interface LogOptions {
  level: LogLevel;
  component: string;
  message: any;
  additionalInfo?: any;
}

function formatMessage({ level, component, message }: LogOptions): string {
  const timestamp = new Date().toISOString();
  const formattedMessage =
    typeof message === 'string' ? message : JSON.stringify(message);

  return `[${timestamp}] [smartdeer-ui: ${component}] [${level.toUpperCase()}] ${formattedMessage}`;
}

function log({ level, component, message, additionalInfo }: LogOptions): void {
  const formattedMessage = formatMessage({
    level,
    component,
    message,
  });
  if (isDev || level === 'error') {
    console[level](formattedMessage);

    if (!isEmpty(additionalInfo)) {
      console[level](additionalInfo);
    }
  }
}

export function devLog(
  component: string,
  message: any,
  additionalInfo?: any,
): void {
  log({ level: 'log', component, message, additionalInfo });
}

export function devInfo(
  component: string,
  message: any,
  additionalInfo?: any,
): void {
  log({ level: 'info', component, message, additionalInfo });
}

export function devWarning(
  component: string,
  message: any,
  additionalInfo?: any,
): void {
  log({ level: 'warn', component, message, additionalInfo });
}

export function devError(
  component: string,
  message: any,
  additionalInfo?: any,
): void {
  log({ level: 'error', component, message, additionalInfo });
}

export function devDebug(
  component: string,
  message: any,
  additionalInfo?: any,
): void {
  log({ level: 'debug', component, message, additionalInfo });
}

export function prodError(
  component: string,
  message: any,
  additionalInfo?: any,
) {
  log({ level: 'error', component, message, additionalInfo });
}

export function prodWarning(
  component: string,
  message: any,
  additionalInfo?: any,
) {
  log({ level: 'warn', component, message, additionalInfo });
}
