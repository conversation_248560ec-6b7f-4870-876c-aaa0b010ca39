export type ControlType = {
  value: any;
  method: 'disabled' | 'show' | 'hide' | 'required';
  condition: '===' | '!==' | '>' | '>=' | '<' | '<=';
  rule: string[];
  action?: 'or' | 'and';
  field?: string;
};

export type DependencyType = {
  value: any;
  method: 'disabled' | 'show' | 'hide' | 'required';
  condition: '===' | '!==' | '>' | '>=' | '<' | '<=';
  targetField: string;
  action?: 'or' | 'and';
  name?: string;
};

export type TransformDataRule = {
  from: string | Array<string>;
  to?: string | Array<string>;
  format?: string;
};

export type TransformDataRules = Array<TransformDataRule>;

export type FetchMethodType = 'get' | 'GET' | 'post' | 'POST' | 'put' | 'PUT';

export type FetchWayType = 'function' | 'api' | 'process' | 'object';

export type FetchType = {
  if?: string;
  type: FetchWayType;
  api?: string;
  functionKey?: string;
  processKey?: string;
  objectKey?: string;
  method?: FetchMethodType;
  headers?: Record<string, any>;
  paramsInjectionLocation?: string[];
  defaultParams?: Record<string, any>;
  data?: string;
  sortKey?: string;
  sortOrder?: 'ascend' | 'descend';
  transformRequestParams?: TransformDataRules;
  transformResponseData?: TransformDataRules;
  isCached?: boolean;

  // 正在舍弃的字段
  action?: string;
  transformKey?: string;
  transformIndex?: string;
  transform?: TransformDataRules;
  transformParams?: TransformDataRules;
};

type OnSuccessTypeButton = OnSuccessType & {
  name?: string;
};

export type OnSuccessType = {
  type:
  | 'link'
  | 'replace'
  | 'retry'
  | 'message'
  | 'modal'
  | 'reset'
  | 'effectRetry';
  url?: string;
  message?: string;
  modal?: {
    title?: string;
    description?: string;
    ok?: OnSuccessTypeButton;
    cancel?: OnSuccessTypeButton;
    width?: string;
    footer?: OnSuccessTypeButton[];
  };
  delay?: string;
};

export type OnFail = {
  type:
  | 'link'
  | 'replace'
  | 'retry'
  | 'message'
  | 'modal'
  | 'reset'
  | 'effectRetry';
  url?: string;
  message?: string;
  modal?: {
    title?: string;
    description?: string;
    ok?: OnSuccessType & {
      name?: string;
    };
    cancel?: OnSuccessType & {
      name?: string;
    };
    width?: string;
  };
  delay?: string;
};

export type OnCloseType = {
  type: 'link' | 'replace' | 'retry';
  url?: string;
  message?: string;
};

export type SwrFetcherType = {
  api: string;
  method: FetchMethodType;
  params: Record<string, any>;
  extra?: Record<string, any>;
  type?: FetchWayType;
  dataIndex?: string;
};

export type SwitchValueType = {
  case: string;
  value: string;
};

export type EffectType = {
  show?: string;
  fetch?: FetchType;
  value?: string;
  switchValue?: SwitchValueType[];
  getValue?: string;
  required?: string;
  labelDescription?: SwitchValueType[];
  disabled?: string;
  publish?: string | string[];
  click?: string | string[];
};

export type DataOperationConfigType = {
  onGet?: FetchType | FetchType[];
  onCheck?: FetchType | FetchType[];

  onBeforeCreate?: FetchType | FetchType[];
  onCreate?: FetchType;
  onAfterCreate?: FetchType | FetchType[];

  onBeforeUpdate?: FetchType | FetchType[];
  onUpdate?: FetchType;
  onAfterUpdate?: FetchType | FetchType[];

  onSuccess?: OnSuccessType;
};
