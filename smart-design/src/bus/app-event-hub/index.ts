import EventEmitter from 'eventemitter3';
import { PAGE_EVENT_NAME_VALUE } from '../../consts';
import { devLog, prodWarning } from '../../utils/log';

class AppEventBus {
  ee: EventEmitter;

  /**
   * 构造函数
   *
   * 初始化一个EventEmitter实例，并将其赋值给类的属性ee
   */
  constructor() {
    this.ee = new EventEmitter();
  }

  /**
   * 订阅指定事件
   *
   * @param eventName 事件名称
   * @param callback 事件触发时调用的回调函数
   */
  subscribe(
    eventName: PAGE_EVENT_NAME_VALUE,
    callback: (...args: any[]) => void,
  ) {
    this.ee.on(eventName, callback);
  }

  /**
   * 取消订阅某个事件
   *
   * @param eventName 事件名称
   * @param callback 事件回调函数
   */
  unsubscribe(
    eventName: PAGE_EVENT_NAME_VALUE,
    callback: (...args: any[]) => void,
  ) {
    this.ee.removeListener(eventName, callback);
  }

  /**
   * 取消订阅所有事件监听器
   *
   * 调用此方法将移除当前实例中所有事件监听器。
   */
  unsubscribeAll() {
    this.ee.removeAllListeners();
  }

  /**
   * 发布事件
   *
   * @param eventName 事件名称
   * @param args 事件参数列表
   */
  publish(eventName: string, ...args: any[]) {
    devLog('AppEventBus', '------------------------------');
    devLog('AppEventBus', `发布事件：${eventName}`);
    devLog('AppEventBus', `${eventName}事件参数列表：${JSON.stringify(args)}`);
    devLog('AppEventBus', '------------------------------');

    const eventNames = this.ee.eventNames();

    if (!eventNames.includes(eventName)) {
      prodWarning('PageEventBus', `不存在该事件：${eventName}`);
      return;
    }

    this.ee.emit(eventName, ...args);
  }
}

const appEventBus = new AppEventBus();

export default appEventBus;
