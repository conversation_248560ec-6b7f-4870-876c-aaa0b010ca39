import EventEmitter from 'eventemitter3';
import { devLog, prodWarning } from '../../utils/log';

export type RecordType = Record<string, any>;

class PageEventBus {
  ee: EventEmitter;

  /**
   * 构造函数
   *
   * 初始化一个EventEmitter实例，并将其赋值给类的属性ee
   */
  constructor() {
    this.ee = new EventEmitter();
  }

  /**
   * 订阅指定事件
   *
   * @param eventName 事件名称
   * @param callback 事件触发时调用的回调函数
   */
  subscribe(eventName: string, callback: (...args: any[]) => void) {
    devLog('PageEventBus', `订阅事件：${eventName}`);
    this.ee.on(eventName, callback);
  }

  /**
   * 一次性订阅多个事件
   *
   * @param events 事件名称和回调函数的数组
   */
  subscribeMultiple(
    events: { eventName: string; callback: (...args: any[]) => void }[],
  ) {
    events.forEach(({ eventName, callback }) => {
      devLog('PageEventBus', `订阅事件：${eventName}`);
      this.ee.on(eventName, callback);
    });
  }

  /**
   * 取消订阅某个事件
   *
   * @param eventName 事件名称
   * @param callback 事件回调函数
   */
  unsubscribe(eventName: string, callback: (...args: any[]) => void) {
    devLog('PageEventBus', `取消订阅事件：${eventName}`);
    this.ee.removeListener(eventName, callback);
  }

  /**
   * 取消订阅所有事件监听器
   *
   * 调用此方法将移除当前实例中所有事件监听器。
   */
  unsubscribeAll() {
    devLog('PageEventBus', '------------------------------');
    devLog('PageEventBus', `当前 Page 事件监听器被移除`);
    devLog('PageEventBus', '------------------------------');

    this.ee.removeAllListeners();
  }

  /**
   * 获取当前订阅的所有事件名称
   *
   * @returns 事件名称数组
   */
  getSubscribedEvents(): string[] {
    return this.ee.eventNames() as string[];
  }

  /**
   * 发布事件
   *
   * @param eventName 事件名称
   * @param payload 事件参数
   */
  async publish(
    eventName: string,
    payload: RecordType,
  ): Promise<any[] | undefined> {
    devLog('PageEventBus', '------------------------------');
    devLog('PageEventBus', `发布事件：${eventName}`, payload);
    devLog('PageEventBus', '------------------------------');

    const eventNames = this.ee.eventNames();

    if (!eventNames.includes(eventName)) {
      prodWarning('PageEventBus', `不存在该事件：${eventName}`);
      return;
    }

    // 返回为给定事件注册的侦听器。
    const listeners = this.ee.listeners(eventName);

    const results = await Promise.all(
      listeners.map((listener) => listener(payload)),
    );

    return results;
  }

  /**
   * 并行发布多个事件
   *
   * @param events 包含事件名称和事件载荷的事件数组
   * @returns 发布事件的结果数组
   */
  async publishMultipleParallel(
    events: { eventName: string; payload: RecordType }[],
  ) {
    devLog('PageEventBus', '------------------------------');
    devLog(
      'PageEventBus',
      `并行发布多个事件：${events.map((e) => e.eventName).join(', ')}`,
      events,
    );
    devLog('PageEventBus', '------------------------------');

    const results = await Promise.all(
      events.map(({ eventName, payload }) => {
        return this.publish(eventName, payload);
      }),
    );
    return results;
  }

  /**
   * 串行一次性发布多个事件
   *
   * @param events 事件名称和参数的数组
   */
  async publishMultipleSerial(
    events: { eventName: string; payload: RecordType }[],
  ) {
    devLog('PageEventBus', '------------------------------');
    devLog(
      'PageEventBus',
      `串行发布多个事件：${events.map((e) => e.eventName).join(', ')}`,
    );
    devLog('PageEventBus', '------------------------------');

    const results = [];
    for (const { eventName, payload } of events) {
      devLog('PageEventBus', `开始发布事件：${eventName}`, payload);
      const result = await this.publish(eventName, payload);
      devLog('PageEventBus', `完成发布事件：${eventName}`);
      results.push(result);
    }
    return results;
  }
}

const pageEventBus = new PageEventBus();

export default pageEventBus;
