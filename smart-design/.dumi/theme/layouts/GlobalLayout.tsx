
import React from 'react';
import { ConfigProvider } from 'smart-design';
import { history, useOutlet, useParams, useSearchParams, useServerInsertedHTML } from 'dumi';


const APP_DEFAULT_ENTITY_ID = process.env.APP_DEFAULT_ENTITY_ID
const APP_API_BASE_URL = process.env.APP_API_BASE_URL
const APP_FUNCTION_API = process.env.APP_FUNCTION_API
const APP_PROCESS_API = process.env.APP_PROCESS_API
const APP_SERVICE_ABILITY_KEY = process.env.APP_SERVICE_ABILITY_KEY
const APP_FILE_TOKEN_COMMON_UPLOAD_API = process.env.APP_FILE_TOKEN_COMMON_UPLOAD_API

const Layout: React.FC = () => {
  const outlet = useOutlet();

  return (
    <ConfigProvider
      appDefaultEntityId={APP_DEFAULT_ENTITY_ID}
      userAccountId='1331'
      locale='en-US'
      appApiBaseUrl={APP_API_BASE_URL}
      appFunctionApi={APP_FUNCTION_API}
      appProcessApi={APP_PROCESS_API}

      appFileTokenCommonUploadApi={APP_FILE_TOKEN_COMMON_UPLOAD_API}

      serviceAbilityKey={APP_SERVICE_ABILITY_KEY}

      timezone={'Asia/Shanghai'}
      router={history}
      useUrlParams={useParams}
    >
      {outlet}
    </ConfigProvider>
  );
};

export default Layout;
