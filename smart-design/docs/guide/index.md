---
toc: content
title: 快速上手
---

## 环境准备

确保正确安装 [Node.js](https://nodejs.org/en/) 推荐版本 v20.10.0

```bash
$ node -v
v20.10.0
```

## 配置凭据

:::warning
使用配置文件设置凭证，`.npmrc` 文件不可以提交。

推荐使用交互式命令行设置凭证 `npm login`。
:::

参照下图 [配置 coding 配置](https://linglu.coding.net/p/linglupin/artifacts/22946316/npm/packages?hash=cfa2002384204407921f6fec65eccbaf)

![Alt text](./image.png)
## 安装

```jsx {5} | pure
npm install smart-design --registry=https://linglu-npm.pkg.coding.net/linglupin/smartdeer-ui/
```

## 引入

直接引入组件即可。

```js
import { Button } from 'smart-design'
```

## 问题反馈

如果在使用过程中发现任何问题、或者有改善建议，欢迎在飞书联系研发人员
