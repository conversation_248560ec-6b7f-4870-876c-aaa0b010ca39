# nginx文件
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 4096;
    multi_accept on;
}

http {
    map $upstream_response_time $upstream_response_time_map {
      default $upstream_response_time;
      ""      -1;
    }

    map $upstream_connect_time $upstream_connect_time_map {
      default $upstream_connect_time;
      ""      -1;
    }

    # log formate config
    log_format main   '$time_local|$hostname|$remote_addr|$remote_user|$upstream_addr|$request_time|'
                      '$upstream_response_time|$upstream_connect_time|$status|$upstream_status|'
                      '-|$bytes_sent|$upstream_bytes_sent|$upstream_bytes_received|'
                      '$request|$http_user_agent|$http_referer|$http_host|$http_x_forwarded_for|$http_authorization|'
                      '$scheme|$request_method|$cookie_user|';

    access_log  /var/log/nginx/access.log  main;

    sendfile             on;
    tcp_nopush           on;
    tcp_nodelay          on;
    keepalive_timeout    65;
    types_hash_max_size  2048;

    # enable gzip
    gzip  on;
    gzip_comp_level 6;
    gzip_types      text/xml application/xml application/atom+xml application/rss+xml application/xhtml+xml image/svg+xml
                    text/javascript application/javascript application/x-javascript
                    text/x-json application/json application/x-web-app-manifest+json
                    text/css text/plain text/x-component
                    font/opentype application/x-font-ttf application/vnd.ms-fontobject
                    image/x-icon;

    # mime config
    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # server config
    server {
      listen        80;
      server_name   localhost;
      root /www/;

      location / {
        alias /www/;
        add_header Cache-Control no-cache;
        try_files $uri /index.html =404;
      }

      location /assets {
        # 资源文件的缓存设置，并关闭log
        expires 365d;
        access_log off;
      }
    }
}