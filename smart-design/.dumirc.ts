import { defineConfig } from 'dumi';
import { version } from './package.json';

export default defineConfig({
  outputPath: 'docs-dist',
  themeConfig: {
    name: `smartdeer-ui/pc`,
    logo: 'https://global-image.smartdeer.work/p/images/0x7b96b6f4c47245cbb5fd9f395532b1c2.png',
    footer: `SmartDeer`,
    nav: {
      mode: 'override',
      value: [
        {
          title: '指南',
          link: '/guide',
        },
        {
          title: '组件',
          link: '/components/button',
        },
      ],
    },
  },
  // 修改 dumi 默认主题的主色，更多变量详见：
  // https://github.com/umijs/dumi/blob/master/src/client/theme-default/styles/variables.less
  theme: {
    '@s-sidebar-width': '240px',
    '@c-primary': '#FF814F',
    '@s-content-width': '100%',
    '@c-site-bg': '#fff',
    '@c-border': 'rgba(5, 5, 5, 0.06)',
  },
  plugins: ['@umijs/plugins/dist/tailwindcss', '@umijs/plugins/dist/antd'],
  tailwindcss: {},
  antd: {
    configProvider: {},
    theme: {
      token: {
        colorPrimary: '#FE9111',
        colorLink: '#FE9111',
        colorInfo: '#FE9111',
      },
    },
    styleProvider: {
      hashPriority: 'high',
      legacyTransformer: true,
    },
  },
  resolve: {
    // 配置入口文件路径，API 解析将从这里开始
    entryFile: './src/index.ts',
    atomDirs: [{ type: 'component', dir: 'src/components' }],
  },
  define: {
    'process.env.DUMI_VERSION': version,
    'process.env.APP_DEFAULT_ENTITY_ID': process.env.APP_DEFAULT_ENTITY_ID,

    'process.env.APP_API_BASE_URL': process.env.APP_API_BASE_URL,
    'process.env.APP_FUNCTION_API': process.env.APP_FUNCTION_API,
    'process.env.APP_PROCESS_API': process.env.APP_PROCESS_API,
    'process.env.APP_FILE_TOKEN_COMMON_UPLOAD_API':
      process.env.APP_FILE_TOKEN_COMMON_UPLOAD_API,
    'process.env.APP_SERVICE_ABILITY_KEY': process.env.APP_SERVICE_ABILITY_KEY,
  },
});
