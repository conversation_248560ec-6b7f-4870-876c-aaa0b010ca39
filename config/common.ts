import { defineConfig } from '@umijs/max';
import routes from './router/index';

export default defineConfig({
  title: 'SmartDeer',
  metas: [{ name: 'color-scheme', content: 'light' }],
  antd: {
    configProvider: {},
    theme: {
      token: {
        colorPrimary: '#FE9111',
        colorLink: '#FE9111',
        colorInfo: '#FE9111',
      },

      components: {
        Tabs: {
          horizontalItemGutter: '16px',
          horizontalItemPadding: '3px 0 7px 0',
          horizontalMargin: '0 0 20px 0',
        },
        Menu: {},
        Descriptions: {
          itemPaddingBottom: 8,
          itemPaddingEnd: 0,
          titleMarginBottom: 8,
        }
      },
    },
    styleProvider: {
      hashPriority: 'high',
      legacyTransformer: true,
    },
  },
  mfsu: {
    mfName: '',
    strategy: 'normal',
  },
  hash: true,
  manifest: {
    fileName: 'manifest.json',
  },
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {},
  tailwindcss: {},
  locale: {
    default: 'en-US',
    antd: true,
    baseNavigator: true,
    baseSeparator: '-',
    useLocalStorage: true,
    title: false,
  },
  routes: routes,
  npmClient: 'pnpm',
  jsMinifier: 'terser',
  cssMinifier: 'cssnano',
});
