import { defineConfig } from '@umijs/max';
import path from 'path';

import common from './common';

export default defineConfig({
  ...common,

  scripts: [
    'https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.30.js',
    'https://lf-package-cn.feishucdn.com/obj/feishu-static/lark/passport/qrcode/LarkSSOSDKWebQRCode-1.0.3.js',
  ],

  alias: {
    '@smartdeer-ui/pc': path.resolve(__dirname, '../smartdeer-ui-pc/src'),
    '@smart-design': path.resolve(__dirname, '../smart-design/src'),
  },

  define: {
    'process.env.APP_API_BASE_URL': process.env.APP_API_BASE_URL,
    'process.env.APP_FUNCTION_API': process.env.APP_FUNCTION_API,
    'process.env.APP_PROCESS_API': process.env.APP_PROCESS_API,
    'process.env.APP_GS_TIMEZONE': process.env.APP_GS_TIMEZONE,
    'process.env.APP_ENABLE_DEBUGGER': process.env.APP_ENABLE_DEBUGGER,
    'process.env.APP_IS_REQUES_ID': process.env.APP_IS_REQUES_ID,
    'process.env.APP_IS_ENCRYPT': process.env.APP_IS_ENCRYPT,
    'process.env.APP_ENCRYPT_TYPE': process.env.APP_ENCRYPT_TYPE,
    'process.env.APP_NODE_ENV': process.env.APP_NODE_ENV,
    'process.env.APP_RSA_PUBLIC_KEY': process.env.APP_RSA_PUBLIC_KEY,
    'process.env.APP_RSA_PRIVATE_KEY': process.env.APP_RSA_PRIVATE_KEY,
    'process.env.APP_IS_FILE_TOKEN': process.env.APP_IS_FILE_TOKEN,
    'process.env.APP_FILE_TOKEN_UPLOAD_API':
      process.env.APP_FILE_TOKEN_UPLOAD_API,
    'process.env.APP_FILE_TOKEN_API': process.env.APP_FILE_TOKEN_API,
    'process.env.APP_FILE_TOKEN_EXPIRE_TIME':
      process.env.APP_FILE_TOKEN_EXPIRE_TIME,
    'process.env.APP_FILE_DOWNLOAD_API': process.env.APP_FILE_DOWNLOAD_API,
    'process.env.APP_FLOW_TOKEN_FILE_UPLOAD_API':
      process.env.APP_FLOW_TOKEN_FILE_UPLOAD_API,
    'process.env.APP_TOKEN_EXPIRED': process.env.APP_TOKEN_EXPIRED,
    'process.env.APP_ADMIN_FUNCTION_API': process.env.APP_ADMIN_FUNCTION_API,
    'process.env.APP_ADMIN_PROCESS_API': process.env.APP_ADMIN_PROCESS_API,
    'process.env.APP_FORMATTER_UI_BY_KEY': process.env.APP_FORMATTER_UI_BY_KEY,
    'process.env.APP_FORMATTER_FORM_BY_KEY':
      process.env.APP_FORMATTER_FORM_BY_KEY,
    'process.env.APP_FORMATTER_ELEMENT_BY_KEY':
      process.env.APP_FORMATTER_ELEMENT_BY_KEY,
    'process.env.APP_MODULE_CHECK': process.env.APP_MODULE_CHECK,
    'process.env.APP_PDF_DOWNLOAD_URL': process.env.APP_PDF_DOWNLOAD_URL,
    'process.env.APP_SERVICE_ABILITY_KEY': process.env.APP_SERVICE_ABILITY_KEY,
    'process.env.APP_FUNCTION_KEY_GET_PRO_FLOW_AUDIT_PREVIEW':
      process.env.APP_FUNCTION_KEY_GET_PRO_FLOW_AUDIT_PREVIEW,
    'process.env.APP_SMART_PAGE_MOCK': process.env.APP_SMART_PAGE_MOCK,
    'process.env.APP_RIVER_FUNCTION_API': process.env.APP_RIVER_FUNCTION_API,
    'process.env.APP_RIVER_PROCESS_API': process.env.APP_RIVER_PROCESS_API,
    'process.env.APP_RIVER_AUTH_FUNCTION_API':
      process.env.APP_RIVER_AUTH_FUNCTION_API,
    'process.env.APP_RIVER_AUTH_PROCESS_API':
      process.env.APP_RIVER_AUTH_PROCESS_API,
    'process.env.APP_RIVER_ENTITY_FUNCTION_API':
      process.env.APP_RIVER_ENTITY_FUNCTION_API,
    'process.env.APP_RIVER_ENTITY_PROCESS_API':
      process.env.APP_RIVER_ENTITY_PROCESS_API,
    'process.env.APP_API_RIVER_URL': process.env.APP_API_RIVER_URL,
    'process.env.APP_SYSTEM_NAME':
      process.env.APP_SYSTEM_NAME,
  },
});
