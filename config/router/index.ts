import account from './account';
import approvalLeave from './approval-leave';
import audit from './audit';
import bill from './bill';
import bm from './bm';
import contract from './contract';
import contractor from './contractor';
import corehr from './corehr';
import employeeOffboardingProcess from './employee-offboarding-process';
import employeeOnboardingProcess from './employee-onboarding-process';
import eor from './eor';
import finance from './finance';
import leave from './leave';
import login from './login';
import reimburse from './reimburse';
import ssc from './ssc';
import balance from './balance';
import hro from './hro';

const routes = [
  {
    path: '/',
    redirect: '/login',
    layout: false,
    wrappers: ['@/wrappers/project-access', '@/wrappers/source'],
  },
  {
    name: 'lizi',
    path: '/lizi-test',
    layout: false,
    component: '@/pages/bm/lizi-test',
  },

  ...login,

  {
    name: '忘记密码',
    path: '/forgot-password',
    component: '@/pages/forgot-password',
    layout: false,
    wrappers: ['@/wrappers/project-access'],
  },

  {
    name: 'landing',
    path: '/:entityId/landing',
    component: '@/pages/landing',
    layout: false,
    wrappers: ['@/wrappers/project-access'],
  },

  {
    name: '菜单',
    path: '/home',
    component: '@/pages/home',
    layout: false,
    wrappers: ['@/wrappers/project-access', '@/wrappers/auth'],
  },

  ...bm,

  {
    path: '/:entityId',
    layout: false,
    wrappers: ['@/wrappers/project-access', '@/wrappers/auth', '@/wrappers/to-new-page'],
    routes: [
      account,
      leave,
      reimburse,
      ssc,
      corehr,
      eor,
      contract,
      finance,
      contractor,
      audit,
      bill,
      hro,
    ],
  },

  {
    path: '/:entityId/account/language-setting',
    wrappers: ['@/wrappers/project-access', '@/wrappers/auth', '@/wrappers/to-new-page'],
    layout: false,
    component: '@/pages/account/language-setting',
  },

  // 假期审批
  ...approvalLeave,

  // 员工入职流程
  ...employeeOnboardingProcess,
  ...employeeOffboardingProcess,

  {
    name: 'xxx登录',
    path: '/n/:entityUUID/login',
    layout: false,
    component: '@/layouts/login',
    wrappers: ['@/wrappers/project-access', '@/wrappers/source', '@/wrappers/login'],
    routes: [
      {
        path: '',
        name: '登录',
        component: '@/pages/login/default-new',
      },
    ],
  },
  {
    name: '忘记密码',
    path: '/n/:entityUUID/forgot-password',
    component: '@/pages/forgot-password-new',
    layout: false,
    wrappers: ['@/wrappers/project-access'],
  },
  {
    name: 'xxx菜单',
    path: '/n/:entityUUID/home',
    component: '@/pages/home-new',
    layout: false,
    wrappers: ['@/wrappers/project-access', '@/wrappers/auth'],
  },
  {
    name: '语言设置',
    path: '/n/:entityUUID/account/language-setting',
    component: '@/pages/account/language-setting',
    wrappers: ['@/wrappers/project-access'],
  },

  {
    name: 'H5公司卡报销',
    path: '/n/:entityUUID/eor/corporate-card/h5-apply',
    component: '@/pages/eor/corporate-card/h5-apply',
    layout: false,
  },

  {
    path: '/n/:entityUUID',
    wrappers: [
      '@/wrappers/project-access',
      '@/wrappers/auth',
      '@/wrappers/entity-route-auth',
      '@/wrappers/user-module-auth',
    ],
    routes: [
      account,
      leave,
      reimburse,
      ssc,
      corehr,
      eor,
      contract,
      finance,
      contractor,
      audit,
      bill,
      balance,
      hro,
    ],
  },

  {
    path: '/n/:entityUUID/chat',
    component: '@/pages/chat'
  },

  //  {
  //    path: ':entityUUID/smart-page-preview',
  //    component: '@/pages/smart-page-preview'
  // },

  // 动态路由
  {
    path: '/n/:entityUUID/:moduleKey/dynamic/:confkey/page',
    wrappers: ['@/wrappers/project-access', '@/wrappers/auth'],
    component: '@/pages/dynamic/page',
  },

  {
    name: 'n',
    path: 'n/:entityUUID/app/:productCode/:urlKey',
    wrappers: ['@/wrappers/project-access', '@/wrappers/auth'],
    component: '@/pages/smart-page',
  },

  {
    name: '403',
    path: '/403',
    component: '@/pages/403',
    layout: false,
    wrappers: ['@/wrappers/project-access'],
  },
  // {
  //   path: '/*',
  //   redirect: '/404',
  //   layout: false,
  //   wrappers: ['@/wrappers/project-access'],
  // },
  // {
  //   name: '404',
  //   path: '/404',
  //   component: '@/pages/404',
  //   layout: false,
  //   wrappers: ['@/wrappers/project-access'],
  // },
];

export default routes;
