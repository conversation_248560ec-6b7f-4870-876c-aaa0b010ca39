const routes = [
  {
    name: 'employee-onboarding-process',
    path: '/employee-onboarding-process',
    layout: false,
    component: '@/layouts/employee-onboarding-process/index',
    routes: [
      {
        name: '欢迎页',
        path: 'welcome',
        component: '@/pages/employee-onboarding-process/welcome/index',
      },
    ],
  },
  {
    name: 'employee-onboarding-process',
    path: '/employee-onboarding-process/:flowToken',
    layout: false,
    component: '@/layouts/employee-onboarding-process/index',
    routes: [
      {
        name: '授权页',
        path: 'auth',
        component: '@/pages/employee-onboarding-process/auth/index',
      },
      {
        name: '流程',
        path: 'flow',
        component: '@/pages/employee-onboarding-process/flow/index',
      },
      {
        name: '流程执行',
        path: 'step/:key',
        component: '@/pages/employee-onboarding-process/step/index',
      },
    ],
  },
];

export default routes;
