const routes = {
  name: '核心人事',
  custom_module_key: 'hro.hr.management',
  path: 'bm/:entityUUID/corehr',
  hideInMenu: true,
  wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  routes: [
    {
      name: '员工信息',
      custom_module_key: 'hro.hr.management',
      path: 'employee',
      routes: [
        {
          name: '在职员工列表',
          custom_module_key: 'hro.hr.management',
          path: 'working',
          routes: [
            {
              name: '在职员工列表',
              custom_module_key: 'hro.hr.management',
              path: 'list',
              component: '@/pages/corehr/employee/working/list',
            },
            {
              name: '员工详情',
              custom_module_key: 'hro.hr.management',
              path: 'detail/:uuid',
              component: '@/pages/corehr/employee/working/detail',
            },
          ],
        },
        {
          name: '待入职',
          custom_module_key: 'hro.hr.management',
          path: 'on-boarding',
          routes: [
            {
              name: '待入职',
              custom_module_key: 'hro.hr.management',
              path: 'list',
              component: '@/pages/corehr/employee/on-boarding/list',
            },
            {
              name: '预入职员工详情',
              custom_module_key: 'hro.hr.management',
              path: 'detail/:uuid',
              component: '@/pages/corehr/employee/on-boarding/detail',
            },
          ],
        },
        {
          name: '待离职',
          path: 'off-boarding',
          routes: [
            {
              name: '待离职',
              custom_module_key: 'hro.hr.management',
              path: 'list',
              component: '@/pages/corehr/employee/off-boarding/list',
            },
            {
              name: '员工详情',
              custom_module_key: 'hro.hr.management',
              path: 'detail/:uuid',
              component: '@/pages/corehr/employee/working/detail',
            },
          ],
        },
        {
          name: '已离职',
          custom_module_key: 'hro.hr.management',
          path: 'resigned',
          routes: [
            {
              name: '已离职',
              custom_module_key: 'hro.hr.management',
              path: 'list',
              component: '@/pages/corehr/employee/resigned/list',
            },
            {
              name: '员工详情',
              custom_module_key: 'hro.hr.management',
              path: 'detail/:uuid',
              component: '@/pages/corehr/employee/resigned/detail',
            },
          ],
        },
      ],
    },

    {
      name: '薪资单',
      custom_module_key: 'hro.hr.management',
      path: 'payroll',
      routes: [
        {
          name: '薪资单',
          custom_module_key: 'hro.hr.management',
          path: 'employee',
          routes: [
            {
              name: '薪资单',
              custom_module_key: 'hro.hr.management',
              custom_conf_key: 'hro.hr.management.salary',
              custom_check_key: '2FA_HRO_HR_MANAGEMENT_SALARY_CHECK',
              path: 'list',
              component: '@/pages/corehr/payroll/employee/list',
            },
            {
              name: '薪资单详情',
              custom_module_key: 'hro.hr.management',
              path: 'detail/:uuid',
              component: '@/pages/corehr/payroll/employee/detail',
            },
            {
              name: '薪资单详情月份列表',
              custom_module_key: 'hro.hr.management',
              path: 'month',
              component: '@/pages/corehr/payroll/employee/month',
            },
            {
              name: '报税单',
              custom_module_key: 'hro.hr.management',
              path: 'file',
              component: '@/pages/corehr/payroll/files',
            },
          ],
        },

        {
          name: '薪资组',
          custom_module_key: 'hro.hr.management',
          path: 'group',
          routes: [
            {
              name: '薪资组',
              custom_module_key: 'hro.hr.management',
              path: 'list',
              component: '@/pages/corehr/payroll/group/list',
            },
            {
              name: '薪资人员',
              custom_module_key: 'hro.hr.management',
              path: ':id/people',
              component: '@/pages/corehr/payroll/group/people',
            },
          ],
        },

        {
          name: '薪资项',
          custom_module_key: 'hro.hr.management',
          path: 'setting',
          routes: [
            {
              name: '薪资项',
              custom_module_key: 'hro.hr.management',
              path: 'list',
              component: '@/pages/corehr/payroll/setting/list',
            },
          ],
        },
      ],
    },

    {
      name: '组织架构',
      custom_module_key: 'hro.hr.management',
      path: 'organizational-structure',
      routes: [
        {
          name: '公司部门',
          custom_module_key: 'hro.hr.management',
          path: 'department',
          component: '@/pages/corehr/organizational-structure/department',
        },
        {
          name: '职位架构',
          custom_module_key: 'hro.hr.management',
          path: 'position',
          component: '@/pages/corehr/organizational-structure/position',
        },
        {
          name: '汇报关系',
          custom_module_key: 'hro.hr.management',
          path: 'report',
          component: '@/pages/corehr/organizational-structure/report',
        },
      ],
    },

    {
      name: '成本中心',
      custom_module_key: 'hro.hr.management',
      path: 'cost-center',
      routes: [
        {
          name: '成本中心',
          custom_module_key: 'hro.hr.management',
          path: 'group',
          routes: [
            {
              name: '成本中心',
              custom_module_key: 'hro.hr.management',
              path: 'list',
              component: '@/pages/corehr/cost-center/group/list',
            },
            {
              name: '成本中心查看人员',
              custom_module_key: 'hro.hr.management',
              path: ':id/people',
              component: '@/pages/corehr/cost-center/group/people',
            },
          ],
        },
      ],
    },
  ],
};

export default routes;
