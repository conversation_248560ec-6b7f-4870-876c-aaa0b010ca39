const routes = {
  name: '实体',
  path: 'entity',
  layout: false,
  key: 'gs.entity',
  wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  routes: [
    {
      path: '',
      redirect: 'list',
    },
    {
      name: '添加实体',
      path: 'new',
      key: 'gs.entity.create',
      component: '@/pages/bm/entity/new',
      hideInMenu: true,
    },
    {
      name: '编辑实体',
      path: ':id/edit',
      key: 'gs.entity.edit',
      component: '@/pages/bm/entity/edit',
      hideInMenu: true,
    },
    {
      name: '实体列表',
      path: 'list',
      key: 'gs.entity.list',
      component: '@/pages/bm/entity/list',
      hideInMenu: true,
    },
    {
      name: '添加实体成功',
      path: 'success',
      // key: 'gs.entity.success',
      component: '@/pages/bm/entity/success',
      hideInMenu: true,
    },
  ],
}

export default routes;
