const routes = {
  name: '分组',
  path: 'group',
  layout: false,
  key: 'gs.group',
  wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  routes: [
    {
      path: '',
      redirect: 'list',
    },
    {
      name: '添加分组',
      path: 'new',
      key: 'gs.group.create',
      component: '@/pages/bm/group/new',
      hideInMenu: true,
    },
    {
      name: '编辑分组',
      path: ':id/edit',
      key: 'gs.group.edit',
      component: '@/pages/bm/group/edit',
      hideInMenu: true,
    },
    {
      name: '分组列表',
      path: 'list',
      key: 'gs.group.list',
      component: '@/pages/bm/group/list',
      hideInMenu: true,
    },
  ],
}

export default routes;
