const route = {
  name: '我的休假',
  key: 'gs.employee.leave',
  custom_module_key: 'hro.leave',
  path: 'bm/:entityUUID/leave',
  // wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  routes: [
    {
      path: '',
      redirect: 'remainder/list'
    },
    {
      name: '假期余额',
      custom_module_key: 'hro.leave',
      path: 'remainder',
      routes: [
        {
          name: '假期余额',
          custom_module_key: 'hro.leave',
          path: 'list',
          component: '@/pages/leave/remainder/list',
        },

        {
          name: '余额明细',
          custom_module_key: 'hro.leave',
          path: 'detail',
          component: '@/pages/leave/remainder/detail',
        },
      ],
    },

    {
      name: '请假/销假',
      custom_module_key: 'hro.leave',
      path: 'application-or-revocation',
      component: '@/pages/leave/application-or-revocation',
    },

    {
      name: '请假/销假记录',
      custom_module_key: 'hro.leave',
      path: 'record',
      routes: [
        {
          name: '请假/销假列表',
          custom_module_key: 'hro.leave',
          path: 'list',
          component: '@/pages/leave/record/list',
        },

        {
          name: '请假/销假详情',
          path: 'detail',
          component: '@/pages/leave/record/detail',
        },
      ],
    },

    {
      name: '审批',
      custom_module_key: 'hro.leave',
      path: 'audit',
      routes: [
        {
          name: '审批列表',
          custom_module_key: 'hro.leave',
          path: 'list',
          component: '@/pages/leave/audit/list',
        },

        {
          name: '审批详情',
          custom_module_key: 'hro.leave',
          path: 'detail',
          component: '@/pages/leave/audit/detail',
        },
      ],
    },

    {
      name: '休假日历',
      custom_module_key: 'hro.leave',
      path: 'calendar',
      component: '@/pages/leave/calendar',
    },

    {
      name: 'Admin',
      custom_module_key: 'hro.leave',
      path: 'admin',
      routes: [
        {
          name: '员工请假记录',
          custom_module_key: 'hro.leave',
          path: 'list',
          component: '@/pages/leave/admin/list',
        },
        {
          name: '员工请假数据',
          custom_module_key: 'hro.leave',
          path: 'data',
          component: '@/pages/leave/admin/data',
        },
        {
          name: '代请假',
          custom_module_key: 'hro.leave',
          path: 'proxy-leave',
          component: '@/pages/leave/admin/proxy-leave',
        },
        {
          name: '代审批',
          custom_module_key: 'hro.leave',
          path: 'proxy-approval',
          component: '@/pages/leave/admin/proxy-approval',
        },
      ],
    },
  ],
};

export default route;
