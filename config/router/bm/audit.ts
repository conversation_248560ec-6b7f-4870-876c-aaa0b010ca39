const routes = {
  name: '审批管理',
  custom_module_key: 'hro.audit',
  path: 'bm/:entityUUID/audit',
  wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  routes: [
    {
      name: 'eor 报销审批',
      custom_module_key: 'hro.audit',
      path: 'eor-reimburse',
      routes: [
        {
          name: '列表',
          custom_module_key: 'hro.audit',
          path: 'list',
          component: '@/pages/audit/eor-reimburse/list',
        },
        {
          name: '报销审批详情',
          custom_module_key: 'hro.audit',
          path: ':uuid/detail',
          component: '@/pages/audit/eor-reimburse/detail',
        },
      ],
    },
    {
      name: '请假审批',
      custom_module_key: 'hro.audit',
      path: 'eor-leave',
      routes: [
        {
          name: '列表',
          custom_module_key: 'hro.audit',
          path: 'list',
          component: '@/pages/audit/eor-leave/list',
        },
        {
          name: '详情',
          custom_module_key: 'hro.audit',
          path: 'detail',
          component: '@/pages/audit/eor-leave/detail',
        },
      ],
    },
  ],
};

export default routes;
