const route = {
  name: 'EOR 报销',
  custom_module_key: 'eor.receipt',
  path: 'reimburse',
  wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  routes: [
    {
      name: '申请报销',
      custom_module_key: 'eor.receipt',
      path: 'apply',
      component: '@/pages/eor/reimburse/apply',
    },
    {
      name: '重新申请报销',
      custom_module_key: 'eor.receipt',
      path: 'reapply',
      component: '@/pages/eor/reimburse/reapply',
    },
    {
      name: '报销记录',
      custom_module_key: 'eor.receipt',
      path: 'record',
      routes: [
        {
          name: '列表',
          custom_module_key: 'eor.receipt',
          path: 'list',
          component: '@/pages/eor/reimburse/record/list',
        },
        {
          name: '报销记录详情',
          custom_module_key: 'eor.receipt',
          path: 'detail/:uuid',
          component: '@/pages/eor/reimburse/record/detail',
        },
      ],
    },
    {
      name: '报销审批',
      custom_module_key: 'eor.receipt',
      path: 'audit',
      routes: [
        {
          name: '报销审批详情',
          custom_module_key: 'eor.receipt',
          path: 'detail/:uuid',
          component: '@/pages/eor/reimburse/audit/detail',
        },
      ],
    },
  ],
};

export default route;
