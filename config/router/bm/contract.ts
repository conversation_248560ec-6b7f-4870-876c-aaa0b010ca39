const routes = {
  name: '合同管理',
  path: 'bm/:entityUUID/contract',
  key: 'gs.menu.contract',
  wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  routes: [
    {
      name: '员工合同',
      path: 'employee',
      key: 'gs.menu.contract.employee',
      routes: [
        {
          path: 'list',
          component: '@/pages/bm/contract/employee/list',
        },
      ],
    },
    {
      name: '企业服务合同',
      path: 'business',
      key: 'gs.menu.contract.business',
      routes: [
        {
          path: 'list',
          component: '@/pages/bm/contract/business/list',
        },
        {
          path: 'add',
          component: '@/pages/bm/contract/business/add',
        },
      ],
    },
  ],
}

export default routes;
