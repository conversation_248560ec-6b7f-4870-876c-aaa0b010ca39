const routes = {
  name: '我的',
  path: 'bm/:entityUUID/account',
  routes: [
    {
      name: '我的信息',
      path: 'basic/detail',
      component: '@/pages/account/basic',
      wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
    },
    {
      name: '我的团队',
      path: 'team',
      component: '@/pages/account/team',
      wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
    },
    {
      name: '消息通知详情',
      path: 'notification',
      routes: [
        {
          name: '消息通知',
          path: 'list',
          component: '@/pages/account/notification/list',
        },
        {
          name: '消息通知详情',
          path: ':uuid/detail',
          component: '@/pages/account/notification/detail',
        },
      ],
    },
    {
      name: '我的薪酬',
      path: 'salary',
      wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
      routes: [
        {
          name: '薪酬档案',
          path: 'list',
          component: '@/pages/account/salary/list',
        },
        {
          name: '我的文件',
          path: 'files',
          component: '@/pages/account/salary/files',
        },
      ],
    },

    // {
    //   name: '公司卡',
    //   path: 'corporate-card',
    //   component: '@/pages/account/corporate-card',
    //   wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
    // },


    {
      name: '我的账户',
      path: 'a',
      routes: [
        {
          name: 'EOR/HRO',
          path: 'eor-hro',
          wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
          routes: [
            {
              name: '薪酬',
              path: 'payslip',
              component: '@/pages/bm/account/eor-hro/payslip',
            },
            {
              name: '公司卡',
              path: 'corporate-card',
              component: '@/pages/bm/account/eor-hro/corporate-card',
            },
          ],
        },

        {
          name: 'IC 账户',
          path: 'ic',
          wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
          routes: [
            {
              name: '余额',
              path: 'balance',
              component: '@/pages/bm/account/ic/balance',
            },
            {
              name: '发票',
              path: 'invoice',
              component: '@/pages/bm/account/ic/invoice',
            },
            {
              name: '钱包',
              path: 'wallet',
              component: '@/pages/bm/account/ic/wallet',
            },
            {
              name: '卡',
              path: 'card',
              component: '@/pages/bm/account/ic/card',
            },
            {
              name: 'EWA',
              path: 'ewa',
              component: '@/pages/bm/account/ic/ewa',
            },
          ],
        },
      ],
    },
  ],
};

export default routes;
