const routes = {
  name: '权限',
  path: 'bm/:entityUUID/permission',
  layout: false,
  key: 'gs.permission',
  wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  routes: [
    {
      path: '',
      redirect: 'role/list',
    },
    {
      name: '角色',
      path: 'role',
      key: 'gs.permission.role',
      routes: [
        {
          path: '',
          redirect: 'list',
        },
        {
          name: '添加角色',
          path: 'new',
          key: 'gs.permission.role.create',
          component: '@/pages/bm/permission/role/new',
          hideInMenu: true,
          layout: false,
        },
        {
          name: '编辑角色',
          path: ':id/edit',
          key: 'gs.permission.role.edit',
          component: '@/pages/bm/permission/role/edit',
          hideInMenu: true,
          layout: false,
        },
        {
          name: '角色列表',
          path: 'list',
          key: 'gs.permission.role.list',
          component: '@/pages/bm/permission/role/list',
          hideInMenu: true,
        },
        {
          name: '角色成员列表',
          path: ':roleId/members',
          component: '@/pages/bm/permission/role/members',
          hideInMenu: true,
        },
      ],
    },
    {
      name: '成员',
      path: 'member',
      key: 'gs.permission.member',
      routes: [
        {
          path: '',
          redirect: 'list',
        },
        {
          name: '成员列表',
          path: 'list',
          key: 'gs.permission.member.list',
          component: '@/pages/bm/permission/member/list',
          hideInMenu: true,
        },
      ],
    },
  ],
}

export default routes;
