import leave from './leave';
import corehr from './corehr';
import contract from './contract';
import permission from './permission';
import eor from './eor';
import audit from './audit';
import setting from './setting';
import account from './account';
import balance from './balance';

const routes = [
  {
    name: 'login',
    path: 'bm/login',
    layout: false,
    component: '@/pages/bm/login',
    wrappers: ['@/wrappers/project-access', '@/wrappers/bm/login'],
  },

  {
    name: '注册',
    path: 'bm/signup',
    layout: false,
    component: '@/layouts/signup/side-info-layout',
    wrappers: ['@/wrappers/project-access'],
    routes: [
      {
        name: '注册账号',
        path: 'account',
        component: '@/pages/bm/signup/account',
      },

      {
        name: 'steps',
        path: 'steps',
        component: '@/pages/bm/signup/steps',
      },

      {
        name: 'success',
        path: 'success',
        component: '@/pages/bm/signup/success',
      },
    ],
  },

  {
    name: '忘记密码',
    path: 'bm/forgot-password',
    layout: false,
    component: '@/pages/bm/forgot-password',
    wrappers: ['@/wrappers/project-access'],
  },

  {
    name: '修改密码',
    path: 'bm/:entityUUID/forgot-password',
    layout: false,
    component: '@/pages/bm/forgot-password',
    wrappers: ['@/wrappers/project-access'],
  },

  {
    name: '首页',
    path: 'bm/home',
    layout: false,
    component: '@/pages/bm/home',
    wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
  },

  {
    name: '邀请',
    path: 'bm/invite/account',
    layout: false,
    component: '@/layouts/signup/side-info-layout',
    wrappers: ['@/wrappers/project-access'],
    routes: [
      {
        path: '',
        component: '@/pages/bm/invite/account-profile',
      },
    ],
  },

  account,
  permission,
  contract,
  leave,
  corehr,
  eor,
  audit,
  balance,
  setting,

  {
    name: 'bm',
    path: 'bm/:entityUUID/app/:productCode/:urlKey',
    wrappers: ['@/wrappers/project-access', '@/wrappers/bm/auth', '@/wrappers/bm/access'],
    component: '@/pages/bm/smart-page',
  },

  {
    name: '联系销售',
    path: 'bm/:entityUUID/contact-service',
    layout: false,
    component: '@/pages/bm/contact-service',
    wrappers: ['@/wrappers/project-access'],
  },

  {
    name: '403',
    path: 'bm/403',
    layout: false,
    component: '@/pages/bm/403',
    wrappers: ['@/wrappers/project-access'],
  },


  // {
  //   name: 'bm',
  //   path: 'bm',
  //   routes: [
  //     {
  //       name: '主实体',
  //       path: ':entityUUID',
  //       wrappers: ['@/wrappers/bm/auth', '@/wrappers/bm/access'],
  //       routes: [
  //         {
  //           name: '工单',
  //           path: 'app/:productCode/:urlKey',
  //           component: '@/pages/bm/smart-page',
  //         },
  //       ],
  //     },
  //   ],
  // },
];

export default routes;
