const routes = [
  {
    name: '登录',
    path: '/login',
    // redirect: '/login/method',
    layout: false,
    hideInMenu: true,
    component: '@/layouts/login',
    wrappers: ['@/wrappers/project-access', '@/wrappers/source'],
    routes: [
      {
        name: '登录方式',
        path: '',
        component: '@/pages/login/default',
        wrappers: ['@/wrappers/login'],
      },
      {
        name: '登录方式',
        path: 'method',
        component: '@/pages/login/method',
        wrappers: ['@/wrappers/login'],
      },
      {
        name: '选择实体',
        path: 'select-entity/:appName/:address',
        component: '@/pages/login/select-entity',
        wrappers: ['@/wrappers/login'],
      },
      {
        name: '单点登录',
        path: 'sso/:app',
        component: '@/pages/login/sso',
        wrappers: ['@/wrappers/login'],
      },
      {
        name: '二维码登录',
        path: 'qr-code/:app',
        component: '@/pages/login/qr-code',
        wrappers: ['@/wrappers/login'],
      },
      {
        name: '默认登录方式',
        path: 'default',
        component: '@/pages/login/default',
        wrappers: ['@/wrappers/login'],
      },
      {
        name: '选择语言',
        path: 'system-language',
        component: '@/pages/login/system-language',
      },
    ],
  },
];
export default routes;
