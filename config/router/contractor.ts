const route = {
  name: 'contractor管理',
  custom_module_key: 'hro.contractor',
  path: 'contractor',
  routes: [
    {
      name: 'contractors',
      custom_module_key: 'hro.contractor',
      path: 'contractors',
      routes: [
        {
          name: '在职员工列表',
          custom_module_key: 'hro.contractor',
          path: 'working',
          routes: [
            {
              name: '在职员工列表',
              custom_module_key: 'hro.contractor',
              path: 'list',
              component: '@/pages/contractor/contractors/working/list',
            },
            {
              name: '员工详情',
              custom_module_key: 'hro.contractor',
              path: 'detail/:uuid',
              component: '@/pages/contractor/contractors/working/detail',
            },
          ],
        },
        {
          name: '待入职',
          custom_module_key: 'hro.contractor',
          path: 'on-boarding',
          routes: [
            {
              name: '待入职',
              custom_module_key: 'hro.contractor',
              path: 'list',
              component: '@/pages/contractor/contractors/on-boarding/list',
            },
            {
              name: '预入职员工详情',
              custom_module_key: 'hro.contractor',
              path: 'detail/:uuid',
              component: '@/pages/contractor/contractors/on-boarding/detail',
            },
          ],
        },
        {
          name: '待离职',
          custom_module_key: 'hro.contractor',
          path: 'off-boarding',
          routes: [
            {
              name: '待离职',
              custom_module_key: 'hro.contractor',
              path: 'list',
              component: '@/pages/contractor/contractors/off-boarding/list',
            },
          ],
        },
        {
          name: '已离职',
          custom_module_key: 'hro.contractor',
          path: 'resigned',
          routes: [
            {
              name: '已离职',
              custom_module_key: 'hro.contractor',
              path: 'list',
              component: '@/pages/contractor/contractors/resigned/list',
            },
            {
              name: '员工详情',
              custom_module_key: 'hro.contractor',
              path: 'detail/:uuid',
              component: '@/pages/contractor/contractors/resigned/detail',
            },
          ],
        },

        {
          name: '合同',
          custom_module_key: 'hro.contractor',
          path: 'contract',
          routes: [
            {
              name: '列表',
              custom_module_key: 'hro.contractor',
              path: 'list',
              component: '@/pages/contractor/contractors/contract/list',
            },
            {
              name: '新增',
              custom_module_key: 'hro.contractor',
              path: 'add',
              component: '@/pages/contractor/contractors/contract/add',
            },
            {
              name: '编辑',
              custom_module_key: 'hro.contractor',
              path: ':uuid/edit',
              component: '@/pages/contractor/contractors/contract/add',
            },
            {
              name: '详情',
              custom_module_key: 'hro.contractor',
              path: ':uuid/detail',
              component: '@/pages/contractor/contractors/contract/detail',
            },
          ],
        },
      ],
    },

    {
      name: 'payroll',
      custom_module_key: 'hro.contractor',
      path: 'payroll',
      routes: [
        {
          name: '账单',
          custom_module_key: 'hro.contractor',
          path: 'payment',
          routes: [
            {
              name: '列表',
              custom_module_key: 'hro.contractor',
              path: 'list',
              component: '@/pages/contractor/payroll/payment/list',
            },
          ],
        },
        {
          name: '订单',
          custom_module_key: 'hro.contractor',
          path: 'billing',
          routes: [
            {
              name: '订单列表',
              custom_module_key: 'hro.contractor',
              path: 'list',
              component: '@/pages/contractor/payroll/billing/list',
            },
            {
              name: '订单详情预览',
              custom_module_key: 'hro.contractor',
              path: 'detail/preview',
              component: '@/pages/contractor/payroll/billing/detail',
            },
            {
              name: '付款给IC列表',
              custom_module_key: 'hro.contractor',
              path: ':billingUuid/invoice/list',
              component: '@/pages/contractor/payroll/billing/invoice/list',
            },
          ],
        },
        {
          name: '发票',
          custom_module_key: 'hro.contractor',
          path: 'invoice',
          routes: [
            {
              name: '发票列表',
              custom_module_key: 'hro.contractor',
              path: 'list',
              component: '@/pages/contractor/payroll/invoice/list',
            },
          ],
        },
      ],
    },
  ],
};

export default route;
