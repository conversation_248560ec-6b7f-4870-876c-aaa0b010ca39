const route = {
  name: '我的休假',
  custom_module_key: 'hro.leave',
  path: 'leave',
  routes: [
    {
      name: '假期余额',
      custom_module_key: 'hro.leave',
      path: 'remainder',
      routes: [
        {
          name: '假期余额',
          custom_module_key: 'hro.leave',
          path: 'list',
          component: '@/pages/hro/leave/remainder/list',
        },

        {
          name: '余额明细',
          custom_module_key: 'hro.leave',
          path: 'detail',
          component: '@/pages/hro/leave/remainder/detail',
        },
      ],
    },

    {
      name: '请假/销假',
      custom_module_key: 'hro.leave',
      path: 'application-or-revocation',
      component: '@/pages/hro/leave/application-or-revocation',
    },

    {
      name: '请假/销假记录',
      custom_module_key: 'hro.leave',
      path: 'record',
      routes: [
        {
          name: '请假/销假列表',
          custom_module_key: 'hro.leave',
          path: 'list',
          component: '@/pages/hro/leave/record/list',
        },

        {
          name: '请假/销假详情',
          path: 'detail',
          custom_module_key: 'hro.leave',
          component: '@/pages/hro/leave/record/detail',
        },
      ],
    },

    {
      name: '审批',
      custom_module_key: 'hro.leave',
      path: 'audit',
      routes: [
        {
          name: '审批列表',
          custom_module_key: 'hro.leave',
          path: 'list',
          component: '@/pages/hro/leave/audit/list',
        },

        {
          name: '审批详情',
          custom_module_key: 'hro.leave',
          path: 'detail',
          component: '@/pages/hro/leave/audit/detail',
        },
      ],
    },
    {
      name: '休假日历',
      custom_module_key: 'hro.leave',
      path: 'calendar',
      component: '@/pages/hro/leave/calendar',
    },
  ],
};

export default route;
