const routes = {
  name: '工单',
  custom_module_key: 'hro.ticket',
  path: 'ssc',
  hideInMenu: true,
  routes: [
    {
      name: '工单',
      custom_module_key: 'hro.ticket',
      path: 'report',
      routes: [
        {
          name: '工单列表',
          custom_module_key: 'hro.ticket',
          path: 'list',
          component: '@/pages/ssc/list',
        },
        {
          name: '发起工单',
          custom_module_key: 'hro.ticket',
          path: 'create',
          component: '@/pages/ssc/add',
          hideInMenu: true,
        },
        {
          name: '工单详情',
          custom_module_key: 'hro.ticket',
          path: 'detail/:reportId',
          component: '@/pages/ssc/detail',
          hideInMenu: true,
        },
      ],
    },

    {
      name: '新工单',
      custom_module_key: 'hro.ticket',
      path: 'ticket',
      routes: [
        {
          name: '新工单列表',
          custom_module_key: 'hro.ticket',
          path: 'list',
          component: '@/pages/ssc/list-new',
        },
        {
          name: '发起新工单',
          custom_module_key: 'hro.ticket',
          path: 'create',
          component: '@/pages/ssc/add-new',
          hideInMenu: true,
        },
        {
          name: '新工单详情',
          custom_module_key: 'hro.ticket',
          path: 'detail/:uuid',
          component: '@/pages/ssc/detail-new',
          hideInMenu: true,
        },
      ],
    },
  ],
};
export default routes;
